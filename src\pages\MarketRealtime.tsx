import { createSignal, onMount, onCleanup, For } from 'solid-js'
import { css } from '../../styled-system/css'
import { Card, Button, Input, Table, Tag, Dropdown, Message } from '../components/ui'
import { ColumnDef } from '@tanstack/solid-table'

interface StockData {
  code: string
  name: string
  price: number
  change: number
  changePercent: number
  volume: number
  turnover: number
  high: number
  low: number
  open: number
  status: 'trading' | 'suspended' | 'closed'
}

interface MarketIndex {
  name: string
  value: number
  change: number
  changePercent: number
}

export default function MarketRealtime() {
  const [searchValue, setSearchValue] = createSignal('')
  const [selectedSector, setSelectedSector] = createSignal('all')
  const [loading, setLoading] = createSignal(false)
  const [stockData, setStockData] = createSignal<StockData[]>([])
  const [marketIndices, setMarketIndices] = createSignal<MarketIndex[]>([])

  // 模拟数据
  const mockStockData: StockData[] = [
    {
      code: '000001',
      name: '平安银行',
      price: 12.45,
      change: 0.23,
      changePercent: 1.88,
      volume: 125000000,
      turnover: 1550000000,
      high: 12.68,
      low: 12.20,
      open: 12.30,
      status: 'trading',
    },
    {
      code: '000002',
      name: '万科A',
      price: 18.76,
      change: -0.45,
      changePercent: -2.34,
      volume: 89000000,
      turnover: 1670000000,
      high: 19.20,
      low: 18.50,
      open: 19.10,
      status: 'trading',
    },
    {
      code: '600036',
      name: '招商银行',
      price: 35.67,
      change: 1.23,
      changePercent: 3.57,
      volume: 67000000,
      turnover: 2390000000,
      high: 36.00,
      low: 34.80,
      open: 35.00,
      status: 'trading',
    },
    {
      code: '600519',
      name: '贵州茅台',
      price: 1678.90,
      change: -12.50,
      changePercent: -0.74,
      volume: 2100000,
      turnover: 3520000000,
      high: 1695.00,
      low: 1670.00,
      open: 1685.00,
      status: 'trading',
    },
    {
      code: '000858',
      name: '五粮液',
      price: 156.78,
      change: 2.34,
      changePercent: 1.52,
      volume: 15600000,
      turnover: 2440000000,
      high: 158.90,
      low: 154.20,
      open: 155.00,
      status: 'suspended',
    },
  ]

  const mockMarketIndices: MarketIndex[] = [
    { name: '上证指数', value: 3245.67, change: 23.45, changePercent: 0.73 },
    { name: '深证成指', value: 12456.78, change: -45.23, changePercent: -0.36 },
    { name: '创业板指', value: 2567.89, change: 12.34, changePercent: 0.48 },
    { name: '科创50', value: 1234.56, change: -8.90, changePercent: -0.72 },
  ]

  const sectorOptions = [
    { key: 'all', label: '全部板块', onClick: () => setSelectedSector('all') },
    { key: 'bank', label: '银行', onClick: () => setSelectedSector('bank') },
    { key: 'tech', label: '科技', onClick: () => setSelectedSector('tech') },
    { key: 'consumer', label: '消费', onClick: () => setSelectedSector('consumer') },
    { key: 'healthcare', label: '医药', onClick: () => setSelectedSector('healthcare') },
  ]

  const columns: ColumnDef<StockData, any>[] = [
    {
      accessorKey: 'code',
      header: '代码',
      cell: (info) => (
        <span class={css({ fontFamily: 'monospace', fontWeight: '500' })}>
          {info.getValue()}
        </span>
      ),
    },
    {
      accessorKey: 'name',
      header: '名称',
      cell: (info) => (
        <span class={css({ fontWeight: '500', color: 'text.primary' })}>
          {info.getValue()}
        </span>
      ),
    },
    {
      accessorKey: 'price',
      header: '现价',
      cell: (info) => {
        const row = info.row.original
        const color = row.change > 0 ? 'danger.500' : row.change < 0 ? 'success.500' : 'text.regular'
        return (
          <span class={css({ fontFamily: 'monospace', fontWeight: '600', color })}>
            ¥{info.getValue().toFixed(2)}
          </span>
        )
      },
    },
    {
      accessorKey: 'change',
      header: '涨跌额',
      cell: (info) => {
        const value = info.getValue() as number
        const color = value > 0 ? 'danger.500' : value < 0 ? 'success.500' : 'text.regular'
        const prefix = value > 0 ? '+' : ''
        return (
          <span class={css({ fontFamily: 'monospace', color })}>
            {prefix}{value.toFixed(2)}
          </span>
        )
      },
    },
    {
      accessorKey: 'changePercent',
      header: '涨跌幅',
      cell: (info) => {
        const value = info.getValue() as number
        const type = value > 0 ? 'danger' : value < 0 ? 'success' : 'info'
        const prefix = value > 0 ? '+' : ''
        return (
          <Tag type={type} effect="light" size="small">
            {prefix}{value.toFixed(2)}%
          </Tag>
        )
      },
    },
    {
      accessorKey: 'volume',
      header: '成交量',
      cell: (info) => {
        const value = info.getValue() as number
        const formatted = value > 100000000 ? `${(value / 100000000).toFixed(1)}亿` : `${(value / 10000).toFixed(0)}万`
        return (
          <span class={css({ fontFamily: 'monospace', fontSize: '13px' })}>
            {formatted}
          </span>
        )
      },
    },
    {
      accessorKey: 'status',
      header: '状态',
      cell: (info) => {
        const status = info.getValue() as string
        const statusMap = {
          trading: { label: '交易中', type: 'success' as const },
          suspended: { label: '停牌', type: 'warning' as const },
          closed: { label: '休市', type: 'info' as const },
        }
        const config = statusMap[status] || statusMap.closed
        return <Tag type={config.type} effect="light" size="small">{config.label}</Tag>
      },
    },
    {
      id: 'actions',
      header: '操作',
      cell: (info) => (
        <div class={css({ display: 'flex', gap: '8px' })}>
          <Button size="small" variant="primary" onClick={() => handleBuy(info.row.original)}>
            买入
          </Button>
          <Button size="small" variant="danger" onClick={() => handleSell(info.row.original)}>
            卖出
          </Button>
        </div>
      ),
    },
  ]

  const handleBuy = (stock: StockData) => {
    Message.success(`买入 ${stock.name} 操作已提交`)
  }

  const handleSell = (stock: StockData) => {
    Message.error(`卖出 ${stock.name} 操作已提交`)
  }

  const handleRefresh = () => {
    setLoading(true)
    Message.info('正在刷新数据...')
    setTimeout(() => {
      setLoading(false)
      Message.success('数据刷新完成')
    }, 1500)
  }

  onMount(() => {
    setStockData(mockStockData)
    setMarketIndices(mockMarketIndices)
  })

  return (
    <div class={css({ padding: '20px', minHeight: '100vh', backgroundColor: 'bg.page' })}>
      {/* 页面标题和工具栏 */}
      <div class={css({ marginBottom: '20px' })}>
        <div class={css({
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          marginBottom: '16px',
        })}>
          <h1 class={css({
            fontSize: '24px',
            fontWeight: '600',
            color: 'text.primary',
            margin: 0,
          })}>
            实时行情
          </h1>
          <div class={css({ display: 'flex', alignItems: 'center', gap: '12px' })}>
            <Input
              placeholder="搜索股票代码或名称..."
              value={searchValue()}
              onInput={(e) => setSearchValue(e.currentTarget.value)}
              clearable
              onClear={() => setSearchValue('')}
              class={css({ width: '240px' })}
            />
            <Dropdown
              items={sectorOptions}
              trigger={
                <Button variant="default" size="default">
                  {sectorOptions.find(s => s.key === selectedSector())?.label} ▼
                </Button>
              }
            />
            <Button variant="primary" onClick={handleRefresh} loading={loading()}>
              刷新
            </Button>
          </div>
        </div>
      </div>

      {/* 市场指数概览 */}
      <div class={css({ marginBottom: '24px' })}>
        <div class={css({
          display: 'grid',
          gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',
          gap: '16px',
        })}>
          <For each={marketIndices()}>
            {(index) => (
              <Card
                shadow="hover"
                bodyStyle={{
                  padding: '16px',
                  textAlign: 'center',
                }}
              >
                <div class={css({ fontSize: '14px', color: 'text.secondary', marginBottom: '8px' })}>
                  {index.name}
                </div>
                <div class={css({
                  fontSize: '20px',
                  fontWeight: '600',
                  color: 'text.primary',
                  marginBottom: '4px',
                  fontFamily: 'monospace',
                })}>
                  {index.value.toFixed(2)}
                </div>
                <div class={css({ display: 'flex', alignItems: 'center', justifyContent: 'center', gap: '8px' })}>
                  <span class={css({
                    fontSize: '12px',
                    color: index.change > 0 ? 'danger.500' : index.change < 0 ? 'success.500' : 'text.regular',
                    fontFamily: 'monospace',
                  })}>
                    {index.change > 0 ? '+' : ''}{index.change.toFixed(2)}
                  </span>
                  <Tag
                    type={index.changePercent > 0 ? 'danger' : index.changePercent < 0 ? 'success' : 'info'}
                    effect="light"
                    size="small"
                  >
                    {index.changePercent > 0 ? '+' : ''}{index.changePercent.toFixed(2)}%
                  </Tag>
                </div>
              </Card>
            )}
          </For>
        </div>
      </div>

      {/* 股票列表 */}
      <Card header="股票列表" shadow="always">
        <Table
          data={stockData()}
          columns={columns}
          loading={loading()}
          pagination={true}
          pageSize={10}
          sortable={true}
          sticky={true}
          height="600px"
          onRowClick={(row) => Message.info(`查看 ${row.name} 详情`)}
        />
      </Card>
    </div>
  )
}
