# Monaco Editor 本地化配置

## 已完成本地化部署

本项目已将 Monaco Editor 本地化，避免外部 CDN 依赖。

### 文件结构

```
public/libs/monaco-editor/
└── min/
    └── vs/
        ├── base/
        ├── basic-languages/
        ├── editor/
        ├── language/
        └── loader.js
```

### 配置说明

- `VITE_MONACO_LOCAL=true`: 使用本地资源（已配置）
- `VITE_MONACO_LOCAL=false`: 使用 CDN 资源

### 本地化优势

✅ 避免外网依赖  
✅ 提高加载速度  
✅ 支持内网/离线部署  
✅ 避免 CDN 可用性问题  

### 验证方式

访问策略编辑页面，打开浏览器控制台查看日志：
- 本地模式：`Monaco Editor 配置: 本地模式, 路径: /libs/monaco-editor/min/vs`
- 无外网请求到 cdn.jsdelivr.net

当前配置：**本地模式已启用**
