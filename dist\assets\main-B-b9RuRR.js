import{m as U,s as te,t as V,a as lt,i as C,b as Q,c as O,d as f,e as Tt,f as H,g as R,S as ae,h as is,j as Mi,F as Fe,k as Fi,P as Do,l as ls,o as ee,n as ss,D as as,p as Y,u as Xe,q as Ye,r as xe,v as pt,w as cs,x as ds,y as Ei,z as kn,A as zt,B as us,C as Vr,E as gs,R as tt,G as fs,H as ps}from"./vendor-solid-CRKygbqg.js";import{l as Br}from"./vendor-editor-l7stcynF.js";(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const r of document.querySelectorAll('link[rel="modulepreload"]'))o(r);new MutationObserver(r=>{for(const i of r)if(i.type==="childList")for(const l of i.addedNodes)l.tagName==="LINK"&&l.rel==="modulepreload"&&o(l)}).observe(document,{childList:!0,subtree:!0});function n(r){const i={};return r.integrity&&(i.integrity=r.integrity),r.referrerPolicy&&(i.referrerPolicy=r.referrerPolicy),r.crossOrigin==="use-credentials"?i.credentials="include":r.crossOrigin==="anonymous"?i.credentials="omit":i.credentials="same-origin",i}function o(r){if(r.ep)return;r.ep=!0;const i=n(r);fetch(r.href,i)}})();function Bn(e){return typeof e=="object"&&e!=null&&!Array.isArray(e)}function Ai(e){return Object.fromEntries(Object.entries(e??{}).filter(([t,n])=>n!==void 0))}var hs=e=>e==="base";function ms(e){return e.slice().filter(t=>!hs(t))}function Kr(e){return String.fromCharCode(e+(e>25?39:97))}function vs(e){let t="",n;for(n=Math.abs(e);n>52;n=n/52|0)t=Kr(n%52)+t;return Kr(n%52)+t}function xs(e,t){let n=t.length;for(;n;)e=e*33^t.charCodeAt(--n);return e}function bs(e){return vs(xs(5381,e)>>>0)}var Di=/\s*!(important)?/i;function ys(e){return typeof e=="string"?Di.test(e):!1}function Ss(e){return typeof e=="string"?e.replace(Di,"").trim():e}function Ti(e){return typeof e=="string"?e.replaceAll(" ","_"):e}var jn=e=>{const t=new Map;return(...o)=>{const r=JSON.stringify(o);if(t.has(r))return t.get(r);const i=e(...o);return t.set(r,i),i}};function To(...e){return e.filter(Boolean).reduce((n,o)=>(Object.keys(o).forEach(r=>{const i=n[r],l=o[r];Bn(i)&&Bn(l)?n[r]=To(i,l):n[r]=l}),n),{})}var Cs=e=>e!=null;function zi(e,t,n={}){const{stop:o,getKey:r}=n;function i(l,s=[]){if(Bn(l)||Array.isArray(l)){const a={};for(const[c,m]of Object.entries(l)){const p=r?.(c,m)??c,g=[...s,p];if(o?.(l,g))return t(l,s);const u=i(m,g);Cs(u)&&(a[p]=u)}return a}return t(l,s)}return i(e)}function ws(e,t){return e.reduce((n,o,r)=>{const i=t[r];return o!=null&&(n[i]=o),n},{})}function Li(e,t,n=!0){const{utility:o,conditions:r}=t,{hasShorthand:i,resolveShorthand:l}=o;return zi(e,s=>Array.isArray(s)?ws(s,r.breakpoints.keys):s,{stop:s=>Array.isArray(s),getKey:n?s=>i?l(s):s:void 0})}var _s={shift:e=>e,finalize:e=>e,breakpoints:{keys:[]}},Rs=e=>typeof e=="string"?e.replaceAll(/[\n\s]+/g," "):e;function ks(e){const{utility:t,hash:n,conditions:o=_s}=e,r=l=>[t.prefix,l].filter(Boolean).join("-"),i=(l,s)=>{let a;if(n){const c=[...o.finalize(l),s];a=r(t.toHash(c,bs))}else a=[...o.finalize(l),r(s)].join(":");return a};return jn(({base:l,...s}={})=>{const a=Object.assign(s,l),c=Li(a,e),m=new Set;return zi(c,(p,g)=>{const u=ys(p);if(p==null)return;const[h,...b]=o.shift(g),S=ms(b),x=t.transform(h,Ss(Rs(p)));let y=i(S,x.className);u&&(y=`${y}!`),m.add(y)}),Array.from(m).join(" ")})}function Is(...e){return e.flat().filter(t=>Bn(t)&&Object.keys(Ai(t)).length>0)}function Ps(e){function t(r){const i=Is(...r);return i.length===1?i:i.map(l=>Li(l,e))}function n(...r){return To(...t(r))}function o(...r){return Object.assign({},...t(r))}return{mergeCss:jn(n),assignCss:o}}var $s=/([A-Z])/g,Ms=/^ms-/,Fs=jn(e=>e.startsWith("--")?e:e.replace($s,"-$1").replace(Ms,"-ms-").toLowerCase()),Es="cm,mm,Q,in,pc,pt,px,em,ex,ch,rem,lh,rlh,vw,vh,vmin,vmax,vb,vi,svw,svh,lvw,lvh,dvw,dvh,cqw,cqh,cqi,cqb,cqmin,cqmax,%";`${Es.split(",").join("|")}`;function As(e,...t){const n=Object.getOwnPropertyDescriptors(e),o=Object.keys(n),r=l=>{const s={};for(let a=0;a<l.length;a++){const c=l[a];n[c]&&(Object.defineProperty(s,c,n[c]),delete n[c])}return s},i=l=>r(Array.isArray(l)?l:o.filter(l));return t.map(i).concat(r(o))}var Ds=(...e)=>e.filter(Boolean).reduce((t,n)=>Array.from(new Set([...t,...n])),[]);const Ts="_dark,_light,_hover,_focus,_focusWithin,_focusVisible,_disabled,_active,_visited,_target,_readOnly,_readWrite,_empty,_checked,_enabled,_expanded,_highlighted,_before,_after,_firstLetter,_firstLine,_marker,_selection,_file,_backdrop,_first,_last,_only,_even,_odd,_firstOfType,_lastOfType,_onlyOfType,_peerFocus,_peerHover,_peerActive,_peerFocusWithin,_peerFocusVisible,_peerDisabled,_peerChecked,_peerInvalid,_peerExpanded,_peerPlaceholderShown,_groupFocus,_groupHover,_groupActive,_groupFocusWithin,_groupFocusVisible,_groupDisabled,_groupChecked,_groupExpanded,_groupInvalid,_indeterminate,_required,_valid,_invalid,_autofill,_inRange,_outOfRange,_placeholder,_placeholderShown,_pressed,_selected,_default,_optional,_open,_closed,_fullscreen,_loading,_currentPage,_currentStep,_motionReduce,_motionSafe,_print,_landscape,_portrait,_osDark,_osLight,_highContrast,_lessContrast,_moreContrast,_ltr,_rtl,_scrollbar,_scrollbarThumb,_scrollbarTrack,_horizontal,_vertical,_starting,sm,smOnly,smDown,md,mdOnly,mdDown,lg,lgOnly,lgDown,xl,xlOnly,xlDown,2xl,2xlOnly,2xlDown,smToMd,smToLg,smToXl,smTo2xl,mdToLg,mdToXl,mdTo2xl,lgToXl,lgTo2xl,xlTo2xl,@/xs,@/sm,@/md,@/lg,@/xl,@/2xl,@/3xl,@/4xl,@/5xl,@/6xl,@/7xl,@/8xl,base",Oi=new Set(Ts.split(","));function Wr(e){return Oi.has(e)||/^@|&|&$/.test(e)}const zs=/^_/,Ls=/&|@/;function Os(e){return e.map(t=>Oi.has(t)?t.replace(zs,""):Ls.test(t)?`[${Ti(t.trim())}]`:t)}function Vs(e){return e.sort((t,n)=>{const o=Wr(t),r=Wr(n);return o&&!r?1:!o&&r?-1:0})}const Bs="aspectRatio:aspect,boxDecorationBreak:decoration,zIndex:z,boxSizing:box,objectPosition:obj-pos,objectFit:obj-fit,overscrollBehavior:overscroll,overscrollBehaviorX:overscroll-x,overscrollBehaviorY:overscroll-y,position:pos/1,top:top,left:left,insetInline:inset-x/insetX,insetBlock:inset-y/insetY,inset:inset,insetBlockEnd:inset-b,insetBlockStart:inset-t,insetInlineEnd:end/insetEnd/1,insetInlineStart:start/insetStart/1,right:right,bottom:bottom,float:float,visibility:vis,display:d,hideFrom:hide,hideBelow:show,flexBasis:basis,flex:flex,flexDirection:flex/flexDir,flexGrow:grow,flexShrink:shrink,gridTemplateColumns:grid-cols,gridTemplateRows:grid-rows,gridColumn:col-span,gridRow:row-span,gridColumnStart:col-start,gridColumnEnd:col-end,gridAutoFlow:grid-flow,gridAutoColumns:auto-cols,gridAutoRows:auto-rows,gap:gap,gridGap:gap,gridRowGap:gap-x,gridColumnGap:gap-y,rowGap:gap-x,columnGap:gap-y,justifyContent:justify,alignContent:content,alignItems:items,alignSelf:self,padding:p/1,paddingLeft:pl/1,paddingRight:pr/1,paddingTop:pt/1,paddingBottom:pb/1,paddingBlock:py/1/paddingY,paddingBlockEnd:pb,paddingBlockStart:pt,paddingInline:px/paddingX/1,paddingInlineEnd:pe/1/paddingEnd,paddingInlineStart:ps/1/paddingStart,marginLeft:ml/1,marginRight:mr/1,marginTop:mt/1,marginBottom:mb/1,margin:m/1,marginBlock:my/1/marginY,marginBlockEnd:mb,marginBlockStart:mt,marginInline:mx/1/marginX,marginInlineEnd:me/1/marginEnd,marginInlineStart:ms/1/marginStart,spaceX:space-x,spaceY:space-y,outlineWidth:ring-width/ringWidth,outlineColor:ring-color/ringColor,outline:ring/1,outlineOffset:ring-offset/ringOffset,divideX:divide-x,divideY:divide-y,divideColor:divide-color,divideStyle:divide-style,width:w/1,inlineSize:w,minWidth:min-w/minW,minInlineSize:min-w,maxWidth:max-w/maxW,maxInlineSize:max-w,height:h/1,blockSize:h,minHeight:min-h/minH,minBlockSize:min-h,maxHeight:max-h/maxH,maxBlockSize:max-b,color:text,fontFamily:font,fontSize:fs,fontWeight:fw,fontSmoothing:smoothing,fontVariantNumeric:numeric,letterSpacing:tracking,lineHeight:leading,textAlign:text-align,textDecoration:text-decor,textDecorationColor:text-decor-color,textEmphasisColor:text-emphasis-color,textDecorationStyle:decoration-style,textDecorationThickness:decoration-thickness,textUnderlineOffset:underline-offset,textTransform:text-transform,textIndent:indent,textShadow:text-shadow,textShadowColor:text-shadow/textShadowColor,textOverflow:text-overflow,verticalAlign:v-align,wordBreak:break,textWrap:text-wrap,truncate:truncate,lineClamp:clamp,listStyleType:list-type,listStylePosition:list-pos,listStyleImage:list-img,backgroundPosition:bg-pos/bgPosition,backgroundPositionX:bg-pos-x/bgPositionX,backgroundPositionY:bg-pos-y/bgPositionY,backgroundAttachment:bg-attach/bgAttachment,backgroundClip:bg-clip/bgClip,background:bg/1,backgroundColor:bg/bgColor,backgroundOrigin:bg-origin/bgOrigin,backgroundImage:bg-img/bgImage,backgroundRepeat:bg-repeat/bgRepeat,backgroundBlendMode:bg-blend/bgBlendMode,backgroundSize:bg-size/bgSize,backgroundGradient:bg-gradient/bgGradient,textGradient:text-gradient,gradientFromPosition:gradient-from-pos,gradientToPosition:gradient-to-pos,gradientFrom:gradient-from,gradientTo:gradient-to,gradientVia:gradient-via,gradientViaPosition:gradient-via-pos,borderRadius:rounded/1,borderTopLeftRadius:rounded-tl/roundedTopLeft,borderTopRightRadius:rounded-tr/roundedTopRight,borderBottomRightRadius:rounded-br/roundedBottomRight,borderBottomLeftRadius:rounded-bl/roundedBottomLeft,borderTopRadius:rounded-t/roundedTop,borderRightRadius:rounded-r/roundedRight,borderBottomRadius:rounded-b/roundedBottom,borderLeftRadius:rounded-l/roundedLeft,borderStartStartRadius:rounded-ss/roundedStartStart,borderStartEndRadius:rounded-se/roundedStartEnd,borderStartRadius:rounded-s/roundedStart,borderEndStartRadius:rounded-es/roundedEndStart,borderEndEndRadius:rounded-ee/roundedEndEnd,borderEndRadius:rounded-e/roundedEnd,border:border,borderWidth:border-w,borderTopWidth:border-tw,borderLeftWidth:border-lw,borderRightWidth:border-rw,borderBottomWidth:border-bw,borderColor:border,borderInline:border-x/borderX,borderInlineWidth:border-x/borderXWidth,borderInlineColor:border-x/borderXColor,borderBlock:border-y/borderY,borderBlockWidth:border-y/borderYWidth,borderBlockColor:border-y/borderYColor,borderLeft:border-l,borderLeftColor:border-l,borderInlineStart:border-s/borderStart,borderInlineStartWidth:border-s/borderStartWidth,borderInlineStartColor:border-s/borderStartColor,borderRight:border-r,borderRightColor:border-r,borderInlineEnd:border-e/borderEnd,borderInlineEndWidth:border-e/borderEndWidth,borderInlineEndColor:border-e/borderEndColor,borderTop:border-t,borderTopColor:border-t,borderBottom:border-b,borderBottomColor:border-b,borderBlockEnd:border-be,borderBlockEndColor:border-be,borderBlockStart:border-bs,borderBlockStartColor:border-bs,boxShadow:shadow/1,boxShadowColor:shadow-color/shadowColor,mixBlendMode:mix-blend,filter:filter,brightness:brightness,contrast:contrast,grayscale:grayscale,hueRotate:hue-rotate,invert:invert,saturate:saturate,sepia:sepia,dropShadow:drop-shadow,blur:blur,backdropFilter:backdrop,backdropBlur:backdrop-blur,backdropBrightness:backdrop-brightness,backdropContrast:backdrop-contrast,backdropGrayscale:backdrop-grayscale,backdropHueRotate:backdrop-hue-rotate,backdropInvert:backdrop-invert,backdropOpacity:backdrop-opacity,backdropSaturate:backdrop-saturate,backdropSepia:backdrop-sepia,borderCollapse:border,borderSpacing:border-spacing,borderSpacingX:border-spacing-x,borderSpacingY:border-spacing-y,tableLayout:table,transitionTimingFunction:ease,transitionDelay:delay,transitionDuration:duration,transitionProperty:transition-prop,transition:transition,animation:animation,animationName:animation-name,animationTimingFunction:animation-ease,animationDuration:animation-duration,animationDelay:animation-delay,transformOrigin:origin,rotate:rotate,rotateX:rotate-x,rotateY:rotate-y,rotateZ:rotate-z,scale:scale,scaleX:scale-x,scaleY:scale-y,translate:translate,translateX:translate-x/x,translateY:translate-y/y,translateZ:translate-z/z,accentColor:accent,caretColor:caret,scrollBehavior:scroll,scrollbar:scrollbar,scrollMargin:scroll-m,scrollMarginLeft:scroll-ml,scrollMarginRight:scroll-mr,scrollMarginTop:scroll-mt,scrollMarginBottom:scroll-mb,scrollMarginBlock:scroll-my/scrollMarginY,scrollMarginBlockEnd:scroll-mb,scrollMarginBlockStart:scroll-mt,scrollMarginInline:scroll-mx/scrollMarginX,scrollMarginInlineEnd:scroll-me,scrollMarginInlineStart:scroll-ms,scrollPadding:scroll-p,scrollPaddingBlock:scroll-pb/scrollPaddingY,scrollPaddingBlockStart:scroll-pt,scrollPaddingBlockEnd:scroll-pb,scrollPaddingInline:scroll-px/scrollPaddingX,scrollPaddingInlineEnd:scroll-pe,scrollPaddingInlineStart:scroll-ps,scrollPaddingLeft:scroll-pl,scrollPaddingRight:scroll-pr,scrollPaddingTop:scroll-pt,scrollPaddingBottom:scroll-pb,scrollSnapAlign:snap-align,scrollSnapStop:snap-stop,scrollSnapType:snap-type,scrollSnapStrictness:snap-strictness,scrollSnapMargin:snap-m,scrollSnapMarginTop:snap-mt,scrollSnapMarginBottom:snap-mb,scrollSnapMarginLeft:snap-ml,scrollSnapMarginRight:snap-mr,touchAction:touch,userSelect:select,fill:fill,stroke:stroke,strokeWidth:stroke-w,srOnly:sr,debug:debug,appearance:appearance,backfaceVisibility:backface,clipPath:clip-path,hyphens:hyphens,mask:mask,maskImage:mask-image,maskSize:mask-size,textSizeAdjust:text-adjust,container:cq,containerName:cq-name,containerType:cq-type,textStyle:textStyle",Vi=new Map,Bi=new Map;Bs.split(",").forEach(e=>{const[t,n]=e.split(":"),[o,...r]=n.split("/");Vi.set(t,o),r.length&&r.forEach(i=>{Bi.set(i==="1"?o:i,t)})});const Hr=e=>Bi.get(e)||e,Ki={conditions:{shift:Vs,finalize:Os,breakpoints:{keys:["base","sm","md","lg","xl","2xl"]}},utility:{transform:(e,t)=>{const n=Hr(e);return{className:`${Vi.get(n)||Fs(n)}_${Ti(t)}`}},hasShorthand:!0,toHash:(e,t)=>t(e.join(":")),resolveShorthand:Hr}},Ks=ks(Ki),d=(...e)=>Ks(Ht(...e));d.raw=(...e)=>Ht(...e);const{mergeCss:Ht}=Ps(Ki);function Nt(){let e="",t=0,n;for(;t<arguments.length;)(n=arguments[t++])&&typeof n=="string"&&(e&&(e+=" "),e+=n);return e}const Nr=e=>({base:{},variants:{},defaultVariants:{},compoundVariants:[],...e});function Un(e){const{base:t,variants:n,defaultVariants:o,compoundVariants:r}=Nr(e),i=g=>({...o,...Ai(g)});function l(g={}){const u=i(g);let h={...t};for(const[S,x]of Object.entries(u))n[S]?.[x]&&(h=Ht(h,n[S][x]));const b=Ws(r,u);return Ht(h,b)}function s(g){const u=Nr(g.config),h=Ds(g.variantKeys,Object.keys(n));return Un({base:Ht(t,u.base),variants:Object.fromEntries(h.map(b=>[b,Ht(n[b],u.variants[b])])),defaultVariants:To(o,u.defaultVariants),compoundVariants:[...r,...u.compoundVariants]})}function a(g){return d(l(g))}const c=Object.keys(n);function m(g){return As(g,c)}const p=Object.fromEntries(Object.entries(n).map(([g,u])=>[g,Object.keys(u)]));return Object.assign(jn(a),{__cva__:!0,variantMap:p,variantKeys:c,raw:l,config:e,merge:s,splitVariantProps:m,getVariantProps:i})}function Ws(e,t){let n={};return e.forEach(o=>{Object.entries(o).every(([i,l])=>i==="css"?!0:(Array.isArray(l)?l:[l]).some(a=>t[i]===a))&&(n=Ht(n,o.css))}),n}function Wi(e){var t,n,o="";if(typeof e=="string"||typeof e=="number")o+=e;else if(typeof e=="object")if(Array.isArray(e)){var r=e.length;for(t=0;t<r;t++)e[t]&&(n=Wi(e[t]))&&(o&&(o+=" "),o+=n)}else for(n in e)e[n]&&(o&&(o+=" "),o+=n);return o}function jt(){for(var e,t,n=0,o="",r=arguments.length;n<r;n++)(e=arguments[n])&&(t=Wi(e))&&(o&&(o+=" "),o+=t);return o}var Hs=V("<button>"),Ns=V("<span>⟳");function Ie(e){const t=U({variant:"default",size:"default",type:"button"},e),[n,o]=te(t,["variant","size","loading","disabled","icon","round","circle","plain","children","class"]),r=()=>{const i={display:"inline-flex",alignItems:"center",justifyContent:"center",gap:"8px",fontWeight:"500",borderRadius:n.round?"20px":n.circle?"50%":"4px",border:"1px solid",cursor:"pointer",transition:"all 0.3s",outline:"none",userSelect:"none",verticalAlign:"middle",whiteSpace:"nowrap",textDecoration:"none",_focus:{outline:"2px solid",outlineOffset:"2px"},_disabled:{cursor:"not-allowed",opacity:"0.5"}},l={large:{height:"40px",padding:n.circle?"0":"12px 20px",fontSize:"14px",minWidth:n.circle?"40px":"auto"},default:{height:"32px",padding:n.circle?"0":"8px 16px",fontSize:"14px",minWidth:n.circle?"32px":"auto"},small:{height:"24px",padding:n.circle?"0":"4px 12px",fontSize:"12px",minWidth:n.circle?"24px":"auto"}},s={primary:n.plain?{color:"primary.500",backgroundColor:"primary.50",borderColor:"primary.200",_hover:{backgroundColor:"primary.500",color:"white",borderColor:"primary.500"},_focus:{outlineColor:"primary.500"}}:{color:"white",backgroundColor:"primary.500",borderColor:"primary.500",_hover:{backgroundColor:"primary.600",borderColor:"primary.600"},_focus:{outlineColor:"primary.500"}},success:n.plain?{color:"success.500",backgroundColor:"success.50",borderColor:"success.200",_hover:{backgroundColor:"success.500",color:"white",borderColor:"success.500"},_focus:{outlineColor:"success.500"}}:{color:"white",backgroundColor:"success.500",borderColor:"success.500",_hover:{backgroundColor:"success.600",borderColor:"success.600"},_focus:{outlineColor:"success.500"}},warning:n.plain?{color:"warning.500",backgroundColor:"warning.50",borderColor:"warning.200",_hover:{backgroundColor:"warning.500",color:"white",borderColor:"warning.500"},_focus:{outlineColor:"warning.500"}}:{color:"white",backgroundColor:"warning.500",borderColor:"warning.500",_hover:{backgroundColor:"warning.600",borderColor:"warning.600"},_focus:{outlineColor:"warning.500"}},danger:n.plain?{color:"danger.500",backgroundColor:"danger.50",borderColor:"danger.200",_hover:{backgroundColor:"danger.500",color:"white",borderColor:"danger.500"},_focus:{outlineColor:"danger.500"}}:{color:"white",backgroundColor:"danger.500",borderColor:"danger.500",_hover:{backgroundColor:"danger.600",borderColor:"danger.600"},_focus:{outlineColor:"danger.500"}},info:n.plain?{color:"info.500",backgroundColor:"info.50",borderColor:"info.200",_hover:{backgroundColor:"info.500",color:"white",borderColor:"info.500"},_focus:{outlineColor:"info.500"}}:{color:"white",backgroundColor:"info.500",borderColor:"info.500",_hover:{backgroundColor:"info.600",borderColor:"info.600"},_focus:{outlineColor:"info.500"}},text:{color:"primary.500",backgroundColor:"transparent",borderColor:"transparent",_hover:{color:"primary.600",backgroundColor:"primary.50"},_focus:{outlineColor:"primary.500"}},default:{color:"text.regular",backgroundColor:"white",borderColor:"border.base",_hover:{color:"primary.500",borderColor:"primary.300"},_focus:{outlineColor:"primary.500"}}};return{...i,...l[n.size],...s[n.variant]}};return(()=>{var i=Hs();return lt(i,U({get class(){return jt(d(r()),n.class)},get disabled(){return n.disabled||n.loading}},o),!1,!0),C(i,(()=>{var l=Q(()=>!!n.loading);return()=>l()&&(()=>{var s=Ns();return O(()=>f(s,d({animation:"spin 1s linear infinite"}))),s})()})(),null),C(i,(()=>{var l=Q(()=>!!(n.icon&&!n.loading));return()=>l()&&n.icon})(),null),C(i,(()=>{var l=Q(()=>!!(n.children&&!n.circle));return()=>l()&&n.children})(),null),i})()}var no=V("<span>"),Gs=V("<span>✕"),js=V("<div><input>");function Hi(e){const t=U({size:"default",type:"text"},e),[n,o]=te(t,["size","disabled","clearable","prefixIcon","suffixIcon","showPassword","error","onClear","class","value"]),[r,i]=H(!1),[l,s]=H(!1),a=()=>{const h={position:"relative",display:"inline-flex",alignItems:"center",width:"100%",borderRadius:"4px",border:"1px solid",backgroundColor:"white",transition:"all 0.3s",_focusWithin:{borderColor:"primary.500",boxShadow:"0 0 0 2px rgba(64, 158, 255, 0.2)"}},b={large:{height:"40px",fontSize:"14px"},default:{height:"32px",fontSize:"14px"},small:{height:"24px",fontSize:"12px"}},S={borderColor:n.error?"danger.500":l()?"primary.500":"border.base",_hover:n.disabled?{}:{borderColor:n.error?"danger.600":"border.light"},_disabled:{backgroundColor:"bg.page",borderColor:"border.lighter",cursor:"not-allowed"}};return{...h,...b[n.size],...S}},c=()=>{const h=n.prefixIcon?"32px":"12px",b=n.clearable||n.suffixIcon||n.showPassword?"32px":"12px";return{width:"100%",height:"100%",border:"none",outline:"none",backgroundColor:"transparent",color:"text.primary",fontSize:"inherit",paddingLeft:h,paddingRight:b,_placeholder:{color:"text.placeholder"},_disabled:{cursor:"not-allowed",color:"text.placeholder"}}},m=h=>({position:"absolute",top:"50%",transform:"translateY(-50%)",[h==="prefix"?"left":"right"]:"8px",color:"text.secondary",fontSize:"14px",cursor:h==="suffix"&&n.clearable?"pointer":"default",zIndex:1}),p=()=>{n.onClear?.()},g=()=>{i(!r())},u=()=>n.showPassword?r()?"text":"password":o.type||"text";return(()=>{var h=js(),b=h.firstChild;return C(h,R(ae,{get when(){return n.prefixIcon},get children(){var S=no();return C(S,()=>n.prefixIcon),O(()=>f(S,d(m("prefix")))),S}}),b),lt(b,U(o,{get type(){return u()},get class(){return d(c())},get disabled(){return n.disabled},get value(){return n.value},onFocus:()=>s(!0),onBlur:()=>s(!1)}),!1,!1),C(h,R(ae,{get when(){return Q(()=>!!n.clearable)()&&n.value},get children(){var S=Gs();return S.$$click=p,O(()=>f(S,d({...m("suffix"),cursor:"pointer",_hover:{color:"text.primary"}}))),S}}),null),C(h,R(ae,{get when(){return n.showPassword},get children(){var S=no();return S.$$click=g,C(S,()=>r()?"👁️":"👁️‍🗨️"),O(()=>f(S,d({...m("suffix"),cursor:"pointer",_hover:{color:"text.primary"}}))),S}}),null),C(h,R(ae,{get when(){return Q(()=>!!(n.suffixIcon&&!n.clearable))()&&!n.showPassword},get children(){var S=no();return C(S,()=>n.suffixIcon),O(()=>f(S,d(m("suffix")))),S}}),null),O(()=>f(h,jt(d(a()),n.class))),h})()}Tt(["click"]);var Us=V("<div>"),qs=V("<div><div>");function Kn(e){const[t,n]=te(e,["header","shadow","bodyStyle","headerStyle","children","class"]),o=()=>({...{backgroundColor:"white",borderRadius:"4px",border:"1px solid",borderColor:"border.lighter",overflow:"hidden",transition:"all 0.3s"},...{always:{boxShadow:"base"},hover:{_hover:{boxShadow:"base"}},never:{}}[t.shadow||"always"]}),r=()=>({padding:"18px 20px",borderBottom:"1px solid",borderColor:"border.lighter",backgroundColor:"bg.page",fontSize:"16px",fontWeight:"500",color:"text.primary",...t.headerStyle}),i=()=>({padding:"20px",...t.bodyStyle});return(()=>{var l=qs(),s=l.firstChild;return lt(l,U({get class(){return jt(d(o()),t.class)}},n),!1,!0),C(l,R(ae,{get when(){return t.header},get children(){var a=Us();return C(a,(()=>{var c=Q(()=>typeof t.header=="string");return()=>(c(),t.header)})()),O(()=>f(a,d(r()))),a}}),s),C(s,()=>t.children),O(()=>f(s,d(i()))),l})()}var Xs=V("<span>✕"),Ys=V("<span>");function Vn(e){const[t,n]=te(e,["type","size","effect","closable","round","onClose","children","class"]),o=()=>{const l={display:"inline-flex",alignItems:"center",gap:"4px",borderRadius:t.round?"16px":"4px",border:"1px solid",fontSize:"12px",fontWeight:"400",lineHeight:1,whiteSpace:"nowrap",verticalAlign:"middle"},s={large:{height:"32px",padding:"0 12px",fontSize:"14px"},default:{height:"24px",padding:"0 8px",fontSize:"12px"},small:{height:"20px",padding:"0 6px",fontSize:"11px"}},c={primary:"primary",success:"success",warning:"warning",danger:"danger",info:"info"}[t.type||"primary"],m={dark:{color:"white",backgroundColor:`${c}.500`,borderColor:`${c}.500`},light:{color:`${c}.600`,backgroundColor:`${c}.50`,borderColor:`${c}.200`},plain:{color:`${c}.500`,backgroundColor:"transparent",borderColor:`${c}.500`}};return{...l,...s[t.size||"default"],...m[t.effect||"light"]}},r=()=>({cursor:"pointer",fontSize:"10px",marginLeft:"4px",borderRadius:"50%",width:"14px",height:"14px",display:"flex",alignItems:"center",justifyContent:"center",_hover:{backgroundColor:"rgba(0, 0, 0, 0.1)"}}),i=l=>{l.stopPropagation(),t.onClose?.()};return(()=>{var l=Ys();return lt(l,U({get class(){return jt(d(o()),t.class)}},n),!1,!0),C(l,()=>t.children,null),C(l,R(ae,{get when(){return t.closable},get children(){var s=Xs();return s.$$click=i,O(()=>f(s,d(r()))),s}}),null),l})()}Tt(["click"]);/**
   * table-core
   *
   * Copyright (c) TanStack
   *
   * This source code is licensed under the MIT license found in the
   * LICENSE.md file in the root directory of this source tree.
   *
   * @license MIT
   */function $t(e,t){return typeof e=="function"?e(t):e}function Be(e,t){return n=>{t.setState(o=>({...o,[e]:$t(n,o[e])}))}}function qn(e){return e instanceof Function}function Zs(e){return Array.isArray(e)&&e.every(t=>typeof t=="number")}function Js(e,t){const n=[],o=r=>{r.forEach(i=>{n.push(i);const l=t(i);l!=null&&l.length&&o(l)})};return o(e),n}function N(e,t,n){let o=[],r;return i=>{let l;n.key&&n.debug&&(l=Date.now());const s=e(i);if(!(s.length!==o.length||s.some((m,p)=>o[p]!==m)))return r;o=s;let c;if(n.key&&n.debug&&(c=Date.now()),r=t(...s),n==null||n.onChange==null||n.onChange(r),n.key&&n.debug&&n!=null&&n.debug()){const m=Math.round((Date.now()-l)*100)/100,p=Math.round((Date.now()-c)*100)/100,g=p/16,u=(h,b)=>{for(h=String(h);h.length<b;)h=" "+h;return h};console.info(`%c⏱ ${u(p,5)} /${u(m,5)} ms`,`
            font-size: .6rem;
            font-weight: bold;
            color: hsl(${Math.max(0,Math.min(120-120*g,120))}deg 100% 31%);`,n?.key)}return r}}function G(e,t,n,o){return{debug:()=>{var r;return(r=e?.debugAll)!=null?r:e[t]},key:n,onChange:o}}function Qs(e,t,n,o){const r=()=>{var l;return(l=i.getValue())!=null?l:e.options.renderFallbackValue},i={id:`${t.id}_${n.id}`,row:t,column:n,getValue:()=>t.getValue(o),renderValue:r,getContext:N(()=>[e,n,t,i],(l,s,a,c)=>({table:l,column:s,row:a,cell:c,getValue:c.getValue,renderValue:c.renderValue}),G(e.options,"debugCells","cell.getContext"))};return e._features.forEach(l=>{l.createCell==null||l.createCell(i,n,t,e)},{}),i}function ea(e,t,n,o){var r,i;const s={...e._getDefaultColumnDef(),...t},a=s.accessorKey;let c=(r=(i=s.id)!=null?i:a?typeof String.prototype.replaceAll=="function"?a.replaceAll(".","_"):a.replace(/\./g,"_"):void 0)!=null?r:typeof s.header=="string"?s.header:void 0,m;if(s.accessorFn?m=s.accessorFn:a&&(a.includes(".")?m=g=>{let u=g;for(const b of a.split(".")){var h;u=(h=u)==null?void 0:h[b],u===void 0&&console.warn(`"${b}" in deeply nested key "${a}" returned undefined.`)}return u}:m=g=>g[s.accessorKey]),!c)throw new Error(s.accessorFn?"Columns require an id when using an accessorFn":"Columns require an id when using a non-string header");let p={id:`${String(c)}`,accessorFn:m,parent:o,depth:n,columnDef:s,columns:[],getFlatColumns:N(()=>[!0],()=>{var g;return[p,...(g=p.columns)==null?void 0:g.flatMap(u=>u.getFlatColumns())]},G(e.options,"debugColumns","column.getFlatColumns")),getLeafColumns:N(()=>[e._getOrderColumnsFn()],g=>{var u;if((u=p.columns)!=null&&u.length){let h=p.columns.flatMap(b=>b.getLeafColumns());return g(h)}return[p]},G(e.options,"debugColumns","column.getLeafColumns"))};for(const g of e._features)g.createColumn==null||g.createColumn(p,e);return p}const _e="debugHeaders";function Gr(e,t,n){var o;let i={id:(o=n.id)!=null?o:t.id,column:t,index:n.index,isPlaceholder:!!n.isPlaceholder,placeholderId:n.placeholderId,depth:n.depth,subHeaders:[],colSpan:0,rowSpan:0,headerGroup:null,getLeafHeaders:()=>{const l=[],s=a=>{a.subHeaders&&a.subHeaders.length&&a.subHeaders.map(s),l.push(a)};return s(i),l},getContext:()=>({table:e,header:i,column:t})};return e._features.forEach(l=>{l.createHeader==null||l.createHeader(i,e)}),i}const ta={createTable:e=>{e.getHeaderGroups=N(()=>[e.getAllColumns(),e.getVisibleLeafColumns(),e.getState().columnPinning.left,e.getState().columnPinning.right],(t,n,o,r)=>{var i,l;const s=(i=o?.map(p=>n.find(g=>g.id===p)).filter(Boolean))!=null?i:[],a=(l=r?.map(p=>n.find(g=>g.id===p)).filter(Boolean))!=null?l:[],c=n.filter(p=>!(o!=null&&o.includes(p.id))&&!(r!=null&&r.includes(p.id)));return Pn(t,[...s,...c,...a],e)},G(e.options,_e,"getHeaderGroups")),e.getCenterHeaderGroups=N(()=>[e.getAllColumns(),e.getVisibleLeafColumns(),e.getState().columnPinning.left,e.getState().columnPinning.right],(t,n,o,r)=>(n=n.filter(i=>!(o!=null&&o.includes(i.id))&&!(r!=null&&r.includes(i.id))),Pn(t,n,e,"center")),G(e.options,_e,"getCenterHeaderGroups")),e.getLeftHeaderGroups=N(()=>[e.getAllColumns(),e.getVisibleLeafColumns(),e.getState().columnPinning.left],(t,n,o)=>{var r;const i=(r=o?.map(l=>n.find(s=>s.id===l)).filter(Boolean))!=null?r:[];return Pn(t,i,e,"left")},G(e.options,_e,"getLeftHeaderGroups")),e.getRightHeaderGroups=N(()=>[e.getAllColumns(),e.getVisibleLeafColumns(),e.getState().columnPinning.right],(t,n,o)=>{var r;const i=(r=o?.map(l=>n.find(s=>s.id===l)).filter(Boolean))!=null?r:[];return Pn(t,i,e,"right")},G(e.options,_e,"getRightHeaderGroups")),e.getFooterGroups=N(()=>[e.getHeaderGroups()],t=>[...t].reverse(),G(e.options,_e,"getFooterGroups")),e.getLeftFooterGroups=N(()=>[e.getLeftHeaderGroups()],t=>[...t].reverse(),G(e.options,_e,"getLeftFooterGroups")),e.getCenterFooterGroups=N(()=>[e.getCenterHeaderGroups()],t=>[...t].reverse(),G(e.options,_e,"getCenterFooterGroups")),e.getRightFooterGroups=N(()=>[e.getRightHeaderGroups()],t=>[...t].reverse(),G(e.options,_e,"getRightFooterGroups")),e.getFlatHeaders=N(()=>[e.getHeaderGroups()],t=>t.map(n=>n.headers).flat(),G(e.options,_e,"getFlatHeaders")),e.getLeftFlatHeaders=N(()=>[e.getLeftHeaderGroups()],t=>t.map(n=>n.headers).flat(),G(e.options,_e,"getLeftFlatHeaders")),e.getCenterFlatHeaders=N(()=>[e.getCenterHeaderGroups()],t=>t.map(n=>n.headers).flat(),G(e.options,_e,"getCenterFlatHeaders")),e.getRightFlatHeaders=N(()=>[e.getRightHeaderGroups()],t=>t.map(n=>n.headers).flat(),G(e.options,_e,"getRightFlatHeaders")),e.getCenterLeafHeaders=N(()=>[e.getCenterFlatHeaders()],t=>t.filter(n=>{var o;return!((o=n.subHeaders)!=null&&o.length)}),G(e.options,_e,"getCenterLeafHeaders")),e.getLeftLeafHeaders=N(()=>[e.getLeftFlatHeaders()],t=>t.filter(n=>{var o;return!((o=n.subHeaders)!=null&&o.length)}),G(e.options,_e,"getLeftLeafHeaders")),e.getRightLeafHeaders=N(()=>[e.getRightFlatHeaders()],t=>t.filter(n=>{var o;return!((o=n.subHeaders)!=null&&o.length)}),G(e.options,_e,"getRightLeafHeaders")),e.getLeafHeaders=N(()=>[e.getLeftHeaderGroups(),e.getCenterHeaderGroups(),e.getRightHeaderGroups()],(t,n,o)=>{var r,i,l,s,a,c;return[...(r=(i=t[0])==null?void 0:i.headers)!=null?r:[],...(l=(s=n[0])==null?void 0:s.headers)!=null?l:[],...(a=(c=o[0])==null?void 0:c.headers)!=null?a:[]].map(m=>m.getLeafHeaders()).flat()},G(e.options,_e,"getLeafHeaders"))}};function Pn(e,t,n,o){var r,i;let l=0;const s=function(g,u){u===void 0&&(u=1),l=Math.max(l,u),g.filter(h=>h.getIsVisible()).forEach(h=>{var b;(b=h.columns)!=null&&b.length&&s(h.columns,u+1)},0)};s(e);let a=[];const c=(g,u)=>{const h={depth:u,id:[o,`${u}`].filter(Boolean).join("_"),headers:[]},b=[];g.forEach(S=>{const x=[...b].reverse()[0],y=S.column.depth===h.depth;let v,_=!1;if(y&&S.column.parent?v=S.column.parent:(v=S.column,_=!0),x&&x?.column===v)x.subHeaders.push(S);else{const w=Gr(n,v,{id:[o,u,v.id,S?.id].filter(Boolean).join("_"),isPlaceholder:_,placeholderId:_?`${b.filter(M=>M.column===v).length}`:void 0,depth:u,index:b.length});w.subHeaders.push(S),b.push(w)}h.headers.push(S),S.headerGroup=h}),a.push(h),u>0&&c(b,u-1)},m=t.map((g,u)=>Gr(n,g,{depth:l,index:u}));c(m,l-1),a.reverse();const p=g=>g.filter(h=>h.column.getIsVisible()).map(h=>{let b=0,S=0,x=[0];h.subHeaders&&h.subHeaders.length?(x=[],p(h.subHeaders).forEach(v=>{let{colSpan:_,rowSpan:w}=v;b+=_,x.push(w)})):b=1;const y=Math.min(...x);return S=S+y,h.colSpan=b,h.rowSpan=S,{colSpan:b,rowSpan:S}});return p((r=(i=a[0])==null?void 0:i.headers)!=null?r:[]),a}const zo=(e,t,n,o,r,i,l)=>{let s={id:t,index:o,original:n,depth:r,parentId:l,_valuesCache:{},_uniqueValuesCache:{},getValue:a=>{if(s._valuesCache.hasOwnProperty(a))return s._valuesCache[a];const c=e.getColumn(a);if(c!=null&&c.accessorFn)return s._valuesCache[a]=c.accessorFn(s.original,o),s._valuesCache[a]},getUniqueValues:a=>{if(s._uniqueValuesCache.hasOwnProperty(a))return s._uniqueValuesCache[a];const c=e.getColumn(a);if(c!=null&&c.accessorFn)return c.columnDef.getUniqueValues?(s._uniqueValuesCache[a]=c.columnDef.getUniqueValues(s.original,o),s._uniqueValuesCache[a]):(s._uniqueValuesCache[a]=[s.getValue(a)],s._uniqueValuesCache[a])},renderValue:a=>{var c;return(c=s.getValue(a))!=null?c:e.options.renderFallbackValue},subRows:[],getLeafRows:()=>Js(s.subRows,a=>a.subRows),getParentRow:()=>s.parentId?e.getRow(s.parentId,!0):void 0,getParentRows:()=>{let a=[],c=s;for(;;){const m=c.getParentRow();if(!m)break;a.push(m),c=m}return a.reverse()},getAllCells:N(()=>[e.getAllLeafColumns()],a=>a.map(c=>Qs(e,s,c,c.id)),G(e.options,"debugRows","getAllCells")),_getAllCellsByColumnId:N(()=>[s.getAllCells()],a=>a.reduce((c,m)=>(c[m.column.id]=m,c),{}),G(e.options,"debugRows","getAllCellsByColumnId"))};for(let a=0;a<e._features.length;a++){const c=e._features[a];c==null||c.createRow==null||c.createRow(s,e)}return s},na={createColumn:(e,t)=>{e._getFacetedRowModel=t.options.getFacetedRowModel&&t.options.getFacetedRowModel(t,e.id),e.getFacetedRowModel=()=>e._getFacetedRowModel?e._getFacetedRowModel():t.getPreFilteredRowModel(),e._getFacetedUniqueValues=t.options.getFacetedUniqueValues&&t.options.getFacetedUniqueValues(t,e.id),e.getFacetedUniqueValues=()=>e._getFacetedUniqueValues?e._getFacetedUniqueValues():new Map,e._getFacetedMinMaxValues=t.options.getFacetedMinMaxValues&&t.options.getFacetedMinMaxValues(t,e.id),e.getFacetedMinMaxValues=()=>{if(e._getFacetedMinMaxValues)return e._getFacetedMinMaxValues()}}},Ni=(e,t,n)=>{var o,r;const i=n==null||(o=n.toString())==null?void 0:o.toLowerCase();return!!(!((r=e.getValue(t))==null||(r=r.toString())==null||(r=r.toLowerCase())==null)&&r.includes(i))};Ni.autoRemove=e=>qe(e);const Gi=(e,t,n)=>{var o;return!!(!((o=e.getValue(t))==null||(o=o.toString())==null)&&o.includes(n))};Gi.autoRemove=e=>qe(e);const ji=(e,t,n)=>{var o;return((o=e.getValue(t))==null||(o=o.toString())==null?void 0:o.toLowerCase())===n?.toLowerCase()};ji.autoRemove=e=>qe(e);const Ui=(e,t,n)=>{var o;return(o=e.getValue(t))==null?void 0:o.includes(n)};Ui.autoRemove=e=>qe(e);const qi=(e,t,n)=>!n.some(o=>{var r;return!((r=e.getValue(t))!=null&&r.includes(o))});qi.autoRemove=e=>qe(e)||!(e!=null&&e.length);const Xi=(e,t,n)=>n.some(o=>{var r;return(r=e.getValue(t))==null?void 0:r.includes(o)});Xi.autoRemove=e=>qe(e)||!(e!=null&&e.length);const Yi=(e,t,n)=>e.getValue(t)===n;Yi.autoRemove=e=>qe(e);const Zi=(e,t,n)=>e.getValue(t)==n;Zi.autoRemove=e=>qe(e);const Lo=(e,t,n)=>{let[o,r]=n;const i=e.getValue(t);return i>=o&&i<=r};Lo.resolveFilterValue=e=>{let[t,n]=e,o=typeof t!="number"?parseFloat(t):t,r=typeof n!="number"?parseFloat(n):n,i=t===null||Number.isNaN(o)?-1/0:o,l=n===null||Number.isNaN(r)?1/0:r;if(i>l){const s=i;i=l,l=s}return[i,l]};Lo.autoRemove=e=>qe(e)||qe(e[0])&&qe(e[1]);const dt={includesString:Ni,includesStringSensitive:Gi,equalsString:ji,arrIncludes:Ui,arrIncludesAll:qi,arrIncludesSome:Xi,equals:Yi,weakEquals:Zi,inNumberRange:Lo};function qe(e){return e==null||e===""}const oa={getDefaultColumnDef:()=>({filterFn:"auto"}),getInitialState:e=>({columnFilters:[],...e}),getDefaultOptions:e=>({onColumnFiltersChange:Be("columnFilters",e),filterFromLeafRows:!1,maxLeafRowFilterDepth:100}),createColumn:(e,t)=>{e.getAutoFilterFn=()=>{const n=t.getCoreRowModel().flatRows[0],o=n?.getValue(e.id);return typeof o=="string"?dt.includesString:typeof o=="number"?dt.inNumberRange:typeof o=="boolean"||o!==null&&typeof o=="object"?dt.equals:Array.isArray(o)?dt.arrIncludes:dt.weakEquals},e.getFilterFn=()=>{var n,o;return qn(e.columnDef.filterFn)?e.columnDef.filterFn:e.columnDef.filterFn==="auto"?e.getAutoFilterFn():(n=(o=t.options.filterFns)==null?void 0:o[e.columnDef.filterFn])!=null?n:dt[e.columnDef.filterFn]},e.getCanFilter=()=>{var n,o,r;return((n=e.columnDef.enableColumnFilter)!=null?n:!0)&&((o=t.options.enableColumnFilters)!=null?o:!0)&&((r=t.options.enableFilters)!=null?r:!0)&&!!e.accessorFn},e.getIsFiltered=()=>e.getFilterIndex()>-1,e.getFilterValue=()=>{var n;return(n=t.getState().columnFilters)==null||(n=n.find(o=>o.id===e.id))==null?void 0:n.value},e.getFilterIndex=()=>{var n,o;return(n=(o=t.getState().columnFilters)==null?void 0:o.findIndex(r=>r.id===e.id))!=null?n:-1},e.setFilterValue=n=>{t.setColumnFilters(o=>{const r=e.getFilterFn(),i=o?.find(m=>m.id===e.id),l=$t(n,i?i.value:void 0);if(jr(r,l,e)){var s;return(s=o?.filter(m=>m.id!==e.id))!=null?s:[]}const a={id:e.id,value:l};if(i){var c;return(c=o?.map(m=>m.id===e.id?a:m))!=null?c:[]}return o!=null&&o.length?[...o,a]:[a]})}},createRow:(e,t)=>{e.columnFilters={},e.columnFiltersMeta={}},createTable:e=>{e.setColumnFilters=t=>{const n=e.getAllLeafColumns(),o=r=>{var i;return(i=$t(t,r))==null?void 0:i.filter(l=>{const s=n.find(a=>a.id===l.id);if(s){const a=s.getFilterFn();if(jr(a,l.value,s))return!1}return!0})};e.options.onColumnFiltersChange==null||e.options.onColumnFiltersChange(o)},e.resetColumnFilters=t=>{var n,o;e.setColumnFilters(t?[]:(n=(o=e.initialState)==null?void 0:o.columnFilters)!=null?n:[])},e.getPreFilteredRowModel=()=>e.getCoreRowModel(),e.getFilteredRowModel=()=>(!e._getFilteredRowModel&&e.options.getFilteredRowModel&&(e._getFilteredRowModel=e.options.getFilteredRowModel(e)),e.options.manualFiltering||!e._getFilteredRowModel?e.getPreFilteredRowModel():e._getFilteredRowModel())}};function jr(e,t,n){return(e&&e.autoRemove?e.autoRemove(t,n):!1)||typeof t>"u"||typeof t=="string"&&!t}const ra=(e,t,n)=>n.reduce((o,r)=>{const i=r.getValue(e);return o+(typeof i=="number"?i:0)},0),ia=(e,t,n)=>{let o;return n.forEach(r=>{const i=r.getValue(e);i!=null&&(o>i||o===void 0&&i>=i)&&(o=i)}),o},la=(e,t,n)=>{let o;return n.forEach(r=>{const i=r.getValue(e);i!=null&&(o<i||o===void 0&&i>=i)&&(o=i)}),o},sa=(e,t,n)=>{let o,r;return n.forEach(i=>{const l=i.getValue(e);l!=null&&(o===void 0?l>=l&&(o=r=l):(o>l&&(o=l),r<l&&(r=l)))}),[o,r]},aa=(e,t)=>{let n=0,o=0;if(t.forEach(r=>{let i=r.getValue(e);i!=null&&(i=+i)>=i&&(++n,o+=i)}),n)return o/n},ca=(e,t)=>{if(!t.length)return;const n=t.map(i=>i.getValue(e));if(!Zs(n))return;if(n.length===1)return n[0];const o=Math.floor(n.length/2),r=n.sort((i,l)=>i-l);return n.length%2!==0?r[o]:(r[o-1]+r[o])/2},da=(e,t)=>Array.from(new Set(t.map(n=>n.getValue(e))).values()),ua=(e,t)=>new Set(t.map(n=>n.getValue(e))).size,ga=(e,t)=>t.length,oo={sum:ra,min:ia,max:la,extent:sa,mean:aa,median:ca,unique:da,uniqueCount:ua,count:ga},fa={getDefaultColumnDef:()=>({aggregatedCell:e=>{var t,n;return(t=(n=e.getValue())==null||n.toString==null?void 0:n.toString())!=null?t:null},aggregationFn:"auto"}),getInitialState:e=>({grouping:[],...e}),getDefaultOptions:e=>({onGroupingChange:Be("grouping",e),groupedColumnMode:"reorder"}),createColumn:(e,t)=>{e.toggleGrouping=()=>{t.setGrouping(n=>n!=null&&n.includes(e.id)?n.filter(o=>o!==e.id):[...n??[],e.id])},e.getCanGroup=()=>{var n,o;return((n=e.columnDef.enableGrouping)!=null?n:!0)&&((o=t.options.enableGrouping)!=null?o:!0)&&(!!e.accessorFn||!!e.columnDef.getGroupingValue)},e.getIsGrouped=()=>{var n;return(n=t.getState().grouping)==null?void 0:n.includes(e.id)},e.getGroupedIndex=()=>{var n;return(n=t.getState().grouping)==null?void 0:n.indexOf(e.id)},e.getToggleGroupingHandler=()=>{const n=e.getCanGroup();return()=>{n&&e.toggleGrouping()}},e.getAutoAggregationFn=()=>{const n=t.getCoreRowModel().flatRows[0],o=n?.getValue(e.id);if(typeof o=="number")return oo.sum;if(Object.prototype.toString.call(o)==="[object Date]")return oo.extent},e.getAggregationFn=()=>{var n,o;if(!e)throw new Error;return qn(e.columnDef.aggregationFn)?e.columnDef.aggregationFn:e.columnDef.aggregationFn==="auto"?e.getAutoAggregationFn():(n=(o=t.options.aggregationFns)==null?void 0:o[e.columnDef.aggregationFn])!=null?n:oo[e.columnDef.aggregationFn]}},createTable:e=>{e.setGrouping=t=>e.options.onGroupingChange==null?void 0:e.options.onGroupingChange(t),e.resetGrouping=t=>{var n,o;e.setGrouping(t?[]:(n=(o=e.initialState)==null?void 0:o.grouping)!=null?n:[])},e.getPreGroupedRowModel=()=>e.getFilteredRowModel(),e.getGroupedRowModel=()=>(!e._getGroupedRowModel&&e.options.getGroupedRowModel&&(e._getGroupedRowModel=e.options.getGroupedRowModel(e)),e.options.manualGrouping||!e._getGroupedRowModel?e.getPreGroupedRowModel():e._getGroupedRowModel())},createRow:(e,t)=>{e.getIsGrouped=()=>!!e.groupingColumnId,e.getGroupingValue=n=>{if(e._groupingValuesCache.hasOwnProperty(n))return e._groupingValuesCache[n];const o=t.getColumn(n);return o!=null&&o.columnDef.getGroupingValue?(e._groupingValuesCache[n]=o.columnDef.getGroupingValue(e.original),e._groupingValuesCache[n]):e.getValue(n)},e._groupingValuesCache={}},createCell:(e,t,n,o)=>{e.getIsGrouped=()=>t.getIsGrouped()&&t.id===n.groupingColumnId,e.getIsPlaceholder=()=>!e.getIsGrouped()&&t.getIsGrouped(),e.getIsAggregated=()=>{var r;return!e.getIsGrouped()&&!e.getIsPlaceholder()&&!!((r=n.subRows)!=null&&r.length)}}};function pa(e,t,n){if(!(t!=null&&t.length)||!n)return e;const o=e.filter(i=>!t.includes(i.id));return n==="remove"?o:[...t.map(i=>e.find(l=>l.id===i)).filter(Boolean),...o]}const ha={getInitialState:e=>({columnOrder:[],...e}),getDefaultOptions:e=>({onColumnOrderChange:Be("columnOrder",e)}),createColumn:(e,t)=>{e.getIndex=N(n=>[yn(t,n)],n=>n.findIndex(o=>o.id===e.id),G(t.options,"debugColumns","getIndex")),e.getIsFirstColumn=n=>{var o;return((o=yn(t,n)[0])==null?void 0:o.id)===e.id},e.getIsLastColumn=n=>{var o;const r=yn(t,n);return((o=r[r.length-1])==null?void 0:o.id)===e.id}},createTable:e=>{e.setColumnOrder=t=>e.options.onColumnOrderChange==null?void 0:e.options.onColumnOrderChange(t),e.resetColumnOrder=t=>{var n;e.setColumnOrder(t?[]:(n=e.initialState.columnOrder)!=null?n:[])},e._getOrderColumnsFn=N(()=>[e.getState().columnOrder,e.getState().grouping,e.options.groupedColumnMode],(t,n,o)=>r=>{let i=[];if(!(t!=null&&t.length))i=r;else{const l=[...t],s=[...r];for(;s.length&&l.length;){const a=l.shift(),c=s.findIndex(m=>m.id===a);c>-1&&i.push(s.splice(c,1)[0])}i=[...i,...s]}return pa(i,n,o)},G(e.options,"debugTable","_getOrderColumnsFn"))}},ro=()=>({left:[],right:[]}),ma={getInitialState:e=>({columnPinning:ro(),...e}),getDefaultOptions:e=>({onColumnPinningChange:Be("columnPinning",e)}),createColumn:(e,t)=>{e.pin=n=>{const o=e.getLeafColumns().map(r=>r.id).filter(Boolean);t.setColumnPinning(r=>{var i,l;if(n==="right"){var s,a;return{left:((s=r?.left)!=null?s:[]).filter(p=>!(o!=null&&o.includes(p))),right:[...((a=r?.right)!=null?a:[]).filter(p=>!(o!=null&&o.includes(p))),...o]}}if(n==="left"){var c,m;return{left:[...((c=r?.left)!=null?c:[]).filter(p=>!(o!=null&&o.includes(p))),...o],right:((m=r?.right)!=null?m:[]).filter(p=>!(o!=null&&o.includes(p)))}}return{left:((i=r?.left)!=null?i:[]).filter(p=>!(o!=null&&o.includes(p))),right:((l=r?.right)!=null?l:[]).filter(p=>!(o!=null&&o.includes(p)))}})},e.getCanPin=()=>e.getLeafColumns().some(o=>{var r,i,l;return((r=o.columnDef.enablePinning)!=null?r:!0)&&((i=(l=t.options.enableColumnPinning)!=null?l:t.options.enablePinning)!=null?i:!0)}),e.getIsPinned=()=>{const n=e.getLeafColumns().map(s=>s.id),{left:o,right:r}=t.getState().columnPinning,i=n.some(s=>o?.includes(s)),l=n.some(s=>r?.includes(s));return i?"left":l?"right":!1},e.getPinnedIndex=()=>{var n,o;const r=e.getIsPinned();return r?(n=(o=t.getState().columnPinning)==null||(o=o[r])==null?void 0:o.indexOf(e.id))!=null?n:-1:0}},createRow:(e,t)=>{e.getCenterVisibleCells=N(()=>[e._getAllVisibleCells(),t.getState().columnPinning.left,t.getState().columnPinning.right],(n,o,r)=>{const i=[...o??[],...r??[]];return n.filter(l=>!i.includes(l.column.id))},G(t.options,"debugRows","getCenterVisibleCells")),e.getLeftVisibleCells=N(()=>[e._getAllVisibleCells(),t.getState().columnPinning.left],(n,o)=>(o??[]).map(i=>n.find(l=>l.column.id===i)).filter(Boolean).map(i=>({...i,position:"left"})),G(t.options,"debugRows","getLeftVisibleCells")),e.getRightVisibleCells=N(()=>[e._getAllVisibleCells(),t.getState().columnPinning.right],(n,o)=>(o??[]).map(i=>n.find(l=>l.column.id===i)).filter(Boolean).map(i=>({...i,position:"right"})),G(t.options,"debugRows","getRightVisibleCells"))},createTable:e=>{e.setColumnPinning=t=>e.options.onColumnPinningChange==null?void 0:e.options.onColumnPinningChange(t),e.resetColumnPinning=t=>{var n,o;return e.setColumnPinning(t?ro():(n=(o=e.initialState)==null?void 0:o.columnPinning)!=null?n:ro())},e.getIsSomeColumnsPinned=t=>{var n;const o=e.getState().columnPinning;if(!t){var r,i;return!!((r=o.left)!=null&&r.length||(i=o.right)!=null&&i.length)}return!!((n=o[t])!=null&&n.length)},e.getLeftLeafColumns=N(()=>[e.getAllLeafColumns(),e.getState().columnPinning.left],(t,n)=>(n??[]).map(o=>t.find(r=>r.id===o)).filter(Boolean),G(e.options,"debugColumns","getLeftLeafColumns")),e.getRightLeafColumns=N(()=>[e.getAllLeafColumns(),e.getState().columnPinning.right],(t,n)=>(n??[]).map(o=>t.find(r=>r.id===o)).filter(Boolean),G(e.options,"debugColumns","getRightLeafColumns")),e.getCenterLeafColumns=N(()=>[e.getAllLeafColumns(),e.getState().columnPinning.left,e.getState().columnPinning.right],(t,n,o)=>{const r=[...n??[],...o??[]];return t.filter(i=>!r.includes(i.id))},G(e.options,"debugColumns","getCenterLeafColumns"))}};function va(e){return e||(typeof document<"u"?document:null)}const $n={size:150,minSize:20,maxSize:Number.MAX_SAFE_INTEGER},io=()=>({startOffset:null,startSize:null,deltaOffset:null,deltaPercentage:null,isResizingColumn:!1,columnSizingStart:[]}),xa={getDefaultColumnDef:()=>$n,getInitialState:e=>({columnSizing:{},columnSizingInfo:io(),...e}),getDefaultOptions:e=>({columnResizeMode:"onEnd",columnResizeDirection:"ltr",onColumnSizingChange:Be("columnSizing",e),onColumnSizingInfoChange:Be("columnSizingInfo",e)}),createColumn:(e,t)=>{e.getSize=()=>{var n,o,r;const i=t.getState().columnSizing[e.id];return Math.min(Math.max((n=e.columnDef.minSize)!=null?n:$n.minSize,(o=i??e.columnDef.size)!=null?o:$n.size),(r=e.columnDef.maxSize)!=null?r:$n.maxSize)},e.getStart=N(n=>[n,yn(t,n),t.getState().columnSizing],(n,o)=>o.slice(0,e.getIndex(n)).reduce((r,i)=>r+i.getSize(),0),G(t.options,"debugColumns","getStart")),e.getAfter=N(n=>[n,yn(t,n),t.getState().columnSizing],(n,o)=>o.slice(e.getIndex(n)+1).reduce((r,i)=>r+i.getSize(),0),G(t.options,"debugColumns","getAfter")),e.resetSize=()=>{t.setColumnSizing(n=>{let{[e.id]:o,...r}=n;return r})},e.getCanResize=()=>{var n,o;return((n=e.columnDef.enableResizing)!=null?n:!0)&&((o=t.options.enableColumnResizing)!=null?o:!0)},e.getIsResizing=()=>t.getState().columnSizingInfo.isResizingColumn===e.id},createHeader:(e,t)=>{e.getSize=()=>{let n=0;const o=r=>{if(r.subHeaders.length)r.subHeaders.forEach(o);else{var i;n+=(i=r.column.getSize())!=null?i:0}};return o(e),n},e.getStart=()=>{if(e.index>0){const n=e.headerGroup.headers[e.index-1];return n.getStart()+n.getSize()}return 0},e.getResizeHandler=n=>{const o=t.getColumn(e.column.id),r=o?.getCanResize();return i=>{if(!o||!r||(i.persist==null||i.persist(),lo(i)&&i.touches&&i.touches.length>1))return;const l=e.getSize(),s=e?e.getLeafHeaders().map(x=>[x.column.id,x.column.getSize()]):[[o.id,o.getSize()]],a=lo(i)?Math.round(i.touches[0].clientX):i.clientX,c={},m=(x,y)=>{typeof y=="number"&&(t.setColumnSizingInfo(v=>{var _,w;const M=t.options.columnResizeDirection==="rtl"?-1:1,A=(y-((_=v?.startOffset)!=null?_:0))*M,I=Math.max(A/((w=v?.startSize)!=null?w:0),-.999999);return v.columnSizingStart.forEach(F=>{let[D,T]=F;c[D]=Math.round(Math.max(T+T*I,0)*100)/100}),{...v,deltaOffset:A,deltaPercentage:I}}),(t.options.columnResizeMode==="onChange"||x==="end")&&t.setColumnSizing(v=>({...v,...c})))},p=x=>m("move",x),g=x=>{m("end",x),t.setColumnSizingInfo(y=>({...y,isResizingColumn:!1,startOffset:null,startSize:null,deltaOffset:null,deltaPercentage:null,columnSizingStart:[]}))},u=va(n),h={moveHandler:x=>p(x.clientX),upHandler:x=>{u?.removeEventListener("mousemove",h.moveHandler),u?.removeEventListener("mouseup",h.upHandler),g(x.clientX)}},b={moveHandler:x=>(x.cancelable&&(x.preventDefault(),x.stopPropagation()),p(x.touches[0].clientX),!1),upHandler:x=>{var y;u?.removeEventListener("touchmove",b.moveHandler),u?.removeEventListener("touchend",b.upHandler),x.cancelable&&(x.preventDefault(),x.stopPropagation()),g((y=x.touches[0])==null?void 0:y.clientX)}},S=ba()?{passive:!1}:!1;lo(i)?(u?.addEventListener("touchmove",b.moveHandler,S),u?.addEventListener("touchend",b.upHandler,S)):(u?.addEventListener("mousemove",h.moveHandler,S),u?.addEventListener("mouseup",h.upHandler,S)),t.setColumnSizingInfo(x=>({...x,startOffset:a,startSize:l,deltaOffset:0,deltaPercentage:0,columnSizingStart:s,isResizingColumn:o.id}))}}},createTable:e=>{e.setColumnSizing=t=>e.options.onColumnSizingChange==null?void 0:e.options.onColumnSizingChange(t),e.setColumnSizingInfo=t=>e.options.onColumnSizingInfoChange==null?void 0:e.options.onColumnSizingInfoChange(t),e.resetColumnSizing=t=>{var n;e.setColumnSizing(t?{}:(n=e.initialState.columnSizing)!=null?n:{})},e.resetHeaderSizeInfo=t=>{var n;e.setColumnSizingInfo(t?io():(n=e.initialState.columnSizingInfo)!=null?n:io())},e.getTotalSize=()=>{var t,n;return(t=(n=e.getHeaderGroups()[0])==null?void 0:n.headers.reduce((o,r)=>o+r.getSize(),0))!=null?t:0},e.getLeftTotalSize=()=>{var t,n;return(t=(n=e.getLeftHeaderGroups()[0])==null?void 0:n.headers.reduce((o,r)=>o+r.getSize(),0))!=null?t:0},e.getCenterTotalSize=()=>{var t,n;return(t=(n=e.getCenterHeaderGroups()[0])==null?void 0:n.headers.reduce((o,r)=>o+r.getSize(),0))!=null?t:0},e.getRightTotalSize=()=>{var t,n;return(t=(n=e.getRightHeaderGroups()[0])==null?void 0:n.headers.reduce((o,r)=>o+r.getSize(),0))!=null?t:0}}};let Mn=null;function ba(){if(typeof Mn=="boolean")return Mn;let e=!1;try{const t={get passive(){return e=!0,!1}},n=()=>{};window.addEventListener("test",n,t),window.removeEventListener("test",n)}catch{e=!1}return Mn=e,Mn}function lo(e){return e.type==="touchstart"}const ya={getInitialState:e=>({columnVisibility:{},...e}),getDefaultOptions:e=>({onColumnVisibilityChange:Be("columnVisibility",e)}),createColumn:(e,t)=>{e.toggleVisibility=n=>{e.getCanHide()&&t.setColumnVisibility(o=>({...o,[e.id]:n??!e.getIsVisible()}))},e.getIsVisible=()=>{var n,o;const r=e.columns;return(n=r.length?r.some(i=>i.getIsVisible()):(o=t.getState().columnVisibility)==null?void 0:o[e.id])!=null?n:!0},e.getCanHide=()=>{var n,o;return((n=e.columnDef.enableHiding)!=null?n:!0)&&((o=t.options.enableHiding)!=null?o:!0)},e.getToggleVisibilityHandler=()=>n=>{e.toggleVisibility==null||e.toggleVisibility(n.target.checked)}},createRow:(e,t)=>{e._getAllVisibleCells=N(()=>[e.getAllCells(),t.getState().columnVisibility],n=>n.filter(o=>o.column.getIsVisible()),G(t.options,"debugRows","_getAllVisibleCells")),e.getVisibleCells=N(()=>[e.getLeftVisibleCells(),e.getCenterVisibleCells(),e.getRightVisibleCells()],(n,o,r)=>[...n,...o,...r],G(t.options,"debugRows","getVisibleCells"))},createTable:e=>{const t=(n,o)=>N(()=>[o(),o().filter(r=>r.getIsVisible()).map(r=>r.id).join("_")],r=>r.filter(i=>i.getIsVisible==null?void 0:i.getIsVisible()),G(e.options,"debugColumns",n));e.getVisibleFlatColumns=t("getVisibleFlatColumns",()=>e.getAllFlatColumns()),e.getVisibleLeafColumns=t("getVisibleLeafColumns",()=>e.getAllLeafColumns()),e.getLeftVisibleLeafColumns=t("getLeftVisibleLeafColumns",()=>e.getLeftLeafColumns()),e.getRightVisibleLeafColumns=t("getRightVisibleLeafColumns",()=>e.getRightLeafColumns()),e.getCenterVisibleLeafColumns=t("getCenterVisibleLeafColumns",()=>e.getCenterLeafColumns()),e.setColumnVisibility=n=>e.options.onColumnVisibilityChange==null?void 0:e.options.onColumnVisibilityChange(n),e.resetColumnVisibility=n=>{var o;e.setColumnVisibility(n?{}:(o=e.initialState.columnVisibility)!=null?o:{})},e.toggleAllColumnsVisible=n=>{var o;n=(o=n)!=null?o:!e.getIsAllColumnsVisible(),e.setColumnVisibility(e.getAllLeafColumns().reduce((r,i)=>({...r,[i.id]:n||!(i.getCanHide!=null&&i.getCanHide())}),{}))},e.getIsAllColumnsVisible=()=>!e.getAllLeafColumns().some(n=>!(n.getIsVisible!=null&&n.getIsVisible())),e.getIsSomeColumnsVisible=()=>e.getAllLeafColumns().some(n=>n.getIsVisible==null?void 0:n.getIsVisible()),e.getToggleAllColumnsVisibilityHandler=()=>n=>{var o;e.toggleAllColumnsVisible((o=n.target)==null?void 0:o.checked)}}};function yn(e,t){return t?t==="center"?e.getCenterVisibleLeafColumns():t==="left"?e.getLeftVisibleLeafColumns():e.getRightVisibleLeafColumns():e.getVisibleLeafColumns()}const Sa={createTable:e=>{e._getGlobalFacetedRowModel=e.options.getFacetedRowModel&&e.options.getFacetedRowModel(e,"__global__"),e.getGlobalFacetedRowModel=()=>e.options.manualFiltering||!e._getGlobalFacetedRowModel?e.getPreFilteredRowModel():e._getGlobalFacetedRowModel(),e._getGlobalFacetedUniqueValues=e.options.getFacetedUniqueValues&&e.options.getFacetedUniqueValues(e,"__global__"),e.getGlobalFacetedUniqueValues=()=>e._getGlobalFacetedUniqueValues?e._getGlobalFacetedUniqueValues():new Map,e._getGlobalFacetedMinMaxValues=e.options.getFacetedMinMaxValues&&e.options.getFacetedMinMaxValues(e,"__global__"),e.getGlobalFacetedMinMaxValues=()=>{if(e._getGlobalFacetedMinMaxValues)return e._getGlobalFacetedMinMaxValues()}}},Ca={getInitialState:e=>({globalFilter:void 0,...e}),getDefaultOptions:e=>({onGlobalFilterChange:Be("globalFilter",e),globalFilterFn:"auto",getColumnCanGlobalFilter:t=>{var n;const o=(n=e.getCoreRowModel().flatRows[0])==null||(n=n._getAllCellsByColumnId()[t.id])==null?void 0:n.getValue();return typeof o=="string"||typeof o=="number"}}),createColumn:(e,t)=>{e.getCanGlobalFilter=()=>{var n,o,r,i;return((n=e.columnDef.enableGlobalFilter)!=null?n:!0)&&((o=t.options.enableGlobalFilter)!=null?o:!0)&&((r=t.options.enableFilters)!=null?r:!0)&&((i=t.options.getColumnCanGlobalFilter==null?void 0:t.options.getColumnCanGlobalFilter(e))!=null?i:!0)&&!!e.accessorFn}},createTable:e=>{e.getGlobalAutoFilterFn=()=>dt.includesString,e.getGlobalFilterFn=()=>{var t,n;const{globalFilterFn:o}=e.options;return qn(o)?o:o==="auto"?e.getGlobalAutoFilterFn():(t=(n=e.options.filterFns)==null?void 0:n[o])!=null?t:dt[o]},e.setGlobalFilter=t=>{e.options.onGlobalFilterChange==null||e.options.onGlobalFilterChange(t)},e.resetGlobalFilter=t=>{e.setGlobalFilter(t?void 0:e.initialState.globalFilter)}}},wa={getInitialState:e=>({expanded:{},...e}),getDefaultOptions:e=>({onExpandedChange:Be("expanded",e),paginateExpandedRows:!0}),createTable:e=>{let t=!1,n=!1;e._autoResetExpanded=()=>{var o,r;if(!t){e._queue(()=>{t=!0});return}if((o=(r=e.options.autoResetAll)!=null?r:e.options.autoResetExpanded)!=null?o:!e.options.manualExpanding){if(n)return;n=!0,e._queue(()=>{e.resetExpanded(),n=!1})}},e.setExpanded=o=>e.options.onExpandedChange==null?void 0:e.options.onExpandedChange(o),e.toggleAllRowsExpanded=o=>{o??!e.getIsAllRowsExpanded()?e.setExpanded(!0):e.setExpanded({})},e.resetExpanded=o=>{var r,i;e.setExpanded(o?{}:(r=(i=e.initialState)==null?void 0:i.expanded)!=null?r:{})},e.getCanSomeRowsExpand=()=>e.getPrePaginationRowModel().flatRows.some(o=>o.getCanExpand()),e.getToggleAllRowsExpandedHandler=()=>o=>{o.persist==null||o.persist(),e.toggleAllRowsExpanded()},e.getIsSomeRowsExpanded=()=>{const o=e.getState().expanded;return o===!0||Object.values(o).some(Boolean)},e.getIsAllRowsExpanded=()=>{const o=e.getState().expanded;return typeof o=="boolean"?o===!0:!(!Object.keys(o).length||e.getRowModel().flatRows.some(r=>!r.getIsExpanded()))},e.getExpandedDepth=()=>{let o=0;return(e.getState().expanded===!0?Object.keys(e.getRowModel().rowsById):Object.keys(e.getState().expanded)).forEach(i=>{const l=i.split(".");o=Math.max(o,l.length)}),o},e.getPreExpandedRowModel=()=>e.getSortedRowModel(),e.getExpandedRowModel=()=>(!e._getExpandedRowModel&&e.options.getExpandedRowModel&&(e._getExpandedRowModel=e.options.getExpandedRowModel(e)),e.options.manualExpanding||!e._getExpandedRowModel?e.getPreExpandedRowModel():e._getExpandedRowModel())},createRow:(e,t)=>{e.toggleExpanded=n=>{t.setExpanded(o=>{var r;const i=o===!0?!0:!!(o!=null&&o[e.id]);let l={};if(o===!0?Object.keys(t.getRowModel().rowsById).forEach(s=>{l[s]=!0}):l=o,n=(r=n)!=null?r:!i,!i&&n)return{...l,[e.id]:!0};if(i&&!n){const{[e.id]:s,...a}=l;return a}return o})},e.getIsExpanded=()=>{var n;const o=t.getState().expanded;return!!((n=t.options.getIsRowExpanded==null?void 0:t.options.getIsRowExpanded(e))!=null?n:o===!0||o?.[e.id])},e.getCanExpand=()=>{var n,o,r;return(n=t.options.getRowCanExpand==null?void 0:t.options.getRowCanExpand(e))!=null?n:((o=t.options.enableExpanding)!=null?o:!0)&&!!((r=e.subRows)!=null&&r.length)},e.getIsAllParentsExpanded=()=>{let n=!0,o=e;for(;n&&o.parentId;)o=t.getRow(o.parentId,!0),n=o.getIsExpanded();return n},e.getToggleExpandedHandler=()=>{const n=e.getCanExpand();return()=>{n&&e.toggleExpanded()}}}},bo=0,yo=10,so=()=>({pageIndex:bo,pageSize:yo}),_a={getInitialState:e=>({...e,pagination:{...so(),...e?.pagination}}),getDefaultOptions:e=>({onPaginationChange:Be("pagination",e)}),createTable:e=>{let t=!1,n=!1;e._autoResetPageIndex=()=>{var o,r;if(!t){e._queue(()=>{t=!0});return}if((o=(r=e.options.autoResetAll)!=null?r:e.options.autoResetPageIndex)!=null?o:!e.options.manualPagination){if(n)return;n=!0,e._queue(()=>{e.resetPageIndex(),n=!1})}},e.setPagination=o=>{const r=i=>$t(o,i);return e.options.onPaginationChange==null?void 0:e.options.onPaginationChange(r)},e.resetPagination=o=>{var r;e.setPagination(o?so():(r=e.initialState.pagination)!=null?r:so())},e.setPageIndex=o=>{e.setPagination(r=>{let i=$t(o,r.pageIndex);const l=typeof e.options.pageCount>"u"||e.options.pageCount===-1?Number.MAX_SAFE_INTEGER:e.options.pageCount-1;return i=Math.max(0,Math.min(i,l)),{...r,pageIndex:i}})},e.resetPageIndex=o=>{var r,i;e.setPageIndex(o?bo:(r=(i=e.initialState)==null||(i=i.pagination)==null?void 0:i.pageIndex)!=null?r:bo)},e.resetPageSize=o=>{var r,i;e.setPageSize(o?yo:(r=(i=e.initialState)==null||(i=i.pagination)==null?void 0:i.pageSize)!=null?r:yo)},e.setPageSize=o=>{e.setPagination(r=>{const i=Math.max(1,$t(o,r.pageSize)),l=r.pageSize*r.pageIndex,s=Math.floor(l/i);return{...r,pageIndex:s,pageSize:i}})},e.setPageCount=o=>e.setPagination(r=>{var i;let l=$t(o,(i=e.options.pageCount)!=null?i:-1);return typeof l=="number"&&(l=Math.max(-1,l)),{...r,pageCount:l}}),e.getPageOptions=N(()=>[e.getPageCount()],o=>{let r=[];return o&&o>0&&(r=[...new Array(o)].fill(null).map((i,l)=>l)),r},G(e.options,"debugTable","getPageOptions")),e.getCanPreviousPage=()=>e.getState().pagination.pageIndex>0,e.getCanNextPage=()=>{const{pageIndex:o}=e.getState().pagination,r=e.getPageCount();return r===-1?!0:r===0?!1:o<r-1},e.previousPage=()=>e.setPageIndex(o=>o-1),e.nextPage=()=>e.setPageIndex(o=>o+1),e.firstPage=()=>e.setPageIndex(0),e.lastPage=()=>e.setPageIndex(e.getPageCount()-1),e.getPrePaginationRowModel=()=>e.getExpandedRowModel(),e.getPaginationRowModel=()=>(!e._getPaginationRowModel&&e.options.getPaginationRowModel&&(e._getPaginationRowModel=e.options.getPaginationRowModel(e)),e.options.manualPagination||!e._getPaginationRowModel?e.getPrePaginationRowModel():e._getPaginationRowModel()),e.getPageCount=()=>{var o;return(o=e.options.pageCount)!=null?o:Math.ceil(e.getRowCount()/e.getState().pagination.pageSize)},e.getRowCount=()=>{var o;return(o=e.options.rowCount)!=null?o:e.getPrePaginationRowModel().rows.length}}},ao=()=>({top:[],bottom:[]}),Ra={getInitialState:e=>({rowPinning:ao(),...e}),getDefaultOptions:e=>({onRowPinningChange:Be("rowPinning",e)}),createRow:(e,t)=>{e.pin=(n,o,r)=>{const i=o?e.getLeafRows().map(a=>{let{id:c}=a;return c}):[],l=r?e.getParentRows().map(a=>{let{id:c}=a;return c}):[],s=new Set([...l,e.id,...i]);t.setRowPinning(a=>{var c,m;if(n==="bottom"){var p,g;return{top:((p=a?.top)!=null?p:[]).filter(b=>!(s!=null&&s.has(b))),bottom:[...((g=a?.bottom)!=null?g:[]).filter(b=>!(s!=null&&s.has(b))),...Array.from(s)]}}if(n==="top"){var u,h;return{top:[...((u=a?.top)!=null?u:[]).filter(b=>!(s!=null&&s.has(b))),...Array.from(s)],bottom:((h=a?.bottom)!=null?h:[]).filter(b=>!(s!=null&&s.has(b)))}}return{top:((c=a?.top)!=null?c:[]).filter(b=>!(s!=null&&s.has(b))),bottom:((m=a?.bottom)!=null?m:[]).filter(b=>!(s!=null&&s.has(b)))}})},e.getCanPin=()=>{var n;const{enableRowPinning:o,enablePinning:r}=t.options;return typeof o=="function"?o(e):(n=o??r)!=null?n:!0},e.getIsPinned=()=>{const n=[e.id],{top:o,bottom:r}=t.getState().rowPinning,i=n.some(s=>o?.includes(s)),l=n.some(s=>r?.includes(s));return i?"top":l?"bottom":!1},e.getPinnedIndex=()=>{var n,o;const r=e.getIsPinned();if(!r)return-1;const i=(n=r==="top"?t.getTopRows():t.getBottomRows())==null?void 0:n.map(l=>{let{id:s}=l;return s});return(o=i?.indexOf(e.id))!=null?o:-1}},createTable:e=>{e.setRowPinning=t=>e.options.onRowPinningChange==null?void 0:e.options.onRowPinningChange(t),e.resetRowPinning=t=>{var n,o;return e.setRowPinning(t?ao():(n=(o=e.initialState)==null?void 0:o.rowPinning)!=null?n:ao())},e.getIsSomeRowsPinned=t=>{var n;const o=e.getState().rowPinning;if(!t){var r,i;return!!((r=o.top)!=null&&r.length||(i=o.bottom)!=null&&i.length)}return!!((n=o[t])!=null&&n.length)},e._getPinnedRows=(t,n,o)=>{var r;return((r=e.options.keepPinnedRows)==null||r?(n??[]).map(l=>{const s=e.getRow(l,!0);return s.getIsAllParentsExpanded()?s:null}):(n??[]).map(l=>t.find(s=>s.id===l))).filter(Boolean).map(l=>({...l,position:o}))},e.getTopRows=N(()=>[e.getRowModel().rows,e.getState().rowPinning.top],(t,n)=>e._getPinnedRows(t,n,"top"),G(e.options,"debugRows","getTopRows")),e.getBottomRows=N(()=>[e.getRowModel().rows,e.getState().rowPinning.bottom],(t,n)=>e._getPinnedRows(t,n,"bottom"),G(e.options,"debugRows","getBottomRows")),e.getCenterRows=N(()=>[e.getRowModel().rows,e.getState().rowPinning.top,e.getState().rowPinning.bottom],(t,n,o)=>{const r=new Set([...n??[],...o??[]]);return t.filter(i=>!r.has(i.id))},G(e.options,"debugRows","getCenterRows"))}},ka={getInitialState:e=>({rowSelection:{},...e}),getDefaultOptions:e=>({onRowSelectionChange:Be("rowSelection",e),enableRowSelection:!0,enableMultiRowSelection:!0,enableSubRowSelection:!0}),createTable:e=>{e.setRowSelection=t=>e.options.onRowSelectionChange==null?void 0:e.options.onRowSelectionChange(t),e.resetRowSelection=t=>{var n;return e.setRowSelection(t?{}:(n=e.initialState.rowSelection)!=null?n:{})},e.toggleAllRowsSelected=t=>{e.setRowSelection(n=>{t=typeof t<"u"?t:!e.getIsAllRowsSelected();const o={...n},r=e.getPreGroupedRowModel().flatRows;return t?r.forEach(i=>{i.getCanSelect()&&(o[i.id]=!0)}):r.forEach(i=>{delete o[i.id]}),o})},e.toggleAllPageRowsSelected=t=>e.setRowSelection(n=>{const o=typeof t<"u"?t:!e.getIsAllPageRowsSelected(),r={...n};return e.getRowModel().rows.forEach(i=>{So(r,i.id,o,!0,e)}),r}),e.getPreSelectedRowModel=()=>e.getCoreRowModel(),e.getSelectedRowModel=N(()=>[e.getState().rowSelection,e.getCoreRowModel()],(t,n)=>Object.keys(t).length?co(e,n):{rows:[],flatRows:[],rowsById:{}},G(e.options,"debugTable","getSelectedRowModel")),e.getFilteredSelectedRowModel=N(()=>[e.getState().rowSelection,e.getFilteredRowModel()],(t,n)=>Object.keys(t).length?co(e,n):{rows:[],flatRows:[],rowsById:{}},G(e.options,"debugTable","getFilteredSelectedRowModel")),e.getGroupedSelectedRowModel=N(()=>[e.getState().rowSelection,e.getSortedRowModel()],(t,n)=>Object.keys(t).length?co(e,n):{rows:[],flatRows:[],rowsById:{}},G(e.options,"debugTable","getGroupedSelectedRowModel")),e.getIsAllRowsSelected=()=>{const t=e.getFilteredRowModel().flatRows,{rowSelection:n}=e.getState();let o=!!(t.length&&Object.keys(n).length);return o&&t.some(r=>r.getCanSelect()&&!n[r.id])&&(o=!1),o},e.getIsAllPageRowsSelected=()=>{const t=e.getPaginationRowModel().flatRows.filter(r=>r.getCanSelect()),{rowSelection:n}=e.getState();let o=!!t.length;return o&&t.some(r=>!n[r.id])&&(o=!1),o},e.getIsSomeRowsSelected=()=>{var t;const n=Object.keys((t=e.getState().rowSelection)!=null?t:{}).length;return n>0&&n<e.getFilteredRowModel().flatRows.length},e.getIsSomePageRowsSelected=()=>{const t=e.getPaginationRowModel().flatRows;return e.getIsAllPageRowsSelected()?!1:t.filter(n=>n.getCanSelect()).some(n=>n.getIsSelected()||n.getIsSomeSelected())},e.getToggleAllRowsSelectedHandler=()=>t=>{e.toggleAllRowsSelected(t.target.checked)},e.getToggleAllPageRowsSelectedHandler=()=>t=>{e.toggleAllPageRowsSelected(t.target.checked)}},createRow:(e,t)=>{e.toggleSelected=(n,o)=>{const r=e.getIsSelected();t.setRowSelection(i=>{var l;if(n=typeof n<"u"?n:!r,e.getCanSelect()&&r===n)return i;const s={...i};return So(s,e.id,n,(l=o?.selectChildren)!=null?l:!0,t),s})},e.getIsSelected=()=>{const{rowSelection:n}=t.getState();return Oo(e,n)},e.getIsSomeSelected=()=>{const{rowSelection:n}=t.getState();return Co(e,n)==="some"},e.getIsAllSubRowsSelected=()=>{const{rowSelection:n}=t.getState();return Co(e,n)==="all"},e.getCanSelect=()=>{var n;return typeof t.options.enableRowSelection=="function"?t.options.enableRowSelection(e):(n=t.options.enableRowSelection)!=null?n:!0},e.getCanSelectSubRows=()=>{var n;return typeof t.options.enableSubRowSelection=="function"?t.options.enableSubRowSelection(e):(n=t.options.enableSubRowSelection)!=null?n:!0},e.getCanMultiSelect=()=>{var n;return typeof t.options.enableMultiRowSelection=="function"?t.options.enableMultiRowSelection(e):(n=t.options.enableMultiRowSelection)!=null?n:!0},e.getToggleSelectedHandler=()=>{const n=e.getCanSelect();return o=>{var r;n&&e.toggleSelected((r=o.target)==null?void 0:r.checked)}}}},So=(e,t,n,o,r)=>{var i;const l=r.getRow(t,!0);n?(l.getCanMultiSelect()||Object.keys(e).forEach(s=>delete e[s]),l.getCanSelect()&&(e[t]=!0)):delete e[t],o&&(i=l.subRows)!=null&&i.length&&l.getCanSelectSubRows()&&l.subRows.forEach(s=>So(e,s.id,n,o,r))};function co(e,t){const n=e.getState().rowSelection,o=[],r={},i=function(l,s){return l.map(a=>{var c;const m=Oo(a,n);if(m&&(o.push(a),r[a.id]=a),(c=a.subRows)!=null&&c.length&&(a={...a,subRows:i(a.subRows)}),m)return a}).filter(Boolean)};return{rows:i(t.rows),flatRows:o,rowsById:r}}function Oo(e,t){var n;return(n=t[e.id])!=null?n:!1}function Co(e,t,n){var o;if(!((o=e.subRows)!=null&&o.length))return!1;let r=!0,i=!1;return e.subRows.forEach(l=>{if(!(i&&!r)&&(l.getCanSelect()&&(Oo(l,t)?i=!0:r=!1),l.subRows&&l.subRows.length)){const s=Co(l,t);s==="all"?i=!0:(s==="some"&&(i=!0),r=!1)}}),r?"all":i?"some":!1}const wo=/([0-9]+)/gm,Ia=(e,t,n)=>Ji(Mt(e.getValue(n)).toLowerCase(),Mt(t.getValue(n)).toLowerCase()),Pa=(e,t,n)=>Ji(Mt(e.getValue(n)),Mt(t.getValue(n))),$a=(e,t,n)=>Vo(Mt(e.getValue(n)).toLowerCase(),Mt(t.getValue(n)).toLowerCase()),Ma=(e,t,n)=>Vo(Mt(e.getValue(n)),Mt(t.getValue(n))),Fa=(e,t,n)=>{const o=e.getValue(n),r=t.getValue(n);return o>r?1:o<r?-1:0},Ea=(e,t,n)=>Vo(e.getValue(n),t.getValue(n));function Vo(e,t){return e===t?0:e>t?1:-1}function Mt(e){return typeof e=="number"?isNaN(e)||e===1/0||e===-1/0?"":String(e):typeof e=="string"?e:""}function Ji(e,t){const n=e.split(wo).filter(Boolean),o=t.split(wo).filter(Boolean);for(;n.length&&o.length;){const r=n.shift(),i=o.shift(),l=parseInt(r,10),s=parseInt(i,10),a=[l,s].sort();if(isNaN(a[0])){if(r>i)return 1;if(i>r)return-1;continue}if(isNaN(a[1]))return isNaN(l)?-1:1;if(l>s)return 1;if(s>l)return-1}return n.length-o.length}const fn={alphanumeric:Ia,alphanumericCaseSensitive:Pa,text:$a,textCaseSensitive:Ma,datetime:Fa,basic:Ea},Aa={getInitialState:e=>({sorting:[],...e}),getDefaultColumnDef:()=>({sortingFn:"auto",sortUndefined:1}),getDefaultOptions:e=>({onSortingChange:Be("sorting",e),isMultiSortEvent:t=>t.shiftKey}),createColumn:(e,t)=>{e.getAutoSortingFn=()=>{const n=t.getFilteredRowModel().flatRows.slice(10);let o=!1;for(const r of n){const i=r?.getValue(e.id);if(Object.prototype.toString.call(i)==="[object Date]")return fn.datetime;if(typeof i=="string"&&(o=!0,i.split(wo).length>1))return fn.alphanumeric}return o?fn.text:fn.basic},e.getAutoSortDir=()=>{const n=t.getFilteredRowModel().flatRows[0];return typeof n?.getValue(e.id)=="string"?"asc":"desc"},e.getSortingFn=()=>{var n,o;if(!e)throw new Error;return qn(e.columnDef.sortingFn)?e.columnDef.sortingFn:e.columnDef.sortingFn==="auto"?e.getAutoSortingFn():(n=(o=t.options.sortingFns)==null?void 0:o[e.columnDef.sortingFn])!=null?n:fn[e.columnDef.sortingFn]},e.toggleSorting=(n,o)=>{const r=e.getNextSortingOrder(),i=typeof n<"u"&&n!==null;t.setSorting(l=>{const s=l?.find(u=>u.id===e.id),a=l?.findIndex(u=>u.id===e.id);let c=[],m,p=i?n:r==="desc";if(l!=null&&l.length&&e.getCanMultiSort()&&o?s?m="toggle":m="add":l!=null&&l.length&&a!==l.length-1?m="replace":s?m="toggle":m="replace",m==="toggle"&&(i||r||(m="remove")),m==="add"){var g;c=[...l,{id:e.id,desc:p}],c.splice(0,c.length-((g=t.options.maxMultiSortColCount)!=null?g:Number.MAX_SAFE_INTEGER))}else m==="toggle"?c=l.map(u=>u.id===e.id?{...u,desc:p}:u):m==="remove"?c=l.filter(u=>u.id!==e.id):c=[{id:e.id,desc:p}];return c})},e.getFirstSortDir=()=>{var n,o;return((n=(o=e.columnDef.sortDescFirst)!=null?o:t.options.sortDescFirst)!=null?n:e.getAutoSortDir()==="desc")?"desc":"asc"},e.getNextSortingOrder=n=>{var o,r;const i=e.getFirstSortDir(),l=e.getIsSorted();return l?l!==i&&((o=t.options.enableSortingRemoval)==null||o)&&(!(n&&(r=t.options.enableMultiRemove)!=null)||r)?!1:l==="desc"?"asc":"desc":i},e.getCanSort=()=>{var n,o;return((n=e.columnDef.enableSorting)!=null?n:!0)&&((o=t.options.enableSorting)!=null?o:!0)&&!!e.accessorFn},e.getCanMultiSort=()=>{var n,o;return(n=(o=e.columnDef.enableMultiSort)!=null?o:t.options.enableMultiSort)!=null?n:!!e.accessorFn},e.getIsSorted=()=>{var n;const o=(n=t.getState().sorting)==null?void 0:n.find(r=>r.id===e.id);return o?o.desc?"desc":"asc":!1},e.getSortIndex=()=>{var n,o;return(n=(o=t.getState().sorting)==null?void 0:o.findIndex(r=>r.id===e.id))!=null?n:-1},e.clearSorting=()=>{t.setSorting(n=>n!=null&&n.length?n.filter(o=>o.id!==e.id):[])},e.getToggleSortingHandler=()=>{const n=e.getCanSort();return o=>{n&&(o.persist==null||o.persist(),e.toggleSorting==null||e.toggleSorting(void 0,e.getCanMultiSort()?t.options.isMultiSortEvent==null?void 0:t.options.isMultiSortEvent(o):!1))}}},createTable:e=>{e.setSorting=t=>e.options.onSortingChange==null?void 0:e.options.onSortingChange(t),e.resetSorting=t=>{var n,o;e.setSorting(t?[]:(n=(o=e.initialState)==null?void 0:o.sorting)!=null?n:[])},e.getPreSortedRowModel=()=>e.getGroupedRowModel(),e.getSortedRowModel=()=>(!e._getSortedRowModel&&e.options.getSortedRowModel&&(e._getSortedRowModel=e.options.getSortedRowModel(e)),e.options.manualSorting||!e._getSortedRowModel?e.getPreSortedRowModel():e._getSortedRowModel())}},Da=[ta,ya,ha,ma,na,oa,Sa,Ca,Aa,fa,wa,_a,Ra,ka,xa];function Ta(e){var t,n;(e.debugAll||e.debugTable)&&console.info("Creating Table Instance...");const o=[...Da,...(t=e._features)!=null?t:[]];let r={_features:o};const i=r._features.reduce((g,u)=>Object.assign(g,u.getDefaultOptions==null?void 0:u.getDefaultOptions(r)),{}),l=g=>r.options.mergeOptions?r.options.mergeOptions(i,g):{...i,...g};let a={...{},...(n=e.initialState)!=null?n:{}};r._features.forEach(g=>{var u;a=(u=g.getInitialState==null?void 0:g.getInitialState(a))!=null?u:a});const c=[];let m=!1;const p={_features:o,options:{...i,...e},initialState:a,_queue:g=>{c.push(g),m||(m=!0,Promise.resolve().then(()=>{for(;c.length;)c.shift()();m=!1}).catch(u=>setTimeout(()=>{throw u})))},reset:()=>{r.setState(r.initialState)},setOptions:g=>{const u=$t(g,r.options);r.options=l(u)},getState:()=>r.options.state,setState:g=>{r.options.onStateChange==null||r.options.onStateChange(g)},_getRowId:(g,u,h)=>{var b;return(b=r.options.getRowId==null?void 0:r.options.getRowId(g,u,h))!=null?b:`${h?[h.id,u].join("."):u}`},getCoreRowModel:()=>(r._getCoreRowModel||(r._getCoreRowModel=r.options.getCoreRowModel(r)),r._getCoreRowModel()),getRowModel:()=>r.getPaginationRowModel(),getRow:(g,u)=>{let h=(u?r.getPrePaginationRowModel():r.getRowModel()).rowsById[g];if(!h&&(h=r.getCoreRowModel().rowsById[g],!h))throw new Error(`getRow could not find row with ID: ${g}`);return h},_getDefaultColumnDef:N(()=>[r.options.defaultColumn],g=>{var u;return g=(u=g)!=null?u:{},{header:h=>{const b=h.header.column.columnDef;return b.accessorKey?b.accessorKey:b.accessorFn?b.id:null},cell:h=>{var b,S;return(b=(S=h.renderValue())==null||S.toString==null?void 0:S.toString())!=null?b:null},...r._features.reduce((h,b)=>Object.assign(h,b.getDefaultColumnDef==null?void 0:b.getDefaultColumnDef()),{}),...g}},G(e,"debugColumns","_getDefaultColumnDef")),_getColumnDefs:()=>r.options.columns,getAllColumns:N(()=>[r._getColumnDefs()],g=>{const u=function(h,b,S){return S===void 0&&(S=0),h.map(x=>{const y=ea(r,x,S,b),v=x;return y.columns=v.columns?u(v.columns,y,S+1):[],y})};return u(g)},G(e,"debugColumns","getAllColumns")),getAllFlatColumns:N(()=>[r.getAllColumns()],g=>g.flatMap(u=>u.getFlatColumns()),G(e,"debugColumns","getAllFlatColumns")),_getAllFlatColumnsById:N(()=>[r.getAllFlatColumns()],g=>g.reduce((u,h)=>(u[h.id]=h,u),{}),G(e,"debugColumns","getAllFlatColumnsById")),getAllLeafColumns:N(()=>[r.getAllColumns(),r._getOrderColumnsFn()],(g,u)=>{let h=g.flatMap(b=>b.getLeafColumns());return u(h)},G(e,"debugColumns","getAllLeafColumns")),getColumn:g=>{const u=r._getAllFlatColumnsById()[g];return u||console.error(`[Table] Column with id '${g}' does not exist.`),u}};Object.assign(r,p);for(let g=0;g<r._features.length;g++){const u=r._features[g];u==null||u.createTable==null||u.createTable(r)}return r}function za(){return e=>N(()=>[e.options.data],t=>{const n={rows:[],flatRows:[],rowsById:{}},o=function(r,i,l){i===void 0&&(i=0);const s=[];for(let c=0;c<r.length;c++){const m=zo(e,e._getRowId(r[c],c,l),r[c],c,i,void 0,l?.id);if(n.flatRows.push(m),n.rowsById[m.id]=m,s.push(m),e.options.getSubRows){var a;m.originalSubRows=e.options.getSubRows(r[c],c),(a=m.originalSubRows)!=null&&a.length&&(m.subRows=o(m.originalSubRows,i+1,m))}}return s};return n.rows=o(t),n},G(e.options,"debugTable","getRowModel",()=>e._autoResetPageIndex()))}function La(e){const t=[],n=o=>{var r;t.push(o),(r=o.subRows)!=null&&r.length&&o.getIsExpanded()&&o.subRows.forEach(n)};return e.rows.forEach(n),{rows:t,flatRows:e.flatRows,rowsById:e.rowsById}}function Oa(e,t,n){return n.options.filterFromLeafRows?Va(e,t,n):Ba(e,t,n)}function Va(e,t,n){var o;const r=[],i={},l=(o=n.options.maxLeafRowFilterDepth)!=null?o:100,s=function(a,c){c===void 0&&(c=0);const m=[];for(let g=0;g<a.length;g++){var p;let u=a[g];const h=zo(n,u.id,u.original,u.index,u.depth,void 0,u.parentId);if(h.columnFilters=u.columnFilters,(p=u.subRows)!=null&&p.length&&c<l){if(h.subRows=s(u.subRows,c+1),u=h,t(u)&&!h.subRows.length){m.push(u),i[u.id]=u,r.push(u);continue}if(t(u)||h.subRows.length){m.push(u),i[u.id]=u,r.push(u);continue}}else u=h,t(u)&&(m.push(u),i[u.id]=u,r.push(u))}return m};return{rows:s(e),flatRows:r,rowsById:i}}function Ba(e,t,n){var o;const r=[],i={},l=(o=n.options.maxLeafRowFilterDepth)!=null?o:100,s=function(a,c){c===void 0&&(c=0);const m=[];for(let g=0;g<a.length;g++){let u=a[g];if(t(u)){var p;if((p=u.subRows)!=null&&p.length&&c<l){const b=zo(n,u.id,u.original,u.index,u.depth,void 0,u.parentId);b.subRows=s(u.subRows,c+1),u=b}m.push(u),r.push(u),i[u.id]=u}}return m};return{rows:s(e),flatRows:r,rowsById:i}}function Ka(){return e=>N(()=>[e.getPreFilteredRowModel(),e.getState().columnFilters,e.getState().globalFilter],(t,n,o)=>{if(!t.rows.length||!(n!=null&&n.length)&&!o){for(let g=0;g<t.flatRows.length;g++)t.flatRows[g].columnFilters={},t.flatRows[g].columnFiltersMeta={};return t}const r=[],i=[];(n??[]).forEach(g=>{var u;const h=e.getColumn(g.id);if(!h)return;const b=h.getFilterFn();if(!b){console.warn(`Could not find a valid 'column.filterFn' for column with the ID: ${h.id}.`);return}r.push({id:g.id,filterFn:b,resolvedValue:(u=b.resolveFilterValue==null?void 0:b.resolveFilterValue(g.value))!=null?u:g.value})});const l=(n??[]).map(g=>g.id),s=e.getGlobalFilterFn(),a=e.getAllLeafColumns().filter(g=>g.getCanGlobalFilter());o&&s&&a.length&&(l.push("__global__"),a.forEach(g=>{var u;i.push({id:g.id,filterFn:s,resolvedValue:(u=s.resolveFilterValue==null?void 0:s.resolveFilterValue(o))!=null?u:o})}));let c,m;for(let g=0;g<t.flatRows.length;g++){const u=t.flatRows[g];if(u.columnFilters={},r.length)for(let h=0;h<r.length;h++){c=r[h];const b=c.id;u.columnFilters[b]=c.filterFn(u,b,c.resolvedValue,S=>{u.columnFiltersMeta[b]=S})}if(i.length){for(let h=0;h<i.length;h++){m=i[h];const b=m.id;if(m.filterFn(u,b,m.resolvedValue,S=>{u.columnFiltersMeta[b]=S})){u.columnFilters.__global__=!0;break}}u.columnFilters.__global__!==!0&&(u.columnFilters.__global__=!1)}}const p=g=>{for(let u=0;u<l.length;u++)if(g.columnFilters[l[u]]===!1)return!1;return!0};return Oa(t.rows,p,e)},G(e.options,"debugTable","getFilteredRowModel",()=>e._autoResetPageIndex()))}function Wa(e){return t=>N(()=>[t.getState().pagination,t.getPrePaginationRowModel(),t.options.paginateExpandedRows?void 0:t.getState().expanded],(n,o)=>{if(!o.rows.length)return o;const{pageSize:r,pageIndex:i}=n;let{rows:l,flatRows:s,rowsById:a}=o;const c=r*i,m=c+r;l=l.slice(c,m);let p;t.options.paginateExpandedRows?p={rows:l,flatRows:s,rowsById:a}:p=La({rows:l,flatRows:s,rowsById:a}),p.flatRows=[];const g=u=>{p.flatRows.push(u),u.subRows.length&&u.subRows.forEach(g)};return p.rows.forEach(g),p},G(t.options,"debugTable","getPaginationRowModel"))}function Ha(){return e=>N(()=>[e.getState().sorting,e.getPreSortedRowModel()],(t,n)=>{if(!n.rows.length||!(t!=null&&t.length))return n;const o=e.getState().sorting,r=[],i=o.filter(a=>{var c;return(c=e.getColumn(a.id))==null?void 0:c.getCanSort()}),l={};i.forEach(a=>{const c=e.getColumn(a.id);c&&(l[a.id]={sortUndefined:c.columnDef.sortUndefined,invertSorting:c.columnDef.invertSorting,sortingFn:c.getSortingFn()})});const s=a=>{const c=a.map(m=>({...m}));return c.sort((m,p)=>{for(let u=0;u<i.length;u+=1){var g;const h=i[u],b=l[h.id],S=b.sortUndefined,x=(g=h?.desc)!=null?g:!1;let y=0;if(S){const v=m.getValue(h.id),_=p.getValue(h.id),w=v===void 0,M=_===void 0;if(w||M){if(S==="first")return w?-1:1;if(S==="last")return w?1:-1;y=w&&M?0:w?S:-S}}if(y===0&&(y=b.sortingFn(m,p,h.id)),y!==0)return x&&(y*=-1),b.invertSorting&&(y*=-1),y}return m.index-p.index}),c.forEach(m=>{var p;r.push(m),(p=m.subRows)!=null&&p.length&&(m.subRows=s(m.subRows))}),c};return{rows:s(n.rows),flatRows:r,rowsById:n.rowsById}},G(e.options,"debugTable","getSortedRowModel",()=>e._autoResetPageIndex()))}/**
   * solid-table
   *
   * Copyright (c) TanStack
   *
   * This source code is licensed under the MIT license found in the
   * LICENSE.md file in the root directory of this source tree.
   *
   * @license MIT
   */function Ur(e,t){return e?typeof e=="function"?R(e,t):e:null}function Na(e){const t=U({state:{},onStateChange:()=>{},renderFallbackValue:null,mergeOptions:(i,l)=>U(i,l)},e),n=Ta(t),[o,r]=is(n.initialState);return Mi(()=>{n.setOptions(i=>U(i,e,{state:U(o,e.state||{}),onStateChange:l=>{r(l),e.onStateChange==null||e.onStateChange(l)}}))}),n}var Ga=V("<div><div><span>⟳</span><span>加载中..."),ja=V("<div><div><span>共 <!> 条记录</span></div><div><span>第 <!> 页，共 <!> 页"),Ua=V("<div><div><table><thead></thead><tbody>"),qr=V("<tr>"),qa=V("<span>"),Xa=V("<th><div>"),Ya=V("<td>");function Za(e){const[t,n]=te(e,["data","columns","loading","pagination","pageSize","sortable","filterable","selectable","onRowSelect","onRowClick","class","height","sticky"]),o=Na({get data(){return t.data||[]},get columns(){return t.columns},getCoreRowModel:za(),getSortedRowModel:t.sortable?Ha():void 0,getFilteredRowModel:t.filterable?Ka():void 0,getPaginationRowModel:t.pagination?Wa():void 0,initialState:{pagination:{pageSize:t.pageSize||10}}}),r=()=>({width:"100%",backgroundColor:"white",borderRadius:"4px",border:"1px solid",borderColor:"border.base",overflow:"hidden",boxShadow:"base"}),i=()=>({width:"100%",height:t.height||"auto",overflow:"auto",position:"relative"}),l=()=>({backgroundColor:"bg.page",borderBottom:"1px solid",borderColor:"border.base",position:t.sticky?"sticky":"static",top:0,zIndex:10}),s=()=>({padding:"12px 16px",textAlign:"left",fontSize:"14px",fontWeight:"500",color:"text.primary",borderRight:"1px solid",borderColor:"border.lighter",cursor:"pointer",userSelect:"none",_hover:{backgroundColor:"border.extra"},_last:{borderRight:"none"}}),a=u=>({backgroundColor:u?"white":"bg.page",borderBottom:"1px solid",borderColor:"border.lighter",transition:"all 0.2s",cursor:t.onRowClick?"pointer":"default",_hover:{backgroundColor:"primary.50"},_last:{borderBottom:"none"}}),c=()=>({padding:"12px 16px",fontSize:"14px",color:"text.regular",borderRight:"1px solid",borderColor:"border.lighter",_last:{borderRight:"none"}}),m=()=>({position:"absolute",top:0,left:0,right:0,bottom:0,backgroundColor:"rgba(255, 255, 255, 0.8)",display:"flex",alignItems:"center",justifyContent:"center",zIndex:100}),p=()=>({display:"flex",alignItems:"center",justifyContent:"space-between",padding:"16px",borderTop:"1px solid",borderColor:"border.base",backgroundColor:"white"}),g=u=>u==="asc"?"↑":u==="desc"?"↓":"↕";return(()=>{var u=Ua(),h=u.firstChild,b=h.firstChild,S=b.firstChild,x=S.nextSibling;return lt(u,U({get class(){return jt(d(r()),t.class)}},n),!1,!0),C(S,R(Fe,{get each(){return o.getHeaderGroups()},children:y=>(()=>{var v=qr();return C(v,R(Fe,{get each(){return y.headers},children:_=>(()=>{var w=Xa(),M=w.firstChild;return Fi(w,"click",_.column.getToggleSortingHandler(),!0),C(M,()=>Ur(_.column.columnDef.header,_.getContext()),null),C(M,R(ae,{get when(){return Q(()=>!!t.sortable)()&&_.column.getCanSort()},get children(){var A=qa();return C(A,()=>g(_.column.getIsSorted())),O(()=>f(A,d({fontSize:"12px",color:"text.secondary"}))),A}}),null),O(A=>{var I=d(s()),F=d({display:"flex",alignItems:"center",gap:"8px"});return I!==A.e&&f(w,A.e=I),F!==A.t&&f(M,A.t=F),A},{e:void 0,t:void 0}),w})()})),v})()})),C(x,R(Fe,{get each(){return o.getRowModel().rows},children:(y,v)=>(()=>{var _=qr();return _.$$click=()=>t.onRowClick?.(y.original),C(_,R(Fe,{get each(){return y.getVisibleCells()},children:w=>(()=>{var M=Ya();return C(M,()=>Ur(w.column.columnDef.cell,w.getContext())),O(()=>f(M,d(c()))),M})()})),O(()=>f(_,d(a(v()%2===0)))),_})()})),C(h,R(ae,{get when(){return t.loading},get children(){var y=Ga(),v=y.firstChild,_=v.firstChild,w=_.nextSibling;return O(M=>{var A=d(m()),I=d({display:"flex",alignItems:"center",gap:"8px"}),F=d({animation:"spin 1s linear infinite",fontSize:"20px"}),D=d({color:"text.secondary"});return A!==M.e&&f(y,M.e=A),I!==M.t&&f(v,M.t=I),F!==M.a&&f(_,M.a=F),D!==M.o&&f(w,M.o=D),M},{e:void 0,t:void 0,a:void 0,o:void 0}),y}}),null),C(u,R(ae,{get when(){return t.pagination},get children(){var y=ja(),v=y.firstChild,_=v.firstChild,w=_.firstChild,M=w.nextSibling;M.nextSibling;var A=v.nextSibling,I=A.firstChild,F=I.firstChild,D=F.nextSibling,T=D.nextSibling,P=T.nextSibling;return P.nextSibling,C(_,()=>o.getFilteredRowModel().rows.length,M),C(A,R(Ie,{size:"small",variant:"default",get disabled(){return!o.getCanPreviousPage()},onClick:()=>o.previousPage(),children:"上一页"}),I),C(I,()=>o.getState().pagination.pageIndex+1,D),C(I,()=>o.getPageCount(),P),C(A,R(Ie,{size:"small",variant:"default",get disabled(){return!o.getCanNextPage()},onClick:()=>o.nextPage(),children:"下一页"}),null),O(z=>{var B=d(p()),K=d({display:"flex",alignItems:"center",gap:"8px"}),j=d({fontSize:"14px",color:"text.secondary"}),W=d({display:"flex",alignItems:"center",gap:"8px"}),q=d({fontSize:"14px",color:"text.regular"});return B!==z.e&&f(y,z.e=B),K!==z.t&&f(v,z.t=K),j!==z.a&&f(_,z.a=j),W!==z.o&&f(A,z.o=W),q!==z.i&&f(I,z.i=q),z},{e:void 0,t:void 0,a:void 0,o:void 0,i:void 0}),y}}),null),O(y=>{var v=d(i()),_=d({width:"100%",borderCollapse:"collapse"}),w=d(l());return v!==y.e&&f(h,y.e=v),_!==y.t&&f(b,y.t=_),w!==y.a&&f(S,y.a=w),y},{e:void 0,t:void 0,a:void 0}),u})()}Tt(["click"]);var Ja=V("<span>✕"),Qa=V("<div><span></span><span>"),ec=V("<div>"),tc=V("<div style=pointer-events:auto>");const[nc,Bo]=H([]);let oc=0;const pn={success:(e,t)=>hn({...t,message:e,type:"success"}),warning:(e,t)=>hn({...t,message:e,type:"warning"}),info:(e,t)=>hn({...t,message:e,type:"info"}),error:(e,t)=>hn({...t,message:e,type:"error"}),show:hn,clear:()=>Bo([])};function hn(e){const t=`message-${++oc}`,n={id:t,type:"info",duration:3e3,showClose:!1,visible:!0,...e};return Bo(o=>[...o,n]),n.duration&&n.duration>0&&setTimeout(()=>{Qi(t)},n.duration),t}function Qi(e){Bo(t=>t.filter(n=>n.id!==e))}function rc(e){const t=()=>({...{display:"flex",alignItems:"center",gap:"8px",padding:"12px 16px",borderRadius:"4px",border:"1px solid",backgroundColor:"white",boxShadow:"light",fontSize:"14px",fontWeight:"400",minWidth:"300px",maxWidth:"500px",marginBottom:"8px",transform:"translateX(0)",opacity:1,transition:"all 0.3s ease",animation:"slideInRight 0.3s ease"},...{success:{color:"success.600",backgroundColor:"success.50",borderColor:"success.200"},warning:{color:"warning.600",backgroundColor:"warning.50",borderColor:"warning.200"},info:{color:"info.600",backgroundColor:"info.50",borderColor:"info.200"},error:{color:"danger.600",backgroundColor:"danger.50",borderColor:"danger.200"}}[e.message.type||"info"]}),n=()=>({fontSize:"16px",flexShrink:0}),o=()=>({marginLeft:"auto",cursor:"pointer",fontSize:"12px",padding:"2px",borderRadius:"2px",_hover:{backgroundColor:"rgba(0, 0, 0, 0.1)"}}),r=()=>{switch(e.message.type){case"success":return"✓";case"warning":return"⚠";case"error":return"✕";default:return"ℹ"}},i=()=>{e.message.onClose?.(),Qi(e.message.id)};return(()=>{var l=Qa(),s=l.firstChild,a=s.nextSibling;return C(s,r),C(a,()=>e.message.message),C(l,R(ae,{get when(){return e.message.showClose},get children(){var c=Ja();return c.$$click=i,O(()=>f(c,d(o()))),c}}),null),O(c=>{var m=d(t()),p=`${20+e.index*60}px`,g=d(n()),u=d({flex:1});return m!==c.e&&f(l,c.e=m),p!==c.t&&ls(l,"top",c.t=p),g!==c.a&&f(s,c.a=g),u!==c.o&&f(a,c.o=u),c},{e:void 0,t:void 0,a:void 0,o:void 0}),l})()}function ic(){const e=()=>({position:"fixed",top:0,right:"20px",zIndex:9999,pointerEvents:"none"});return R(Do,{get children(){var t=ec();return C(t,R(Fe,{get each(){return nc()},children:(n,o)=>(()=>{var r=tc();return C(r,R(rc,{message:n,get index(){return o()}})),r})()})),O(()=>f(t,d(e()))),t}})}const el=document.createElement("style");el.textContent=`
  @keyframes slideInRight {
    from {
      transform: translateX(100%);
      opacity: 0;
    }
    to {
      transform: translateX(0);
      opacity: 1;
    }
  }
  
  @keyframes spin {
    from {
      transform: rotate(0deg);
    }
    to {
      transform: rotate(360deg);
    }
  }
`;document.head.appendChild(el);Tt(["click"]);const lc=!!as;function sc(e){return(...t)=>{for(const n of e)n&&n(...t)}}const L=e=>typeof e=="function"&&!e.length?e():e,Xr=e=>Array.isArray(e)?e:e?[e]:[];function ac(e,...t){return typeof e=="function"?e(...t):e}const cc=lc?e=>ss()?ee(e):e:ee;function dc(e,t,n,o){return e.addEventListener(t,n,o),cc(e.removeEventListener.bind(e,t,n,o))}function uc(e,t,n,o){const r=()=>{Xr(L(e)).forEach(i=>{i&&Xr(L(t)).forEach(l=>dc(i,l,n,o))})};typeof e=="function"?Y(r):O(r)}const gc=/((?:--)?(?:\w+-?)+)\s*:\s*([^;]*)/g;function Yr(e){const t={};let n;for(;n=gc.exec(e);)t[n[1]]=n[2];return t}function Ko(e,t){if(typeof e=="string"){if(typeof t=="string")return`${e};${t}`;e=Yr(e)}else typeof t=="string"&&(t=Yr(t));return{...e,...t}}function Ze(...e){return sc(e)}function fc(e,t,n=-1){return n in e?[...e.slice(0,n),t,...e.slice(n)]:[...e,t]}function _o(e,t){const n=[...e],o=n.indexOf(t);return o!==-1&&n.splice(o,1),n}function pc(e){return typeof e=="number"}function Xt(e){return Object.prototype.toString.call(e)==="[object String]"}function hc(e){return typeof e=="function"}function Wo(e){return t=>`${e()}-${t}`}function We(e,t){return e?e===t||e.contains(t):!1}function xn(e,t=!1){const{activeElement:n}=ot(e);if(!n?.nodeName)return null;if(tl(n)&&n.contentDocument)return xn(n.contentDocument.body,t);if(t){const o=n.getAttribute("aria-activedescendant");if(o){const r=ot(n).getElementById(o);if(r)return r}}return n}function mc(e){return ot(e).defaultView||window}function ot(e){return e?e.ownerDocument||e:document}function tl(e){return e.tagName==="IFRAME"}var nl=(e=>(e.Escape="Escape",e.Enter="Enter",e.Tab="Tab",e.Space=" ",e.ArrowDown="ArrowDown",e.ArrowLeft="ArrowLeft",e.ArrowRight="ArrowRight",e.ArrowUp="ArrowUp",e.End="End",e.Home="Home",e.PageDown="PageDown",e.PageUp="PageUp",e))(nl||{});function Ho(e){return typeof window<"u"&&window.navigator!=null?e.test(window.navigator.userAgentData?.platform||window.navigator.platform):!1}function Xn(){return Ho(/^Mac/i)}function vc(){return Ho(/^iPhone/i)}function xc(){return Ho(/^iPad/i)||Xn()&&navigator.maxTouchPoints>1}function bc(){return vc()||xc()}function yc(){return Xn()||bc()}function ye(e,t){return t&&(hc(t)?t(e):t[0](t[1],e)),e?.defaultPrevented}function be(e){return t=>{for(const n of e)ye(t,n)}}function Sc(e){return Xn()?e.metaKey&&!e.ctrlKey:e.ctrlKey&&!e.metaKey}function ke(e){if(e)if(Cc())e.focus({preventScroll:!0});else{const t=wc(e);e.focus(),_c(t)}}var Fn=null;function Cc(){if(Fn==null){Fn=!1;try{document.createElement("div").focus({get preventScroll(){return Fn=!0,!0}})}catch{}}return Fn}function wc(e){let t=e.parentNode;const n=[],o=document.scrollingElement||document.documentElement;for(;t instanceof HTMLElement&&t!==o;)(t.offsetHeight<t.scrollHeight||t.offsetWidth<t.scrollWidth)&&n.push({element:t,scrollTop:t.scrollTop,scrollLeft:t.scrollLeft}),t=t.parentNode;return o instanceof HTMLElement&&n.push({element:o,scrollTop:o.scrollTop,scrollLeft:o.scrollLeft}),n}function _c(e){for(const{element:t,scrollTop:n,scrollLeft:o}of e)t.scrollTop=n,t.scrollLeft=o}var ol=["input:not([type='hidden']):not([disabled])","select:not([disabled])","textarea:not([disabled])","button:not([disabled])","a[href]","area[href]","[tabindex]","iframe","object","embed","audio[controls]","video[controls]","[contenteditable]:not([contenteditable='false'])"],Rc=[...ol,'[tabindex]:not([tabindex="-1"]):not([disabled])'],No=`${ol.join(":not([hidden]),")},[tabindex]:not([disabled]):not([hidden])`,kc=Rc.join(':not([hidden]):not([tabindex="-1"]),');function rl(e,t){const o=Array.from(e.querySelectorAll(No)).filter(Zr);return t&&Zr(e)&&o.unshift(e),o.forEach((r,i)=>{if(tl(r)&&r.contentDocument){const l=r.contentDocument.body,s=rl(l,!1);o.splice(i,1,...s)}}),o}function Zr(e){return il(e)&&!Ic(e)}function il(e){return e.matches(No)&&Go(e)}function Ic(e){return Number.parseInt(e.getAttribute("tabindex")||"0",10)<0}function Go(e,t){return e.nodeName!=="#comment"&&Pc(e)&&$c(e,t)&&(!e.parentElement||Go(e.parentElement,e))}function Pc(e){if(!(e instanceof HTMLElement)&&!(e instanceof SVGElement))return!1;const{display:t,visibility:n}=e.style;let o=t!=="none"&&n!=="hidden"&&n!=="collapse";if(o){if(!e.ownerDocument.defaultView)return o;const{getComputedStyle:r}=e.ownerDocument.defaultView,{display:i,visibility:l}=r(e);o=i!=="none"&&l!=="hidden"&&l!=="collapse"}return o}function $c(e,t){return!e.hasAttribute("hidden")&&(e.nodeName==="DETAILS"&&t&&t.nodeName!=="SUMMARY"?e.hasAttribute("open"):!0)}function Mc(e,t,n){const o=t?.tabbable?kc:No,r=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode(i){return t?.from?.contains(i)?NodeFilter.FILTER_REJECT:i.matches(o)&&Go(i)&&(!t?.accept||t.accept(i))?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});return t?.from&&(r.currentNode=t.from),r}function Jr(e){let t=e;for(;t&&!Fc(t);)t=t.parentElement;return t||document.scrollingElement||document.documentElement}function Fc(e){const t=window.getComputedStyle(e);return/(auto|scroll)/.test(t.overflow+t.overflowX+t.overflowY)}function Ec(){}function Ac(e,t){const[n,o]=e;let r=!1;const i=t.length;for(let l=i,s=0,a=l-1;s<l;a=s++){const[c,m]=t[s],[p,g]=t[a],[,u]=t[a===0?l-1:a-1]||[0,0],h=(m-g)*(n-c)-(c-p)*(o-m);if(g<m){if(o>=g&&o<m){if(h===0)return!0;h>0&&(o===g?o>u&&(r=!r):r=!r)}}else if(m<g){if(o>m&&o<=g){if(h===0)return!0;h<0&&(o===g?o<u&&(r=!r):r=!r)}}else if(o===m&&(n>=p&&n<=c||n>=c&&n<=p))return!0}return r}function ge(e,t){return U(e,t)}var mn=new Map,Qr=new Set;function ei(){if(typeof window>"u")return;const e=n=>{if(!n.target)return;let o=mn.get(n.target);o||(o=new Set,mn.set(n.target,o),n.target.addEventListener("transitioncancel",t)),o.add(n.propertyName)},t=n=>{if(!n.target)return;const o=mn.get(n.target);if(o&&(o.delete(n.propertyName),o.size===0&&(n.target.removeEventListener("transitioncancel",t),mn.delete(n.target)),mn.size===0)){for(const r of Qr)r();Qr.clear()}};document.body.addEventListener("transitionrun",e),document.body.addEventListener("transitionend",t)}typeof document<"u"&&(document.readyState!=="loading"?ei():document.addEventListener("DOMContentLoaded",ei));function Ro(e,t){const n=ti(e,t,"left"),o=ti(e,t,"top"),r=t.offsetWidth,i=t.offsetHeight;let l=e.scrollLeft,s=e.scrollTop;const a=l+e.offsetWidth,c=s+e.offsetHeight;n<=l?l=n:n+r>a&&(l+=n+r-a),o<=s?s=o:o+i>c&&(s+=o+i-c),e.scrollLeft=l,e.scrollTop=s}function ti(e,t,n){const o=n==="left"?"offsetLeft":"offsetTop";let r=0;for(;t.offsetParent&&(r+=t[o],t.offsetParent!==e);){if(t.offsetParent.contains(e)){r-=e[o];break}t=t.offsetParent}return r}function Dc(e,t){if(document.contains(e)){const n=document.scrollingElement||document.documentElement;if(window.getComputedStyle(n).overflow==="hidden"){let r=Jr(e);for(;e&&r&&e!==n&&r!==n;)Ro(r,e),e=r,r=Jr(e)}else{const{left:r,top:i}=e.getBoundingClientRect();e?.scrollIntoView?.({block:"nearest"});const{left:l,top:s}=e.getBoundingClientRect();(Math.abs(r-l)>1||Math.abs(i-s)>1)&&e.scrollIntoView?.({block:"nearest"})}}}var Tc={border:"0",clip:"rect(0 0 0 0)","clip-path":"inset(50%)",height:"1px",margin:"0 -1px -1px 0",overflow:"hidden",padding:"0",position:"absolute",width:"1px","white-space":"nowrap"},zc=new Set(["Avst","Arab","Armi","Syrc","Samr","Mand","Thaa","Mend","Nkoo","Adlm","Rohg","Hebr"]),Lc=new Set(["ae","ar","arc","bcc","bqi","ckb","dv","fa","glk","he","ku","mzn","nqo","pnb","ps","sd","ug","ur","yi"]);function Oc(e){if(Intl.Locale){const n=new Intl.Locale(e).maximize().script??"";return zc.has(n)}const t=e.split("-")[0];return Lc.has(t)}function Vc(e){return Oc(e)?"rtl":"ltr"}function ll(){let e=typeof navigator<"u"&&(navigator.language||navigator.userLanguage)||"en-US";try{Intl.DateTimeFormat.supportedLocalesOf([e])}catch{e="en-US"}return{locale:e,direction:Vc(e)}}var ko=ll(),bn=new Set;function ni(){ko=ll();for(const e of bn)e(ko)}function Bc(){const[e,t]=H(ko),n=xe(()=>e());return pt(()=>{bn.size===0&&window.addEventListener("languagechange",ni),bn.add(t),ee(()=>{bn.delete(t),bn.size===0&&window.removeEventListener("languagechange",ni)})}),{locale:()=>n().locale,direction:()=>n().direction}}var Kc=Ye();function Lt(){const e=Bc();return Xe(Kc)||e}var uo=new Map;function Wc(e){const{locale:t}=Lt(),n=xe(()=>t()+(e?Object.entries(e).sort((o,r)=>o[0]<r[0]?-1:1).join():""));return xe(()=>{const o=n();let r;return uo.has(o)&&(r=uo.get(o)),r||(r=new Intl.Collator(t(),e),uo.set(o,r)),r})}function Te(e){const[t,n]=te(e,["as"]);if(!t.as)throw new Error("[kobalte]: Polymorphic is missing the required `as` prop.");return R(cs,U(n,{get component(){return t.as}}))}const Hc=["top","right","bottom","left"],Ft=Math.min,Oe=Math.max,Wn=Math.round,En=Math.floor,Et=e=>({x:e,y:e}),Nc={left:"right",right:"left",bottom:"top",top:"bottom"},Gc={start:"end",end:"start"};function Io(e,t,n){return Oe(e,Ft(t,n))}function Ut(e,t){return typeof e=="function"?e(t):e}function At(e){return e.split("-")[0]}function en(e){return e.split("-")[1]}function sl(e){return e==="x"?"y":"x"}function jo(e){return e==="y"?"height":"width"}const jc=new Set(["top","bottom"]);function gt(e){return jc.has(At(e))?"y":"x"}function Uo(e){return sl(gt(e))}function Uc(e,t,n){n===void 0&&(n=!1);const o=en(e),r=Uo(e),i=jo(r);let l=r==="x"?o===(n?"end":"start")?"right":"left":o==="start"?"bottom":"top";return t.reference[i]>t.floating[i]&&(l=Hn(l)),[l,Hn(l)]}function qc(e){const t=Hn(e);return[Po(e),t,Po(t)]}function Po(e){return e.replace(/start|end/g,t=>Gc[t])}const oi=["left","right"],ri=["right","left"],Xc=["top","bottom"],Yc=["bottom","top"];function Zc(e,t,n){switch(e){case"top":case"bottom":return n?t?ri:oi:t?oi:ri;case"left":case"right":return t?Xc:Yc;default:return[]}}function Jc(e,t,n,o){const r=en(e);let i=Zc(At(e),n==="start",o);return r&&(i=i.map(l=>l+"-"+r),t&&(i=i.concat(i.map(Po)))),i}function Hn(e){return e.replace(/left|right|bottom|top/g,t=>Nc[t])}function Qc(e){return{top:0,right:0,bottom:0,left:0,...e}}function al(e){return typeof e!="number"?Qc(e):{top:e,right:e,bottom:e,left:e}}function Nn(e){const{x:t,y:n,width:o,height:r}=e;return{width:o,height:r,top:n,left:t,right:t+o,bottom:n+r,x:t,y:n}}function ii(e,t,n){let{reference:o,floating:r}=e;const i=gt(t),l=Uo(t),s=jo(l),a=At(t),c=i==="y",m=o.x+o.width/2-r.width/2,p=o.y+o.height/2-r.height/2,g=o[s]/2-r[s]/2;let u;switch(a){case"top":u={x:m,y:o.y-r.height};break;case"bottom":u={x:m,y:o.y+o.height};break;case"right":u={x:o.x+o.width,y:p};break;case"left":u={x:o.x-r.width,y:p};break;default:u={x:o.x,y:o.y}}switch(en(t)){case"start":u[l]-=g*(n&&c?-1:1);break;case"end":u[l]+=g*(n&&c?-1:1);break}return u}const ed=async(e,t,n)=>{const{placement:o="bottom",strategy:r="absolute",middleware:i=[],platform:l}=n,s=i.filter(Boolean),a=await(l.isRTL==null?void 0:l.isRTL(t));let c=await l.getElementRects({reference:e,floating:t,strategy:r}),{x:m,y:p}=ii(c,o,a),g=o,u={},h=0;for(let b=0;b<s.length;b++){const{name:S,fn:x}=s[b],{x:y,y:v,data:_,reset:w}=await x({x:m,y:p,initialPlacement:o,placement:g,strategy:r,middlewareData:u,rects:c,platform:l,elements:{reference:e,floating:t}});m=y??m,p=v??p,u={...u,[S]:{...u[S],..._}},w&&h<=50&&(h++,typeof w=="object"&&(w.placement&&(g=w.placement),w.rects&&(c=w.rects===!0?await l.getElementRects({reference:e,floating:t,strategy:r}):w.rects),{x:m,y:p}=ii(c,g,a)),b=-1)}return{x:m,y:p,placement:g,strategy:r,middlewareData:u}};async function Sn(e,t){var n;t===void 0&&(t={});const{x:o,y:r,platform:i,rects:l,elements:s,strategy:a}=e,{boundary:c="clippingAncestors",rootBoundary:m="viewport",elementContext:p="floating",altBoundary:g=!1,padding:u=0}=Ut(t,e),h=al(u),S=s[g?p==="floating"?"reference":"floating":p],x=Nn(await i.getClippingRect({element:(n=await(i.isElement==null?void 0:i.isElement(S)))==null||n?S:S.contextElement||await(i.getDocumentElement==null?void 0:i.getDocumentElement(s.floating)),boundary:c,rootBoundary:m,strategy:a})),y=p==="floating"?{x:o,y:r,width:l.floating.width,height:l.floating.height}:l.reference,v=await(i.getOffsetParent==null?void 0:i.getOffsetParent(s.floating)),_=await(i.isElement==null?void 0:i.isElement(v))?await(i.getScale==null?void 0:i.getScale(v))||{x:1,y:1}:{x:1,y:1},w=Nn(i.convertOffsetParentRelativeRectToViewportRelativeRect?await i.convertOffsetParentRelativeRectToViewportRelativeRect({elements:s,rect:y,offsetParent:v,strategy:a}):y);return{top:(x.top-w.top+h.top)/_.y,bottom:(w.bottom-x.bottom+h.bottom)/_.y,left:(x.left-w.left+h.left)/_.x,right:(w.right-x.right+h.right)/_.x}}const td=e=>({name:"arrow",options:e,async fn(t){const{x:n,y:o,placement:r,rects:i,platform:l,elements:s,middlewareData:a}=t,{element:c,padding:m=0}=Ut(e,t)||{};if(c==null)return{};const p=al(m),g={x:n,y:o},u=Uo(r),h=jo(u),b=await l.getDimensions(c),S=u==="y",x=S?"top":"left",y=S?"bottom":"right",v=S?"clientHeight":"clientWidth",_=i.reference[h]+i.reference[u]-g[u]-i.floating[h],w=g[u]-i.reference[u],M=await(l.getOffsetParent==null?void 0:l.getOffsetParent(c));let A=M?M[v]:0;(!A||!await(l.isElement==null?void 0:l.isElement(M)))&&(A=s.floating[v]||i.floating[h]);const I=_/2-w/2,F=A/2-b[h]/2-1,D=Ft(p[x],F),T=Ft(p[y],F),P=D,z=A-b[h]-T,B=A/2-b[h]/2+I,K=Io(P,B,z),j=!a.arrow&&en(r)!=null&&B!==K&&i.reference[h]/2-(B<P?D:T)-b[h]/2<0,W=j?B<P?B-P:B-z:0;return{[u]:g[u]+W,data:{[u]:K,centerOffset:B-K-W,...j&&{alignmentOffset:W}},reset:j}}}),nd=function(e){return e===void 0&&(e={}),{name:"flip",options:e,async fn(t){var n,o;const{placement:r,middlewareData:i,rects:l,initialPlacement:s,platform:a,elements:c}=t,{mainAxis:m=!0,crossAxis:p=!0,fallbackPlacements:g,fallbackStrategy:u="bestFit",fallbackAxisSideDirection:h="none",flipAlignment:b=!0,...S}=Ut(e,t);if((n=i.arrow)!=null&&n.alignmentOffset)return{};const x=At(r),y=gt(s),v=At(s)===s,_=await(a.isRTL==null?void 0:a.isRTL(c.floating)),w=g||(v||!b?[Hn(s)]:qc(s)),M=h!=="none";!g&&M&&w.push(...Jc(s,b,h,_));const A=[s,...w],I=await Sn(t,S),F=[];let D=((o=i.flip)==null?void 0:o.overflows)||[];if(m&&F.push(I[x]),p){const B=Uc(r,l,_);F.push(I[B[0]],I[B[1]])}if(D=[...D,{placement:r,overflows:F}],!F.every(B=>B<=0)){var T,P;const B=(((T=i.flip)==null?void 0:T.index)||0)+1,K=A[B];if(K&&(!(p==="alignment"?y!==gt(K):!1)||D.every(q=>gt(q.placement)===y?q.overflows[0]>0:!0)))return{data:{index:B,overflows:D},reset:{placement:K}};let j=(P=D.filter(W=>W.overflows[0]<=0).sort((W,q)=>W.overflows[1]-q.overflows[1])[0])==null?void 0:P.placement;if(!j)switch(u){case"bestFit":{var z;const W=(z=D.filter(q=>{if(M){const Z=gt(q.placement);return Z===y||Z==="y"}return!0}).map(q=>[q.placement,q.overflows.filter(Z=>Z>0).reduce((Z,J)=>Z+J,0)]).sort((q,Z)=>q[1]-Z[1])[0])==null?void 0:z[0];W&&(j=W);break}case"initialPlacement":j=s;break}if(r!==j)return{reset:{placement:j}}}return{}}}};function li(e,t){return{top:e.top-t.height,right:e.right-t.width,bottom:e.bottom-t.height,left:e.left-t.width}}function si(e){return Hc.some(t=>e[t]>=0)}const od=function(e){return e===void 0&&(e={}),{name:"hide",options:e,async fn(t){const{rects:n}=t,{strategy:o="referenceHidden",...r}=Ut(e,t);switch(o){case"referenceHidden":{const i=await Sn(t,{...r,elementContext:"reference"}),l=li(i,n.reference);return{data:{referenceHiddenOffsets:l,referenceHidden:si(l)}}}case"escaped":{const i=await Sn(t,{...r,altBoundary:!0}),l=li(i,n.floating);return{data:{escapedOffsets:l,escaped:si(l)}}}default:return{}}}}},rd=new Set(["left","top"]);async function id(e,t){const{placement:n,platform:o,elements:r}=e,i=await(o.isRTL==null?void 0:o.isRTL(r.floating)),l=At(n),s=en(n),a=gt(n)==="y",c=rd.has(l)?-1:1,m=i&&a?-1:1,p=Ut(t,e);let{mainAxis:g,crossAxis:u,alignmentAxis:h}=typeof p=="number"?{mainAxis:p,crossAxis:0,alignmentAxis:null}:{mainAxis:p.mainAxis||0,crossAxis:p.crossAxis||0,alignmentAxis:p.alignmentAxis};return s&&typeof h=="number"&&(u=s==="end"?h*-1:h),a?{x:u*m,y:g*c}:{x:g*c,y:u*m}}const ld=function(e){return e===void 0&&(e=0),{name:"offset",options:e,async fn(t){var n,o;const{x:r,y:i,placement:l,middlewareData:s}=t,a=await id(t,e);return l===((n=s.offset)==null?void 0:n.placement)&&(o=s.arrow)!=null&&o.alignmentOffset?{}:{x:r+a.x,y:i+a.y,data:{...a,placement:l}}}}},sd=function(e){return e===void 0&&(e={}),{name:"shift",options:e,async fn(t){const{x:n,y:o,placement:r}=t,{mainAxis:i=!0,crossAxis:l=!1,limiter:s={fn:S=>{let{x,y}=S;return{x,y}}},...a}=Ut(e,t),c={x:n,y:o},m=await Sn(t,a),p=gt(At(r)),g=sl(p);let u=c[g],h=c[p];if(i){const S=g==="y"?"top":"left",x=g==="y"?"bottom":"right",y=u+m[S],v=u-m[x];u=Io(y,u,v)}if(l){const S=p==="y"?"top":"left",x=p==="y"?"bottom":"right",y=h+m[S],v=h-m[x];h=Io(y,h,v)}const b=s.fn({...t,[g]:u,[p]:h});return{...b,data:{x:b.x-n,y:b.y-o,enabled:{[g]:i,[p]:l}}}}}},ad=function(e){return e===void 0&&(e={}),{name:"size",options:e,async fn(t){var n,o;const{placement:r,rects:i,platform:l,elements:s}=t,{apply:a=()=>{},...c}=Ut(e,t),m=await Sn(t,c),p=At(r),g=en(r),u=gt(r)==="y",{width:h,height:b}=i.floating;let S,x;p==="top"||p==="bottom"?(S=p,x=g===(await(l.isRTL==null?void 0:l.isRTL(s.floating))?"start":"end")?"left":"right"):(x=p,S=g==="end"?"top":"bottom");const y=b-m.top-m.bottom,v=h-m.left-m.right,_=Ft(b-m[S],y),w=Ft(h-m[x],v),M=!t.middlewareData.shift;let A=_,I=w;if((n=t.middlewareData.shift)!=null&&n.enabled.x&&(I=v),(o=t.middlewareData.shift)!=null&&o.enabled.y&&(A=y),M&&!g){const D=Oe(m.left,0),T=Oe(m.right,0),P=Oe(m.top,0),z=Oe(m.bottom,0);u?I=h-2*(D!==0||T!==0?D+T:Oe(m.left,m.right)):A=b-2*(P!==0||z!==0?P+z:Oe(m.top,m.bottom))}await a({...t,availableWidth:I,availableHeight:A});const F=await l.getDimensions(s.floating);return h!==F.width||b!==F.height?{reset:{rects:!0}}:{}}}};function Yn(){return typeof window<"u"}function Dt(e){return cl(e)?(e.nodeName||"").toLowerCase():"#document"}function Ve(e){var t;return(e==null||(t=e.ownerDocument)==null?void 0:t.defaultView)||window}function ht(e){var t;return(t=(cl(e)?e.ownerDocument:e.document)||window.document)==null?void 0:t.documentElement}function cl(e){return Yn()?e instanceof Node||e instanceof Ve(e).Node:!1}function rt(e){return Yn()?e instanceof Element||e instanceof Ve(e).Element:!1}function it(e){return Yn()?e instanceof HTMLElement||e instanceof Ve(e).HTMLElement:!1}function ai(e){return!Yn()||typeof ShadowRoot>"u"?!1:e instanceof ShadowRoot||e instanceof Ve(e).ShadowRoot}const cd=new Set(["inline","contents"]);function In(e){const{overflow:t,overflowX:n,overflowY:o,display:r}=He(e);return/auto|scroll|overlay|hidden|clip/.test(t+o+n)&&!cd.has(r)}const dd=new Set(["table","td","th"]);function ud(e){return dd.has(Dt(e))}const gd=[":popover-open",":modal"];function fd(e){return gd.some(t=>{try{return e.matches(t)}catch{return!1}})}const pd=["transform","translate","scale","rotate","perspective"],hd=["transform","translate","scale","rotate","perspective","filter"],md=["paint","layout","strict","content"];function qo(e){const t=Xo(),n=rt(e)?He(e):e;return pd.some(o=>n[o]?n[o]!=="none":!1)||(n.containerType?n.containerType!=="normal":!1)||!t&&(n.backdropFilter?n.backdropFilter!=="none":!1)||!t&&(n.filter?n.filter!=="none":!1)||hd.some(o=>(n.willChange||"").includes(o))||md.some(o=>(n.contain||"").includes(o))}function dl(e){let t=Qt(e);for(;it(t)&&!Zn(t);){if(qo(t))return t;if(fd(t))return null;t=Qt(t)}return null}function Xo(){return typeof CSS>"u"||!CSS.supports?!1:CSS.supports("-webkit-backdrop-filter","none")}const vd=new Set(["html","body","#document"]);function Zn(e){return vd.has(Dt(e))}function He(e){return Ve(e).getComputedStyle(e)}function Jn(e){return rt(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.scrollX,scrollTop:e.scrollY}}function Qt(e){if(Dt(e)==="html")return e;const t=e.assignedSlot||e.parentNode||ai(e)&&e.host||ht(e);return ai(t)?t.host:t}function ul(e){const t=Qt(e);return Zn(t)?e.ownerDocument?e.ownerDocument.body:e.body:it(t)&&In(t)?t:ul(t)}function Cn(e,t,n){var o;t===void 0&&(t=[]),n===void 0&&(n=!0);const r=ul(e),i=r===((o=e.ownerDocument)==null?void 0:o.body),l=Ve(r);if(i){const s=xd(l);return t.concat(l,l.visualViewport||[],In(r)?r:[],s&&n?Cn(s):[])}return t.concat(r,Cn(r,[],n))}function xd(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}function gl(e){const t=He(e);let n=parseFloat(t.width)||0,o=parseFloat(t.height)||0;const r=it(e),i=r?e.offsetWidth:n,l=r?e.offsetHeight:o,s=Wn(n)!==i||Wn(o)!==l;return s&&(n=i,o=l),{width:n,height:o,$:s}}function Yo(e){return rt(e)?e:e.contextElement}function Jt(e){const t=Yo(e);if(!it(t))return Et(1);const n=t.getBoundingClientRect(),{width:o,height:r,$:i}=gl(t);let l=(i?Wn(n.width):n.width)/o,s=(i?Wn(n.height):n.height)/r;return(!l||!Number.isFinite(l))&&(l=1),(!s||!Number.isFinite(s))&&(s=1),{x:l,y:s}}const bd=Et(0);function fl(e){const t=Ve(e);return!Xo()||!t.visualViewport?bd:{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}}function yd(e,t,n){return t===void 0&&(t=!1),!n||t&&n!==Ve(e)?!1:t}function Gt(e,t,n,o){t===void 0&&(t=!1),n===void 0&&(n=!1);const r=e.getBoundingClientRect(),i=Yo(e);let l=Et(1);t&&(o?rt(o)&&(l=Jt(o)):l=Jt(e));const s=yd(i,n,o)?fl(i):Et(0);let a=(r.left+s.x)/l.x,c=(r.top+s.y)/l.y,m=r.width/l.x,p=r.height/l.y;if(i){const g=Ve(i),u=o&&rt(o)?Ve(o):o;let h=g.frameElement;for(;h&&o&&u!==g;){const b=Jt(h),S=h.getBoundingClientRect(),x=He(h),y=S.left+(h.clientLeft+parseFloat(x.paddingLeft))*b.x,v=S.top+(h.clientTop+parseFloat(x.paddingTop))*b.y;a*=b.x,c*=b.y,m*=b.x,p*=b.y,a+=y,c+=v,h=Ve(h).frameElement}}return Nn({width:m,height:p,x:a,y:c})}const Sd=[":popover-open",":modal"];function pl(e){let t=!1,n=0,o=0;function r(i){try{t=t||e.matches(i)}catch{}}if(Sd.forEach(i=>{r(i)}),t){const i=dl(e);if(i){const l=i.getBoundingClientRect();n=l.x,o=l.y}}return[t,n,o]}function Cd(e){let{elements:t,rect:n,offsetParent:o,strategy:r}=e;const i=ht(o),[l]=t?pl(t.floating):[!1];if(o===i||l)return n;let s={scrollLeft:0,scrollTop:0},a=Et(1);const c=Et(0),m=it(o);if((m||!m&&r!=="fixed")&&((Dt(o)!=="body"||In(i))&&(s=Jn(o)),it(o))){const p=Gt(o);a=Jt(o),c.x=p.x+o.clientLeft,c.y=p.y+o.clientTop}return{width:n.width*a.x,height:n.height*a.y,x:n.x*a.x-s.scrollLeft*a.x+c.x,y:n.y*a.y-s.scrollTop*a.y+c.y}}function wd(e){return Array.from(e.getClientRects())}function hl(e){return Gt(ht(e)).left+Jn(e).scrollLeft}function _d(e){const t=ht(e),n=Jn(e),o=e.ownerDocument.body,r=Oe(t.scrollWidth,t.clientWidth,o.scrollWidth,o.clientWidth),i=Oe(t.scrollHeight,t.clientHeight,o.scrollHeight,o.clientHeight);let l=-n.scrollLeft+hl(e);const s=-n.scrollTop;return He(o).direction==="rtl"&&(l+=Oe(t.clientWidth,o.clientWidth)-r),{width:r,height:i,x:l,y:s}}function Rd(e,t){const n=Ve(e),o=ht(e),r=n.visualViewport;let i=o.clientWidth,l=o.clientHeight,s=0,a=0;if(r){i=r.width,l=r.height;const c=Xo();(!c||c&&t==="fixed")&&(s=r.offsetLeft,a=r.offsetTop)}return{width:i,height:l,x:s,y:a}}function kd(e,t){const n=Gt(e,!0,t==="fixed"),o=n.top+e.clientTop,r=n.left+e.clientLeft,i=it(e)?Jt(e):Et(1),l=e.clientWidth*i.x,s=e.clientHeight*i.y,a=r*i.x,c=o*i.y;return{width:l,height:s,x:a,y:c}}function ci(e,t,n){let o;if(t==="viewport")o=Rd(e,n);else if(t==="document")o=_d(ht(e));else if(rt(t))o=kd(t,n);else{const r=fl(e);o={...t,x:t.x-r.x,y:t.y-r.y}}return Nn(o)}function ml(e,t){const n=Qt(e);return n===t||!rt(n)||Zn(n)?!1:He(n).position==="fixed"||ml(n,t)}function Id(e,t){const n=t.get(e);if(n)return n;let o=Cn(e,[],!1).filter(s=>rt(s)&&Dt(s)!=="body"),r=null;const i=He(e).position==="fixed";let l=i?Qt(e):e;for(;rt(l)&&!Zn(l);){const s=He(l),a=qo(l);!a&&s.position==="fixed"&&(r=null),(i?!a&&!r:!a&&s.position==="static"&&!!r&&["absolute","fixed"].includes(r.position)||In(l)&&!a&&ml(e,l))?o=o.filter(m=>m!==l):r=s,l=Qt(l)}return t.set(e,o),o}function Pd(e){let{element:t,boundary:n,rootBoundary:o,strategy:r}=e;const l=[...n==="clippingAncestors"?Id(t,this._c):[].concat(n),o],s=l[0],a=l.reduce((c,m)=>{const p=ci(t,m,r);return c.top=Oe(p.top,c.top),c.right=Ft(p.right,c.right),c.bottom=Ft(p.bottom,c.bottom),c.left=Oe(p.left,c.left),c},ci(t,s,r));return{width:a.right-a.left,height:a.bottom-a.top,x:a.left,y:a.top}}function $d(e){const{width:t,height:n}=gl(e);return{width:t,height:n}}function Md(e,t,n,o){const r=it(t),i=ht(t),l=n==="fixed",s=Gt(e,!0,l,t);let a={scrollLeft:0,scrollTop:0};const c=Et(0);if(r||!r&&!l)if((Dt(t)!=="body"||In(i))&&(a=Jn(t)),r){const b=Gt(t,!0,l,t);c.x=b.x+t.clientLeft,c.y=b.y+t.clientTop}else i&&(c.x=hl(i));let m=s.left+a.scrollLeft-c.x,p=s.top+a.scrollTop-c.y;const[g,u,h]=pl(o);return g&&(m+=u,p+=h,r&&(m+=t.clientLeft,p+=t.clientTop)),{x:m,y:p,width:s.width,height:s.height}}function di(e,t){return!it(e)||He(e).position==="fixed"?null:t?t(e):e.offsetParent}function vl(e,t){const n=Ve(e);if(!it(e))return n;let o=di(e,t);for(;o&&ud(o)&&He(o).position==="static";)o=di(o,t);return o&&(Dt(o)==="html"||Dt(o)==="body"&&He(o).position==="static"&&!qo(o))?n:o||dl(e)||n}const Fd=async function(e){const t=this.getOffsetParent||vl,n=this.getDimensions;return{reference:Md(e.reference,await t(e.floating),e.strategy,e.floating),floating:{x:0,y:0,...await n(e.floating)}}};function Ed(e){return He(e).direction==="rtl"}const xl={convertOffsetParentRelativeRectToViewportRelativeRect:Cd,getDocumentElement:ht,getClippingRect:Pd,getOffsetParent:vl,getElementRects:Fd,getClientRects:wd,getDimensions:$d,getScale:Jt,isElement:rt,isRTL:Ed};function Ad(e,t){let n=null,o;const r=ht(e);function i(){var s;clearTimeout(o),(s=n)==null||s.disconnect(),n=null}function l(s,a){s===void 0&&(s=!1),a===void 0&&(a=1),i();const{left:c,top:m,width:p,height:g}=e.getBoundingClientRect();if(s||t(),!p||!g)return;const u=En(m),h=En(r.clientWidth-(c+p)),b=En(r.clientHeight-(m+g)),S=En(c),y={rootMargin:-u+"px "+-h+"px "+-b+"px "+-S+"px",threshold:Oe(0,Ft(1,a))||1};let v=!0;function _(w){const M=w[0].intersectionRatio;if(M!==a){if(!v)return l();M?l(!1,M):o=setTimeout(()=>{l(!1,1e-7)},100)}v=!1}try{n=new IntersectionObserver(_,{...y,root:r.ownerDocument})}catch{n=new IntersectionObserver(_,y)}n.observe(e)}return l(!0),i}function Dd(e,t,n,o){o===void 0&&(o={});const{ancestorScroll:r=!0,ancestorResize:i=!0,elementResize:l=typeof ResizeObserver=="function",layoutShift:s=typeof IntersectionObserver=="function",animationFrame:a=!1}=o,c=Yo(e),m=r||i?[...c?Cn(c):[],...Cn(t)]:[];m.forEach(x=>{r&&x.addEventListener("scroll",n,{passive:!0}),i&&x.addEventListener("resize",n)});const p=c&&s?Ad(c,n):null;let g=-1,u=null;l&&(u=new ResizeObserver(x=>{let[y]=x;y&&y.target===c&&u&&(u.unobserve(t),cancelAnimationFrame(g),g=requestAnimationFrame(()=>{var v;(v=u)==null||v.observe(t)})),n()}),c&&!a&&u.observe(c),u.observe(t));let h,b=a?Gt(e):null;a&&S();function S(){const x=Gt(e);b&&(x.x!==b.x||x.y!==b.y||x.width!==b.width||x.height!==b.height)&&n(),b=x,h=requestAnimationFrame(S)}return n(),()=>{var x;m.forEach(y=>{r&&y.removeEventListener("scroll",n),i&&y.removeEventListener("resize",n)}),p?.(),(x=u)==null||x.disconnect(),u=null,a&&cancelAnimationFrame(h)}}const Td=sd,zd=nd,Ld=ad,Od=od,Vd=td,Bd=(e,t,n)=>{const o=new Map,r={platform:xl,...n},i={...r.platform,_c:o};return ed(e,t,{...r,platform:i})};var Kd=V('<svg display=block viewBox="0 0 30 30"style=transform:scale(1.02)><g><path fill=none d=M23,27.8c1.1,1.2,3.4,2.2,5,2.2h2H0h2c1.7,0,3.9-1,5-2.2l6.6-7.2c0.7-0.8,2-0.8,2.7,0L23,27.8L23,27.8z></path><path stroke=none d=M23,27.8c1.1,1.2,3.4,2.2,5,2.2h2H0h2c1.7,0,3.9-1,5-2.2l6.6-7.2c0.7-0.8,2-0.8,2.7,0L23,27.8L23,27.8z>'),Zo=Ye();function Jo(){const e=Xe(Zo);if(e===void 0)throw new Error("[kobalte]: `usePopperContext` must be used within a `Popper` component");return e}var $o=30,ui=$o/2,Wd={top:180,right:-90,bottom:0,left:90};function Qo(e){const t=Jo(),n=ge({size:$o},e),[o,r]=te(n,["ref","style","size"]),i=()=>t.currentPlacement().split("-")[0],l=Hd(t.contentRef),s=()=>l()?.getPropertyValue("background-color")||"none",a=()=>l()?.getPropertyValue(`border-${i()}-color`)||"none",c=()=>l()?.getPropertyValue(`border-${i()}-width`)||"0px",m=()=>Number.parseInt(c())*2*($o/o.size),p=()=>`rotate(${Wd[i()]} ${ui} ${ui}) translate(0 2)`;return R(Te,U({as:"div",ref(g){var u=Ze(t.setArrowRef,o.ref);typeof u=="function"&&u(g)},"aria-hidden":"true",get style(){return Ko({position:"absolute","font-size":`${o.size}px`,width:"1em",height:"1em","pointer-events":"none",fill:s(),stroke:a(),"stroke-width":m()},o.style)}},r,{get children(){var g=Kd(),u=g.firstChild,h=u.firstChild;return h.nextSibling,O(()=>ds(u,"transform",p())),g}}))}function Hd(e){const[t,n]=H();return Y(()=>{const o=e();o&&n(mc(o).getComputedStyle(o))}),t}function Nd(e){const t=Jo(),[n,o]=te(e,["ref","style"]);return R(Te,U({as:"div",ref(r){var i=Ze(t.setPositionerRef,n.ref);typeof i=="function"&&i(r)},"data-popper-positioner":"",get style(){return Ko({position:"absolute",top:0,left:0,"min-width":"max-content"},n.style)}},o))}function gi(e){const{x:t=0,y:n=0,width:o=0,height:r=0}=e??{};if(typeof DOMRect=="function")return new DOMRect(t,n,o,r);const i={x:t,y:n,width:o,height:r,top:n,right:t+o,bottom:n+r,left:t};return{...i,toJSON:()=>i}}function Gd(e,t){return{contextElement:e,getBoundingClientRect:()=>{const o=t(e);return o?gi(o):e?e.getBoundingClientRect():gi()}}}function jd(e){return/^(?:top|bottom|left|right)(?:-(?:start|end))?$/.test(e)}var Ud={top:"bottom",right:"left",bottom:"top",left:"right"};function qd(e,t){const[n,o]=e.split("-"),r=Ud[n];return o?n==="left"||n==="right"?`${r} ${o==="start"?"top":"bottom"}`:o==="start"?`${r} ${t==="rtl"?"right":"left"}`:`${r} ${t==="rtl"?"left":"right"}`:`${r} center`}function Xd(e){const t=ge({getAnchorRect:g=>g?.getBoundingClientRect(),placement:"bottom",gutter:0,shift:0,flip:!0,slide:!0,overlap:!1,sameWidth:!1,fitViewport:!1,hideWhenDetached:!1,detachedPadding:0,arrowPadding:4,overflowPadding:8},e),[n,o]=H(),[r,i]=H(),[l,s]=H(t.placement),a=()=>Gd(t.anchorRef?.(),t.getAnchorRect),{direction:c}=Lt();async function m(){const g=a(),u=n(),h=r();if(!g||!u)return;const b=(h?.clientHeight||0)/2,S=typeof t.gutter=="number"?t.gutter+b:t.gutter??b;u.style.setProperty("--kb-popper-content-overflow-padding",`${t.overflowPadding}px`),g.getBoundingClientRect();const x=[ld(({placement:M})=>{const A=!!M.split("-")[1];return{mainAxis:S,crossAxis:A?void 0:t.shift,alignmentAxis:t.shift}})];if(t.flip!==!1){const M=typeof t.flip=="string"?t.flip.split(" "):void 0;if(M!==void 0&&!M.every(jd))throw new Error("`flip` expects a spaced-delimited list of placements");x.push(zd({padding:t.overflowPadding,fallbackPlacements:M}))}(t.slide||t.overlap)&&x.push(Td({mainAxis:t.slide,crossAxis:t.overlap,padding:t.overflowPadding})),x.push(Ld({padding:t.overflowPadding,apply({availableWidth:M,availableHeight:A,rects:I}){const F=Math.round(I.reference.width);M=Math.floor(M),A=Math.floor(A),u.style.setProperty("--kb-popper-anchor-width",`${F}px`),u.style.setProperty("--kb-popper-content-available-width",`${M}px`),u.style.setProperty("--kb-popper-content-available-height",`${A}px`),t.sameWidth&&(u.style.width=`${F}px`),t.fitViewport&&(u.style.maxWidth=`${M}px`,u.style.maxHeight=`${A}px`)}})),t.hideWhenDetached&&x.push(Od({padding:t.detachedPadding})),h&&x.push(Vd({element:h,padding:t.arrowPadding}));const y=await Bd(g,u,{placement:t.placement,strategy:"absolute",middleware:x,platform:{...xl,isRTL:()=>c()==="rtl"}});if(s(y.placement),t.onCurrentPlacementChange?.(y.placement),!u)return;u.style.setProperty("--kb-popper-content-transform-origin",qd(y.placement,c()));const v=Math.round(y.x),_=Math.round(y.y);let w;if(t.hideWhenDetached&&(w=y.middlewareData.hide?.referenceHidden?"hidden":"visible"),Object.assign(u.style,{top:"0",left:"0",transform:`translate3d(${v}px, ${_}px, 0)`,visibility:w}),h&&y.middlewareData.arrow){const{x:M,y:A}=y.middlewareData.arrow,I=y.placement.split("-")[0];Object.assign(h.style,{left:M!=null?`${M}px`:"",top:A!=null?`${A}px`:"",[I]:"100%"})}}Y(()=>{const g=a(),u=n();if(!g||!u)return;const h=Dd(g,u,m,{elementResize:typeof ResizeObserver=="function"});ee(h)}),Y(()=>{const g=n(),u=t.contentRef?.();!g||!u||queueMicrotask(()=>{g.style.zIndex=getComputedStyle(u).zIndex})});const p={currentPlacement:l,contentRef:()=>t.contentRef?.(),setPositionerRef:o,setArrowRef:i};return R(Zo.Provider,{value:p,get children(){return t.children}})}var bl=Object.assign(Xd,{Arrow:Qo,Context:Zo,usePopperContext:Jo,Positioner:Nd});function yl(e){let t=e.startIndex??0;const n=e.startLevel??0,o=[],r=a=>{if(a==null)return"";const c=e.getKey??"key",m=Xt(c)?a[c]:c(a);return m!=null?String(m):""},i=a=>{if(a==null)return"";const c=e.getTextValue??"textValue",m=Xt(c)?a[c]:c(a);return m!=null?String(m):""},l=a=>{if(a==null)return!1;const c=e.getDisabled??"disabled";return(Xt(c)?a[c]:c(a))??!1},s=a=>{if(a!=null)return Xt(e.getSectionChildren)?a[e.getSectionChildren]:e.getSectionChildren?.(a)};for(const a of e.dataSource){if(Xt(a)||pc(a)){o.push({type:"item",rawValue:a,key:String(a),textValue:String(a),disabled:l(a),level:n,index:t}),t++;continue}if(s(a)!=null){o.push({type:"section",rawValue:a,key:"",textValue:"",disabled:!1,level:n,index:t}),t++;const c=s(a)??[];if(c.length>0){const m=yl({dataSource:c,getKey:e.getKey,getTextValue:e.getTextValue,getDisabled:e.getDisabled,getSectionChildren:e.getSectionChildren,startIndex:t,startLevel:n+1});o.push(...m),t+=m.length}}else o.push({type:"item",rawValue:a,key:r(a),textValue:i(a),disabled:l(a),level:n,index:t}),t++}return o}function Yd(e,t=[]){return xe(()=>{const n=yl({dataSource:L(e.dataSource),getKey:L(e.getKey),getTextValue:L(e.getTextValue),getDisabled:L(e.getDisabled),getSectionChildren:L(e.getSectionChildren)});for(let o=0;o<t.length;o++)t[o]();return e.factory(n)})}function Qn(e){const[t,n]=H(e.defaultValue?.()),o=xe(()=>e.value?.()!==void 0),r=xe(()=>o()?e.value?.():t());return[r,l=>{Ei(()=>{const s=ac(l,r());return Object.is(s,r())||(o()||n(s),e.onChange?.(s)),s})}]}function Sl(e){const[t,n]=Qn(e);return[()=>t()??!1,n]}function Zd(e){const[t,n]=Qn(e);return[()=>t()??[],n]}var ut=class Cl extends Set{anchorKey;currentKey;constructor(t,n,o){super(t),t instanceof Cl?(this.anchorKey=n||t.anchorKey,this.currentKey=o||t.currentKey):(this.anchorKey=n,this.currentKey=o)}};function Jd(e){const[t,n]=Qn(e);return[()=>t()??new ut,n]}function wl(e){return yc()?e.altKey:e.ctrlKey}function Yt(e){return Xn()?e.metaKey:e.ctrlKey}function fi(e){return new ut(e)}function Qd(e,t){if(e.size!==t.size)return!1;for(const n of e)if(!t.has(n))return!1;return!0}function eu(e){const t=ge({selectionMode:"none",selectionBehavior:"toggle"},e),[n,o]=H(!1),[r,i]=H(),l=xe(()=>{const b=L(t.selectedKeys);return b!=null?fi(b):b}),s=xe(()=>{const b=L(t.defaultSelectedKeys);return b!=null?fi(b):new ut}),[a,c]=Jd({value:l,defaultValue:s,onChange:b=>t.onSelectionChange?.(b)}),[m,p]=H(L(t.selectionBehavior)),g=()=>L(t.selectionMode),u=()=>L(t.disallowEmptySelection)??!1,h=b=>{(L(t.allowDuplicateSelectionEvents)||!Qd(b,a()))&&c(b)};return Y(()=>{const b=a();L(t.selectionBehavior)==="replace"&&m()==="toggle"&&typeof b=="object"&&b.size===0&&p("replace")}),Y(()=>{p(L(t.selectionBehavior)??"toggle")}),{selectionMode:g,disallowEmptySelection:u,selectionBehavior:m,setSelectionBehavior:p,isFocused:n,setFocused:o,focusedKey:r,setFocusedKey:i,selectedKeys:a,setSelectedKeys:h}}function tu(e){const[t,n]=H(""),[o,r]=H(-1);return{typeSelectHandlers:{onKeyDown:l=>{if(L(e.isDisabled))return;const s=L(e.keyboardDelegate),a=L(e.selectionManager);if(!s.getKeyForSearch)return;const c=nu(l.key);if(!c||l.ctrlKey||l.metaKey)return;c===" "&&t().trim().length>0&&(l.preventDefault(),l.stopPropagation());let m=n(g=>g+c),p=s.getKeyForSearch(m,a.focusedKey())??s.getKeyForSearch(m);p==null&&ou(m)&&(m=m[0],p=s.getKeyForSearch(m,a.focusedKey())??s.getKeyForSearch(m)),p!=null&&(a.setFocusedKey(p),e.onTypeSelect?.(p)),clearTimeout(o()),r(window.setTimeout(()=>n(""),500))}}}}function nu(e){return e.length===1||!/^[A-Z]/i.test(e)?e:""}function ou(e){return e.split("").every(t=>t===e[0])}function ru(e,t,n){const r=U({selectOnFocus:()=>L(e.selectionManager).selectionBehavior()==="replace"},e),i=()=>t(),{direction:l}=Lt();let s={top:0,left:0};uc(()=>L(r.isVirtualized)?void 0:i(),"scroll",()=>{const S=i();S&&(s={top:S.scrollTop,left:S.scrollLeft})});const{typeSelectHandlers:a}=tu({isDisabled:()=>L(r.disallowTypeAhead),keyboardDelegate:()=>L(r.keyboardDelegate),selectionManager:()=>L(r.selectionManager)}),c=()=>L(r.orientation)??"vertical",m=S=>{ye(S,a.onKeyDown),S.altKey&&S.key==="Tab"&&S.preventDefault();const x=t();if(!x?.contains(S.target))return;const y=L(r.selectionManager),v=L(r.selectOnFocus),_=I=>{I!=null&&(y.setFocusedKey(I),S.shiftKey&&y.selectionMode()==="multiple"?y.extendSelection(I):v&&!wl(S)&&y.replaceSelection(I))},w=L(r.keyboardDelegate),M=L(r.shouldFocusWrap),A=y.focusedKey();switch(S.key){case(c()==="vertical"?"ArrowDown":"ArrowRight"):{if(w.getKeyBelow){S.preventDefault();let I;A!=null?I=w.getKeyBelow(A):I=w.getFirstKey?.(),I==null&&M&&(I=w.getFirstKey?.(A)),_(I)}break}case(c()==="vertical"?"ArrowUp":"ArrowLeft"):{if(w.getKeyAbove){S.preventDefault();let I;A!=null?I=w.getKeyAbove(A):I=w.getLastKey?.(),I==null&&M&&(I=w.getLastKey?.(A)),_(I)}break}case(c()==="vertical"?"ArrowLeft":"ArrowUp"):{if(w.getKeyLeftOf){S.preventDefault();const I=l()==="rtl";let F;A!=null?F=w.getKeyLeftOf(A):F=I?w.getFirstKey?.():w.getLastKey?.(),_(F)}break}case(c()==="vertical"?"ArrowRight":"ArrowDown"):{if(w.getKeyRightOf){S.preventDefault();const I=l()==="rtl";let F;A!=null?F=w.getKeyRightOf(A):F=I?w.getLastKey?.():w.getFirstKey?.(),_(F)}break}case"Home":if(w.getFirstKey){S.preventDefault();const I=w.getFirstKey(A,Yt(S));I!=null&&(y.setFocusedKey(I),Yt(S)&&S.shiftKey&&y.selectionMode()==="multiple"?y.extendSelection(I):v&&y.replaceSelection(I))}break;case"End":if(w.getLastKey){S.preventDefault();const I=w.getLastKey(A,Yt(S));I!=null&&(y.setFocusedKey(I),Yt(S)&&S.shiftKey&&y.selectionMode()==="multiple"?y.extendSelection(I):v&&y.replaceSelection(I))}break;case"PageDown":if(w.getKeyPageBelow&&A!=null){S.preventDefault();const I=w.getKeyPageBelow(A);_(I)}break;case"PageUp":if(w.getKeyPageAbove&&A!=null){S.preventDefault();const I=w.getKeyPageAbove(A);_(I)}break;case"a":Yt(S)&&y.selectionMode()==="multiple"&&L(r.disallowSelectAll)!==!0&&(S.preventDefault(),y.selectAll());break;case"Escape":S.defaultPrevented||(S.preventDefault(),L(r.disallowEmptySelection)||y.clearSelection());break;case"Tab":if(!L(r.allowsTabNavigation)){if(S.shiftKey)x.focus();else{const I=Mc(x,{tabbable:!0});let F,D;do D=I.lastChild(),D&&(F=D);while(D);F&&!F.contains(document.activeElement)&&ke(F)}break}}},p=S=>{const x=L(r.selectionManager),y=L(r.keyboardDelegate),v=L(r.selectOnFocus);if(x.isFocused()){S.currentTarget.contains(S.target)||x.setFocused(!1);return}if(S.currentTarget.contains(S.target)){if(x.setFocused(!0),x.focusedKey()==null){const _=M=>{M!=null&&(x.setFocusedKey(M),v&&x.replaceSelection(M))},w=S.relatedTarget;w&&S.currentTarget.compareDocumentPosition(w)&Node.DOCUMENT_POSITION_FOLLOWING?_(x.lastSelectedKey()??y.getLastKey?.()):_(x.firstSelectedKey()??y.getFirstKey?.())}else if(!L(r.isVirtualized)){const _=i();if(_){_.scrollTop=s.top,_.scrollLeft=s.left;const w=_.querySelector(`[data-key="${x.focusedKey()}"]`);w&&(ke(w),Ro(_,w))}}}},g=S=>{const x=L(r.selectionManager);S.currentTarget.contains(S.relatedTarget)||x.setFocused(!1)},u=S=>{i()===S.target&&S.preventDefault()},h=()=>{const S=L(r.autoFocus);if(!S)return;const x=L(r.selectionManager),y=L(r.keyboardDelegate);let v;S==="first"&&(v=y.getFirstKey?.()),S==="last"&&(v=y.getLastKey?.());const _=x.selectedKeys();_.size&&(v=_.values().next().value),x.setFocused(!0),x.setFocusedKey(v);const w=t();w&&v==null&&!L(r.shouldUseVirtualFocus)&&ke(w)};return pt(()=>{r.deferAutoFocus?setTimeout(h,0):h()}),Y(kn([i,()=>L(r.isVirtualized),()=>L(r.selectionManager).focusedKey()],S=>{const[x,y,v]=S;if(y)v&&r.scrollToKey?.(v);else if(v&&x){const _=x.querySelector(`[data-key="${v}"]`);_&&Ro(x,_)}})),{tabIndex:xe(()=>{if(!L(r.shouldUseVirtualFocus))return L(r.selectionManager).focusedKey()==null?0:-1}),onKeyDown:m,onMouseDown:u,onFocusIn:p,onFocusOut:g}}function _l(e,t){const n=()=>L(e.selectionManager),o=()=>L(e.key),r=()=>L(e.shouldUseVirtualFocus),i=y=>{n().selectionMode()!=="none"&&(n().selectionMode()==="single"?n().isSelected(o())&&!n().disallowEmptySelection()?n().toggleSelection(o()):n().replaceSelection(o()):y?.shiftKey?n().extendSelection(o()):n().selectionBehavior()==="toggle"||Yt(y)||"pointerType"in y&&y.pointerType==="touch"?n().toggleSelection(o()):n().replaceSelection(o()))},l=()=>n().isSelected(o()),s=()=>L(e.disabled)||n().isDisabled(o()),a=()=>!s()&&n().canSelectItem(o());let c=null;const m=y=>{a()&&(c=y.pointerType,y.pointerType==="mouse"&&y.button===0&&!L(e.shouldSelectOnPressUp)&&i(y))},p=y=>{a()&&y.pointerType==="mouse"&&y.button===0&&L(e.shouldSelectOnPressUp)&&L(e.allowsDifferentPressOrigin)&&i(y)},g=y=>{a()&&(L(e.shouldSelectOnPressUp)&&!L(e.allowsDifferentPressOrigin)||c!=="mouse")&&i(y)},u=y=>{!a()||!["Enter"," "].includes(y.key)||(wl(y)?n().toggleSelection(o()):i(y))},h=y=>{s()&&y.preventDefault()},b=y=>{const v=t();r()||s()||!v||y.target===v&&n().setFocusedKey(o())},S=xe(()=>{if(!(r()||s()))return o()===n().focusedKey()?0:-1}),x=xe(()=>L(e.virtualized)?void 0:o());return Y(kn([t,o,r,()=>n().focusedKey(),()=>n().isFocused()],([y,v,_,w,M])=>{y&&v===w&&M&&!_&&document.activeElement!==y&&(e.focus?e.focus():ke(y))})),{isSelected:l,isDisabled:s,allowsSelection:a,tabIndex:S,dataKey:x,onPointerDown:m,onPointerUp:p,onClick:g,onKeyDown:u,onMouseDown:h,onFocus:b}}var iu=class{collection;state;constructor(e,t){this.collection=e,this.state=t}selectionMode(){return this.state.selectionMode()}disallowEmptySelection(){return this.state.disallowEmptySelection()}selectionBehavior(){return this.state.selectionBehavior()}setSelectionBehavior(e){this.state.setSelectionBehavior(e)}isFocused(){return this.state.isFocused()}setFocused(e){this.state.setFocused(e)}focusedKey(){return this.state.focusedKey()}setFocusedKey(e){(e==null||this.collection().getItem(e))&&this.state.setFocusedKey(e)}selectedKeys(){return this.state.selectedKeys()}isSelected(e){if(this.state.selectionMode()==="none")return!1;const t=this.getKey(e);return t==null?!1:this.state.selectedKeys().has(t)}isEmpty(){return this.state.selectedKeys().size===0}isSelectAll(){if(this.isEmpty())return!1;const e=this.state.selectedKeys();return this.getAllSelectableKeys().every(t=>e.has(t))}firstSelectedKey(){let e;for(const t of this.state.selectedKeys()){const n=this.collection().getItem(t),o=n?.index!=null&&e?.index!=null&&n.index<e.index;(!e||o)&&(e=n)}return e?.key}lastSelectedKey(){let e;for(const t of this.state.selectedKeys()){const n=this.collection().getItem(t),o=n?.index!=null&&e?.index!=null&&n.index>e.index;(!e||o)&&(e=n)}return e?.key}extendSelection(e){if(this.selectionMode()==="none")return;if(this.selectionMode()==="single"){this.replaceSelection(e);return}const t=this.getKey(e);if(t==null)return;const n=this.state.selectedKeys(),o=n.anchorKey||t,r=new ut(n,o,t);for(const i of this.getKeyRange(o,n.currentKey||t))r.delete(i);for(const i of this.getKeyRange(t,o))this.canSelectItem(i)&&r.add(i);this.state.setSelectedKeys(r)}getKeyRange(e,t){const n=this.collection().getItem(e),o=this.collection().getItem(t);return n&&o?n.index!=null&&o.index!=null&&n.index<=o.index?this.getKeyRangeInternal(e,t):this.getKeyRangeInternal(t,e):[]}getKeyRangeInternal(e,t){const n=[];let o=e;for(;o!=null;){const r=this.collection().getItem(o);if(r&&r.type==="item"&&n.push(o),o===t)return n;o=this.collection().getKeyAfter(o)}return[]}getKey(e){const t=this.collection().getItem(e);return t?!t||t.type!=="item"?null:t.key:e}toggleSelection(e){if(this.selectionMode()==="none")return;if(this.selectionMode()==="single"&&!this.isSelected(e)){this.replaceSelection(e);return}const t=this.getKey(e);if(t==null)return;const n=new ut(this.state.selectedKeys());n.has(t)?n.delete(t):this.canSelectItem(t)&&(n.add(t),n.anchorKey=t,n.currentKey=t),!(this.disallowEmptySelection()&&n.size===0)&&this.state.setSelectedKeys(n)}replaceSelection(e){if(this.selectionMode()==="none")return;const t=this.getKey(e);if(t==null)return;const n=this.canSelectItem(t)?new ut([t],t,t):new ut;this.state.setSelectedKeys(n)}setSelectedKeys(e){if(this.selectionMode()==="none")return;const t=new ut;for(const n of e){const o=this.getKey(n);if(o!=null&&(t.add(o),this.selectionMode()==="single"))break}this.state.setSelectedKeys(t)}selectAll(){this.selectionMode()==="multiple"&&this.state.setSelectedKeys(new Set(this.getAllSelectableKeys()))}clearSelection(){const e=this.state.selectedKeys();!this.disallowEmptySelection()&&e.size>0&&this.state.setSelectedKeys(new ut)}toggleSelectAll(){this.isSelectAll()?this.clearSelection():this.selectAll()}select(e,t){this.selectionMode()!=="none"&&(this.selectionMode()==="single"?this.isSelected(e)&&!this.disallowEmptySelection()?this.toggleSelection(e):this.replaceSelection(e):this.selectionBehavior()==="toggle"||t&&t.pointerType==="touch"?this.toggleSelection(e):this.replaceSelection(e))}isSelectionEqual(e){if(e===this.state.selectedKeys())return!0;const t=this.selectedKeys();if(e.size!==t.size)return!1;for(const n of e)if(!t.has(n))return!1;for(const n of t)if(!e.has(n))return!1;return!0}canSelectItem(e){if(this.state.selectionMode()==="none")return!1;const t=this.collection().getItem(e);return t!=null&&!t.disabled}isDisabled(e){const t=this.collection().getItem(e);return!t||t.disabled}getAllSelectableKeys(){const e=[];return(n=>{for(;n!=null;){if(this.canSelectItem(n)){const o=this.collection().getItem(n);if(!o)continue;o.type==="item"&&e.push(n)}n=this.collection().getKeyAfter(n)}})(this.collection().getFirstKey()),e}},pi=class{keyMap=new Map;iterable;firstKey;lastKey;constructor(e){this.iterable=e;for(const o of e)this.keyMap.set(o.key,o);if(this.keyMap.size===0)return;let t,n=0;for(const[o,r]of this.keyMap)t?(t.nextKey=o,r.prevKey=t.key):(this.firstKey=o,r.prevKey=void 0),r.type==="item"&&(r.index=n++),t=r,t.nextKey=void 0;this.lastKey=t.key}*[Symbol.iterator](){yield*this.iterable}getSize(){return this.keyMap.size}getKeys(){return this.keyMap.keys()}getKeyBefore(e){return this.keyMap.get(e)?.prevKey}getKeyAfter(e){return this.keyMap.get(e)?.nextKey}getFirstKey(){return this.firstKey}getLastKey(){return this.lastKey}getItem(e){return this.keyMap.get(e)}at(e){const t=[...this.getKeys()];return this.getItem(t[e])}};function lu(e){const t=eu(e),o=Yd({dataSource:()=>L(e.dataSource),getKey:()=>L(e.getKey),getTextValue:()=>L(e.getTextValue),getDisabled:()=>L(e.getDisabled),getSectionChildren:()=>L(e.getSectionChildren),factory:i=>e.filter?new pi(e.filter(i)):new pi(i)},[()=>e.filter]),r=new iu(o,t);return Mi(()=>{const i=t.focusedKey();i!=null&&!o().getItem(i)&&t.setFocusedKey(void 0)}),{collection:o,selectionManager:()=>r}}var su=class{collection;ref;collator;constructor(e,t,n){this.collection=e,this.ref=t,this.collator=n}getKeyBelow(e){let t=this.collection().getKeyAfter(e);for(;t!=null;){const n=this.collection().getItem(t);if(n&&n.type==="item"&&!n.disabled)return t;t=this.collection().getKeyAfter(t)}}getKeyAbove(e){let t=this.collection().getKeyBefore(e);for(;t!=null;){const n=this.collection().getItem(t);if(n&&n.type==="item"&&!n.disabled)return t;t=this.collection().getKeyBefore(t)}}getFirstKey(){let e=this.collection().getFirstKey();for(;e!=null;){const t=this.collection().getItem(e);if(t&&t.type==="item"&&!t.disabled)return e;e=this.collection().getKeyAfter(e)}}getLastKey(){let e=this.collection().getLastKey();for(;e!=null;){const t=this.collection().getItem(e);if(t&&t.type==="item"&&!t.disabled)return e;e=this.collection().getKeyBefore(e)}}getItem(e){return this.ref?.()?.querySelector(`[data-key="${e}"]`)??null}getKeyPageAbove(e){const t=this.ref?.();let n=this.getItem(e);if(!t||!n)return;const o=Math.max(0,n.offsetTop+n.offsetHeight-t.offsetHeight);let r=e;for(;r&&n&&n.offsetTop>o;)r=this.getKeyAbove(r),n=r!=null?this.getItem(r):null;return r}getKeyPageBelow(e){const t=this.ref?.();let n=this.getItem(e);if(!t||!n)return;const o=Math.min(t.scrollHeight,n.offsetTop-n.offsetHeight+t.offsetHeight);let r=e;for(;r&&n&&n.offsetTop<o;)r=this.getKeyBelow(r),n=r!=null?this.getItem(r):null;return r}getKeyForSearch(e,t){const n=this.collator?.();if(!n)return;let o=t!=null?this.getKeyBelow(t):this.getFirstKey();for(;o!=null;){const r=this.collection().getItem(o);if(r){const i=r.textValue.slice(0,e.length);if(r.textValue&&n.compare(i,e)===0)return o}o=this.getKeyBelow(o)}}};function au(e,t,n){const o=Wc({usage:"search",sensitivity:"base"}),r=xe(()=>{const i=L(e.keyboardDelegate);return i||new su(e.collection,t,o)});return ru({selectionManager:()=>L(e.selectionManager),keyboardDelegate:r,autoFocus:()=>L(e.autoFocus),deferAutoFocus:()=>L(e.deferAutoFocus),shouldFocusWrap:()=>L(e.shouldFocusWrap),disallowEmptySelection:()=>L(e.disallowEmptySelection),selectOnFocus:()=>L(e.selectOnFocus),disallowTypeAhead:()=>L(e.disallowTypeAhead),shouldUseVirtualFocus:()=>L(e.shouldUseVirtualFocus),allowsTabNavigation:()=>L(e.allowsTabNavigation),isVirtualized:()=>L(e.isVirtualized),scrollToKey:i=>L(e.scrollToKey)?.(i),orientation:()=>L(e.orientation)},t)}var Rl=Ye();function kl(){return Xe(Rl)}function cu(){const e=kl();if(e===void 0)throw new Error("[kobalte]: `useDomCollectionContext` must be used within a `DomCollectionProvider` component");return e}function Il(e,t){return!!(t.compareDocumentPosition(e)&Node.DOCUMENT_POSITION_PRECEDING)}function du(e,t){const n=t.ref();if(!n)return-1;let o=e.length;if(!o)return-1;for(;o--;){const r=e[o]?.ref();if(r&&Il(r,n))return o+1}return 0}function uu(e){const t=e.map((o,r)=>[r,o]);let n=!1;return t.sort(([o,r],[i,l])=>{const s=r.ref(),a=l.ref();return s===a||!s||!a?0:Il(s,a)?(o>i&&(n=!0),-1):(o<i&&(n=!0),1)}),n?t.map(([o,r])=>r):e}function Pl(e,t){const n=uu(e);e!==n&&t(n)}function gu(e){const t=e[0],n=e[e.length-1]?.ref();let o=t?.ref()?.parentElement;for(;o;){if(n&&o.contains(n))return o;o=o.parentElement}return ot(o).body}function fu(e,t){Y(()=>{const n=setTimeout(()=>{Pl(e(),t)});ee(()=>clearTimeout(n))})}function pu(e,t){if(typeof IntersectionObserver!="function"){fu(e,t);return}let n=[];Y(()=>{const o=()=>{const l=!!n.length;n=e(),l&&Pl(e(),t)},r=gu(e()),i=new IntersectionObserver(o,{root:r});for(const l of e()){const s=l.ref();s&&i.observe(s)}ee(()=>i.disconnect())})}function hu(e={}){const[t,n]=Zd({value:()=>L(e.items),onChange:i=>e.onItemsChange?.(i)});pu(t,n);const o=i=>(n(l=>{const s=du(l,i);return fc(l,i,s)}),()=>{n(l=>{const s=l.filter(a=>a.ref()!==i.ref());return l.length===s.length?l:s})});return{DomCollectionProvider:i=>R(Rl.Provider,{value:{registerItem:o},get children(){return i.children}})}}function mu(e){const t=cu(),n=ge({shouldRegisterItem:!0},e);Y(()=>{if(!n.shouldRegisterItem)return;const o=t.registerItem(n.getItem());ee(o)})}var Gn="data-kb-top-layer",$l,Mo=!1,ft=[];function wn(e){return ft.findIndex(t=>t.node===e)}function vu(e){return ft[wn(e)]}function xu(e){return ft[ft.length-1].node===e}function Ml(){return ft.filter(e=>e.isPointerBlocking)}function bu(){return[...Ml()].slice(-1)[0]}function er(){return Ml().length>0}function Fl(e){const t=wn(bu()?.node);return wn(e)<t}function yu(e){ft.push(e)}function Su(e){const t=wn(e);t<0||ft.splice(t,1)}function Cu(){for(const{node:e}of ft)e.style.pointerEvents=Fl(e)?"none":"auto"}function wu(e){if(er()&&!Mo){const t=ot(e);$l=document.body.style.pointerEvents,t.body.style.pointerEvents="none",Mo=!0}}function _u(e){if(er())return;const t=ot(e);t.body.style.pointerEvents=$l,t.body.style.length===0&&t.body.removeAttribute("style"),Mo=!1}var Le={layers:ft,isTopMostLayer:xu,hasPointerBlockingLayer:er,isBelowPointerBlockingLayer:Fl,addLayer:yu,removeLayer:Su,indexOf:wn,find:vu,assignPointerEventToLayers:Cu,disableBodyPointerEvents:wu,restoreBodyPointerEvents:_u},go="focusScope.autoFocusOnMount",fo="focusScope.autoFocusOnUnmount",hi={bubbles:!1,cancelable:!0},mi={stack:[],active(){return this.stack[0]},add(e){e!==this.active()&&this.active()?.pause(),this.stack=_o(this.stack,e),this.stack.unshift(e)},remove(e){this.stack=_o(this.stack,e),this.active()?.resume()}};function Ru(e,t){const[n,o]=H(!1),r={pause(){o(!0)},resume(){o(!1)}};let i=null;const l=h=>e.onMountAutoFocus?.(h),s=h=>e.onUnmountAutoFocus?.(h),a=()=>ot(t()),c=()=>{const h=a().createElement("span");return h.setAttribute("data-focus-trap",""),h.tabIndex=0,Object.assign(h.style,Tc),h},m=()=>{const h=t();return h?rl(h,!0).filter(b=>!b.hasAttribute("data-focus-trap")):[]},p=()=>{const h=m();return h.length>0?h[0]:null},g=()=>{const h=m();return h.length>0?h[h.length-1]:null},u=()=>{const h=t();if(!h)return!1;const b=xn(h);return!b||We(h,b)?!1:il(b)};Y(()=>{const h=t();if(!h)return;mi.add(r);const b=xn(h);if(!We(h,b)){const x=new CustomEvent(go,hi);h.addEventListener(go,l),h.dispatchEvent(x),x.defaultPrevented||setTimeout(()=>{ke(p()),xn(h)===b&&ke(h)},0)}ee(()=>{h.removeEventListener(go,l),setTimeout(()=>{const x=new CustomEvent(fo,hi);u()&&x.preventDefault(),h.addEventListener(fo,s),h.dispatchEvent(x),x.defaultPrevented||ke(b??a().body),h.removeEventListener(fo,s),mi.remove(r)},0)})}),Y(()=>{const h=t();if(!h||!L(e.trapFocus)||n())return;const b=x=>{const y=x.target;y?.closest(`[${Gn}]`)||(We(h,y)?i=y:ke(i))},S=x=>{const v=x.relatedTarget??xn(h);v?.closest(`[${Gn}]`)||We(h,v)||ke(i)};a().addEventListener("focusin",b),a().addEventListener("focusout",S),ee(()=>{a().removeEventListener("focusin",b),a().removeEventListener("focusout",S)})}),Y(()=>{const h=t();if(!h||!L(e.trapFocus)||n())return;const b=c();h.insertAdjacentElement("afterbegin",b);const S=c();h.insertAdjacentElement("beforeend",S);function x(v){const _=p(),w=g();v.relatedTarget===_?ke(w):ke(_)}b.addEventListener("focusin",x),S.addEventListener("focusin",x);const y=new MutationObserver(v=>{for(const _ of v)_.previousSibling===S&&(S.remove(),h.insertAdjacentElement("beforeend",S)),_.nextSibling===b&&(b.remove(),h.insertAdjacentElement("afterbegin",b))});y.observe(h,{childList:!0,subtree:!1}),ee(()=>{b.removeEventListener("focusin",x),S.removeEventListener("focusin",x),b.remove(),S.remove(),y.disconnect()})})}var ku="data-live-announcer";function Iu(e){Y(()=>{L(e.isDisabled)||ee(Pu(L(e.targets),L(e.root)))})}var vn=new WeakMap,Ke=[];function Pu(e,t=document.body){const n=new Set(e),o=new Set,r=a=>{for(const g of a.querySelectorAll(`[${ku}], [${Gn}]`))n.add(g);const c=g=>{if(n.has(g)||g.parentElement&&o.has(g.parentElement)&&g.parentElement.getAttribute("role")!=="row")return NodeFilter.FILTER_REJECT;for(const u of n)if(g.contains(u))return NodeFilter.FILTER_SKIP;return NodeFilter.FILTER_ACCEPT},m=document.createTreeWalker(a,NodeFilter.SHOW_ELEMENT,{acceptNode:c}),p=c(a);if(p===NodeFilter.FILTER_ACCEPT&&i(a),p!==NodeFilter.FILTER_REJECT){let g=m.nextNode();for(;g!=null;)i(g),g=m.nextNode()}},i=a=>{const c=vn.get(a)??0;a.getAttribute("aria-hidden")==="true"&&c===0||(c===0&&a.setAttribute("aria-hidden","true"),o.add(a),vn.set(a,c+1))};Ke.length&&Ke[Ke.length-1].disconnect(),r(t);const l=new MutationObserver(a=>{for(const c of a)if(!(c.type!=="childList"||c.addedNodes.length===0)&&![...n,...o].some(m=>m.contains(c.target))){for(const m of c.removedNodes)m instanceof Element&&(n.delete(m),o.delete(m));for(const m of c.addedNodes)(m instanceof HTMLElement||m instanceof SVGElement)&&(m.dataset.liveAnnouncer==="true"||m.dataset.reactAriaTopLayer==="true")?n.add(m):m instanceof Element&&r(m)}});l.observe(t,{childList:!0,subtree:!0});const s={observe(){l.observe(t,{childList:!0,subtree:!0})},disconnect(){l.disconnect()}};return Ke.push(s),()=>{l.disconnect();for(const a of o){const c=vn.get(a);if(c==null)return;c===1?(a.removeAttribute("aria-hidden"),vn.delete(a)):vn.set(a,c-1)}s===Ke[Ke.length-1]?(Ke.pop(),Ke.length&&Ke[Ke.length-1].observe()):Ke.splice(Ke.indexOf(s),1)}}var vi="interactOutside.pointerDownOutside",xi="interactOutside.focusOutside";function $u(e,t){let n,o=Ec;const r=()=>ot(t()),i=p=>e.onPointerDownOutside?.(p),l=p=>e.onFocusOutside?.(p),s=p=>e.onInteractOutside?.(p),a=p=>{const g=p.target;return!(g instanceof Element)||g.closest(`[${Gn}]`)||!We(r(),g)||We(t(),g)?!1:!e.shouldExcludeElement?.(g)},c=p=>{function g(){const u=t(),h=p.target;if(!u||!h||!a(p))return;const b=be([i,s]);h.addEventListener(vi,b,{once:!0});const S=new CustomEvent(vi,{bubbles:!1,cancelable:!0,detail:{originalEvent:p,isContextMenu:p.button===2||Sc(p)&&p.button===0}});h.dispatchEvent(S)}p.pointerType==="touch"?(r().removeEventListener("click",g),o=g,r().addEventListener("click",g,{once:!0})):g()},m=p=>{const g=t(),u=p.target;if(!g||!u||!a(p))return;const h=be([l,s]);u.addEventListener(xi,h,{once:!0});const b=new CustomEvent(xi,{bubbles:!1,cancelable:!0,detail:{originalEvent:p,isContextMenu:!1}});u.dispatchEvent(b)};Y(()=>{L(e.isDisabled)||(n=window.setTimeout(()=>{r().addEventListener("pointerdown",c,!0)},0),r().addEventListener("focusin",m,!0),ee(()=>{window.clearTimeout(n),r().removeEventListener("click",o),r().removeEventListener("pointerdown",c,!0),r().removeEventListener("focusin",m,!0)}))})}function Mu(e){const t=n=>{n.key===nl.Escape&&e.onEscapeKeyDown?.(n)};Y(()=>{if(L(e.isDisabled))return;const n=e.ownerDocument?.()??ot();n.addEventListener("keydown",t),ee(()=>{n.removeEventListener("keydown",t)})})}var El=Ye();function Fu(){return Xe(El)}function Eu(e){let t;const n=Fu(),[o,r]=te(e,["ref","disableOutsidePointerEvents","excludedElements","onEscapeKeyDown","onPointerDownOutside","onFocusOutside","onInteractOutside","onDismiss","bypassTopMostLayerCheck"]),i=new Set([]),l=p=>{i.add(p);const g=n?.registerNestedLayer(p);return()=>{i.delete(p),g?.()}};$u({shouldExcludeElement:p=>t?o.excludedElements?.some(g=>We(g(),p))||[...i].some(g=>We(g,p)):!1,onPointerDownOutside:p=>{!t||Le.isBelowPointerBlockingLayer(t)||!o.bypassTopMostLayerCheck&&!Le.isTopMostLayer(t)||(o.onPointerDownOutside?.(p),o.onInteractOutside?.(p),p.defaultPrevented||o.onDismiss?.())},onFocusOutside:p=>{o.onFocusOutside?.(p),o.onInteractOutside?.(p),p.defaultPrevented||o.onDismiss?.()}},()=>t),Mu({ownerDocument:()=>ot(t),onEscapeKeyDown:p=>{!t||!Le.isTopMostLayer(t)||(o.onEscapeKeyDown?.(p),!p.defaultPrevented&&o.onDismiss&&(p.preventDefault(),o.onDismiss()))}}),pt(()=>{if(!t)return;Le.addLayer({node:t,isPointerBlocking:o.disableOutsidePointerEvents,dismiss:o.onDismiss});const p=n?.registerNestedLayer(t);Le.assignPointerEventToLayers(),Le.disableBodyPointerEvents(t),ee(()=>{t&&(Le.removeLayer(t),p?.(),Le.assignPointerEventToLayers(),Le.restoreBodyPointerEvents(t))})}),Y(kn([()=>t,()=>o.disableOutsidePointerEvents],([p,g])=>{if(!p)return;const u=Le.find(p);u&&u.isPointerBlocking!==g&&(u.isPointerBlocking=g,Le.assignPointerEventToLayers()),g&&Le.disableBodyPointerEvents(p),ee(()=>{Le.restoreBodyPointerEvents(p)})},{defer:!0}));const m={registerNestedLayer:l};return R(El.Provider,{value:m,get children(){return R(Te,U({as:"div",ref(p){var g=Ze(u=>t=u,o.ref);typeof g=="function"&&g(p)}},r))}})}function Al(e={}){const[t,n]=Sl({value:()=>L(e.open),defaultValue:()=>!!L(e.defaultOpen),onChange:l=>e.onOpenChange?.(l)}),o=()=>{n(!0)},r=()=>{n(!1)};return{isOpen:t,setIsOpen:n,open:o,close:r,toggle:()=>{t()?r():o()}}}function tr(e,t){const[n,o]=H(bi(t?.()));return Y(()=>{o(e()?.tagName.toLowerCase()||bi(t?.()))}),n}function bi(e){return Xt(e)?e:void 0}var Au=Object.defineProperty,nr=(e,t)=>{for(var n in t)Au(e,n,{get:t[n],enumerable:!0})},Du={};nr(Du,{Button:()=>Lu,Root:()=>or});var Tu=["button","color","file","image","reset","submit"];function zu(e){const t=e.tagName.toLowerCase();return t==="button"?!0:t==="input"&&e.type?Tu.indexOf(e.type)!==-1:!1}function or(e){let t;const n=ge({type:"button"},e),[o,r]=te(n,["ref","type","disabled"]),i=tr(()=>t,()=>"button"),l=xe(()=>{const c=i();return c==null?!1:zu({tagName:c,type:o.type})}),s=xe(()=>i()==="input"),a=xe(()=>i()==="a"&&t?.getAttribute("href")!=null);return R(Te,U({as:"button",ref(c){var m=Ze(p=>t=p,o.ref);typeof m=="function"&&m(c)},get type(){return Q(()=>!!(l()||s()))()?o.type:void 0},get role(){return!l()&&!a()?"button":void 0},get tabIndex(){return!l()&&!a()&&!o.disabled?0:void 0},get disabled(){return Q(()=>!!(l()||s()))()?o.disabled:void 0},get"aria-disabled"(){return!l()&&!s()&&o.disabled?!0:void 0},get"data-disabled"(){return o.disabled?"":void 0}},r))}var Lu=or;function Ou(e={}){const[t,n]=Sl({value:()=>L(e.isSelected),defaultValue:()=>!!L(e.defaultIsSelected),onChange:i=>e.onSelectedChange?.(i)});return{isSelected:t,setIsSelected:i=>{!L(e.isReadOnly)&&!L(e.isDisabled)&&n(i)},toggle:()=>{!L(e.isReadOnly)&&!L(e.isDisabled)&&n(!t())}}}function _n(e){return t=>(e(t),()=>e(void 0))}var Re=e=>typeof e=="function"?e():e,Fo=(e,t)=>{if(e.contains(t))return!0;let n=t;for(;n;){if(n===e)return!0;n=n._$host??n.parentElement}return!1},An=new Map,Vu=e=>{Y(()=>{const t=Re(e.style)??{},n=Re(e.properties)??[],o={};for(const i in t)o[i]=e.element.style[i];const r=An.get(e.key);r?r.activeCount++:An.set(e.key,{activeCount:1,originalStyles:o,properties:n.map(i=>i.key)}),Object.assign(e.element.style,e.style);for(const i of n)e.element.style.setProperty(i.key,i.value);ee(()=>{const i=An.get(e.key);if(i){if(i.activeCount!==1){i.activeCount--;return}An.delete(e.key);for(const[l,s]of Object.entries(i.originalStyles))e.element.style[l]=s;for(const l of i.properties)e.element.style.removeProperty(l);e.element.style.length===0&&e.element.removeAttribute("style"),e.cleanup?.()}})})},yi=Vu,Bu=(e,t)=>{switch(t){case"x":return[e.clientWidth,e.scrollLeft,e.scrollWidth];case"y":return[e.clientHeight,e.scrollTop,e.scrollHeight]}},Ku=(e,t)=>{const n=getComputedStyle(e),o=t==="x"?n.overflowX:n.overflowY;return o==="auto"||o==="scroll"||e.tagName==="HTML"&&o==="visible"},Wu=(e,t,n)=>{const o=t==="x"&&window.getComputedStyle(e).direction==="rtl"?-1:1;let r=e,i=0,l=0,s=!1;do{const[a,c,m]=Bu(r,t),p=m-a-o*c;(c!==0||p!==0)&&Ku(r,t)&&(i+=p,l+=c),r===(n??document.documentElement)?s=!0:r=r._$host??r.parentElement}while(r&&!s);return[i,l]},[Si,Ci]=H([]),Hu=e=>Si().indexOf(e)===Si().length-1,Nu=e=>{const t=U({element:null,enabled:!0,hideScrollbar:!0,preventScrollbarShift:!0,preventScrollbarShiftMode:"padding",restoreScrollPosition:!0,allowPinchZoom:!1},e),n=zt();let o=[0,0],r=null,i=null;Y(()=>{Re(t.enabled)&&(Ci(c=>[...c,n]),ee(()=>{Ci(c=>c.filter(m=>m!==n))}))}),Y(()=>{if(!Re(t.enabled)||!Re(t.hideScrollbar))return;const{body:c}=document,m=window.innerWidth-c.offsetWidth;if(Re(t.preventScrollbarShift)){const p={overflow:"hidden"},g=[];m>0&&(Re(t.preventScrollbarShiftMode)==="padding"?p.paddingRight=`calc(${window.getComputedStyle(c).paddingRight} + ${m}px)`:p.marginRight=`calc(${window.getComputedStyle(c).marginRight} + ${m}px)`,g.push({key:"--scrollbar-width",value:`${m}px`}));const u=window.scrollY,h=window.scrollX;yi({key:"prevent-scroll",element:c,style:p,properties:g,cleanup:()=>{Re(t.restoreScrollPosition)&&m>0&&window.scrollTo(h,u)}})}else yi({key:"prevent-scroll",element:c,style:{overflow:"hidden"}})}),Y(()=>{!Hu(n)||!Re(t.enabled)||(document.addEventListener("wheel",s,{passive:!1}),document.addEventListener("touchstart",l,{passive:!1}),document.addEventListener("touchmove",a,{passive:!1}),ee(()=>{document.removeEventListener("wheel",s),document.removeEventListener("touchstart",l),document.removeEventListener("touchmove",a)}))});const l=c=>{o=wi(c),r=null,i=null},s=c=>{const m=c.target,p=Re(t.element),g=Gu(c),u=Math.abs(g[0])>Math.abs(g[1])?"x":"y",h=u==="x"?g[0]:g[1],b=_i(m,u,h,p);let S;p&&Fo(p,m)?S=!b:S=!0,S&&c.cancelable&&c.preventDefault()},a=c=>{const m=Re(t.element),p=c.target;let g;if(c.touches.length===2)g=!Re(t.allowPinchZoom);else{if(r==null||i===null){const u=wi(c).map((b,S)=>o[S]-b),h=Math.abs(u[0])>Math.abs(u[1])?"x":"y";r=h,i=h==="x"?u[0]:u[1]}if(p.type==="range")g=!1;else{const u=_i(p,r,i,m);m&&Fo(m,p)?g=!u:g=!0}}g&&c.cancelable&&c.preventDefault()}},Gu=e=>[e.deltaX,e.deltaY],wi=e=>e.changedTouches[0]?[e.changedTouches[0].clientX,e.changedTouches[0].clientY]:[0,0],_i=(e,t,n,o)=>{const r=o!==null&&Fo(o,e),[i,l]=Wu(e,t,r?o:void 0);return!(n>0&&Math.abs(i)<=1||n<0&&Math.abs(l)<1)},ju=Nu,Uu=ju,qu=e=>{const t=xe(()=>{const l=Re(e.element);if(l)return getComputedStyle(l)}),n=()=>t()?.animationName??"none",[o,r]=H(Re(e.show)?"present":"hidden");let i="none";return Y(l=>{const s=Re(e.show);return Ei(()=>{if(l===s)return s;const a=i,c=n();s?r("present"):c==="none"||t()?.display==="none"?r("hidden"):r(l===!0&&a!==c?"hiding":"hidden")}),s}),Y(()=>{const l=Re(e.element);if(!l)return;const s=c=>{c.target===l&&(i=n())},a=c=>{const p=n().includes(c.animationName);c.target===l&&p&&o()==="hiding"&&r("hidden")};l.addEventListener("animationstart",s),l.addEventListener("animationcancel",a),l.addEventListener("animationend",a),ee(()=>{l.removeEventListener("animationstart",s),l.removeEventListener("animationcancel",a),l.removeEventListener("animationend",a)})}),{present:()=>o()==="present"||o()==="hiding",state:o,setState:r}},Xu=qu,Yu=Xu,Zu=Ye();function eo(){return Xe(Zu)}var Ju=Ye();function Dl(){return Xe(Ju)}var Tl=Ye();function zl(){return Xe(Tl)}function mt(){const e=zl();if(e===void 0)throw new Error("[kobalte]: `useMenuContext` must be used within a `Menu` component");return e}var Ll=Ye();function rr(){const e=Xe(Ll);if(e===void 0)throw new Error("[kobalte]: `useMenuItemContext` must be used within a `Menu.Item` component");return e}var Ol=Ye();function st(){const e=Xe(Ol);if(e===void 0)throw new Error("[kobalte]: `useMenuRootContext` must be used within a `MenuRoot` component");return e}function ir(e){let t;const n=st(),o=mt(),r=ge({id:n.generateId(`item-${zt()}`)},e),[i,l]=te(r,["ref","textValue","disabled","closeOnSelect","checked","indeterminate","onSelect","onPointerMove","onPointerLeave","onPointerDown","onPointerUp","onClick","onKeyDown","onMouseDown","onFocus"]),[s,a]=H(),[c,m]=H(),[p,g]=H(),u=()=>o.listState().selectionManager(),h=()=>l.id,b=()=>u().focusedKey()===h(),S=()=>{i.onSelect?.(),i.closeOnSelect&&setTimeout(()=>{o.close(!0)})};mu({getItem:()=>({ref:()=>t,type:"item",key:h(),textValue:i.textValue??p()?.textContent??t?.textContent??"",disabled:i.disabled??!1})});const x=_l({key:h,selectionManager:u,shouldSelectOnPressUp:!0,allowsDifferentPressOrigin:!0,disabled:()=>i.disabled},()=>t),y=F=>{ye(F,i.onPointerMove),F.pointerType==="mouse"&&(i.disabled?o.onItemLeave(F):(o.onItemEnter(F),F.defaultPrevented||(ke(F.currentTarget),o.listState().selectionManager().setFocused(!0),o.listState().selectionManager().setFocusedKey(h()))))},v=F=>{ye(F,i.onPointerLeave),F.pointerType==="mouse"&&o.onItemLeave(F)},_=F=>{ye(F,i.onPointerUp),!i.disabled&&F.button===0&&S()},w=F=>{if(ye(F,i.onKeyDown),!F.repeat&&!i.disabled)switch(F.key){case"Enter":case" ":S();break}},M=xe(()=>{if(i.indeterminate)return"mixed";if(i.checked!=null)return i.checked}),A=xe(()=>({"data-indeterminate":i.indeterminate?"":void 0,"data-checked":i.checked&&!i.indeterminate?"":void 0,"data-disabled":i.disabled?"":void 0,"data-highlighted":b()?"":void 0})),I={isChecked:()=>i.checked,dataset:A,setLabelRef:g,generateId:Wo(()=>l.id),registerLabel:_n(a),registerDescription:_n(m)};return R(Ll.Provider,{value:I,get children(){return R(Te,U({as:"div",ref(F){var D=Ze(T=>t=T,i.ref);typeof D=="function"&&D(F)},get tabIndex(){return x.tabIndex()},get"aria-checked"(){return M()},get"aria-disabled"(){return i.disabled},get"aria-labelledby"(){return s()},get"aria-describedby"(){return c()},get"data-key"(){return x.dataKey()},get onPointerDown(){return be([i.onPointerDown,x.onPointerDown])},get onPointerUp(){return be([_,x.onPointerUp])},get onClick(){return be([i.onClick,x.onClick])},get onKeyDown(){return be([w,x.onKeyDown])},get onMouseDown(){return be([i.onMouseDown,x.onMouseDown])},get onFocus(){return be([i.onFocus,x.onFocus])},onPointerMove:y,onPointerLeave:v},A,l))}})}function Vl(e){const t=ge({closeOnSelect:!1},e),[n,o]=te(t,["checked","defaultChecked","onChange","onSelect"]),r=Ou({isSelected:()=>n.checked,defaultIsSelected:()=>n.defaultChecked,onSelectedChange:l=>n.onChange?.(l),isDisabled:()=>o.disabled});return R(ir,U({role:"menuitemcheckbox",get checked(){return r.isSelected()},onSelect:()=>{n.onSelect?.(),r.toggle()}},o))}var Rn={next:(e,t)=>e==="ltr"?t==="horizontal"?"ArrowRight":"ArrowDown":t==="horizontal"?"ArrowLeft":"ArrowUp",previous:(e,t)=>Rn.next(e==="ltr"?"rtl":"ltr",t)},Ri={first:e=>e==="horizontal"?"ArrowDown":"ArrowRight",last:e=>e==="horizontal"?"ArrowUp":"ArrowLeft"};function Bl(e){const t=st(),n=mt(),o=eo(),{direction:r}=Lt(),i=ge({id:t.generateId("trigger")},e),[l,s]=te(i,["ref","id","disabled","onPointerDown","onClick","onKeyDown","onMouseOver","onFocus"]);let a=()=>t.value();o!==void 0&&(a=()=>t.value()??l.id,o.lastValue()===void 0&&o.setLastValue(a));const c=tr(()=>n.triggerRef(),()=>"button"),m=xe(()=>c()==="a"&&n.triggerRef()?.getAttribute("href")!=null);Y(kn(()=>o?.value(),x=>{m()&&x===a()&&n.triggerRef()?.focus()}));const p=()=>{o!==void 0?n.isOpen()?o.value()===a()&&o.closeMenu():(o.autoFocusMenu()||o.setAutoFocusMenu(!0),n.open(!1)):n.toggle(!0)},g=x=>{ye(x,l.onPointerDown),x.currentTarget.dataset.pointerType=x.pointerType,!l.disabled&&x.pointerType!=="touch"&&x.button===0&&p()},u=x=>{ye(x,l.onClick),l.disabled||x.currentTarget.dataset.pointerType==="touch"&&p()},h=x=>{if(ye(x,l.onKeyDown),!l.disabled){if(m())switch(x.key){case"Enter":case" ":return}switch(x.key){case"Enter":case" ":case Ri.first(t.orientation()):x.stopPropagation(),x.preventDefault(),Dc(x.currentTarget),n.open("first"),o?.setAutoFocusMenu(!0),o?.setValue(a);break;case Ri.last(t.orientation()):x.stopPropagation(),x.preventDefault(),n.open("last");break;case Rn.next(r(),t.orientation()):if(o===void 0)break;x.stopPropagation(),x.preventDefault(),o.nextMenu();break;case Rn.previous(r(),t.orientation()):if(o===void 0)break;x.stopPropagation(),x.preventDefault(),o.previousMenu();break}}},b=x=>{ye(x,l.onMouseOver),n.triggerRef()?.dataset.pointerType!=="touch"&&!l.disabled&&o!==void 0&&o.value()!==void 0&&o.setValue(a)},S=x=>{ye(x,l.onFocus),o!==void 0&&x.currentTarget.dataset.pointerType!=="touch"&&o.setValue(a)};return Y(()=>ee(n.registerTriggerId(l.id))),R(or,U({ref(x){var y=Ze(n.setTriggerRef,l.ref);typeof y=="function"&&y(x)},get"data-kb-menu-value-trigger"(){return t.value()},get id(){return l.id},get disabled(){return l.disabled},"aria-haspopup":"true",get"aria-expanded"(){return n.isOpen()},get"aria-controls"(){return Q(()=>!!n.isOpen())()?n.contentId():void 0},get"data-highlighted"(){return a()!==void 0&&o?.value()===a()?!0:void 0},get tabIndex(){return o!==void 0?o.value()===a()||o.lastValue()===a()?0:-1:void 0},onPointerDown:g,onMouseOver:b,onClick:u,onKeyDown:h,onFocus:S,role:o!==void 0?"menuitem":void 0},()=>n.dataset(),s))}function Kl(e){let t;const n=st(),o=mt(),r=eo(),i=Dl(),{direction:l}=Lt(),s=ge({id:n.generateId(`content-${zt()}`)},e),[a,c]=te(s,["ref","id","style","onOpenAutoFocus","onCloseAutoFocus","onEscapeKeyDown","onFocusOutside","onPointerEnter","onPointerMove","onKeyDown","onMouseDown","onFocusIn","onFocusOut"]);let m=0;const p=()=>o.parentMenuContext()==null&&r===void 0&&n.isModal(),g=au({selectionManager:o.listState().selectionManager,collection:o.listState().collection,autoFocus:o.autoFocus,deferAutoFocus:!0,shouldFocusWrap:!0,disallowTypeAhead:()=>!o.listState().selectionManager().isFocused(),orientation:()=>n.orientation()==="horizontal"?"vertical":"horizontal"},()=>t);Ru({trapFocus:()=>p()&&o.isOpen(),onMountAutoFocus:v=>{r===void 0&&a.onOpenAutoFocus?.(v)},onUnmountAutoFocus:a.onCloseAutoFocus},()=>t);const u=v=>{if(We(v.currentTarget,v.target)&&(v.key==="Tab"&&o.isOpen()&&v.preventDefault(),r!==void 0&&v.currentTarget.getAttribute("aria-haspopup")!=="true"))switch(v.key){case Rn.next(l(),n.orientation()):v.stopPropagation(),v.preventDefault(),o.close(!0),r.setAutoFocusMenu(!0),r.nextMenu();break;case Rn.previous(l(),n.orientation()):if(v.currentTarget.hasAttribute("data-closed"))break;v.stopPropagation(),v.preventDefault(),o.close(!0),r.setAutoFocusMenu(!0),r.previousMenu();break}},h=v=>{a.onEscapeKeyDown?.(v),r?.setAutoFocusMenu(!1),o.close(!0)},b=v=>{a.onFocusOutside?.(v),n.isModal()&&v.preventDefault()},S=v=>{ye(v,a.onPointerEnter),o.isOpen()&&(o.parentMenuContext()?.listState().selectionManager().setFocused(!1),o.parentMenuContext()?.listState().selectionManager().setFocusedKey(void 0))},x=v=>{if(ye(v,a.onPointerMove),v.pointerType!=="mouse")return;const _=v.target,w=m!==v.clientX;We(v.currentTarget,_)&&w&&(o.setPointerDir(v.clientX>m?"right":"left"),m=v.clientX)};Y(()=>ee(o.registerContentId(a.id))),ee(()=>o.setContentRef(void 0));const y={ref:Ze(v=>{o.setContentRef(v),t=v},a.ref),role:"menu",get id(){return a.id},get tabIndex(){return g.tabIndex()},get"aria-labelledby"(){return o.triggerId()},onKeyDown:be([a.onKeyDown,g.onKeyDown,u]),onMouseDown:be([a.onMouseDown,g.onMouseDown]),onFocusIn:be([a.onFocusIn,g.onFocusIn]),onFocusOut:be([a.onFocusOut,g.onFocusOut]),onPointerEnter:S,onPointerMove:x,get"data-orientation"(){return n.orientation()}};return R(ae,{get when(){return o.contentPresent()},get children(){return R(ae,{get when(){return i===void 0||o.parentMenuContext()!=null},get fallback(){return R(Te,U({as:"div"},()=>o.dataset(),y,c))},get children(){return R(bl.Positioner,{get children(){return R(Eu,U({get disableOutsidePointerEvents(){return Q(()=>!!p())()&&o.isOpen()},get excludedElements(){return[o.triggerRef]},bypassTopMostLayerCheck:!0,get style(){return Ko({"--kb-menu-content-transform-origin":"var(--kb-popper-content-transform-origin)",position:"relative"},a.style)},onEscapeKeyDown:h,onFocusOutside:b,get onDismiss(){return o.close}},()=>o.dataset(),y,c))}})}})}})}function Qu(e){let t;const n=st(),o=mt(),[r,i]=te(e,["ref"]);return Uu({element:()=>t??null,enabled:()=>o.contentPresent()&&n.preventScroll()}),R(Kl,U({ref(l){var s=Ze(a=>{t=a},r.ref);typeof s=="function"&&s(l)}},i))}var Wl=Ye();function eg(){const e=Xe(Wl);if(e===void 0)throw new Error("[kobalte]: `useMenuGroupContext` must be used within a `Menu.Group` component");return e}function lr(e){const t=st(),n=ge({id:t.generateId(`group-${zt()}`)},e),[o,r]=H(),i={generateId:Wo(()=>n.id),registerLabelId:_n(r)};return R(Wl.Provider,{value:i,get children(){return R(Te,U({as:"div",role:"group",get"aria-labelledby"(){return o()}},n))}})}function Hl(e){const t=eg(),n=ge({id:t.generateId("label")},e),[o,r]=te(n,["id"]);return Y(()=>ee(t.registerLabelId(o.id))),R(Te,U({as:"span",get id(){return o.id},"aria-hidden":"true"},r))}function Nl(e){const t=mt(),n=ge({children:"▼"},e);return R(Te,U({as:"span","aria-hidden":"true"},()=>t.dataset(),n))}function Gl(e){return R(ir,U({role:"menuitem",closeOnSelect:!0},e))}function jl(e){const t=rr(),n=ge({id:t.generateId("description")},e),[o,r]=te(n,["id"]);return Y(()=>ee(t.registerDescription(o.id))),R(Te,U({as:"div",get id(){return o.id}},()=>t.dataset(),r))}function Ul(e){const t=rr(),n=ge({id:t.generateId("indicator")},e),[o,r]=te(n,["forceMount"]);return R(ae,{get when(){return o.forceMount||t.isChecked()},get children(){return R(Te,U({as:"div"},()=>t.dataset(),r))}})}function ql(e){const t=rr(),n=ge({id:t.generateId("label")},e),[o,r]=te(n,["ref","id"]);return Y(()=>ee(t.registerLabel(o.id))),R(Te,U({as:"div",ref(i){var l=Ze(t.setLabelRef,o.ref);typeof l=="function"&&l(i)},get id(){return o.id}},()=>t.dataset(),r))}function Xl(e){const t=mt();return R(ae,{get when(){return t.contentPresent()},get children(){return R(Do,e)}})}var Yl=Ye();function tg(){const e=Xe(Yl);if(e===void 0)throw new Error("[kobalte]: `useMenuRadioGroupContext` must be used within a `Menu.RadioGroup` component");return e}function Zl(e){const n=st().generateId(`radiogroup-${zt()}`),o=ge({id:n},e),[r,i]=te(o,["value","defaultValue","onChange","disabled"]),[l,s]=Qn({value:()=>r.value,defaultValue:()=>r.defaultValue,onChange:c=>r.onChange?.(c)}),a={isDisabled:()=>r.disabled,isSelectedValue:c=>c===l(),setSelectedValue:c=>s(c)};return R(Yl.Provider,{value:a,get children(){return R(lr,i)}})}function Jl(e){const t=tg(),n=ge({closeOnSelect:!1},e),[o,r]=te(n,["value","onSelect"]);return R(ir,U({role:"menuitemradio",get checked(){return t.isSelectedValue(o.value)},onSelect:()=>{o.onSelect?.(),t.setSelectedValue(o.value)}},r))}function ng(e,t,n){const o=e.split("-")[0],r=n.getBoundingClientRect(),i=[],l=t.clientX,s=t.clientY;switch(o){case"top":i.push([l,s+5]),i.push([r.left,r.bottom]),i.push([r.left,r.top]),i.push([r.right,r.top]),i.push([r.right,r.bottom]);break;case"right":i.push([l-5,s]),i.push([r.left,r.top]),i.push([r.right,r.top]),i.push([r.right,r.bottom]),i.push([r.left,r.bottom]);break;case"bottom":i.push([l,s-5]),i.push([r.right,r.top]),i.push([r.right,r.bottom]),i.push([r.left,r.bottom]),i.push([r.left,r.top]);break;case"left":i.push([l+5,s]),i.push([r.right,r.bottom]),i.push([r.left,r.bottom]),i.push([r.left,r.top]),i.push([r.right,r.top]);break}return i}function og(e,t){return t?Ac([e.clientX,e.clientY],t):!1}function Ql(e){const t=st(),n=kl(),o=zl(),r=eo(),i=Dl(),l=ge({placement:t.orientation()==="horizontal"?"bottom-start":"right-start"},e),[s,a]=te(l,["open","defaultOpen","onOpenChange"]);let c=0,m=null,p="right";const[g,u]=H(),[h,b]=H(),[S,x]=H(),[y,v]=H(),[_,w]=H(!0),[M,A]=H(a.placement),[I,F]=H([]),[D,T]=H([]),{DomCollectionProvider:P}=hu({items:D,onItemsChange:T}),z=Al({open:()=>s.open,defaultOpen:()=>s.defaultOpen,onOpenChange:k=>s.onOpenChange?.(k)}),{present:B}=Yu({show:()=>t.forceMount()||z.isOpen(),element:()=>y()??null}),K=lu({selectionMode:"none",dataSource:D}),j=k=>{w(k),z.open()},W=(k=!1)=>{z.close(),k&&o&&o.close(!0)},q=k=>{w(k),z.toggle()},Z=()=>{const k=y();k&&(ke(k),K.selectionManager().setFocused(!0),K.selectionManager().setFocusedKey(void 0))},J=()=>{i!=null?setTimeout(()=>Z()):Z()},E=k=>{F(le=>[...le,k]);const X=o?.registerNestedMenu(k);return()=>{F(le=>_o(le,k)),X?.()}},ne=k=>p===m?.side&&og(k,m?.area),se=k=>{ne(k)&&k.preventDefault()},fe=k=>{ne(k)||J()},pe=k=>{ne(k)&&k.preventDefault()};Iu({isDisabled:()=>!(o==null&&z.isOpen()&&t.isModal()),targets:()=>[y(),...I()].filter(Boolean)}),Y(()=>{const k=y();if(!k||!o)return;const X=o.registerNestedMenu(k);ee(()=>{X()})}),Y(()=>{o===void 0&&r?.registerMenu(t.value(),[y(),...I()])}),Y(()=>{o!==void 0||r===void 0||(r.value()===t.value()?(S()?.focus(),r.autoFocusMenu()&&j(!0)):W())}),Y(()=>{o!==void 0||r===void 0||z.isOpen()&&r.setValue(t.value())}),ee(()=>{o===void 0&&r?.unregisterMenu(t.value())});const Ce={dataset:xe(()=>({"data-expanded":z.isOpen()?"":void 0,"data-closed":z.isOpen()?void 0:""})),isOpen:z.isOpen,contentPresent:B,nestedMenus:I,currentPlacement:M,pointerGraceTimeoutId:()=>c,autoFocus:_,listState:()=>K,parentMenuContext:()=>o,triggerRef:S,contentRef:y,triggerId:g,contentId:h,setTriggerRef:x,setContentRef:v,open:j,close:W,toggle:q,focusContent:J,onItemEnter:se,onItemLeave:fe,onTriggerLeave:pe,setPointerDir:k=>p=k,setPointerGraceTimeoutId:k=>c=k,setPointerGraceIntent:k=>m=k,registerNestedMenu:E,registerItemToParentDomCollection:n?.registerItem,registerTriggerId:_n(u),registerContentId:_n(b)};return R(P,{get children(){return R(Tl.Provider,{value:Ce,get children(){return R(ae,{when:i===void 0,get fallback(){return a.children},get children(){return R(bl,U({anchorRef:S,contentRef:y,onCurrentPlacementChange:A},a))}})}})}})}function es(e){const{direction:t}=Lt();return R(Ql,U({get placement(){return t()==="rtl"?"left-start":"right-start"},flip:!0},e))}var rg={close:(e,t)=>e==="ltr"?[t==="horizontal"?"ArrowLeft":"ArrowUp"]:[t==="horizontal"?"ArrowRight":"ArrowDown"]};function ts(e){const t=mt(),n=st(),[o,r]=te(e,["onFocusOutside","onKeyDown"]),{direction:i}=Lt();return R(Kl,U({onOpenAutoFocus:m=>{m.preventDefault()},onCloseAutoFocus:m=>{m.preventDefault()},onFocusOutside:m=>{o.onFocusOutside?.(m);const p=m.target;We(t.triggerRef(),p)||t.close()},onKeyDown:m=>{ye(m,o.onKeyDown);const p=We(m.currentTarget,m.target),g=rg.close(i(),n.orientation()).includes(m.key),u=t.parentMenuContext()!=null;p&&g&&u&&(t.close(),ke(t.triggerRef()))}},r))}var ki=["Enter"," "],ig={open:(e,t)=>e==="ltr"?[...ki,t==="horizontal"?"ArrowRight":"ArrowDown"]:[...ki,t==="horizontal"?"ArrowLeft":"ArrowUp"]};function ns(e){let t;const n=st(),o=mt(),r=ge({id:n.generateId(`sub-trigger-${zt()}`)},e),[i,l]=te(r,["ref","id","textValue","disabled","onPointerMove","onPointerLeave","onPointerDown","onPointerUp","onClick","onKeyDown","onMouseDown","onFocus"]);let s=null;const a=()=>{s&&window.clearTimeout(s),s=null},{direction:c}=Lt(),m=()=>i.id,p=()=>{const v=o.parentMenuContext();if(v==null)throw new Error("[kobalte]: `Menu.SubTrigger` must be used within a `Menu.Sub` component");return v.listState().selectionManager()},g=()=>o.listState().collection(),u=()=>p().focusedKey()===m(),h=_l({key:m,selectionManager:p,shouldSelectOnPressUp:!0,allowsDifferentPressOrigin:!0,disabled:()=>i.disabled},()=>t),b=v=>{ye(v,i.onClick),!o.isOpen()&&!i.disabled&&o.open(!0)},S=v=>{if(ye(v,i.onPointerMove),v.pointerType!=="mouse")return;const _=o.parentMenuContext();if(_?.onItemEnter(v),!v.defaultPrevented){if(i.disabled){_?.onItemLeave(v);return}!o.isOpen()&&!s&&(o.parentMenuContext()?.setPointerGraceIntent(null),s=window.setTimeout(()=>{o.open(!1),a()},100)),_?.onItemEnter(v),v.defaultPrevented||(o.listState().selectionManager().isFocused()&&(o.listState().selectionManager().setFocused(!1),o.listState().selectionManager().setFocusedKey(void 0)),ke(v.currentTarget),_?.listState().selectionManager().setFocused(!0),_?.listState().selectionManager().setFocusedKey(m()))}},x=v=>{if(ye(v,i.onPointerLeave),v.pointerType!=="mouse")return;a();const _=o.parentMenuContext(),w=o.contentRef();if(w){_?.setPointerGraceIntent({area:ng(o.currentPlacement(),v,w),side:o.currentPlacement().split("-")[0]}),window.clearTimeout(_?.pointerGraceTimeoutId());const M=window.setTimeout(()=>{_?.setPointerGraceIntent(null)},300);_?.setPointerGraceTimeoutId(M)}else{if(_?.onTriggerLeave(v),v.defaultPrevented)return;_?.setPointerGraceIntent(null)}_?.onItemLeave(v)},y=v=>{ye(v,i.onKeyDown),!v.repeat&&(i.disabled||ig.open(c(),n.orientation()).includes(v.key)&&(v.stopPropagation(),v.preventDefault(),p().setFocused(!1),p().setFocusedKey(void 0),o.isOpen()||o.open("first"),o.focusContent(),o.listState().selectionManager().setFocused(!0),o.listState().selectionManager().setFocusedKey(g().getFirstKey())))};return Y(()=>{if(o.registerItemToParentDomCollection==null)throw new Error("[kobalte]: `Menu.SubTrigger` must be used within a `Menu.Sub` component");const v=o.registerItemToParentDomCollection({ref:()=>t,type:"item",key:m(),textValue:i.textValue??t?.textContent??"",disabled:i.disabled??!1});ee(v)}),Y(kn(()=>o.parentMenuContext()?.pointerGraceTimeoutId(),v=>{ee(()=>{window.clearTimeout(v),o.parentMenuContext()?.setPointerGraceIntent(null)})})),Y(()=>ee(o.registerTriggerId(i.id))),ee(()=>{a()}),R(Te,U({as:"div",ref(v){var _=Ze(w=>{o.setTriggerRef(w),t=w},i.ref);typeof _=="function"&&_(v)},get id(){return i.id},role:"menuitem",get tabIndex(){return h.tabIndex()},"aria-haspopup":"true",get"aria-expanded"(){return o.isOpen()},get"aria-controls"(){return Q(()=>!!o.isOpen())()?o.contentId():void 0},get"aria-disabled"(){return i.disabled},get"data-key"(){return h.dataKey()},get"data-highlighted"(){return u()?"":void 0},get"data-disabled"(){return i.disabled?"":void 0},get onPointerDown(){return be([i.onPointerDown,h.onPointerDown])},get onPointerUp(){return be([i.onPointerUp,h.onPointerUp])},get onClick(){return be([b,h.onClick])},get onKeyDown(){return be([y,h.onKeyDown])},get onMouseDown(){return be([i.onMouseDown,h.onMouseDown])},get onFocus(){return be([i.onFocus,h.onFocus])},onPointerMove:S,onPointerLeave:x},()=>o.dataset(),l))}function lg(e){const t=eo(),n=`menu-${zt()}`,o=ge({id:n,modal:!0},e),[r,i]=te(o,["id","modal","preventScroll","forceMount","open","defaultOpen","onOpenChange","value","orientation"]),l=Al({open:()=>r.open,defaultOpen:()=>r.defaultOpen,onOpenChange:a=>r.onOpenChange?.(a)}),s={isModal:()=>r.modal??!0,preventScroll:()=>r.preventScroll??s.isModal(),forceMount:()=>r.forceMount??!1,generateId:Wo(()=>r.id),value:()=>r.value,orientation:()=>r.orientation??t?.orientation()??"horizontal"};return R(Ol.Provider,{value:s,get children(){return R(Ql,U({get open(){return l.isOpen()},get onOpenChange(){return l.setIsOpen}},i))}})}var sg={};nr(sg,{Root:()=>to,Separator:()=>ag});function to(e){let t;const n=ge({orientation:"horizontal"},e),[o,r]=te(n,["ref","orientation"]),i=tr(()=>t,()=>"hr");return R(Te,U({as:"hr",ref(l){var s=Ze(a=>t=a,o.ref);typeof s=="function"&&s(l)},get role(){return i()!=="hr"?"separator":void 0},get"aria-orientation"(){return o.orientation==="vertical"?"vertical":void 0},get"data-orientation"(){return o.orientation}},r))}var ag=to,cg={};nr(cg,{Arrow:()=>Qo,CheckboxItem:()=>Vl,Content:()=>os,DropdownMenu:()=>Zt,Group:()=>lr,GroupLabel:()=>Hl,Icon:()=>Nl,Item:()=>Gl,ItemDescription:()=>jl,ItemIndicator:()=>Ul,ItemLabel:()=>ql,Portal:()=>Xl,RadioGroup:()=>Zl,RadioItem:()=>Jl,Root:()=>rs,Separator:()=>to,Sub:()=>es,SubContent:()=>ts,SubTrigger:()=>ns,Trigger:()=>Bl});function os(e){const t=st(),n=mt(),[o,r]=te(e,["onCloseAutoFocus","onInteractOutside"]);let i=!1;return R(Qu,U({onCloseAutoFocus:a=>{o.onCloseAutoFocus?.(a),i||ke(n.triggerRef()),i=!1,a.preventDefault()},onInteractOutside:a=>{o.onInteractOutside?.(a),(!t.isModal()||a.detail.isContextMenu)&&(i=!0)}},r))}function rs(e){const t=`dropdownmenu-${zt()}`,n=ge({id:t},e);return R(lg,n)}var Zt=Object.assign(rs,{Arrow:Qo,CheckboxItem:Vl,Content:os,Group:lr,GroupLabel:Hl,Icon:Nl,Item:Gl,ItemDescription:jl,ItemIndicator:Ul,ItemLabel:ql,Portal:Xl,RadioGroup:Zl,RadioItem:Jl,Separator:to,Sub:es,SubContent:ts,SubTrigger:ns,Trigger:Bl}),Ii=V("<span>");function dg(e){const[t,n]=te(e,["items","trigger","placement","disabled","onSelect","class","children"]),o=()=>({display:"inline-flex",alignItems:"center",gap:"4px"}),r=()=>({backgroundColor:"white",borderRadius:"4px",border:"1px solid",borderColor:"border.base",boxShadow:"light",padding:"4px 0",minWidth:"120px",maxWidth:"300px",zIndex:1e3,animation:"fadeIn 0.2s ease"}),i=s=>({display:"flex",alignItems:"center",gap:"8px",padding:"8px 16px",fontSize:"14px",color:s.disabled?"text.placeholder":"text.regular",cursor:s.disabled?"not-allowed":"pointer",borderTop:s.divided?"1px solid":"none",borderColor:"border.lighter",marginTop:s.divided?"4px":"0",paddingTop:s.divided?"12px":"8px",transition:"all 0.2s",_hover:s.disabled?{}:{backgroundColor:"primary.50",color:"primary.600"},_focus:{backgroundColor:"primary.50",color:"primary.600",outline:"none"}}),l=s=>{s.disabled||(s.onClick?.(),t.onSelect?.(s.key))};return R(Zt,{get placement(){return t.placement||"bottom-start"},get disabled(){return t.disabled},get children(){return[R(Zt.Trigger,{get class(){return d(o())},get children(){return t.trigger||R(Ie,{variant:"default",size:"small",children:"操作 ▼"})}}),R(Zt.Portal,{get children(){return R(Zt.Content,{get class(){return jt(d(r()),t.class)},get children(){return[R(Fe,{get each(){return t.items},children:s=>R(Zt.Item,{get class(){return d(i(s))},get disabled(){return s.disabled},onSelect:()=>l(s),get children(){return[R(ae,{get when(){return s.icon},get children(){var a=Ii();return C(a,()=>s.icon),O(()=>f(a,d({fontSize:"14px"}))),a}}),(()=>{var a=Ii();return C(a,()=>s.label),a})()]}})}),Q(()=>t.children)]}})}})]}})}const Eo=document.createElement("style");Eo.textContent=`
  @keyframes fadeIn {
    from {
      opacity: 0;
      transform: translateY(-4px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
`;document.head.querySelector("style[data-dropdown]")||(Eo.setAttribute("data-dropdown","true"),document.head.appendChild(Eo));const Ao=document.createElement("style");Ao.textContent=`
  @keyframes popoverFadeIn {
    from {
      opacity: 0;
      transform: scale(0.95);
    }
    to {
      opacity: 1;
      transform: scale(1);
    }
  }
`;document.head.querySelector("style[data-popover]")||(Ao.setAttribute("data-popover","true"),document.head.appendChild(Ao));var ug=V("<span>量化平台"),gg=V("<div><aside><div><span>📈</span></div><nav></nav><div></div></aside><div><header><div><h1>量化交易平台</h1></div><div><div>用</div></div></header><main>"),Dn=V("<span>"),fg=V("<div>");function pg(e){const[t,n]=H(!1),[o,r]=H(""),i=us(),l=[{id:"dashboard",label:"仪表盘",icon:"📊",path:"/dashboard"},{id:"market",label:"行情中心",icon:"📈",path:"/market",children:[{id:"realtime",label:"实时行情",icon:"⚡",path:"/market/realtime"},{id:"historical",label:"历史数据",icon:"📋",path:"/market/historical"}]},{id:"trading",label:"交易中心",icon:"💰",path:"/trading"},{id:"strategy",label:"策略中心",icon:"🧠",path:"/strategy"},{id:"account",label:"账户中心",icon:"👤",path:"/account"},{id:"settings",label:"系统设置",icon:"⚙️",path:"/settings"}],s=u=>i.pathname===u||i.pathname.startsWith(u+"/"),a=()=>({width:t()?"64px":"240px",height:"100vh",backgroundColor:"white",borderRight:"1px solid",borderColor:"border.base",transition:"width 0.3s ease",display:"flex",flexDirection:"column",position:"fixed",left:0,top:0,zIndex:1e3}),c=()=>({height:"60px",backgroundColor:"white",borderBottom:"1px solid",borderColor:"border.base",display:"flex",alignItems:"center",justifyContent:"space-between",padding:"0 20px",marginLeft:t()?"64px":"240px",transition:"margin-left 0.3s ease"}),m=()=>({marginLeft:t()?"64px":"240px",marginTop:"60px",minHeight:"calc(100vh - 60px)",backgroundColor:"bg.page",transition:"margin-left 0.3s ease"}),p=()=>({height:"60px",display:"flex",alignItems:"center",justifyContent:t()?"center":"flex-start",padding:t()?"0":"0 20px",borderBottom:"1px solid",borderColor:"border.base",fontSize:"18px",fontWeight:"600",color:"primary.500"}),g=(u,h=!1)=>({display:"flex",alignItems:"center",gap:"12px",padding:t()&&!h?"12px 0":"12px 20px",paddingLeft:h?"52px":t()?"0":"20px",justifyContent:t()&&!h?"center":"flex-start",color:s(u.path)?"primary.500":"text.regular",backgroundColor:s(u.path)?"primary.50":"transparent",borderRight:s(u.path)?"3px solid":"none",borderColor:"primary.500",textDecoration:"none",transition:"all 0.3s",cursor:"pointer",_hover:{backgroundColor:"primary.50",color:"primary.500"}});return(()=>{var u=gg(),h=u.firstChild,b=h.firstChild,S=b.firstChild,x=b.nextSibling,y=x.nextSibling,v=h.nextSibling,_=v.firstChild,w=_.firstChild,M=w.firstChild,A=w.nextSibling,I=A.firstChild,F=_.nextSibling;return C(b,R(ae,{get when(){return!t()},get children(){return ug()}}),null),C(x,R(Fe,{each:l,children:D=>(()=>{var T=fg();return C(T,R(Vr,{get href(){return D.path},get class(){return d(g(D))},get title(){return Q(()=>!!t())()?D.label:void 0},get children(){return[(()=>{var P=Dn();return C(P,()=>D.icon),O(()=>f(P,d({fontSize:"16px"}))),P})(),R(ae,{get when(){return!t()},get children(){var P=Dn();return C(P,()=>D.label),O(()=>f(P,d({fontWeight:s(D.path)?"500":"400"}))),P}})]}}),null),C(T,R(ae,{get when(){return Q(()=>!!D.children)()&&!t()},get children(){return R(Fe,{get each(){return D.children},children:P=>R(Vr,{get href(){return P.path},get class(){return d(g(P,!0))},get children(){return[(()=>{var z=Dn();return C(z,()=>P.icon),O(()=>f(z,d({fontSize:"14px"}))),z})(),(()=>{var z=Dn();return C(z,()=>P.label),O(()=>f(z,d({fontWeight:s(P.path)?"500":"400"}))),z})()]}})})}}),null),T})()})),C(y,R(Ie,{variant:"text",size:"small",onClick:()=>n(!t()),get class(){return d({width:"100%"})},get children(){return t()?"→":"←"}})),C(A,R(Hi,{placeholder:"搜索...",size:"small",get value(){return o()},onInput:D=>r(D.currentTarget.value),get class(){return d({width:"200px"})}}),I),C(A,R(Ie,{variant:"text",size:"small",children:"帮助"}),I),C(A,R(Ie,{variant:"text",size:"small",children:"设置"}),I),C(F,()=>e.children),O(D=>{var T=d({display:"flex",minHeight:"100vh"}),P=d(a()),z=d(p()),B=d({fontSize:"20px",marginRight:"8px"}),K=d({flex:1,overflowY:"auto",padding:"8px 0"}),j=d({padding:"16px",borderTop:"1px solid",borderColor:"border.base"}),W=d({flex:1}),q=d(c()),Z=d({display:"flex",alignItems:"center",gap:"16px"}),J=d({fontSize:"18px",fontWeight:"500",color:"text.primary",margin:0}),E=d({display:"flex",alignItems:"center",gap:"16px"}),ne=d({width:"32px",height:"32px",backgroundColor:"primary.500",borderRadius:"50%",display:"flex",alignItems:"center",justifyContent:"center",color:"white",fontSize:"14px",fontWeight:"500"}),se=d(m());return T!==D.e&&f(u,D.e=T),P!==D.t&&f(h,D.t=P),z!==D.a&&f(b,D.a=z),B!==D.o&&f(S,D.o=B),K!==D.i&&f(x,D.i=K),j!==D.n&&f(y,D.n=j),W!==D.s&&f(v,D.s=W),q!==D.h&&f(_,D.h=q),Z!==D.r&&f(w,D.r=Z),J!==D.d&&f(M,D.d=J),E!==D.l&&f(A,D.l=E),ne!==D.u&&f(I,D.u=ne),se!==D.c&&f(F,D.c=se),D},{e:void 0,t:void 0,a:void 0,o:void 0,i:void 0,n:void 0,s:void 0,h:void 0,r:void 0,d:void 0,l:void 0,u:void 0,c:void 0}),u})()}var hg=V("<div>🎉 新版专业量化平台界面已成功加载！"),mg=V("<div><div><h1>投资仪表盘</h1><div></div></div><div></div><div><div><div><h3>资金曲线图</h3><div></div></div><div><div>📈</div><div>资金曲线图表</div><div>显示策略收益走势</div></div></div><div><div><h3>持仓概览</h3><span>查看全部 →</span></div><div></div></div></div><div><div><div><h3>今日行情</h3><span>更新时间: 15:30</span></div><div></div></div><div><div><h3>最新资讯</h3><span>查看更多 →</span></div><div></div></div></div><div><div>当前时间: </div><div><span>数据来源: 模拟数据</span><span>更新频率: 实时</span><div><div></div><span>系统正常"),po=V("<div>"),vg=V("<div><div><span></span><span>%</span></div><div><span></span><span>"),xg=V("<div><div><div></div><span></span></div><div><div></div><div> (<!>)"),bg=V("<div><div></div><div><span></span><span>");function Tn(){console.log("🔥 Dashboard组件已加载 - 新版本 - 时间戳:",Date.now());const[e,t]=H(new Date().toLocaleString("zh-CN"));let n;pt(()=>{n=setInterval(()=>{t(new Date().toLocaleString("zh-CN"))},1e3)}),ee(()=>{n&&clearInterval(n)});const o=[{title:"总资产",value:"¥1,000,000",change:"+2.34%",trend:"up",icon:"💰",description:"总资产",subValue:"¥1,000,000"},{title:"今日盈亏",value:"0",change:"+0.00%",trend:"neutral",icon:"📊",description:"今日盈亏",subValue:"0.00%"},{title:"持仓市值",value:"¥50,000",change:"+0.00%",trend:"neutral",icon:"📈",description:"持仓市值",subValue:"¥50,000"},{title:"可用资金",value:"2",change:"+0.00%",trend:"neutral",icon:"🔒",description:"持仓数量",subValue:"2"}],r=[{code:"000725",name:"京东方A",price:3.45,change:-1.43,changePercent:-29.3,volume:7340.75,amount:-6046,status:"持仓"},{code:"300519",name:"新光药业",price:68.94,change:-1.05,changePercent:-1.5,volume:410.75,amount:-1796,status:"持仓"},{code:"000831",name:"五矿稀土",price:26.09,change:-1.37,changePercent:-5,volume:7558.72,amount:-7688,status:"持仓"}],i=[{name:"上证指数",value:"3,245.67",change:"+23.45",percent:"+0.73%",trend:"up"},{name:"深证成指",value:"10,567.23",change:"+45.67",percent:"+0.43%",trend:"up"},{name:"创业板指",value:"2,234.56",change:"-8.90",percent:"-0.40%",trend:"down"},{name:"科创50",value:"1,123.45",change:"+15.23",percent:"+1.37%",trend:"up"}],l=[{title:"A股市场今日表现强劲，科技股领涨",time:"刚刚发布",type:"market"},{title:"央行宣布降准0.25个百分点",time:"30分钟前",type:"policy"},{title:"新能源板块持续活跃，多只个股涨停",time:"1小时前",type:"sector"}];return(()=>{var s=mg(),a=s.firstChild,c=a.firstChild,m=c.nextSibling,p=a.nextSibling,g=p.nextSibling,u=g.firstChild,h=u.firstChild,b=h.firstChild,S=b.nextSibling,x=h.nextSibling,y=x.firstChild,v=y.nextSibling,_=v.nextSibling,w=u.nextSibling,M=w.firstChild,A=M.firstChild,I=A.nextSibling,F=M.nextSibling,D=g.nextSibling,T=D.firstChild,P=T.firstChild,z=P.firstChild,B=z.nextSibling,K=P.nextSibling,j=T.nextSibling,W=j.firstChild,q=W.firstChild,Z=q.nextSibling,J=W.nextSibling,E=D.nextSibling,ne=E.firstChild;ne.firstChild;var se=ne.nextSibling,fe=se.firstChild,pe=fe.nextSibling,Se=pe.nextSibling,Ce=Se.firstChild;return C(s,R(Kn,{get class(){return d({marginBottom:"20px",backgroundColor:"success.50",borderColor:"success.200"})},get children(){var k=hg();return O(()=>f(k,d({padding:"16px",textAlign:"center",color:"success.700",fontSize:"16px",fontWeight:"600"}))),k}}),a),C(m,R(Ie,{variant:"primary",size:"small",children:"刷新"}),null),C(m,R(Ie,{variant:"default",size:"small",children:"设置"}),null),C(m,R(Ie,{variant:"success",size:"small",children:"新增策略"}),null),C(p,R(Fe,{each:o,children:k=>R(Kn,{shadow:"hover",bodyStyle:{display:"flex",flexDirection:"column",alignItems:"center",textAlign:"center",padding:"24px 16px"},get children(){return[(()=>{var X=po();return C(X,()=>k.icon),O(()=>f(X,d({fontSize:"32px",marginBottom:"12px"}))),X})(),(()=>{var X=po();return C(X,()=>k.value),O(()=>f(X,d({fontSize:"28px",fontWeight:"600",color:"text.primary",marginBottom:"8px"}))),X})(),(()=>{var X=po();return C(X,()=>k.description),O(()=>f(X,d({fontSize:"14px",color:"text.secondary",marginBottom:"12px"}))),X})(),R(Vn,{get type(){return Q(()=>k.trend==="up")()?"success":k.trend==="down"?"danger":"info"},effect:"light",size:"small",get children(){return k.change}})]}})})),C(S,R(Ie,{variant:"primary",size:"sm",children:"日"}),null),C(S,R(Ie,{size:"sm",children:"周"}),null),C(S,R(Ie,{size:"sm",children:"月"}),null),C(F,R(Fe,{each:r,children:k=>(()=>{var X=vg(),le=X.firstChild,he=le.firstChild,de=he.nextSibling,me=de.firstChild,re=le.nextSibling,ue=re.firstChild,Pe=ue.nextSibling;return C(he,()=>k.code),C(de,()=>k.changePercent>0?"+":"",me),C(de,()=>k.changePercent,me),C(ue,()=>k.name),C(Pe,()=>k.status),O(ce=>{var oe=d({padding:"8px",backgroundColor:"#fafafa",borderRadius:"4px",fontSize:"12px"}),we=d({display:"flex",justifyContent:"space-between",alignItems:"center",marginBottom:"4px"}),$e=d({fontWeight:"600",color:"#262626"}),Ee=d({color:k.changePercent>0?"#52c41a":"#f5222d",fontWeight:"500"}),Ae=d({display:"flex",justifyContent:"space-between",color:"#8c8c8c"});return oe!==ce.e&&f(X,ce.e=oe),we!==ce.t&&f(le,ce.t=we),$e!==ce.a&&f(he,ce.a=$e),Ee!==ce.o&&f(de,ce.o=Ee),Ae!==ce.i&&f(re,ce.i=Ae),ce},{e:void 0,t:void 0,a:void 0,o:void 0,i:void 0}),X})()})),C(K,R(Fe,{each:i,children:k=>(()=>{var X=xg(),le=X.firstChild,he=le.firstChild,de=he.nextSibling,me=le.nextSibling,re=me.firstChild,ue=re.nextSibling,Pe=ue.firstChild,ce=Pe.nextSibling;return ce.nextSibling,C(de,()=>k.name),C(re,()=>k.value),C(ue,()=>k.change,Pe),C(ue,()=>k.percent,ce),O(oe=>{var we=d({display:"flex",alignItems:"center",justifyContent:"space-between",padding:"8px",backgroundColor:"#fafafa",borderRadius:"4px",fontSize:"12px"}),$e=d({display:"flex",alignItems:"center",gap:"8px"}),Ee=d({width:"6px",height:"6px",borderRadius:"50%",backgroundColor:k.trend==="up"?"#52c41a":"#f5222d"}),Ae=d({fontWeight:"500",color:"#262626"}),Ne=d({textAlign:"right"}),Ge=d({fontWeight:"600",color:"#262626",marginBottom:"2px"}),je=d({color:k.trend==="up"?"#52c41a":"#f5222d",fontSize:"11px"});return we!==oe.e&&f(X,oe.e=we),$e!==oe.t&&f(le,oe.t=$e),Ee!==oe.a&&f(he,oe.a=Ee),Ae!==oe.o&&f(de,oe.o=Ae),Ne!==oe.i&&f(me,oe.i=Ne),Ge!==oe.n&&f(re,oe.n=Ge),je!==oe.s&&f(ue,oe.s=je),oe},{e:void 0,t:void 0,a:void 0,o:void 0,i:void 0,n:void 0,s:void 0}),X})()})),C(J,R(Fe,{each:l,children:k=>(()=>{var X=bg(),le=X.firstChild,he=le.nextSibling,de=he.firstChild,me=de.nextSibling;return C(le,()=>k.title),C(de,()=>k.time),C(me,()=>k.type),O(re=>{var ue=d({padding:"8px",backgroundColor:"#fafafa",borderRadius:"4px",cursor:"pointer",transition:"background-color 0.2s",_hover:{backgroundColor:"#f0f0f0"}}),Pe=d({fontSize:"12px",fontWeight:"500",color:"#262626",marginBottom:"4px",lineHeight:"1.4"}),ce=d({display:"flex",alignItems:"center",justifyContent:"space-between"}),oe=d({fontSize:"11px",color:"#8c8c8c"}),we=d({fontSize:"10px",color:"#1890ff",backgroundColor:"#e6f7ff",padding:"2px 6px",borderRadius:"2px"});return ue!==re.e&&f(X,re.e=ue),Pe!==re.t&&f(le,re.t=Pe),ce!==re.a&&f(he,re.a=ce),oe!==re.o&&f(de,re.o=oe),we!==re.i&&f(me,re.i=we),re},{e:void 0,t:void 0,a:void 0,o:void 0,i:void 0}),X})()})),C(ne,e,null),O(k=>{var X=d({padding:"20px",minHeight:"100vh",backgroundColor:"bg.page"}),le=d({display:"flex",alignItems:"center",justifyContent:"space-between",marginBottom:"20px"}),he=d({fontSize:"24px",fontWeight:"600",color:"text.primary",margin:0}),de=d({display:"flex",alignItems:"center",gap:"12px"}),me=d({display:"grid",gridTemplateColumns:"repeat(auto-fit, minmax(240px, 1fr))",gap:"20px",marginBottom:"24px"}),re=d({display:"grid",gridTemplateColumns:"2fr 1fr",gap:"16px",marginBottom:"16px","@media (max-width: 768px)":{gridTemplateColumns:"1fr"}}),ue=d({backgroundColor:"white",borderRadius:"8px",border:"1px solid #e8e8e8",padding:"16px"}),Pe=d({display:"flex",alignItems:"center",justifyContent:"space-between",marginBottom:"16px",flexWrap:"wrap",gap:"8px"}),ce=d({fontSize:"16px",fontWeight:"600",color:"#262626",margin:0}),oe=d({display:"flex",alignItems:"center",gap:"8px"}),we=d({height:"200px",backgroundColor:"#fafafa",borderRadius:"4px",display:"flex",alignItems:"center",justifyContent:"center",flexDirection:"column",gap:"8px"}),$e=d({fontSize:"48px"}),Ee=d({fontSize:"14px",color:"#8c8c8c"}),Ae=d({fontSize:"12px",color:"#8c8c8c"}),Ne=d({backgroundColor:"white",borderRadius:"8px",border:"1px solid #e8e8e8",padding:"16px"}),Ge=d({display:"flex",alignItems:"center",justifyContent:"space-between",marginBottom:"16px"}),je=d({fontSize:"16px",fontWeight:"600",color:"#262626",margin:0}),vt=d({fontSize:"12px",color:"#8c8c8c"}),xt=d({display:"flex",flexDirection:"column",gap:"8px"}),bt=d({display:"grid",gridTemplateColumns:"1fr 1fr",gap:"16px","@media (max-width: 768px)":{gridTemplateColumns:"1fr"}}),yt=d({backgroundColor:"white",borderRadius:"8px",border:"1px solid #e8e8e8",padding:"16px"}),St=d({display:"flex",alignItems:"center",justifyContent:"space-between",marginBottom:"16px",flexWrap:"wrap",gap:"8px"}),Ct=d({fontSize:"16px",fontWeight:"600",color:"#262626",margin:0}),Ot=d({fontSize:"12px",color:"#8c8c8c"}),Vt=d({display:"flex",flexDirection:"column",gap:"8px"}),wt=d({backgroundColor:"white",borderRadius:"8px",border:"1px solid #e8e8e8",padding:"16px"}),Je=d({display:"flex",alignItems:"center",justifyContent:"space-between",marginBottom:"16px"}),Bt=d({fontSize:"16px",fontWeight:"600",color:"#262626",margin:0}),tn=d({fontSize:"12px",color:"#1890ff",cursor:"pointer"}),Kt=d({display:"flex",flexDirection:"column",gap:"8px"}),nn=d({display:"flex",alignItems:"center",justifyContent:"space-between",padding:"12px 16px",backgroundColor:"white",borderRadius:"8px",border:"1px solid #e8e8e8",fontSize:"12px",color:"#8c8c8c"}),$=d({display:"flex",alignItems:"center",gap:"16px"}),De=d({display:"flex",alignItems:"center",gap:"4px"}),ze=d({width:"6px",height:"6px",borderRadius:"50%",backgroundColor:"#52c41a"});return X!==k.e&&f(s,k.e=X),le!==k.t&&f(a,k.t=le),he!==k.a&&f(c,k.a=he),de!==k.o&&f(m,k.o=de),me!==k.i&&f(p,k.i=me),re!==k.n&&f(g,k.n=re),ue!==k.s&&f(u,k.s=ue),Pe!==k.h&&f(h,k.h=Pe),ce!==k.r&&f(b,k.r=ce),oe!==k.d&&f(S,k.d=oe),we!==k.l&&f(x,k.l=we),$e!==k.u&&f(y,k.u=$e),Ee!==k.c&&f(v,k.c=Ee),Ae!==k.w&&f(_,k.w=Ae),Ne!==k.m&&f(w,k.m=Ne),Ge!==k.f&&f(M,k.f=Ge),je!==k.y&&f(A,k.y=je),vt!==k.g&&f(I,k.g=vt),xt!==k.p&&f(F,k.p=xt),bt!==k.b&&f(D,k.b=bt),yt!==k.T&&f(T,k.T=yt),St!==k.A&&f(P,k.A=St),Ct!==k.O&&f(z,k.O=Ct),Ot!==k.I&&f(B,k.I=Ot),Vt!==k.S&&f(K,k.S=Vt),wt!==k.W&&f(j,k.W=wt),Je!==k.C&&f(W,k.C=Je),Bt!==k.B&&f(q,k.B=Bt),tn!==k.v&&f(Z,k.v=tn),Kt!==k.k&&f(J,k.k=Kt),nn!==k.x&&f(E,k.x=nn),$!==k.j&&f(se,k.j=$),De!==k.q&&f(Se,k.q=De),ze!==k.z&&f(Ce,k.z=ze),k},{e:void 0,t:void 0,a:void 0,o:void 0,i:void 0,n:void 0,s:void 0,h:void 0,r:void 0,d:void 0,l:void 0,u:void 0,c:void 0,w:void 0,m:void 0,f:void 0,y:void 0,g:void 0,p:void 0,b:void 0,T:void 0,A:void 0,O:void 0,I:void 0,S:void 0,W:void 0,C:void 0,B:void 0,v:void 0,k:void 0,x:void 0,j:void 0,q:void 0,z:void 0}),s})()}var yg=V("<div><div><h1>🔧 API 连接测试</h1><p>测试前端与后端API的连接状态</p></div><div><button>开始测试</button></div><div><div><h2>测试结果</h2></div><div></div></div><div><div><h2>配置信息</h2></div><div><div><div><h3>API Base URL</h3><p></p></div><div><h3>环境模式</h3><p></p></div><div><h3>代理配置</h3><p>/api → localhost:8000"),Sg=V('<p>点击"开始测试"按钮运行API连接测试'),Cg=V("<div>"),wg=V("<div><div><div></div><div><h3></h3><p>"),_g=V("<div>ms");const zn={get:async(e,t)=>{const n=t?"?"+new URLSearchParams(t).toString():"",r=await fetch("https://api.yourdomain.com"+e+n);if(!r.ok)throw new Error(`HTTP ${r.status}`);return r.json().catch(()=>({}))}},Ln={SYSTEM:{HEALTH:"/v1/health"},MARKET:{OVERVIEW:"/v1/market/overview",SEARCH:"/v1/market/search"},AUTH:{PROFILE:"/v1/auth/profile"}};function Rg(){const[e,t]=H([]),n=(r,i,l,s)=>{t(a=>[...a,{name:r,status:i,message:l,duration:s}])},o=async()=>{t([]);try{const r=Date.now();await zn.get(Ln.SYSTEM.HEALTH);const i=Date.now()-r;n("系统健康检查","success","连接成功",i)}catch(r){n("系统健康检查","error",r.message||"连接失败")}try{const r=Date.now();await zn.get(Ln.MARKET.OVERVIEW);const i=Date.now()-r;n("市场概览","success","数据获取成功",i)}catch(r){n("市场概览","error",r.message||"数据获取失败")}try{const r=Date.now();await zn.get(Ln.MARKET.SEARCH,{q:"AAPL"});const i=Date.now()-r;n("股票搜索","success","搜索成功",i)}catch(r){n("股票搜索","error",r.message||"搜索失败")}try{const r=Date.now();await zn.get(Ln.AUTH.PROFILE);const i=Date.now()-r;n("用户信息","success","获取成功",i)}catch(r){n("用户信息","error",r.message||"获取失败（预期，因为未登录）")}};return pt(()=>{console.log("ApiTest mounted")}),(()=>{var r=yg(),i=r.firstChild,l=i.firstChild,s=l.nextSibling,a=i.nextSibling,c=a.firstChild,m=a.nextSibling,p=m.firstChild,g=p.firstChild,u=p.nextSibling,h=m.nextSibling,b=h.firstChild,S=b.firstChild,x=b.nextSibling,y=x.firstChild,v=y.firstChild,_=v.firstChild,w=_.nextSibling,M=v.nextSibling,A=M.firstChild,I=A.nextSibling,F=M.nextSibling,D=F.firstChild,T=D.nextSibling;return c.$$click=o,C(u,(()=>{var P=Q(()=>e().length===0);return()=>P()?(()=>{var z=Sg();return O(()=>f(z,d({color:"gray.500",textAlign:"center",padding:"40px 0"}))),z})():(()=>{var z=Cg();return C(z,()=>e().map(B=>(()=>{var K=wg(),j=K.firstChild,W=j.firstChild,q=W.nextSibling,Z=q.firstChild,J=Z.nextSibling;return C(W,()=>B.status==="success"?"✅":"❌"),C(Z,()=>B.name),C(J,()=>B.message),C(K,(()=>{var E=Q(()=>!!B.duration);return()=>E()&&(()=>{var ne=_g(),se=ne.firstChild;return C(ne,()=>B.duration,se),O(()=>f(ne,d({fontSize:"12px",color:"gray.500",backgroundColor:"gray.100",padding:"4px 8px",borderRadius:"4px"}))),ne})()})(),null),O(E=>{var ne=d({display:"flex",alignItems:"center",justifyContent:"space-between",padding:"16px",borderRadius:"8px",border:"1px solid",borderColor:B.status==="success"?"green.200":"red.200",backgroundColor:B.status==="success"?"green.50":"red.50"}),se=d({display:"flex",alignItems:"center",gap:"12px"}),fe=d({fontSize:"20px"}),pe=d({fontSize:"16px",fontWeight:"600",color:"gray.900",marginBottom:"4px"}),Se=d({fontSize:"14px",color:"gray.600"});return ne!==E.e&&f(K,E.e=ne),se!==E.t&&f(j,E.t=se),fe!==E.a&&f(W,E.a=fe),pe!==E.o&&f(Z,E.o=pe),Se!==E.i&&f(J,E.i=Se),E},{e:void 0,t:void 0,a:void 0,o:void 0,i:void 0}),K})())),O(()=>f(z,d({display:"flex",flexDirection:"column",gap:"16px"}))),z})()})()),C(w,()=>"https://api.yourdomain.com"),C(I,()=>"production"),O(P=>{var z=d({padding:"24px",maxWidth:"1200px",margin:"0 auto"}),B=d({marginBottom:"32px"}),K=d({fontSize:"32px",fontWeight:"bold",color:"gray.900",marginBottom:"8px"}),j=d({fontSize:"16px",color:"gray.600"}),W=d({marginBottom:"32px"}),q=d({padding:"12px 24px",backgroundColor:"blue.600",color:"white",border:"none",borderRadius:"8px",fontSize:"16px",fontWeight:"500",cursor:"pointer",transition:"all 0.2s",_hover:{backgroundColor:"blue.700"}}),Z=d({backgroundColor:"white",borderRadius:"12px",boxShadow:"0 1px 3px rgba(0, 0, 0, 0.1)",border:"1px solid #e5e7eb",overflow:"hidden"}),J=d({padding:"24px",borderBottom:"1px solid #e5e7eb"}),E=d({fontSize:"20px",fontWeight:"bold",color:"gray.900"}),ne=d({padding:"24px"}),se=d({marginTop:"32px",backgroundColor:"white",borderRadius:"12px",boxShadow:"0 1px 3px rgba(0, 0, 0, 0.1)",border:"1px solid #e5e7eb",overflow:"hidden"}),fe=d({padding:"24px",borderBottom:"1px solid #e5e7eb"}),pe=d({fontSize:"20px",fontWeight:"bold",color:"gray.900"}),Se=d({padding:"24px"}),Ce=d({display:"grid",gridTemplateColumns:"repeat(auto-fit, minmax(300px, 1fr))",gap:"16px"}),k=d({fontSize:"14px",fontWeight:"600",color:"gray.700",marginBottom:"8px"}),X=d({fontSize:"14px",color:"gray.600",fontFamily:"monospace",backgroundColor:"gray.100",padding:"8px",borderRadius:"4px"}),le=d({fontSize:"14px",fontWeight:"600",color:"gray.700",marginBottom:"8px"}),he=d({fontSize:"14px",color:"gray.600",fontFamily:"monospace",backgroundColor:"gray.100",padding:"8px",borderRadius:"4px"}),de=d({fontSize:"14px",fontWeight:"600",color:"gray.700",marginBottom:"8px"}),me=d({fontSize:"14px",color:"gray.600",fontFamily:"monospace",backgroundColor:"gray.100",padding:"8px",borderRadius:"4px"});return z!==P.e&&f(r,P.e=z),B!==P.t&&f(i,P.t=B),K!==P.a&&f(l,P.a=K),j!==P.o&&f(s,P.o=j),W!==P.i&&f(a,P.i=W),q!==P.n&&f(c,P.n=q),Z!==P.s&&f(m,P.s=Z),J!==P.h&&f(p,P.h=J),E!==P.r&&f(g,P.r=E),ne!==P.d&&f(u,P.d=ne),se!==P.l&&f(h,P.l=se),fe!==P.u&&f(b,P.u=fe),pe!==P.c&&f(S,P.c=pe),Se!==P.w&&f(x,P.w=Se),Ce!==P.m&&f(y,P.m=Ce),k!==P.f&&f(_,P.f=k),X!==P.y&&f(w,P.y=X),le!==P.g&&f(A,P.g=le),he!==P.p&&f(I,P.p=he),de!==P.b&&f(D,P.b=de),me!==P.T&&f(T,P.T=me),P},{e:void 0,t:void 0,a:void 0,o:void 0,i:void 0,n:void 0,s:void 0,h:void 0,r:void 0,d:void 0,l:void 0,u:void 0,c:void 0,w:void 0,m:void 0,f:void 0,y:void 0,g:void 0,p:void 0,b:void 0,T:void 0}),r})()}Tt(["click"]);class kg{constructor(){this.socket=null,this.reconnectAttempts=0,this.maxReconnectAttempts=5,this.reconnectDelay=1e3,this.connectionStatusSignal=H("disconnected"),this.marketDataSignal=H(new Map),this.connectionStatus=this.connectionStatusSignal[0],this.setConnectionStatus=this.connectionStatusSignal[1],this.marketData=this.marketDataSignal[0],this.setMarketData=this.marketDataSignal[1],this.connect()}connect(){try{this.setConnectionStatus("connecting"),console.log("尝试连接WebSocket服务器:","wss://api.yourdomain.com/ws"),this.socket=null}catch(t){console.error("WebSocket连接失败:",t),this.setConnectionStatus("error"),this.handleReconnect()}}handleReconnect(){this.reconnectAttempts<this.maxReconnectAttempts?(this.reconnectAttempts++,setTimeout(()=>{console.log(`尝试重连 (${this.reconnectAttempts}/${this.maxReconnectAttempts})`),this.connect()},this.reconnectDelay*this.reconnectAttempts)):(console.log("达到最大重连次数，停止重连"),this.setConnectionStatus("error"))}updateMarketData(t){this.setMarketData(n=>{const o=new Map(n);return o.set(t.symbol,t),o})}subscribeToMarketData(t){console.log("订阅市场数据:",t),this.socket&&this.socket.connected&&this.socket.emit("subscribe",{symbols:t})}unsubscribeFromMarketData(t){console.log("取消订阅市场数据:",t),this.socket&&this.socket.connected&&this.socket.emit("unsubscribe",{symbols:t})}reconnect(){console.log("手动重连WebSocket..."),this.disconnect(),this.reconnectAttempts=0,this.connect()}disconnect(){this.socket&&(this.socket.disconnect(),this.socket=null),this.setConnectionStatus("disconnected")}}const nt=new kg;function Ig(){return{connectionStatus:nt.connectionStatus,marketData:nt.marketData,subscribeToMarketData:nt.subscribeToMarketData.bind(nt),unsubscribeFromMarketData:nt.unsubscribeFromMarketData.bind(nt),disconnect:nt.disconnect.bind(nt),reconnect:nt.reconnect.bind(nt)}}var Pg=V("<button>"),$g=V("<span>");const Mg=Un({base:{display:"inline-flex",alignItems:"center",justifyContent:"center",gap:"6px",borderRadius:"4px",fontWeight:"500",cursor:"pointer",transition:"all 0.15s ease",borderWidth:"1px",borderStyle:"solid",userSelect:"none",_disabled:{opacity:.6,cursor:"not-allowed"}},variants:{variant:{default:{backgroundColor:"white",color:"#262626",borderColor:"#d9d9d9",_hover:{backgroundColor:"#f5f5f5"},_active:{backgroundColor:"#eee"}},primary:{backgroundColor:"primary.500",color:"white",borderColor:"primary.500",_hover:{backgroundColor:"primary.600",borderColor:"primary.600"},_active:{backgroundColor:"primary.700",borderColor:"primary.700"}},success:{backgroundColor:"success.500",color:"white",borderColor:"success.500",_hover:{backgroundColor:"success.600",borderColor:"success.600"},_active:{backgroundColor:"success.700",borderColor:"success.700"}},warning:{backgroundColor:"warning.500",color:"white",borderColor:"warning.500"},danger:{backgroundColor:"danger.500",color:"white",borderColor:"danger.500",_hover:{backgroundColor:"danger.600",borderColor:"danger.600"}},plain:{backgroundColor:"white",color:"primary.500",borderColor:"primary.200",_hover:{backgroundColor:"primary.50"}},text:{backgroundColor:"transparent",color:"primary.500",borderColor:"transparent",_hover:{backgroundColor:"primary.50"},_active:{backgroundColor:"primary.100"}}},size:{sm:{height:"28px",fontSize:"12px",px:"10px"},md:{height:"32px",fontSize:"13px",px:"12px"},lg:{height:"36px",fontSize:"14px",px:"14px"}},block:{true:{width:"100%"},false:{}}},defaultVariants:{variant:"default",size:"md",block:!1}});function Me(e){const[t,n]=te(e,["class","children","variant","size","loading","block"]);return(()=>{var o=Pg();return lt(o,U({get class(){return jt(Mg({variant:t.variant,size:t.size,block:!!t.block}),t.class)},get disabled(){return t.loading||n.disabled}},n),!1,!0),C(o,(()=>{var r=Q(()=>!!t.loading);return()=>r()&&(()=>{var i=$g();return O(()=>f(i,d({width:"14px",height:"14px",borderRadius:"50%",borderWidth:"2px",borderStyle:"solid",borderTopColor:"white",borderLeftColor:"white",borderRightColor:"transparent",borderBottomColor:"transparent"}))),i})()})(),null),C(o,()=>t.children,null),o})()}var Fg=V("<div><input>"),Eg=V("<button type=button>×");const Ag=Un({base:{display:"inline-flex",alignItems:"center",borderWidth:"1px",borderStyle:"solid",borderColor:"#d9d9d9",borderRadius:"4px",backgroundColor:"white",transition:"all 0.15s ease",_focusWithin:{borderColor:"primary.500",boxShadow:"0 0 0 1px token(colors.primary.500 / 20%)"}},variants:{size:{sm:{height:"28px",fontSize:"12px",px:"8px",gap:"6px"},md:{height:"32px",fontSize:"13px",px:"10px",gap:"6px"},lg:{height:"36px",fontSize:"14px",px:"12px",gap:"8px"}},status:{default:{},success:{borderColor:"success.500"},warning:{borderColor:"warning.500"},danger:{borderColor:"danger.500"}}},defaultVariants:{size:"md",status:"default"}}),Dg=Un({base:{flex:1,border:"none",outline:"none",background:"transparent",color:"#262626",height:"100%","::placeholder":{color:"#bfbfbf"}}});function Tg(e){const[t,n]=te(e,["class","status","size","clearable","prefix","suffix"]);return(()=>{var o=Fg(),r=o.firstChild;return C(o,()=>t.prefix,r),lt(r,U({get class(){return d(Dg())}},n),!1,!1),C(o,(()=>{var i=Q(()=>!!t.clearable);return()=>i()&&(()=>{var l=Eg();return l.$$click=()=>n.onInput?.({currentTarget:{value:""}}),O(()=>f(l,d({color:"#8c8c8c",_hover:{color:"#595959"}}))),l})()})(),null),C(o,()=>t.suffix,null),O(()=>f(o,d(Ag({status:t.status,size:t.size}),t.class||""))),o})()}Tt(["click"]);var zg=V("<div><div><div><h1>行情分析</h1><div><span>沪深A股</span><span>数据更新: 15:30</span><div><div></div><span></span></div></div></div><div></div></div><div><div><h2>市场概览</h2><div></div></div><div><div><div>上证指数</div><div>3,247.89</div><div>-12.34 (-0.38%)</div></div><div><div>深证成指</div><div>10,567.23</div><div>+45.67 (+0.43%)</div></div><div><div>创业板指</div><div>2,234.56</div><div>-8.90 (-0.40%)</div></div><div><div>科创50</div><div>1,123.45</div><div>+15.23 (+1.37%)</div></div></div></div><div><div><h3>市场筛选</h3><span>共 <!> 只股票</span></div><div><div></div><div><span>板块:</span></div></div></div><div><div><h2>股票列表</h2></div><div><table><thead><tr><th>代码</th><th>名称</th><th>现价</th><th>涨跌额</th><th>涨跌幅</th><th>成交量</th><th>最高价</th><th>最低价</th><th>操作</th></tr></thead><tbody></tbody></table></div><div><div>共 <!> 条数据</div><div><span>1"),Lg=V("<tr><td></td><td></td><td></td><td></td><td>%</td><td></td><td></td><td></td><td><div>"),Og=V("<div><div><h3> 详细信息</h3></div><div><div>📊</div><p>K线图表和技术指标</p><p>这里将显示选中股票的详细分析图表");function Pi(){const e=[{symbol:"000725",name:"京东方A",price:3.45,change:-1.43,changePercent:-29.3,volume:7340750,high:3.52,low:3.4,open:3.48,marketCap:12e10},{symbol:"300519",name:"新光药业",price:68.94,change:-1.05,changePercent:-1.5,volume:410750,high:70.1,low:68.5,open:69.9,marketCap:28e9},{symbol:"000831",name:"五矿稀土",price:26.09,change:-1.37,changePercent:-5,volume:7558720,high:27.5,low:25.8,open:27.2,marketCap:45e9},{symbol:"000001",name:"平安银行",price:12.45,change:.23,changePercent:1.88,volume:1568e4,high:12.58,low:12.2,open:12.3,marketCap:24e10},{symbol:"000002",name:"万科A",price:8.76,change:-.15,changePercent:-1.68,volume:895e4,high:8.95,low:8.65,open:8.85,marketCap:98e9}],[t,n]=H(e),o=Ig(),[r,i]=H("AAPL"),[l,s]=H("");Y(()=>{const p=o.marketData();p.size>0&&n(g=>g.map(u=>{const h=p.get(u.symbol);return h?{...u,price:h.price,change:h.change,changePercent:h.changePercent,volume:h.volume}:u}))}),pt(()=>{console.log("Market page mounted");const p=e.map(h=>h.symbol);o.subscribeToMarketData(p);let g;setTimeout(()=>{g=setInterval(()=>{o.connectionStatus()!=="connected"&&n(h=>h.map(b=>({...b,price:Math.max(.01,b.price+(Math.random()-.5)*2),change:b.change+(Math.random()-.5)*.5,changePercent:b.changePercent+(Math.random()-.5)*.2,volume:Math.max(0,b.volume+Math.floor((Math.random()-.5)*1e5))})))},3e3)},2e3),ee(()=>{g&&clearInterval(g),o.unsubscribeFromMarketData(p)})});const a=()=>{const p=l().toLowerCase();return t().filter(g=>g.symbol.toLowerCase().includes(p)||g.name.toLowerCase().includes(p))},c=(p,g=2)=>new Intl.NumberFormat("zh-CN",{minimumFractionDigits:g,maximumFractionDigits:g}).format(p),m=p=>p>=1e6?`${(p/1e6).toFixed(1)}M`:p>=1e3?`${(p/1e3).toFixed(1)}K`:p.toString();return(()=>{var p=zg(),g=p.firstChild,u=g.firstChild,h=u.firstChild,b=h.nextSibling,S=b.firstChild,x=S.nextSibling,y=x.nextSibling,v=y.firstChild,_=v.nextSibling,w=u.nextSibling,M=g.nextSibling,A=M.firstChild,I=A.firstChild,F=I.nextSibling,D=A.nextSibling,T=D.firstChild,P=T.firstChild,z=P.nextSibling,B=z.nextSibling,K=T.nextSibling,j=K.firstChild,W=j.nextSibling,q=W.nextSibling,Z=K.nextSibling,J=Z.firstChild,E=J.nextSibling,ne=E.nextSibling,se=Z.nextSibling,fe=se.firstChild,pe=fe.nextSibling,Se=pe.nextSibling,Ce=M.nextSibling,k=Ce.firstChild,X=k.firstChild,le=X.nextSibling,he=le.firstChild,de=he.nextSibling;de.nextSibling;var me=k.nextSibling,re=me.firstChild,ue=re.nextSibling,Pe=ue.firstChild,ce=Ce.nextSibling,oe=ce.firstChild,we=oe.firstChild,$e=oe.nextSibling,Ee=$e.firstChild,Ae=Ee.firstChild,Ne=Ae.firstChild,Ge=Ne.firstChild,je=Ge.nextSibling,vt=je.nextSibling,xt=vt.nextSibling,bt=xt.nextSibling,yt=bt.nextSibling,St=yt.nextSibling,Ct=St.nextSibling,Ot=Ct.nextSibling,Vt=Ae.nextSibling,wt=$e.nextSibling,Je=wt.firstChild,Bt=Je.firstChild,tn=Bt.nextSibling;tn.nextSibling;var Kt=Je.nextSibling,nn=Kt.firstChild;return C(_,()=>o.connectionStatus()==="connected"?"实时连接":"模拟数据"),C(w,(()=>{var $=Q(()=>o.connectionStatus()!=="connected");return()=>$()&&R(Me,{variant:"primary",onClick:()=>o.reconnect(),children:"重新连接"})})(),null),C(w,R(Me,{children:"导出数据"}),null),C(w,R(Me,{variant:"success",children:"自选股"}),null),C(F,R(Me,{size:"sm",variant:"primary",children:"日"}),null),C(F,R(Me,{size:"sm",children:"周"}),null),C(F,R(Me,{size:"sm",children:"月"}),null),C(le,()=>a().length,de),C(re,R(Tg,{placeholder:"搜索股票代码或名称",get value(){return l()},onInput:$=>s($.currentTarget.value)}),null),C(re,R(Me,{variant:"primary",children:"搜索"}),null),C(ue,R(Me,{size:"sm",variant:"primary",children:"全部"}),null),C(ue,R(Me,{size:"sm",children:"沪A"}),null),C(ue,R(Me,{size:"sm",children:"深A"}),null),C(ue,R(Me,{size:"sm",children:"创业板"}),null),C(Vt,()=>a().map($=>(()=>{var De=Lg(),ze=De.firstChild,Qe=ze.nextSibling,_t=Qe.nextSibling,et=_t.nextSibling,Ue=et.nextSibling,Wt=Ue.firstChild,Rt=Ue.nextSibling,ve=Rt.nextSibling,at=ve.nextSibling,kt=at.nextSibling,ct=kt.firstChild;return De.$$click=()=>i($.symbol),C(ze,()=>$.symbol),C(Qe,()=>$.name),C(_t,()=>c($.price)),C(et,()=>$.change>=0?"+":"",null),C(et,()=>c($.change),null),C(Ue,()=>$.changePercent>=0?"+":"",Wt),C(Ue,()=>c($.changePercent),Wt),C(Rt,()=>m($.volume)),C(ve,()=>c($.high)),C(at,()=>c($.low)),C(ct,R(Me,{size:"sm",variant:"danger",children:"卖"}),null),C(ct,R(Me,{size:"sm",variant:"success",children:"买"}),null),O(ie=>{var It=d({borderBottom:"1px solid #f0f0f0",cursor:"pointer",transition:"background-color 0.2s",backgroundColor:r()===$.symbol?"#e6f7ff":"transparent",_hover:{backgroundColor:"#fafafa"}}),Pt=d({padding:"12px 16px",fontSize:"14px",fontWeight:"600",color:"#262626"}),on=d({padding:"12px 16px",fontSize:"14px",color:"#262626"}),rn=d({padding:"12px 16px",textAlign:"right",fontSize:"14px",fontWeight:"600",color:"#262626"}),ln=d({padding:"12px 16px",textAlign:"right",fontSize:"14px",fontWeight:"500",color:$.change>=0?"#52c41a":"#f5222d"}),sn=d({padding:"12px 16px",textAlign:"right",fontSize:"14px",fontWeight:"500",color:$.changePercent>=0?"#52c41a":"#f5222d"}),an=d({padding:"12px 16px",textAlign:"right",fontSize:"14px",color:"#8c8c8c"}),cn=d({padding:"12px 16px",textAlign:"right",fontSize:"14px",color:"#8c8c8c"}),dn=d({padding:"12px 16px",textAlign:"right",fontSize:"14px",color:"#8c8c8c"}),un=d({padding:"12px 16px",textAlign:"right"}),gn=d({display:"flex",gap:"4px",justifyContent:"flex-end"});return It!==ie.e&&f(De,ie.e=It),Pt!==ie.t&&f(ze,ie.t=Pt),on!==ie.a&&f(Qe,ie.a=on),rn!==ie.o&&f(_t,ie.o=rn),ln!==ie.i&&f(et,ie.i=ln),sn!==ie.n&&f(Ue,ie.n=sn),an!==ie.s&&f(Rt,ie.s=an),cn!==ie.h&&f(ve,ie.h=cn),dn!==ie.r&&f(at,ie.r=dn),un!==ie.d&&f(kt,ie.d=un),gn!==ie.l&&f(ct,ie.l=gn),ie},{e:void 0,t:void 0,a:void 0,o:void 0,i:void 0,n:void 0,s:void 0,h:void 0,r:void 0,d:void 0,l:void 0}),De})())),C(Je,()=>a().length,tn),C(Kt,R(Me,{size:"sm",children:"上一页"}),nn),C(Kt,R(Me,{size:"sm",children:"下一页"}),null),C(p,(()=>{var $=Q(()=>!!r());return()=>$()&&(()=>{var De=Og(),ze=De.firstChild,Qe=ze.firstChild,_t=Qe.firstChild,et=ze.nextSibling,Ue=et.firstChild,Wt=Ue.nextSibling,Rt=Wt.nextSibling;return C(Qe,r,_t),O(ve=>{var at=d({marginTop:"24px",backgroundColor:"white",borderRadius:"8px",border:"1px solid #e8e8e8",overflow:"hidden"}),kt=d({padding:"20px",borderBottom:"1px solid #e8e8e8"}),ct=d({fontSize:"16px",fontWeight:"600",color:"#262626",margin:"0"}),ie=d({padding:"20px",textAlign:"center",color:"#8c8c8c"}),It=d({fontSize:"48px",marginBottom:"16px"}),Pt=d({fontSize:"12px"});return at!==ve.e&&f(De,ve.e=at),kt!==ve.t&&f(ze,ve.t=kt),ct!==ve.a&&f(Qe,ve.a=ct),ie!==ve.o&&f(et,ve.o=ie),It!==ve.i&&f(Ue,ve.i=It),Pt!==ve.n&&f(Rt,ve.n=Pt),ve},{e:void 0,t:void 0,a:void 0,o:void 0,i:void 0,n:void 0}),De})()})(),null),O($=>{var De=d({padding:"16px",backgroundColor:"#f0f2f5",minHeight:"100%"}),ze=d({display:"flex",alignItems:"center",justifyContent:"space-between",marginBottom:"16px"}),Qe=d({fontSize:"20px",fontWeight:"600",color:"#262626",margin:0,marginBottom:"4px"}),_t=d({display:"flex",alignItems:"center",gap:"12px",fontSize:"12px",color:"#8c8c8c"}),et=d({padding:"2px 6px",backgroundColor:"#f6ffed",color:"#52c41a",borderRadius:"2px",fontSize:"11px"}),Ue=d({display:"flex",alignItems:"center",gap:"4px"}),Wt=d({width:"6px",height:"6px",borderRadius:"50%",backgroundColor:o.connectionStatus()==="connected"?"#52c41a":"#faad14"}),Rt=d({display:"flex",alignItems:"center",gap:"8px"}),ve=d({marginBottom:"16px",backgroundColor:"white",borderRadius:"8px",border:"1px solid #e8e8e8",padding:"16px"}),at=d({display:"flex",alignItems:"center",justifyContent:"space-between",marginBottom:"12px"}),kt=d({fontSize:"16px",fontWeight:"600",color:"#262626",margin:0}),ct=d({display:"flex",alignItems:"center",gap:"8px"}),ie=d({display:"grid",gridTemplateColumns:"repeat(auto-fit, minmax(150px, 1fr))",gap:"16px"}),It=d({padding:"12px",backgroundColor:"#fafafa",borderRadius:"4px",textAlign:"center"}),Pt=d({fontSize:"12px",color:"#8c8c8c",marginBottom:"4px"}),on=d({fontSize:"16px",fontWeight:"600",color:"#f5222d",marginBottom:"2px"}),rn=d({fontSize:"11px",color:"#f5222d"}),ln=d({padding:"12px",backgroundColor:"#fafafa",borderRadius:"4px",textAlign:"center"}),sn=d({fontSize:"12px",color:"#8c8c8c",marginBottom:"4px"}),an=d({fontSize:"16px",fontWeight:"600",color:"#52c41a",marginBottom:"2px"}),cn=d({fontSize:"11px",color:"#52c41a"}),dn=d({padding:"12px",backgroundColor:"#fafafa",borderRadius:"4px",textAlign:"center"}),un=d({fontSize:"12px",color:"#8c8c8c",marginBottom:"4px"}),gn=d({fontSize:"16px",fontWeight:"600",color:"#f5222d",marginBottom:"2px"}),sr=d({fontSize:"11px",color:"#f5222d"}),ar=d({padding:"12px",backgroundColor:"#fafafa",borderRadius:"4px",textAlign:"center"}),cr=d({fontSize:"12px",color:"#8c8c8c",marginBottom:"4px"}),dr=d({fontSize:"16px",fontWeight:"600",color:"#52c41a",marginBottom:"2px"}),ur=d({fontSize:"11px",color:"#52c41a"}),gr=d({marginBottom:"16px",backgroundColor:"white",borderRadius:"8px",border:"1px solid #e8e8e8",padding:"12px 16px"}),fr=d({display:"flex",alignItems:"center",justifyContent:"space-between",marginBottom:"12px"}),pr=d({fontSize:"14px",fontWeight:"600",color:"#262626",margin:0}),hr=d({fontSize:"12px",color:"#8c8c8c"}),mr=d({display:"flex",alignItems:"center",justifyContent:"space-between"}),vr=d({display:"flex",alignItems:"center",gap:"8px"}),xr=d({display:"flex",alignItems:"center",gap:"6px"}),br=d({fontSize:"12px",color:"#8c8c8c",marginRight:"4px"}),yr=d({backgroundColor:"white",borderRadius:"8px",border:"1px solid #e8e8e8",overflow:"hidden"}),Sr=d({padding:"16px 20px",borderBottom:"1px solid #e8e8e8",backgroundColor:"#fafafa"}),Cr=d({fontSize:"16px",fontWeight:"600",color:"#262626",margin:"0"}),wr=d({overflowX:"auto"}),_r=d({width:"100%",borderCollapse:"collapse"}),Rr=d({backgroundColor:"#fafafa"}),kr=d({padding:"12px 16px",textAlign:"left",fontSize:"12px",fontWeight:"600",color:"#8c8c8c"}),Ir=d({padding:"12px 16px",textAlign:"left",fontSize:"12px",fontWeight:"600",color:"#8c8c8c"}),Pr=d({padding:"12px 16px",textAlign:"right",fontSize:"12px",fontWeight:"600",color:"#8c8c8c"}),$r=d({padding:"12px 16px",textAlign:"right",fontSize:"12px",fontWeight:"600",color:"#8c8c8c"}),Mr=d({padding:"12px 16px",textAlign:"right",fontSize:"12px",fontWeight:"600",color:"#8c8c8c"}),Fr=d({padding:"12px 16px",textAlign:"right",fontSize:"12px",fontWeight:"600",color:"#8c8c8c"}),Er=d({padding:"12px 16px",textAlign:"right",fontSize:"12px",fontWeight:"600",color:"#8c8c8c"}),Ar=d({padding:"12px 16px",textAlign:"right",fontSize:"12px",fontWeight:"600",color:"#8c8c8c"}),Dr=d({padding:"12px 16px",textAlign:"right",fontSize:"12px",fontWeight:"600",color:"#8c8c8c"}),Tr=d({padding:"16px 20px",borderTop:"1px solid #f0f0f0",display:"flex",justifyContent:"space-between",alignItems:"center"}),zr=d({fontSize:"14px",color:"#8c8c8c"}),Lr=d({display:"flex",gap:"8px",alignItems:"center"}),Or=d({padding:"4px 8px",backgroundColor:"primary.500",color:"white",borderRadius:"4px",fontSize:"12px"});return De!==$.e&&f(p,$.e=De),ze!==$.t&&f(g,$.t=ze),Qe!==$.a&&f(h,$.a=Qe),_t!==$.o&&f(b,$.o=_t),et!==$.i&&f(S,$.i=et),Ue!==$.n&&f(y,$.n=Ue),Wt!==$.s&&f(v,$.s=Wt),Rt!==$.h&&f(w,$.h=Rt),ve!==$.r&&f(M,$.r=ve),at!==$.d&&f(A,$.d=at),kt!==$.l&&f(I,$.l=kt),ct!==$.u&&f(F,$.u=ct),ie!==$.c&&f(D,$.c=ie),It!==$.w&&f(T,$.w=It),Pt!==$.m&&f(P,$.m=Pt),on!==$.f&&f(z,$.f=on),rn!==$.y&&f(B,$.y=rn),ln!==$.g&&f(K,$.g=ln),sn!==$.p&&f(j,$.p=sn),an!==$.b&&f(W,$.b=an),cn!==$.T&&f(q,$.T=cn),dn!==$.A&&f(Z,$.A=dn),un!==$.O&&f(J,$.O=un),gn!==$.I&&f(E,$.I=gn),sr!==$.S&&f(ne,$.S=sr),ar!==$.W&&f(se,$.W=ar),cr!==$.C&&f(fe,$.C=cr),dr!==$.B&&f(pe,$.B=dr),ur!==$.v&&f(Se,$.v=ur),gr!==$.k&&f(Ce,$.k=gr),fr!==$.x&&f(k,$.x=fr),pr!==$.j&&f(X,$.j=pr),hr!==$.q&&f(le,$.q=hr),mr!==$.z&&f(me,$.z=mr),vr!==$.P&&f(re,$.P=vr),xr!==$.H&&f(ue,$.H=xr),br!==$.F&&f(Pe,$.F=br),yr!==$.M&&f(ce,$.M=yr),Sr!==$.D&&f(oe,$.D=Sr),Cr!==$.R&&f(we,$.R=Cr),wr!==$.E&&f($e,$.E=wr),_r!==$.L&&f(Ee,$.L=_r),Rr!==$.N&&f(Ne,$.N=Rr),kr!==$.G&&f(Ge,$.G=kr),Ir!==$.U&&f(je,$.U=Ir),Pr!==$.K&&f(vt,$.K=Pr),$r!==$.V&&f(xt,$.V=$r),Mr!==$.Y&&f(bt,$.Y=Mr),Fr!==$.J&&f(yt,$.J=Fr),Er!==$.Q&&f(St,$.Q=Er),Ar!==$.Z&&f(Ct,$.Z=Ar),Dr!==$.X&&f(Ot,$.X=Dr),Tr!==$._&&f(wt,$._=Tr),zr!==$.$&&f(Je,$.$=zr),Lr!==$.te&&f(Kt,$.te=Lr),Or!==$.tt&&f(nn,$.tt=Or),$},{e:void 0,t:void 0,a:void 0,o:void 0,i:void 0,n:void 0,s:void 0,h:void 0,r:void 0,d:void 0,l:void 0,u:void 0,c:void 0,w:void 0,m:void 0,f:void 0,y:void 0,g:void 0,p:void 0,b:void 0,T:void 0,A:void 0,O:void 0,I:void 0,S:void 0,W:void 0,C:void 0,B:void 0,v:void 0,k:void 0,x:void 0,j:void 0,q:void 0,z:void 0,P:void 0,H:void 0,F:void 0,M:void 0,D:void 0,R:void 0,E:void 0,L:void 0,N:void 0,G:void 0,U:void 0,K:void 0,V:void 0,Y:void 0,J:void 0,Q:void 0,Z:void 0,X:void 0,_:void 0,$:void 0,te:void 0,tt:void 0}),p})()}Tt(["click"]);var On=V("<span>"),Vg=V("<span>¥"),ho=V("<div>"),Bg=V("<div><div><div><h1>实时行情</h1><div></div></div></div><div><div>"),Kg=V("<div><span>");function Wg(){const[e,t]=H(""),[n,o]=H("all"),[r,i]=H(!1),[l,s]=H([]),[a,c]=H([]),m=[{code:"000001",name:"平安银行",price:12.45,change:.23,changePercent:1.88,volume:125e6,turnover:155e7,high:12.68,low:12.2,open:12.3,status:"trading"},{code:"000002",name:"万科A",price:18.76,change:-.45,changePercent:-2.34,volume:89e6,turnover:167e7,high:19.2,low:18.5,open:19.1,status:"trading"},{code:"600036",name:"招商银行",price:35.67,change:1.23,changePercent:3.57,volume:67e6,turnover:239e7,high:36,low:34.8,open:35,status:"trading"},{code:"600519",name:"贵州茅台",price:1678.9,change:-12.5,changePercent:-.74,volume:21e5,turnover:352e7,high:1695,low:1670,open:1685,status:"trading"},{code:"000858",name:"五粮液",price:156.78,change:2.34,changePercent:1.52,volume:156e5,turnover:244e7,high:158.9,low:154.2,open:155,status:"suspended"}],p=[{name:"上证指数",value:3245.67,change:23.45,changePercent:.73},{name:"深证成指",value:12456.78,change:-45.23,changePercent:-.36},{name:"创业板指",value:2567.89,change:12.34,changePercent:.48},{name:"科创50",value:1234.56,change:-8.9,changePercent:-.72}],g=[{key:"all",label:"全部板块",onClick:()=>o("all")},{key:"bank",label:"银行",onClick:()=>o("bank")},{key:"tech",label:"科技",onClick:()=>o("tech")},{key:"consumer",label:"消费",onClick:()=>o("consumer")},{key:"healthcare",label:"医药",onClick:()=>o("healthcare")}],u=[{accessorKey:"code",header:"代码",cell:x=>(()=>{var y=On();return C(y,()=>x.getValue()),O(()=>f(y,d({fontFamily:"monospace",fontWeight:"500"}))),y})()},{accessorKey:"name",header:"名称",cell:x=>(()=>{var y=On();return C(y,()=>x.getValue()),O(()=>f(y,d({fontWeight:"500",color:"text.primary"}))),y})()},{accessorKey:"price",header:"现价",cell:x=>{const y=x.row.original,v=y.change>0?"danger.500":y.change<0?"success.500":"text.regular";return(()=>{var _=Vg();return _.firstChild,C(_,()=>x.getValue().toFixed(2),null),O(()=>f(_,d({fontFamily:"monospace",fontWeight:"600",color:v}))),_})()}},{accessorKey:"change",header:"涨跌额",cell:x=>{const y=x.getValue(),v=y>0?"danger.500":y<0?"success.500":"text.regular",_=y>0?"+":"";return(()=>{var w=On();return C(w,_,null),C(w,()=>y.toFixed(2),null),O(()=>f(w,d({fontFamily:"monospace",color:v}))),w})()}},{accessorKey:"changePercent",header:"涨跌幅",cell:x=>{const y=x.getValue(),v=y>0?"danger":y<0?"success":"info",_=y>0?"+":"";return R(Vn,{type:v,effect:"light",size:"small",get children(){return[_,Q(()=>y.toFixed(2)),"%"]}})}},{accessorKey:"volume",header:"成交量",cell:x=>{const y=x.getValue(),v=y>1e8?`${(y/1e8).toFixed(1)}亿`:`${(y/1e4).toFixed(0)}万`;return(()=>{var _=On();return C(_,v),O(()=>f(_,d({fontFamily:"monospace",fontSize:"13px"}))),_})()}},{accessorKey:"status",header:"状态",cell:x=>{const y=x.getValue(),v={trading:{label:"交易中",type:"success"},suspended:{label:"停牌",type:"warning"},closed:{label:"休市",type:"info"}},_=v[y]||v.closed;return R(Vn,{get type(){return _.type},effect:"light",size:"small",get children(){return _.label}})}},{id:"actions",header:"操作",cell:x=>(()=>{var y=ho();return C(y,R(Ie,{size:"small",variant:"primary",onClick:()=>h(x.row.original),children:"买入"}),null),C(y,R(Ie,{size:"small",variant:"danger",onClick:()=>b(x.row.original),children:"卖出"}),null),O(()=>f(y,d({display:"flex",gap:"8px"}))),y})()}],h=x=>{pn.success(`买入 ${x.name} 操作已提交`)},b=x=>{pn.error(`卖出 ${x.name} 操作已提交`)},S=()=>{i(!0),pn.info("正在刷新数据..."),setTimeout(()=>{i(!1),pn.success("数据刷新完成")},1500)};return pt(()=>{s(m),c(p)}),(()=>{var x=Bg(),y=x.firstChild,v=y.firstChild,_=v.firstChild,w=_.nextSibling,M=y.nextSibling,A=M.firstChild;return C(w,R(Hi,{placeholder:"搜索股票代码或名称...",get value(){return e()},onInput:I=>t(I.currentTarget.value),clearable:!0,onClear:()=>t(""),get class(){return d({width:"240px"})}}),null),C(w,R(dg,{items:g,get trigger(){return R(Ie,{variant:"default",size:"default",get children(){return[Q(()=>g.find(I=>I.key===n())?.label)," ▼"]}})}}),null),C(w,R(Ie,{variant:"primary",onClick:S,get loading(){return r()},children:"刷新"}),null),C(A,R(Fe,{get each(){return a()},children:I=>R(Kn,{shadow:"hover",bodyStyle:{padding:"16px",textAlign:"center"},get children(){return[(()=>{var F=ho();return C(F,()=>I.name),O(()=>f(F,d({fontSize:"14px",color:"text.secondary",marginBottom:"8px"}))),F})(),(()=>{var F=ho();return C(F,()=>I.value.toFixed(2)),O(()=>f(F,d({fontSize:"20px",fontWeight:"600",color:"text.primary",marginBottom:"4px",fontFamily:"monospace"}))),F})(),(()=>{var F=Kg(),D=F.firstChild;return C(D,()=>I.change>0?"+":"",null),C(D,()=>I.change.toFixed(2),null),C(F,R(Vn,{get type(){return Q(()=>I.changePercent>0)()?"danger":I.changePercent<0?"success":"info"},effect:"light",size:"small",get children(){return[Q(()=>I.changePercent>0?"+":""),Q(()=>I.changePercent.toFixed(2)),"%"]}}),null),O(T=>{var P=d({display:"flex",alignItems:"center",justifyContent:"center",gap:"8px"}),z=d({fontSize:"12px",color:I.change>0?"danger.500":I.change<0?"success.500":"text.regular",fontFamily:"monospace"});return P!==T.e&&f(F,T.e=P),z!==T.t&&f(D,T.t=z),T},{e:void 0,t:void 0}),F})()]}})})),C(x,R(Kn,{header:"股票列表",shadow:"always",get children(){return R(Za,{get data(){return l()},columns:u,get loading(){return r()},pagination:!0,pageSize:10,sortable:!0,sticky:!0,height:"600px",onRowClick:I=>pn.info(`查看 ${I.name} 详情`)})}}),null),O(I=>{var F=d({padding:"20px",minHeight:"100vh",backgroundColor:"bg.page"}),D=d({marginBottom:"20px"}),T=d({display:"flex",alignItems:"center",justifyContent:"space-between",marginBottom:"16px"}),P=d({fontSize:"24px",fontWeight:"600",color:"text.primary",margin:0}),z=d({display:"flex",alignItems:"center",gap:"12px"}),B=d({marginBottom:"24px"}),K=d({display:"grid",gridTemplateColumns:"repeat(auto-fit, minmax(200px, 1fr))",gap:"16px"});return F!==I.e&&f(x,I.e=F),D!==I.t&&f(y,I.t=D),T!==I.a&&f(v,I.a=T),P!==I.o&&f(_,I.o=P),z!==I.i&&f(w,I.i=z),B!==I.n&&f(M,I.n=B),K!==I.s&&f(A,I.s=K),I},{e:void 0,t:void 0,a:void 0,o:void 0,i:void 0,n:void 0,s:void 0}),x})()}var Hg=V("<div><div>");function Ng(e){const[t,n]=H();let o;return pt(async()=>{const r=t();if(r)try{const l="/libs/monaco-editor/min/vs";console.log(`Monaco Editor 配置: 本地模式, 路径: ${l}`),Br.config({paths:{vs:l}});const s=await Br.init();e.language==="python"&&s.languages.registerCompletionItemProvider("python",{provideCompletionItems:(a,c)=>{const m=a.getWordUntilPosition(c),p={startLineNumber:c.lineNumber,endLineNumber:c.lineNumber,startColumn:m.startColumn,endColumn:m.endColumn};return{suggestions:[{label:"def",kind:s.languages.CompletionItemKind.Keyword,insertText:"def ${1:function_name}(${2:parameters}):\n    ${3:pass}",insertTextRules:s.languages.CompletionItemInsertTextRule.InsertAsSnippet,documentation:"Define a function",range:p},{label:"initialize",kind:s.languages.CompletionItemKind.Function,insertText:"def initialize(context):\n    ${1:pass}",insertTextRules:s.languages.CompletionItemInsertTextRule.InsertAsSnippet,documentation:"策略初始化函数",range:p},{label:"handle_data",kind:s.languages.CompletionItemKind.Function,insertText:"def handle_data(context, data):\n    ${1:pass}",insertTextRules:s.languages.CompletionItemInsertTextRule.InsertAsSnippet,documentation:"主要的交易逻辑函数",range:p},{label:"order_target_percent",kind:s.languages.CompletionItemKind.Function,insertText:"order_target_percent(${1:security}, ${2:percent})",insertTextRules:s.languages.CompletionItemInsertTextRule.InsertAsSnippet,documentation:"下单到目标百分比",range:p},{label:"attribute_history",kind:s.languages.CompletionItemKind.Function,insertText:"attribute_history(${1:security}, ${2:count}, ${3:unit}, ${4:fields})",insertTextRules:s.languages.CompletionItemInsertTextRule.InsertAsSnippet,documentation:"获取历史数据",range:p},{label:"log.info",kind:s.languages.CompletionItemKind.Function,insertText:"log.info(${1:message})",insertTextRules:s.languages.CompletionItemInsertTextRule.InsertAsSnippet,documentation:"输出日志信息",range:p}]}}}),o=s.editor.create(r,{value:e.value||"",language:e.language||"python",theme:e.theme||"vs",fontSize:13,lineNumbers:"on",roundedSelection:!1,scrollBeyondLastLine:!1,minimap:{enabled:!0},automaticLayout:!0,tabSize:4,insertSpaces:!0,wordWrap:"on",folding:!0,renderLineHighlight:"all",selectOnLineNumbers:!0,matchBrackets:"always",...e.options}),o.onDidChangeModelContent(()=>{e.onChange&&o&&e.onChange(o.getValue())}),o.addCommand(s.KeyMod.CtrlCmd|s.KeyCode.KeyS,()=>{console.log("保存策略快捷键触发")}),o.addCommand(s.KeyMod.CtrlCmd|s.KeyCode.Enter,()=>{console.log("运行策略快捷键触发")})}catch(i){console.error("Monaco Editor 初始化失败:",i)}}),ee(()=>{o&&o.dispose()}),(()=>{var r=Hg(),i=r.firstChild;return gs(n,i),O(l=>{var s=d({width:"100%",height:`${e.height||400}px`,border:"1px solid #e5e7eb",borderRadius:"6px",overflow:"hidden"}),a=d({width:"100%",height:"100%"});return s!==l.e&&f(r,l.e=s),a!==l.t&&f(i,l.t=a),l},{e:void 0,t:void 0}),r})()}var Gg=V('<div><div><h1>🧠 策略编辑器</h1><p>创建和编辑量化交易策略</p></div><div><div><div><h2>策略代码</h2><div><button>运行回测</button><button>保存策略</button></div></div><div></div></div><div><div><h2>回测结果</h2></div><div><div><div>📊</div><p>等待回测结果</p><p>点击"运行回测"开始策略测试</p></div></div></div></div><div><h3>策略模板</h3><div><button><div>双均线策略</div><div>基于移动平均线的经典策略</div></button><button><div>RSI策略</div><div>基于相对强弱指标的策略</div></button><button><div>布林带策略</div><div>利用布林带进行交易决策</div></button><button><div>机器学习策略</div><div>基于AI模型的量化策略');function jg(){const[e,t]=H(`# 量化策略示例
# 这是一个简单的移动平均线策略

def initialize(context):
    # 设置基准和股票
    g.benchmark = '000300.XSHG'
    g.stock = '000001.XSHE'
    
def handle_data(context, data):
    # 获取历史价格
    hist = attribute_history(g.stock, 20, '1d', ['close'])
    ma5 = hist['close'][-5:].mean()
    ma20 = hist['close'][-20:].mean()
    current_price = data[g.stock].close
    
    # 交易逻辑
    if ma5 > ma20 and current_price > ma5:
        # 金叉买入信号
        order_target_percent(g.stock, 0.8)
        log.info(f"买入信号，价格: {current_price}")
    elif ma5 < ma20:
        # 死叉卖出信号
        order_target_percent(g.stock, 0)
        log.info(f"卖出信号，价格: {current_price}")
`);return(()=>{var n=Gg(),o=n.firstChild,r=o.firstChild,i=r.nextSibling,l=o.nextSibling,s=l.firstChild,a=s.firstChild,c=a.firstChild,m=c.nextSibling,p=m.firstChild,g=p.nextSibling,u=a.nextSibling,h=s.nextSibling,b=h.firstChild,S=b.firstChild,x=b.nextSibling,y=x.firstChild,v=y.firstChild,_=v.nextSibling,w=_.nextSibling,M=l.nextSibling,A=M.firstChild,I=A.nextSibling,F=I.firstChild,D=F.firstChild,T=D.nextSibling,P=F.nextSibling,z=P.firstChild,B=z.nextSibling,K=P.nextSibling,j=K.firstChild,W=j.nextSibling,q=K.nextSibling,Z=q.firstChild,J=Z.nextSibling;return C(u,R(Ng,{get value(){return e()},language:"python",theme:"vs",height:500,onChange:t,options:{minimap:{enabled:!0},fontSize:14,wordWrap:"on",automaticLayout:!0}})),O(E=>{var ne=d({padding:"24px",maxWidth:"1400px",margin:"0 auto",height:"100%"}),se=d({marginBottom:"32px"}),fe=d({fontSize:"32px",fontWeight:"bold",color:"gray.900",marginBottom:"8px"}),pe=d({fontSize:"16px",color:"gray.600"}),Se=d({display:"grid",gridTemplateColumns:"1fr 1fr",gap:"24px",height:"calc(100vh - 200px)"}),Ce=d({backgroundColor:"white",borderRadius:"12px",boxShadow:"0 1px 3px rgba(0, 0, 0, 0.1)",border:"1px solid #e5e7eb",overflow:"hidden",display:"flex",flexDirection:"column"}),k=d({padding:"16px 20px",borderBottom:"1px solid #e5e7eb",backgroundColor:"#f9fafb",display:"flex",justifyContent:"space-between",alignItems:"center"}),X=d({fontSize:"16px",fontWeight:"600",color:"gray.900"}),le=d({display:"flex",gap:"8px"}),he=d({padding:"6px 12px",backgroundColor:"blue.600",color:"white",border:"none",borderRadius:"6px",fontSize:"12px",fontWeight:"500",cursor:"pointer",_hover:{backgroundColor:"blue.700"}}),de=d({padding:"6px 12px",backgroundColor:"green.600",color:"white",border:"none",borderRadius:"6px",fontSize:"12px",fontWeight:"500",cursor:"pointer",_hover:{backgroundColor:"green.700"}}),me=d({flex:1,padding:"8px"}),re=d({backgroundColor:"white",borderRadius:"12px",boxShadow:"0 1px 3px rgba(0, 0, 0, 0.1)",border:"1px solid #e5e7eb",overflow:"hidden",display:"flex",flexDirection:"column"}),ue=d({padding:"16px 20px",borderBottom:"1px solid #e5e7eb",backgroundColor:"#f9fafb"}),Pe=d({fontSize:"16px",fontWeight:"600",color:"gray.900"}),ce=d({flex:1,padding:"20px",display:"flex",alignItems:"center",justifyContent:"center"}),oe=d({textAlign:"center",color:"gray.500"}),we=d({fontSize:"48px",marginBottom:"16px"}),$e=d({fontSize:"16px",marginBottom:"8px"}),Ee=d({fontSize:"14px"}),Ae=d({marginTop:"24px",backgroundColor:"white",borderRadius:"12px",boxShadow:"0 1px 3px rgba(0, 0, 0, 0.1)",border:"1px solid #e5e7eb",padding:"20px"}),Ne=d({fontSize:"18px",fontWeight:"600",color:"gray.900",marginBottom:"16px"}),Ge=d({display:"grid",gridTemplateColumns:"repeat(auto-fit, minmax(200px, 1fr))",gap:"12px"}),je=d({padding:"12px 16px",border:"1px solid #e5e7eb",borderRadius:"8px",backgroundColor:"white",textAlign:"left",cursor:"pointer",transition:"all 0.2s",_hover:{borderColor:"blue.500",backgroundColor:"blue.50"}}),vt=d({fontSize:"14px",fontWeight:"500",color:"gray.900",marginBottom:"4px"}),xt=d({fontSize:"12px",color:"gray.600"}),bt=d({padding:"12px 16px",border:"1px solid #e5e7eb",borderRadius:"8px",backgroundColor:"white",textAlign:"left",cursor:"pointer",transition:"all 0.2s",_hover:{borderColor:"blue.500",backgroundColor:"blue.50"}}),yt=d({fontSize:"14px",fontWeight:"500",color:"gray.900",marginBottom:"4px"}),St=d({fontSize:"12px",color:"gray.600"}),Ct=d({padding:"12px 16px",border:"1px solid #e5e7eb",borderRadius:"8px",backgroundColor:"white",textAlign:"left",cursor:"pointer",transition:"all 0.2s",_hover:{borderColor:"blue.500",backgroundColor:"blue.50"}}),Ot=d({fontSize:"14px",fontWeight:"500",color:"gray.900",marginBottom:"4px"}),Vt=d({fontSize:"12px",color:"gray.600"}),wt=d({padding:"12px 16px",border:"1px solid #e5e7eb",borderRadius:"8px",backgroundColor:"white",textAlign:"left",cursor:"pointer",transition:"all 0.2s",_hover:{borderColor:"blue.500",backgroundColor:"blue.50"}}),Je=d({fontSize:"14px",fontWeight:"500",color:"gray.900",marginBottom:"4px"}),Bt=d({fontSize:"12px",color:"gray.600"});return ne!==E.e&&f(n,E.e=ne),se!==E.t&&f(o,E.t=se),fe!==E.a&&f(r,E.a=fe),pe!==E.o&&f(i,E.o=pe),Se!==E.i&&f(l,E.i=Se),Ce!==E.n&&f(s,E.n=Ce),k!==E.s&&f(a,E.s=k),X!==E.h&&f(c,E.h=X),le!==E.r&&f(m,E.r=le),he!==E.d&&f(p,E.d=he),de!==E.l&&f(g,E.l=de),me!==E.u&&f(u,E.u=me),re!==E.c&&f(h,E.c=re),ue!==E.w&&f(b,E.w=ue),Pe!==E.m&&f(S,E.m=Pe),ce!==E.f&&f(x,E.f=ce),oe!==E.y&&f(y,E.y=oe),we!==E.g&&f(v,E.g=we),$e!==E.p&&f(_,E.p=$e),Ee!==E.b&&f(w,E.b=Ee),Ae!==E.T&&f(M,E.T=Ae),Ne!==E.A&&f(A,E.A=Ne),Ge!==E.O&&f(I,E.O=Ge),je!==E.I&&f(F,E.I=je),vt!==E.S&&f(D,E.S=vt),xt!==E.W&&f(T,E.W=xt),bt!==E.C&&f(P,E.C=bt),yt!==E.B&&f(z,E.B=yt),St!==E.v&&f(B,E.v=St),Ct!==E.k&&f(K,E.k=Ct),Ot!==E.x&&f(j,E.x=Ot),Vt!==E.j&&f(W,E.j=Vt),wt!==E.q&&f(q,E.q=wt),Je!==E.z&&f(Z,E.z=Je),Bt!==E.P&&f(J,E.P=Bt),E},{e:void 0,t:void 0,a:void 0,o:void 0,i:void 0,n:void 0,s:void 0,h:void 0,r:void 0,d:void 0,l:void 0,u:void 0,c:void 0,w:void 0,m:void 0,f:void 0,y:void 0,g:void 0,p:void 0,b:void 0,T:void 0,A:void 0,O:void 0,I:void 0,S:void 0,W:void 0,C:void 0,B:void 0,v:void 0,k:void 0,x:void 0,j:void 0,q:void 0,z:void 0,P:void 0}),n})()}var Ug=V("<button>"),qg=V("<div>");const mo=e=>{const[t,n]=te(e,["variant","size","loading","icon","fullWidth","children","class","disabled"]),o=d({display:"inline-flex",alignItems:"center",justifyContent:"center",gap:"8px",borderRadius:"8px",fontWeight:"500",transition:"all 0.2s",cursor:"pointer",border:"none",outline:"none",textDecoration:"none",userSelect:"none",_focus:{boxShadow:"0 0 0 3px rgba(59, 130, 246, 0.1)"},_disabled:{opacity:.6,cursor:"not-allowed"}}),r={primary:d({backgroundColor:"blue.600",color:"white",_hover:{backgroundColor:"blue.700"},_active:{backgroundColor:"blue.800"}}),secondary:d({backgroundColor:"gray.100",color:"gray.900",_hover:{backgroundColor:"gray.200"},_active:{backgroundColor:"gray.300"}}),success:d({backgroundColor:"green.600",color:"white",_hover:{backgroundColor:"green.700"},_active:{backgroundColor:"green.800"}}),warning:d({backgroundColor:"yellow.500",color:"white",_hover:{backgroundColor:"yellow.600"},_active:{backgroundColor:"yellow.700"}}),danger:d({backgroundColor:"red.600",color:"white",_hover:{backgroundColor:"red.700"},_active:{backgroundColor:"red.800"}}),ghost:d({backgroundColor:"transparent",color:"gray.700",border:"1px solid",borderColor:"gray.300",_hover:{backgroundColor:"gray.50",borderColor:"gray.400"},_active:{backgroundColor:"gray.100"}})},i={sm:d({padding:"6px 12px",fontSize:"14px",minHeight:"32px"}),md:d({padding:"8px 16px",fontSize:"14px",minHeight:"40px"}),lg:d({padding:"12px 24px",fontSize:"16px",minHeight:"48px"})},l=d({width:"100%"}),s=d({width:"16px",height:"16px",border:"2px solid currentColor",borderTopColor:"transparent",borderRadius:"50%",animation:"spin 1s linear infinite"}),a=t.variant||"primary",c=t.size||"md";return(()=>{var m=Ug();return lt(m,U({get class(){return Nt(o,r[a],i[c],t.fullWidth&&l,t.class)},get disabled(){return t.disabled||t.loading}},n),!1,!0),C(m,(()=>{var p=Q(()=>!!t.loading);return()=>p()&&(()=>{var g=qg();return f(g,s),g})()})(),null),C(m,(()=>{var p=Q(()=>!!(!t.loading&&t.icon));return()=>p()&&t.icon})(),null),C(m,()=>t.children,null),m})()};var Xg=V("<div><div>"),Yg=V("<div><div><div>"),Zg=V("<h3>"),Jg=V("<p>");const qt=e=>{const[t,n]=te(e,["title","subtitle","headerAction","padding","shadow","border","hover","children","class"]),o=d({backgroundColor:"white",borderRadius:"12px",overflow:"hidden",transition:"all 0.2s"}),r={none:"",sm:d({boxShadow:"0 1px 2px rgba(0, 0, 0, 0.05)"}),md:d({boxShadow:"0 4px 6px rgba(0, 0, 0, 0.07)"}),lg:d({boxShadow:"0 10px 15px rgba(0, 0, 0, 0.1)"})},i=d({border:"1px solid",borderColor:"gray.200"}),l=d({_hover:{transform:"translateY(-2px)",boxShadow:"0 8px 25px rgba(0, 0, 0, 0.1)"}}),s={none:"",sm:d({padding:"16px"}),md:d({padding:"24px"}),lg:d({padding:"32px"})},a=d({padding:"24px 24px 0 24px",marginBottom:"16px"}),c=d({fontSize:"18px",fontWeight:"600",color:"gray.900",marginBottom:"4px"}),m=d({fontSize:"14px",color:"gray.600"}),p=d({display:"flex",justifyContent:"space-between",alignItems:"flex-start"}),g=t.shadow||"md",u=t.padding||"md";return(()=>{var h=Xg(),b=h.firstChild;return lt(h,U({get class(){return Nt(o,r[g],t.border&&i,t.hover&&l,t.class)}},n),!1,!0),C(h,(()=>{var S=Q(()=>!!(t.title||t.subtitle||t.headerAction));return()=>S()&&(()=>{var x=Yg(),y=x.firstChild,v=y.firstChild;return f(x,a),C(v,(()=>{var _=Q(()=>!!t.title);return()=>_()&&(()=>{var w=Zg();return f(w,c),C(w,()=>t.title),w})()})(),null),C(v,(()=>{var _=Q(()=>!!t.subtitle);return()=>_()&&(()=>{var w=Jg();return f(w,m),C(w,()=>t.subtitle),w})()})(),null),C(y,(()=>{var _=Q(()=>!!t.headerAction);return()=>_()&&t.headerAction})(),null),O(()=>f(y,t.headerAction?p:"")),x})()})(),b),C(b,()=>t.children),O(()=>f(b,s[u])),h})()};var Qg=V('<button aria-label="Close modal">×'),ef=V("<div><h2><span>"),tf=V("<div>"),nf=V("<div><div><div>");const of=e=>{Y(()=>{if(e.isOpen){const p=g=>{g.key==="Escape"&&e.closable!==!1&&e.onClose()};document.addEventListener("keydown",p),document.body.style.overflow="hidden",ee(()=>{document.removeEventListener("keydown",p),document.body.style.overflow=""})}});const t=d({position:"fixed",top:0,left:0,right:0,bottom:0,backgroundColor:"rgba(0, 0, 0, 0.5)",display:"flex",alignItems:"center",justifyContent:"center",zIndex:1e3,padding:"16px"}),n=d({backgroundColor:"white",borderRadius:"12px",boxShadow:"0 20px 25px rgba(0, 0, 0, 0.1)",maxHeight:"90vh",overflow:"hidden",display:"flex",flexDirection:"column",animation:"modalEnter 0.2s ease-out"}),o={sm:d({width:"400px",maxWidth:"90vw"}),md:d({width:"600px",maxWidth:"90vw"}),lg:d({width:"800px",maxWidth:"90vw"}),xl:d({width:"1000px",maxWidth:"90vw"})},r=d({padding:"24px 24px 0 24px",borderBottom:"1px solid",borderColor:"gray.200",paddingBottom:"16px",marginBottom:"24px"}),i=d({fontSize:"20px",fontWeight:"600",color:"gray.900",margin:0,display:"flex",justifyContent:"space-between",alignItems:"center"}),l=d({background:"none",border:"none",fontSize:"24px",cursor:"pointer",color:"gray.400",padding:"4px",borderRadius:"4px",_hover:{color:"gray.600",backgroundColor:"gray.100"}}),s=d({padding:"0 24px",flex:1,overflow:"auto"}),a=d({padding:"16px 24px 24px 24px",borderTop:"1px solid",borderColor:"gray.200",marginTop:"24px",display:"flex",justifyContent:"flex-end",gap:"12px"}),c=e.size||"md",m=p=>{p.target===p.currentTarget&&e.maskClosable!==!1&&e.onClose()};return R(ae,{get when(){return e.isOpen},get children(){return R(Do,{get children(){var p=nf(),g=p.firstChild,u=g.firstChild;return p.$$click=m,f(p,t),g.$$click=h=>h.stopPropagation(),C(g,R(ae,{get when(){return e.title||e.closable!==!1},get children(){var h=ef(),b=h.firstChild,S=b.firstChild;return f(h,r),f(b,i),C(S,()=>e.title),C(b,R(ae,{get when(){return e.closable!==!1},get children(){var x=Qg();return Fi(x,"click",e.onClose,!0),f(x,l),x}}),null),h}}),u),f(u,s),C(u,()=>e.children),C(g,R(ae,{get when(){return e.footer},get children(){var h=tf();return f(h,a),C(h,()=>e.footer),h}}),null),O(()=>f(g,Nt(n,o[c],e.class))),p}})}})};Tt(["click"]);var rf=V("<label>"),vo=V("<div>"),lf=V("<div><div><input>");const xo=e=>{const[t,n]=te(e,["label","error","helperText","leftIcon","rightIcon","size","fullWidth","class"]),o=d({display:"flex",flexDirection:"column",gap:"6px"}),r=d({width:"100%"}),i=d({fontSize:"14px",fontWeight:"500",color:"gray.700"}),l=d({position:"relative",display:"flex",alignItems:"center"}),s=d({width:"100%",border:"1px solid",borderColor:"gray.300",borderRadius:"8px",transition:"all 0.2s",backgroundColor:"white",color:"gray.900",_focus:{outline:"none",borderColor:"blue.500",boxShadow:"0 0 0 3px rgba(59, 130, 246, 0.1)"},_disabled:{backgroundColor:"gray.100",color:"gray.500",cursor:"not-allowed"},_placeholder:{color:"gray.400"}}),a=d({borderColor:"red.500",_focus:{borderColor:"red.500",boxShadow:"0 0 0 3px rgba(239, 68, 68, 0.1)"}}),c={sm:d({padding:"8px 12px",fontSize:"14px",minHeight:"36px"}),md:d({padding:"10px 14px",fontSize:"14px",minHeight:"40px"}),lg:d({padding:"12px 16px",fontSize:"16px",minHeight:"48px"})},m=d({position:"absolute",top:"50%",transform:"translateY(-50%)",color:"gray.400",pointerEvents:"none",zIndex:1}),p=d({left:"12px"}),g=d({right:"12px"}),u=d({paddingLeft:"40px"}),h=d({paddingRight:"40px"}),b=d({fontSize:"12px",color:"gray.600"}),S=d({fontSize:"12px",color:"red.600"}),x=t.size||"md";return(()=>{var y=lf(),v=y.firstChild,_=v.firstChild;return C(y,R(ae,{get when(){return t.label},get children(){var w=rf();return f(w,i),C(w,()=>t.label),w}}),v),f(v,l),C(v,R(ae,{get when(){return t.leftIcon},get children(){var w=vo();return C(w,()=>t.leftIcon),O(()=>f(w,Nt(m,p))),w}}),_),lt(_,U({get class(){return Nt(s,c[x],t.error?a:void 0,t.leftIcon?u:void 0,t.rightIcon?h:void 0,t.class)}},n),!1,!1),C(v,R(ae,{get when(){return t.rightIcon},get children(){var w=vo();return C(w,()=>t.rightIcon),O(()=>f(w,Nt(m,g))),w}}),null),C(y,R(ae,{get when(){return t.error||t.helperText},get children(){var w=vo();return C(w,()=>t.error||t.helperText),O(()=>f(w,t.error?S:b)),w}}),null),O(()=>f(y,Nt(o,t.fullWidth?r:void 0))),y})()};var sf=V("<div><h3>账户总值</h3><p>¥"),af=V("<div><h3>总盈亏</h3><p>¥</p><p>%"),cf=V("<div><h3>可用资金</h3><p>¥"),df=V("<div><h3>已用保证金</h3><p>¥"),uf=V("<div><table><thead><tr><th>代码</th><th>数量</th><th>盈亏</th></tr></thead><tbody>"),gf=V("<div><table><thead><tr><th>代码</th><th>类型</th><th>状态</th></tr></thead><tbody>"),ff=V("<div><div><div><label>交易类型</label><select aria-label=交易类型><option value=buy>买入</option><option value=sell>卖出</option></select></div><div><label>订单类型</label><select aria-label=订单类型><option value=market>市价单</option><option value=limit>限价单</option></select></div></div><div>"),pf=V("<div slot=footer>"),hf=V("<div><div><div><h1>💼 </h1><p>管理您的交易订单和持仓</p></div></div><div></div><div>"),mf=V("<span>➕"),vf=V("<tr><td></td><td></td><td>¥<br><span>%"),xf=V("<tr><td><br><span>股 @ ¥</span></td><td><span></span></td><td><span>");const bf=()=>({t:e=>e});function yf(){const{t:e}=bf(),[t,n]=H([{id:"1",symbol:"AAPL",type:"buy",quantity:100,price:150.25,status:"filled",timestamp:new Date("2024-01-15T10:30:00")},{id:"2",symbol:"TSLA",type:"sell",quantity:50,price:245.8,status:"pending",timestamp:new Date("2024-01-15T11:15:00")},{id:"3",symbol:"MSFT",type:"buy",quantity:75,price:310.45,status:"filled",timestamp:new Date("2024-01-15T09:45:00")}]),[o,r]=H([{symbol:"AAPL",quantity:100,avgPrice:150.25,currentPrice:152.3,unrealizedPnL:205,unrealizedPnLPercent:1.36},{symbol:"MSFT",quantity:75,avgPrice:310.45,currentPrice:308.9,unrealizedPnL:-116.25,unrealizedPnLPercent:-.5}]),[i,l]=H(!1),[s,a]=H({symbol:"",type:"buy",quantity:"",price:"",orderType:"limit"}),[c]=H({totalValue:45678.9,totalPnL:88.75,totalPnLPercent:.19,buyingPower:12345.67,marginUsed:5432.1});pt(()=>{console.log("Trading page mounted");const g=setInterval(()=>{r(u=>u.map(h=>{const b=(Math.random()-.5)*2,S=h.currentPrice+b,x=(S-h.avgPrice)*h.quantity,y=x/(h.avgPrice*h.quantity)*100;return{...h,currentPrice:S,unrealizedPnL:x,unrealizedPnLPercent:y}}))},3e3);return()=>clearInterval(g)});const m=()=>{const g=s();if(!g.symbol||!g.quantity||!g.price&&g.orderType==="limit"){alert("请填写完整的订单信息");return}const u={id:Date.now().toString(),symbol:g.symbol.toUpperCase(),type:g.type,quantity:parseInt(g.quantity),price:g.orderType==="market"?0:parseFloat(g.price),status:"pending",timestamp:new Date};n(h=>[u,...h]),l(!1),a({symbol:"",type:"buy",quantity:"",price:"",orderType:"limit"})},p=c();return(()=>{var g=hf(),u=g.firstChild,h=u.firstChild,b=h.firstChild;b.firstChild;var S=b.nextSibling,x=u.nextSibling,y=x.nextSibling;return C(b,()=>e("nav.trading"),null),C(u,R(mo,{variant:"primary",size:"lg",get icon(){return mf()},onClick:()=>l(!0),children:"新建订单"}),null),C(x,R(qt,{padding:"md",shadow:"md",get children(){var v=sf(),_=v.firstChild,w=_.nextSibling;return w.firstChild,C(w,()=>p.totalValue.toLocaleString(),null),O(M=>{var A=d({textAlign:"center"}),I=d({fontSize:"14px",fontWeight:"500",color:"gray.600",marginBottom:"8px"}),F=d({fontSize:"24px",fontWeight:"bold",color:"gray.900"});return A!==M.e&&f(v,M.e=A),I!==M.t&&f(_,M.t=I),F!==M.a&&f(w,M.a=F),M},{e:void 0,t:void 0,a:void 0}),v}}),null),C(x,R(qt,{padding:"md",shadow:"md",get children(){var v=af(),_=v.firstChild,w=_.nextSibling,M=w.firstChild,A=w.nextSibling,I=A.firstChild;return C(w,()=>p.totalPnL>=0?"+":"",M),C(w,()=>Math.abs(p.totalPnL).toFixed(2),null),C(A,()=>p.totalPnL>=0?"+":"",I),C(A,()=>p.totalPnLPercent.toFixed(2),I),O(F=>{var D=d({textAlign:"center"}),T=d({fontSize:"14px",fontWeight:"500",color:"gray.600",marginBottom:"8px"}),P=d({fontSize:"24px",fontWeight:"bold",color:p.totalPnL>=0?"green.600":"red.600"}),z=d({fontSize:"12px",color:p.totalPnL>=0?"green.600":"red.600"});return D!==F.e&&f(v,F.e=D),T!==F.t&&f(_,F.t=T),P!==F.a&&f(w,F.a=P),z!==F.o&&f(A,F.o=z),F},{e:void 0,t:void 0,a:void 0,o:void 0}),v}}),null),C(x,R(qt,{padding:"md",shadow:"md",get children(){var v=cf(),_=v.firstChild,w=_.nextSibling;return w.firstChild,C(w,()=>p.buyingPower.toLocaleString(),null),O(M=>{var A=d({textAlign:"center"}),I=d({fontSize:"14px",fontWeight:"500",color:"gray.600",marginBottom:"8px"}),F=d({fontSize:"24px",fontWeight:"bold",color:"gray.900"});return A!==M.e&&f(v,M.e=A),I!==M.t&&f(_,M.t=I),F!==M.a&&f(w,M.a=F),M},{e:void 0,t:void 0,a:void 0}),v}}),null),C(x,R(qt,{padding:"md",shadow:"md",get children(){var v=df(),_=v.firstChild,w=_.nextSibling;return w.firstChild,C(w,()=>p.marginUsed.toLocaleString(),null),O(M=>{var A=d({textAlign:"center"}),I=d({fontSize:"14px",fontWeight:"500",color:"gray.600",marginBottom:"8px"}),F=d({fontSize:"24px",fontWeight:"bold",color:"orange.600"});return A!==M.e&&f(v,M.e=A),I!==M.t&&f(_,M.t=I),F!==M.a&&f(w,M.a=F),M},{e:void 0,t:void 0,a:void 0}),v}}),null),C(y,R(qt,{title:"当前持仓",padding:"none",shadow:"md",get children(){var v=uf(),_=v.firstChild,w=_.firstChild,M=w.firstChild,A=M.firstChild,I=A.nextSibling,F=I.nextSibling,D=w.nextSibling;return C(D,R(Fe,{get each(){return o()},children:T=>(()=>{var P=vf(),z=P.firstChild,B=z.nextSibling,K=B.nextSibling,j=K.firstChild,W=j.nextSibling,q=W.nextSibling,Z=q.firstChild;return C(z,()=>T.symbol),C(B,()=>T.quantity),C(K,()=>T.unrealizedPnL>=0?"+":"",j),C(K,()=>Math.abs(T.unrealizedPnL).toFixed(2),W),C(q,()=>T.unrealizedPnL>=0?"+":"",Z),C(q,()=>T.unrealizedPnLPercent.toFixed(2),Z),O(J=>{var E=d({borderBottom:"1px solid",borderColor:"gray.200"}),ne=d({padding:"12px 16px",fontSize:"14px",fontWeight:"500",color:"gray.900"}),se=d({padding:"12px 16px",textAlign:"right",fontSize:"14px",color:"gray.700"}),fe=d({padding:"12px 16px",textAlign:"right",fontSize:"14px",fontWeight:"500",color:T.unrealizedPnL>=0?"green.600":"red.600"}),pe=d({fontSize:"12px"});return E!==J.e&&f(P,J.e=E),ne!==J.t&&f(z,J.t=ne),se!==J.a&&f(B,J.a=se),fe!==J.o&&f(K,J.o=fe),pe!==J.i&&f(q,J.i=pe),J},{e:void 0,t:void 0,a:void 0,o:void 0,i:void 0}),P})()})),O(T=>{var P=d({overflowX:"auto"}),z=d({width:"100%",borderCollapse:"collapse"}),B=d({backgroundColor:"gray.50"}),K=d({padding:"12px 16px",textAlign:"left",fontSize:"12px",fontWeight:"600",color:"gray.600"}),j=d({padding:"12px 16px",textAlign:"right",fontSize:"12px",fontWeight:"600",color:"gray.600"}),W=d({padding:"12px 16px",textAlign:"right",fontSize:"12px",fontWeight:"600",color:"gray.600"});return P!==T.e&&f(v,T.e=P),z!==T.t&&f(_,T.t=z),B!==T.a&&f(M,T.a=B),K!==T.o&&f(A,T.o=K),j!==T.i&&f(I,T.i=j),W!==T.n&&f(F,T.n=W),T},{e:void 0,t:void 0,a:void 0,o:void 0,i:void 0,n:void 0}),v}}),null),C(y,R(qt,{title:"订单历史",padding:"none",shadow:"md",get children(){var v=gf(),_=v.firstChild,w=_.firstChild,M=w.firstChild,A=M.firstChild,I=A.nextSibling,F=I.nextSibling,D=w.nextSibling;return C(D,R(Fe,{get each(){return t()},children:T=>(()=>{var P=xf(),z=P.firstChild,B=z.firstChild,K=B.nextSibling,j=K.firstChild,W=z.nextSibling,q=W.firstChild,Z=W.nextSibling,J=Z.firstChild;return C(z,()=>T.symbol,B),C(K,()=>T.quantity,j),C(K,()=>T.price,null),C(q,()=>T.type==="buy"?"买入":"卖出"),C(J,(()=>{var E=Q(()=>T.status==="filled");return()=>E()?"已成交":T.status==="pending"?"待成交":"已取消"})()),O(E=>{var ne=d({borderBottom:"1px solid",borderColor:"gray.200"}),se=d({padding:"12px 16px",fontSize:"14px",fontWeight:"500",color:"gray.900"}),fe=d({fontSize:"12px",color:"gray.600"}),pe=d({padding:"12px 16px",textAlign:"center",fontSize:"14px"}),Se=d({padding:"4px 8px",borderRadius:"4px",fontSize:"12px",fontWeight:"500",backgroundColor:T.type==="buy"?"green.100":"red.100",color:T.type==="buy"?"green.800":"red.800"}),Ce=d({padding:"12px 16px",textAlign:"right",fontSize:"14px"}),k=d({padding:"4px 8px",borderRadius:"4px",fontSize:"12px",fontWeight:"500",backgroundColor:T.status==="filled"?"green.100":T.status==="pending"?"yellow.100":"red.100",color:T.status==="filled"?"green.800":T.status==="pending"?"yellow.800":"red.800"});return ne!==E.e&&f(P,E.e=ne),se!==E.t&&f(z,E.t=se),fe!==E.a&&f(K,E.a=fe),pe!==E.o&&f(W,E.o=pe),Se!==E.i&&f(q,E.i=Se),Ce!==E.n&&f(Z,E.n=Ce),k!==E.s&&f(J,E.s=k),E},{e:void 0,t:void 0,a:void 0,o:void 0,i:void 0,n:void 0,s:void 0}),P})()})),O(T=>{var P=d({overflowX:"auto"}),z=d({width:"100%",borderCollapse:"collapse"}),B=d({backgroundColor:"gray.50"}),K=d({padding:"12px 16px",textAlign:"left",fontSize:"12px",fontWeight:"600",color:"gray.600"}),j=d({padding:"12px 16px",textAlign:"center",fontSize:"12px",fontWeight:"600",color:"gray.600"}),W=d({padding:"12px 16px",textAlign:"right",fontSize:"12px",fontWeight:"600",color:"gray.600"});return P!==T.e&&f(v,T.e=P),z!==T.t&&f(_,T.t=z),B!==T.a&&f(M,T.a=B),K!==T.o&&f(A,T.o=K),j!==T.i&&f(I,T.i=j),W!==T.n&&f(F,T.n=W),T},{e:void 0,t:void 0,a:void 0,o:void 0,i:void 0,n:void 0}),v}}),null),C(g,R(of,{get isOpen(){return i()},onClose:()=>l(!1),title:"新建订单",size:"md",get children(){return[(()=>{var v=ff(),_=v.firstChild,w=_.firstChild,M=w.firstChild,A=M.nextSibling,I=w.nextSibling,F=I.firstChild,D=F.nextSibling,T=_.nextSibling;return C(v,R(xo,{label:"股票代码",placeholder:"例如: AAPL",get value(){return s().symbol},onInput:P=>a(z=>({...z,symbol:P.currentTarget.value}))}),_),A.addEventListener("change",P=>a(z=>({...z,type:P.currentTarget.value}))),D.addEventListener("change",P=>a(z=>({...z,orderType:P.currentTarget.value}))),C(T,R(xo,{label:"数量",type:"number",placeholder:"100",get value(){return s().quantity},onInput:P=>a(z=>({...z,quantity:P.currentTarget.value}))}),null),C(T,(()=>{var P=Q(()=>s().orderType==="limit");return()=>P()&&R(xo,{label:"价格",type:"number",step:"0.01",placeholder:"150.25",get value(){return s().price},onInput:z=>a(B=>({...B,price:z.currentTarget.value}))})})(),null),O(P=>{var z=d({display:"flex",flexDirection:"column",gap:"16px"}),B=d({display:"grid",gridTemplateColumns:"1fr 1fr",gap:"16px"}),K=d({display:"block",fontSize:"14px",fontWeight:"500",color:"gray.700",marginBottom:"8px"}),j=d({width:"100%",padding:"10px 14px",border:"1px solid",borderColor:"gray.300",borderRadius:"8px",backgroundColor:"white",color:"gray.900"}),W=d({display:"block",fontSize:"14px",fontWeight:"500",color:"gray.700",marginBottom:"8px"}),q=d({width:"100%",padding:"10px 14px",border:"1px solid",borderColor:"gray.300",borderRadius:"8px",backgroundColor:"white",color:"gray.900"}),Z=d({display:"grid",gridTemplateColumns:"1fr 1fr",gap:"16px"});return z!==P.e&&f(v,P.e=z),B!==P.t&&f(_,P.t=B),K!==P.a&&f(M,P.a=K),j!==P.o&&f(A,P.o=j),W!==P.i&&f(F,P.i=W),q!==P.n&&f(D,P.n=q),Z!==P.s&&f(T,P.s=Z),P},{e:void 0,t:void 0,a:void 0,o:void 0,i:void 0,n:void 0,s:void 0}),O(()=>A.value=s().type),O(()=>D.value=s().orderType),v})(),(()=>{var v=pf();return C(v,R(mo,{variant:"ghost",onClick:()=>l(!1),children:"取消"}),null),C(v,R(mo,{variant:"primary",onClick:m,children:"提交订单"}),null),v})()]}}),null),O(v=>{var _=d({padding:"24px",maxWidth:"1400px",margin:"0 auto",backgroundColor:"gray.50",minHeight:"100vh"}),w=d({marginBottom:"32px",display:"flex",justifyContent:"space-between",alignItems:"center"}),M=d({fontSize:"32px",fontWeight:"bold",color:"gray.900",marginBottom:"8px"}),A=d({fontSize:"16px",color:"gray.600"}),I=d({display:"grid",gridTemplateColumns:"repeat(auto-fit, minmax(200px, 1fr))",gap:"24px",marginBottom:"32px"}),F=d({display:"grid",gridTemplateColumns:"1fr 1fr",gap:"24px","@media (max-width: 1024px)":{gridTemplateColumns:"1fr"}});return _!==v.e&&f(g,v.e=_),w!==v.t&&f(u,v.t=w),M!==v.a&&f(b,v.a=M),A!==v.o&&f(S,v.o=A),I!==v.i&&f(x,v.i=I),F!==v.n&&f(y,v.n=F),v},{e:void 0,t:void 0,a:void 0,o:void 0,i:void 0,n:void 0}),g})()}function Sf(){return[R(fs,{get children(){return R(pg,{get children(){return[R(tt,{path:"/",component:Tn}),R(tt,{path:"/dashboard",component:Tn}),R(tt,{path:"/market",component:Pi}),R(tt,{path:"/market/realtime",component:Wg}),R(tt,{path:"/market/historical",component:Pi}),R(tt,{path:"/trading",component:yf}),R(tt,{path:"/strategy",component:jg}),R(tt,{path:"/account",component:Tn}),R(tt,{path:"/settings",component:Tn}),R(tt,{path:"/api-test",component:Rg})]}})}}),R(ic,{})]}const $i=document.getElementById("root");$i&&ps(()=>R(Sf,{}),$i);console.log("🚀 量化交易前端平台启动成功"),console.log("📊 基于 SolidJS + Panda CSS"),console.log("⚡ 极致性能，专业体验");
