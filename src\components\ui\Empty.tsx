import { JSX } from 'solid-js'
import { css } from '../../../styled-system/css'

export interface EmptyProps {
  image?: JSX.Element | string
  description?: JSX.Element | string
  children?: JSX.Element
}

export function Empty(props: EmptyProps) {
  return (
    <div class={css({ textAlign: 'center', color: 'text.secondary', padding: '24px' })}>
      <div class={css({ fontSize: '40px', marginBottom: '8px' })}>
        {props.image ?? '🗂️'}
      </div>
      <div class={css({ fontSize: '12px' })}>
        {props.description ?? '暂无数据'}
      </div>
      {props.children}
    </div>
  )
}

