import { JSX, createSignal, For, Show } from 'solid-js'
import { A, useLocation } from '@solidjs/router'
import { css } from '../../../styled-system/css'
import { Button, Input } from '../ui'

interface MenuItem {
  id: string
  label: string
  icon: string
  path: string
  children?: MenuItem[]
}

interface AppLayoutProps {
  children: JSX.Element
}

export function AppLayout(props: AppLayoutProps) {
  const [collapsed, setCollapsed] = createSignal(false)
  const [searchValue, setSearchValue] = createSignal('')
  const location = useLocation()

  const menuItems: MenuItem[] = [
    {
      id: 'dashboard',
      label: '仪表盘',
      icon: '📊',
      path: '/dashboard',
    },
    {
      id: 'market',
      label: '行情中心',
      icon: '📈',
      path: '/market',
      children: [
        { id: 'realtime', label: '实时行情', icon: '⚡', path: '/market/realtime' },
        { id: 'historical', label: '历史数据', icon: '📋', path: '/market/historical' },
      ],
    },
    {
      id: 'trading',
      label: '交易中心',
      icon: '💰',
      path: '/trading',
    },
    {
      id: 'strategy',
      label: '策略中心',
      icon: '🧠',
      path: '/strategy',
    },
    {
      id: 'account',
      label: '账户中心',
      icon: '👤',
      path: '/account',
    },
    {
      id: 'settings',
      label: '系统设置',
      icon: '⚙️',
      path: '/settings',
    },
  ]

  const isActive = (path: string) => {
    return location.pathname === path || location.pathname.startsWith(path + '/')
  }

  const getSidebarStyles = () => ({
    width: collapsed() ? '64px' : '240px',
    height: '100vh',
    backgroundColor: 'white',
    borderRight: '1px solid',
    borderColor: 'border.base',
    transition: 'width 0.3s ease',
    display: 'flex',
    flexDirection: 'column',
    position: 'fixed',
    left: 0,
    top: 0,
    zIndex: 1000,
  })

  const getHeaderStyles = () => ({
    height: '60px',
    backgroundColor: 'white',
    borderBottom: '1px solid',
    borderColor: 'border.base',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: '0 20px',
    marginLeft: collapsed() ? '64px' : '240px',
    transition: 'margin-left 0.3s ease',
  })

  const getMainStyles = () => ({
    marginLeft: collapsed() ? '64px' : '240px',
    marginTop: '60px',
    minHeight: 'calc(100vh - 60px)',
    backgroundColor: 'bg.page',
    transition: 'margin-left 0.3s ease',
  })

  const getLogoStyles = () => ({
    height: '60px',
    display: 'flex',
    alignItems: 'center',
    justifyContent: collapsed() ? 'center' : 'flex-start',
    padding: collapsed() ? '0' : '0 20px',
    borderBottom: '1px solid',
    borderColor: 'border.base',
    fontSize: '18px',
    fontWeight: '600',
    color: 'primary.500',
  })

  const getMenuItemStyles = (item: MenuItem, isChild = false) => ({
    display: 'flex',
    alignItems: 'center',
    gap: '12px',
    padding: collapsed() && !isChild ? '12px 0' : '12px 20px',
    paddingLeft: isChild ? '52px' : collapsed() ? '0' : '20px',
    justifyContent: collapsed() && !isChild ? 'center' : 'flex-start',
    color: isActive(item.path) ? 'primary.500' : 'text.regular',
    backgroundColor: isActive(item.path) ? 'primary.50' : 'transparent',
    borderRight: isActive(item.path) ? '3px solid' : 'none',
    borderColor: 'primary.500',
    textDecoration: 'none',
    transition: 'all 0.3s',
    cursor: 'pointer',
    _hover: {
      backgroundColor: 'primary.50',
      color: 'primary.500',
    },
  })

  return (
    <div class={css({ display: 'flex', minHeight: '100vh' })}>
      {/* 侧边栏 */}
      <aside class={css(getSidebarStyles())}>
        {/* Logo */}
        <div class={css(getLogoStyles())}>
          <span class={css({ fontSize: '20px', marginRight: '8px' })}>📈</span>
          <Show when={!collapsed()}>
            <span>量化平台</span>
          </Show>
        </div>

        {/* 菜单 */}
        <nav class={css({ flex: 1, overflowY: 'auto', padding: '8px 0' })}>
          <For each={menuItems}>
            {(item) => (
              <div>
                <A
                  href={item.path}
                  class={css(getMenuItemStyles(item))}
                  title={collapsed() ? item.label : undefined}
                >
                  <span class={css({ fontSize: '16px' })}>{item.icon}</span>
                  <Show when={!collapsed()}>
                    <span class={css({ fontWeight: isActive(item.path) ? '500' : '400' })}>
                      {item.label}
                    </span>
                  </Show>
                </A>
                <Show when={item.children && !collapsed()}>
                  <For each={item.children}>
                    {(child) => (
                      <A href={child.path} class={css(getMenuItemStyles(child, true))}>
                        <span class={css({ fontSize: '14px' })}>{child.icon}</span>
                        <span class={css({ fontWeight: isActive(child.path) ? '500' : '400' })}>
                          {child.label}
                        </span>
                      </A>
                    )}
                  </For>
                </Show>
              </div>
            )}
          </For>
        </nav>

        {/* 折叠按钮 */}
        <div class={css({ padding: '16px', borderTop: '1px solid', borderColor: 'border.base' })}>
          <Button
            variant="text"
            size="small"
            onClick={() => setCollapsed(!collapsed())}
            class={css({ width: '100%' })}
          >
            {collapsed() ? '→' : '←'}
          </Button>
        </div>
      </aside>

      <div class={css({ flex: 1 })}>
        {/* 顶部栏 */}
        <header class={css(getHeaderStyles())}>
          <div class={css({ display: 'flex', alignItems: 'center', gap: '16px' })}>
            <h1 class={css({ fontSize: '18px', fontWeight: '500', color: 'text.primary', margin: 0 })}>
              量化交易平台
            </h1>
          </div>

          <div class={css({ display: 'flex', alignItems: 'center', gap: '16px' })}>
            <Input
              placeholder="搜索..."
              size="small"
              value={searchValue()}
              onInput={(e) => setSearchValue(e.currentTarget.value)}
              class={css({ width: '200px' })}
            />
            <Button variant="text" size="small">
              帮助
            </Button>
            <Button variant="text" size="small">
              设置
            </Button>
            <div
              class={css({
                width: '32px',
                height: '32px',
                backgroundColor: 'primary.500',
                borderRadius: '50%',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                color: 'white',
                fontSize: '14px',
                fontWeight: '500',
              })}
            >
              用
            </div>
          </div>
        </header>

        {/* 主内容区 */}
        <main class={css(getMainStyles())}>{props.children}</main>
      </div>
    </div>
  )
}
