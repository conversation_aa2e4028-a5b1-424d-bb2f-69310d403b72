import { createSignal, onMount, onCleanup } from 'solid-js';

export default function Dashboard() {
  console.log('🔥 Dashboard组件已加载 - 简化版本');
  const [currentTime, setCurrentTime] = createSignal(new Date().toLocaleString('zh-CN'));

  // 实时更新时间
  onMount(() => {
    const interval = setInterval(() => {
      setCurrentTime(new Date().toLocaleString('zh-CN'));
    }, 1000);

    onCleanup(() => clearInterval(interval));
  });

  console.log('🚀 Dashboard return 开始渲染...');
  
  return (
    <div style={{
      padding: '20px',
      'min-height': '100vh',
      'background-color': '#f5f5f5',
    }}>
      {/* 简单测试内容 */}
      <div style={{
        'background-color': 'white',
        padding: '20px',
        'border-radius': '8px',
        'margin-bottom': '20px',
        'text-align': 'center',
      }}>
        <h1 style={{ color: '#52c41a', 'font-size': '24px', margin: '0 0 10px 0' }}>
          🎉 量化交易平台
        </h1>
        <p style={{ color: '#666', margin: 0 }}>
          页面正常加载 - 当前时间: {currentTime()}
        </p>
      </div>

      {/* 简单测试按钮 */}
      <div style={{ 'margin-bottom': '20px' }}>
        <button 
          type="button"
          style={{
            padding: '10px 20px',
            'background-color': '#1890ff',
            color: 'white',
            border: 'none',
            'border-radius': '4px',
            cursor: 'pointer',
          }}
          onClick={() => alert('按钮点击测试成功！')}
        >
          测试按钮
        </button>
      </div>

      {/* 简单卡片 */}
      <div style={{
        'background-color': 'white',
        padding: '20px',
        'border-radius': '8px',
        'box-shadow': '0 2px 8px rgba(0,0,0,0.1)',
      }}>
        <h2 style={{ margin: '0 0 15px 0', color: '#333' }}>功能测试</h2>
        <ul style={{ margin: 0, padding: '0 0 0 20px' }}>
          <li>✅ SolidJS 组件渲染正常</li>
          <li>✅ 时间更新功能正常</li>
          <li>✅ 事件处理正常</li>
          <li>✅ 样式应用正常</li>
        </ul>
      </div>
    </div>
  );
}
