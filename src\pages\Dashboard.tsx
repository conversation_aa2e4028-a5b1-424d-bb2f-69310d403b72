
import { createSignal, For, onMount, onCleanup } from 'solid-js';
import { css } from '../../styled-system/css';

export default function Dashboard() {
  const [currentTime, setCurrentTime] = createSignal(new Date().toLocaleString('zh-CN'));

  // 实时更新时间
  let timeInterval: NodeJS.Timeout;
  onMount(() => {
    timeInterval = setInterval(() => {
      setCurrentTime(new Date().toLocaleString('zh-CN'));
    }, 1000);
  });

  onCleanup(() => {
    if (timeInterval) clearInterval(timeInterval);
  });

  // 投资收益数据
  const investmentData = [
    {
      title: '总资产',
      value: '¥1,234,567.89',
      change: '+12.34%',
      trend: 'up',
      icon: '💰',
      description: '较昨日增长'
    },
    {
      title: '今日收益',
      value: '¥8,765.43',
      change: '+2.15%',
      trend: 'up',
      icon: '📈',
      description: '实时盈亏'
    },
    {
      title: '持仓市值',
      value: '¥987,654.32',
      change: '-0.87%',
      trend: 'down',
      icon: '📊',
      description: '当前持仓'
    },
    {
      title: '可用资金',
      value: '¥246,913.57',
      change: '+5.67%',
      trend: 'up',
      icon: '💳',
      description: '可投资金额'
    }
  ];

  // 今日行情数据
  const marketData = [
    { name: 'A股指数', value: '3,245.67', change: '+23.45', percent: '+0.73%', trend: 'up' },
    { name: '创业板', value: '2,156.89', change: '-12.34', percent: '-0.57%', trend: 'down' },
    { name: '科创50', value: '1,234.56', change: '+45.67', percent: '+3.84%', trend: 'up' },
    { name: '沪深300', value: '4,567.89', change: '+78.90', percent: '+1.76%', trend: 'up' }
  ];

  // 最新资讯
  const newsData = [
    { title: 'A股市场今日表现强劲，科技股领涨', time: '10分钟前', type: 'market' },
    { title: '央行宣布降准0.25个百分点', time: '30分钟前', type: 'policy' },
    { title: '新能源板块持续活跃，多只个股涨停', time: '1小时前', type: 'sector' },
    { title: '外资持续流入A股市场', time: '2小时前', type: 'capital' }
  ];

  return (
    <div class={css({
      display: 'flex',
      flexDirection: 'column',
      gap: '24px',
      width: '100%',
      padding: '24px',
      backgroundColor: '#f5f5f5',
      minHeight: '100%'
    })}>
      {/* 专业投资收益概览 */}
      <div class={css({
        display: 'grid',
        gridTemplateColumns: 'repeat(auto-fit, minmax(280px, 1fr))',
        gap: '20px'
      })}>
        <For each={investmentData}>
          {(item) => (
            <div class={css({
              bg: 'white',
              borderRadius: '16px',
              p: '24px',
              boxShadow: '0 4px 20px rgba(0,0,0,0.08)',
              border: '1px solid #f0f0f0',
              position: 'relative',
              overflow: 'hidden',
              transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
              _hover: {
                boxShadow: '0 8px 30px rgba(0,0,0,0.12)',
                transform: 'translateY(-4px)'
              }
            })}>
              {/* 背景装饰 */}
              <div class={css({
                position: 'absolute',
                top: '-20px',
                right: '-20px',
                width: '80px',
                height: '80px',
                borderRadius: '50%',
                background: item.trend === 'up'
                  ? 'linear-gradient(135deg, rgba(82, 196, 26, 0.1), rgba(82, 196, 26, 0.05))'
                  : 'linear-gradient(135deg, rgba(245, 34, 45, 0.1), rgba(245, 34, 45, 0.05))',
                opacity: 0.6
              })} />

              <div class={css({
                display: 'flex',
                alignItems: 'flex-start',
                justifyContent: 'space-between',
                mb: '16px'
              })}>
                <div class={css({
                  fontSize: '32px',
                  lineHeight: 1
                })}>
                  {item.icon}
                </div>
                <div class={css({
                  display: 'flex',
                  alignItems: 'center',
                  gap: '4px',
                  padding: '4px 8px',
                  borderRadius: '12px',
                  fontSize: '12px',
                  fontWeight: '600',
                  backgroundColor: item.trend === 'up' ? '#f6ffed' : '#fff2f0',
                  color: item.trend === 'up' ? '#52c41a' : '#f5222d'
                })}>
                  <span>{item.trend === 'up' ? '↗' : '↘'}</span>
                  {item.change}
                </div>
              </div>

              <div class={css({
                mb: '8px'
              })}>
                <div class={css({
                  fontSize: '28px',
                  fontWeight: '700',
                  color: '#262626',
                  lineHeight: 1.2,
                  mb: '4px'
                })}>
                  {item.value}
                </div>
                <div class={css({
                  fontSize: '14px',
                  color: '#8c8c8c',
                  fontWeight: '500'
                })}>
                  {item.title}
                </div>
              </div>

              <div class={css({
                fontSize: '12px',
                color: '#8c8c8c',
                display: 'flex',
                alignItems: 'center',
                gap: '4px'
              })}>
                <span>📊</span>
                {item.description}
              </div>
            </div>
          )}
        </For>
      </div>

      {/* 市场行情和资讯 */}
      <div class={css({
        display: 'grid',
        gridTemplateColumns: '1fr 1fr',
        gap: '24px'
      })}>
        {/* 今日行情 */}
        <div class={css({
          bg: 'white',
          borderRadius: '16px',
          p: '24px',
          boxShadow: '0 4px 20px rgba(0,0,0,0.08)',
          border: '1px solid #f0f0f0'
        })}>
          <div class={css({
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between',
            mb: '20px'
          })}>
            <h3 class={css({
              fontSize: '18px',
              fontWeight: '600',
              color: '#262626',
              margin: 0
            })}>
              今日行情
            </h3>
            <span class={css({
              fontSize: '12px',
              color: '#8c8c8c',
              bg: '#f5f5f5',
              px: '8px',
              py: '4px',
              borderRadius: '4px'
            })}>
              实时更新
            </span>
          </div>

          <div class={css({ display: 'flex', flexDirection: 'column', gap: '12px' })}>
            <For each={marketData}>
              {(item) => (
                <div class={css({
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'space-between',
                  padding: '12px',
                  borderRadius: '8px',
                  bg: '#fafafa',
                  transition: 'all 0.2s ease',
                  cursor: 'pointer',
                  _hover: { bg: '#f0f0f0' }
                })}>
                  <div class={css({
                    display: 'flex',
                    alignItems: 'center',
                    gap: '12px'
                  })}>
                    <div class={css({
                      width: '8px',
                      height: '8px',
                      borderRadius: '50%',
                      backgroundColor: item.trend === 'up' ? '#52c41a' : '#f5222d'
                    })} />
                    <span class={css({
                      fontSize: '14px',
                      fontWeight: '500',
                      color: '#262626'
                    })}>
                      {item.name}
                    </span>
                  </div>
                  <div class={css({
                    textAlign: 'right'
                  })}>
                    <div class={css({
                      fontSize: '14px',
                      fontWeight: '600',
                      color: '#262626',
                      mb: '2px'
                    })}>
                      {item.value}
                    </div>
                    <div class={css({
                      fontSize: '12px',
                      color: item.trend === 'up' ? '#52c41a' : '#f5222d',
                      display: 'flex',
                      alignItems: 'center',
                      gap: '4px',
                      justifyContent: 'flex-end'
                    })}>
                      <span>{item.change}</span>
                      <span>({item.percent})</span>
                    </div>
                  </div>
                </div>
              )}
            </For>
          </div>
        </div>

        {/* 最新资讯 */}
        <div class={css({
          bg: 'white',
          borderRadius: '16px',
          p: '24px',
          boxShadow: '0 4px 20px rgba(0,0,0,0.08)',
          border: '1px solid #f0f0f0'
        })}>
          <div class={css({
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between',
            mb: '20px'
          })}>
            <h3 class={css({
              fontSize: '18px',
              fontWeight: '600',
              color: '#262626',
              margin: 0
            })}>
              最新资讯
            </h3>
            <button type="button" class={css({
              fontSize: '12px',
              color: '#1890ff',
              bg: 'transparent',
              border: 'none',
              cursor: 'pointer',
              _hover: { textDecoration: 'underline' }
            })}>
              查看更多
            </button>
          </div>

          <div class={css({ display: 'flex', flexDirection: 'column', gap: '16px' })}>
            <For each={newsData}>
              {(item) => (
                <div class={css({
                  p: '12px',
                  borderRadius: '8px',
                  bg: '#fafafa',
                  transition: 'all 0.2s ease',
                  cursor: 'pointer',
                  _hover: { bg: '#f0f0f0' }
                })}>
                  <div class={css({
                    fontSize: '14px',
                    fontWeight: '500',
                    color: '#262626',
                    mb: '8px',
                    lineHeight: '1.4'
                  })}>
                    {item.title}
                  </div>
                  <div class={css({
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'space-between'
                  })}>
                    <span class={css({
                      fontSize: '12px',
                      color: '#8c8c8c'
                    })}>
                      {item.time}
                    </span>
                    <span class={css({
                      fontSize: '10px',
                      color: '#1890ff',
                      bg: '#e6f7ff',
                      px: '6px',
                      py: '2px',
                      borderRadius: '4px'
                    })}>
                      {item.type}
                    </span>
                  </div>
                </div>
              )}
            </For>
          </div>
        </div>
      </div>

      {/* 时间显示 */}
      <div class={css({
        textAlign: 'center',
        fontSize: '12px',
        color: '#8c8c8c',
        mt: '16px'
      })}>
        当前时间: {currentTime()}
      </div>
    </div>
  );
}