
import { createSignal, For, onMount, onCleanup } from 'solid-js';
import { css } from '../../styled-system/css';

export default function Dashboard() {
  console.log('🔥 Dashboard组件已加载 - 新版本 - 时间戳:', Date.now());
  const [currentTime, setCurrentTime] = createSignal(new Date().toLocaleString('zh-CN'));

  // 实时更新时间
  let timeInterval: NodeJS.Timeout;
  onMount(() => {
    timeInterval = setInterval(() => {
      setCurrentTime(new Date().toLocaleString('zh-CN'));
    }, 1000);
  });

  onCleanup(() => {
    if (timeInterval) clearInterval(timeInterval);
  });

  // 投资仪表盘数据 - 参考专业量化平台
  const investmentData = [
    {
      title: '总资产',
      value: '¥1,000,000',
      change: '+2.34%',
      trend: 'up',
      icon: '💰',
      description: '总资产',
      subValue: '¥1,000,000'
    },
    {
      title: '今日盈亏',
      value: '0',
      change: '+0.00%',
      trend: 'neutral',
      icon: '📊',
      description: '今日盈亏',
      subValue: '0.00%'
    },
    {
      title: '持仓市值',
      value: '¥50,000',
      change: '+0.00%',
      trend: 'neutral',
      icon: '📈',
      description: '持仓市值',
      subValue: '¥50,000'
    },
    {
      title: '可用资金',
      value: '2',
      change: '+0.00%',
      trend: 'neutral',
      icon: '🔒',
      description: '持仓数量',
      subValue: '2'
    }
  ];

  // 资金曲线数据 - 模拟专业量化平台的资金曲线
  // 资金曲线暂未使用，先保留数据以便后续接入图表
  const fundCurveData = [] as const;

  // 持仓概览数据
  const holdingsData = [
    { code: '000725', name: '京东方A', price: 3.45, change: -1.43, changePercent: -29.3, volume: 7340.75, amount: -6046, status: '持仓' },
    { code: '300519', name: '新光药业', price: 68.94, change: -1.05, changePercent: -1.5, volume: 410.75, amount: -1796, status: '持仓' },
    { code: '000831', name: '五矿稀土', price: 26.09, change: -1.37, changePercent: -5.0, volume: 7558.72, amount: -7688, status: '持仓' }
  ];

  // 今日行情数据
  const marketData = [
    { name: '上证指数', value: '3,245.67', change: '+23.45', percent: '+0.73%', trend: 'up' },
    { name: '深证成指', value: '10,567.23', change: '+45.67', percent: '+0.43%', trend: 'up' },
    { name: '创业板指', value: '2,234.56', change: '-8.90', percent: '-0.40%', trend: 'down' },
    { name: '科创50', value: '1,123.45', change: '+15.23', percent: '+1.37%', trend: 'up' }
  ];

  // 最新资讯
  const newsData = [
    { title: 'A股市场今日表现强劲，科技股领涨', time: '刚刚发布', type: 'market' },
    { title: '央行宣布降准0.25个百分点', time: '30分钟前', type: 'policy' },
    { title: '新能源板块持续活跃，多只个股涨停', time: '1小时前', type: 'sector' }
  ];

  return (
    <div class={css({
      display: 'flex',
      flexDirection: 'column',
      gap: '16px',
      width: '100%',
      padding: '16px',
      backgroundColor: '#f0f2f5',
      minHeight: '100%'
    })}>
      {/* 新版本标识 */}
      <div class={css({
        backgroundColor: '#52c41a',
        color: 'white',
        padding: '12px 16px',
        borderRadius: '6px',
        textAlign: 'center',
        fontSize: '16px',
        fontWeight: '600',
        boxShadow: '0 2px 8px rgba(82, 196, 26, 0.3)'
      })}>
        🎉 新版专业量化平台界面已成功加载！
      </div>

      {/* 页面标题和操作栏 */}
      <div class={css({
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'space-between',
        marginBottom: '8px'
      })}>
        <h1 class={css({
          fontSize: '20px',
          fontWeight: '600',
          color: '#262626',
          margin: 0
        })}>
          投资仪表盘
        </h1>
        <div class={css({
          display: 'flex',
          alignItems: 'center',
          gap: '8px'
        })}>
          <button class={css({
            padding: '6px 12px',
            backgroundColor: '#1890ff',
            color: 'white',
            border: 'none',
            borderRadius: '4px',
            fontSize: '12px',
            cursor: 'pointer'
          })}>
            刷新
          </button>
          <button class={css({
            padding: '6px 12px',
            backgroundColor: 'white',
            color: '#262626',
            border: '1px solid #d9d9d9',
            borderRadius: '4px',
            fontSize: '12px',
            cursor: 'pointer'
          })}>
            设置
          </button>
          <button class={css({
            padding: '6px 12px',
            backgroundColor: '#52c41a',
            color: 'white',
            border: 'none',
            borderRadius: '4px',
            fontSize: '12px',
            cursor: 'pointer'
          })}>
            新增策略
          </button>
        </div>
      </div>

      {/* 投资仪表盘卡片 */}
      <div class={css({
        display: 'grid',
        gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',
        gap: '16px',
        marginBottom: '16px'
      })}>
        <For each={investmentData}>
          {(item) => (
            <div class={css({
              backgroundColor: 'white',
              borderRadius: '8px',
              padding: '16px',
              border: '1px solid #e8e8e8',
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'center',
              textAlign: 'center',
              transition: 'all 0.2s ease',
              minHeight: '120px',
              _hover: {
                boxShadow: '0 2px 8px rgba(0,0,0,0.1)'
              }
            })}>
              <div class={css({
                fontSize: '24px',
                marginBottom: '8px'
              })}>
                {item.icon}
              </div>
              <div class={css({
                fontSize: '24px',
                fontWeight: '600',
                color: '#262626',
                marginBottom: '4px'
              })}>
                {item.value}
              </div>
              <div class={css({
                fontSize: '12px',
                color: '#8c8c8c',
                marginBottom: '8px'
              })}>
                {item.description}
              </div>
              <div class={css({
                fontSize: '12px',
                color: item.trend === 'up' ? '#52c41a' : item.trend === 'down' ? '#f5222d' : '#8c8c8c',
                fontWeight: '500'
              })}>
                {item.change}
              </div>
            </div>
          )}
        </For>
      </div>

      {/* 资金曲线图和持仓概览 */}
      <div class={css({
        display: 'grid',
        gridTemplateColumns: '2fr 1fr',
        gap: '16px',
        marginBottom: '16px',
        '@media (max-width: 768px)': {
          gridTemplateColumns: '1fr'
        }
      })}>
        {/* 资金曲线图 */}
        <div class={css({
          backgroundColor: 'white',
          borderRadius: '8px',
          border: '1px solid #e8e8e8',
          padding: '16px'
        })}>
          <div class={css({
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between',
            marginBottom: '16px',
            flexWrap: 'wrap',
            gap: '8px'
          })}>
            <h3 class={css({
              fontSize: '16px',
              fontWeight: '600',
              color: '#262626',
              margin: 0
            })}>
              资金曲线图
            </h3>
            <div class={css({
              display: 'flex',
              alignItems: 'center',
              gap: '8px'
            })}>
              <button type="button" class={css({
                padding: '4px 8px',
                backgroundColor: '#1890ff',
                color: 'white',
                border: 'none',
                borderRadius: '4px',
                fontSize: '12px',
                cursor: 'pointer'
              })}>
                日
              </button>
              <button type="button" class={css({
                padding: '4px 8px',
                backgroundColor: 'white',
                color: '#262626',
                border: '1px solid #d9d9d9',
                borderRadius: '4px',
                fontSize: '12px',
                cursor: 'pointer'
              })}>
                周
              </button>
              <button type="button" class={css({
                padding: '4px 8px',
                backgroundColor: 'white',
                color: '#262626',
                border: '1px solid #d9d9d9',
                borderRadius: '4px',
                fontSize: '12px',
                cursor: 'pointer'
              })}>
                月
              </button>
            </div>
          </div>

          {/* 简化的图表区域 */}
          <div class={css({
            height: '200px',
            backgroundColor: '#fafafa',
            borderRadius: '4px',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            flexDirection: 'column',
            gap: '8px'
          })}>
            <div class={css({ fontSize: '48px' })}>📈</div>
            <div class={css({ fontSize: '14px', color: '#8c8c8c' })}>资金曲线图表</div>
            <div class={css({ fontSize: '12px', color: '#8c8c8c' })}>显示策略收益走势</div>
          </div>
        </div>

        {/* 持仓概览 */}
        <div class={css({
          backgroundColor: 'white',
          borderRadius: '8px',
          border: '1px solid #e8e8e8',
          padding: '16px'
        })}>
          <div class={css({
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between',
            marginBottom: '16px'
          })}>
            <h3 class={css({
              fontSize: '16px',
              fontWeight: '600',
              color: '#262626',
              margin: 0
            })}>
              持仓概览
            </h3>
            <span class={css({
              fontSize: '12px',
              color: '#8c8c8c'
            })}>
              查看全部 →
            </span>
          </div>

          <div class={css({
            display: 'flex',
            flexDirection: 'column',
            gap: '8px'
          })}>
            <For each={holdingsData}>
              {(item) => (
                <div class={css({
                  padding: '8px',
                  backgroundColor: '#fafafa',
                  borderRadius: '4px',
                  fontSize: '12px'
                })}>
                  <div class={css({
                    display: 'flex',
                    justifyContent: 'space-between',
                    alignItems: 'center',
                    marginBottom: '4px'
                  })}>
                    <span class={css({ fontWeight: '600', color: '#262626' })}>{item.code}</span>
                    <span class={css({
                      color: item.changePercent > 0 ? '#52c41a' : '#f5222d',
                      fontWeight: '500'
                    })}>
                      {item.changePercent > 0 ? '+' : ''}{item.changePercent}%
                    </span>
                  </div>
                  <div class={css({
                    display: 'flex',
                    justifyContent: 'space-between',
                    color: '#8c8c8c'
                  })}>
                    <span>{item.name}</span>
                    <span>{item.status}</span>
                  </div>
                </div>
              )}
            </For>
          </div>
        </div>
      </div>

      {/* 今日行情和最新资讯 */}
      <div class={css({
        display: 'grid',
        gridTemplateColumns: '1fr 1fr',
        gap: '16px',
        '@media (max-width: 768px)': {
          gridTemplateColumns: '1fr'
        }
      })}>
        {/* 今日行情 */}
        <div class={css({
          backgroundColor: 'white',
          borderRadius: '8px',
          border: '1px solid #e8e8e8',
          padding: '16px'
        })}>
          <div class={css({
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between',
            marginBottom: '16px',
            flexWrap: 'wrap',
            gap: '8px'
          })}>
            <h3 class={css({
              fontSize: '16px',
              fontWeight: '600',
              color: '#262626',
              margin: 0
            })}>
              今日行情
            </h3>
            <span class={css({
              fontSize: '12px',
              color: '#8c8c8c'
            })}>
              更新时间: 15:30
            </span>
          </div>

          <div class={css({
            display: 'flex',
            flexDirection: 'column',
            gap: '8px'
          })}>
            <For each={marketData}>
              {(item) => (
                <div class={css({
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'space-between',
                  padding: '8px',
                  backgroundColor: '#fafafa',
                  borderRadius: '4px',
                  fontSize: '12px'
                })}>
                  <div class={css({
                    display: 'flex',
                    alignItems: 'center',
                    gap: '8px'
                  })}>
                    <div class={css({
                      width: '6px',
                      height: '6px',
                      borderRadius: '50%',
                      backgroundColor: item.trend === 'up' ? '#52c41a' : '#f5222d'
                    })} />
                    <span class={css({
                      fontWeight: '500',
                      color: '#262626'
                    })}>
                      {item.name}
                    </span>
                  </div>
                  <div class={css({
                    textAlign: 'right'
                  })}>
                    <div class={css({
                      fontWeight: '600',
                      color: '#262626',
                      marginBottom: '2px'
                    })}>
                      {item.value}
                    </div>
                    <div class={css({
                      color: item.trend === 'up' ? '#52c41a' : '#f5222d',
                      fontSize: '11px'
                    })}>
                      {item.change} ({item.percent})
                    </div>
                  </div>
                </div>
              )}
            </For>
          </div>
        </div>

        {/* 最新资讯 */}
        <div class={css({
          backgroundColor: 'white',
          borderRadius: '8px',
          border: '1px solid #e8e8e8',
          padding: '16px'
        })}>
          <div class={css({
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between',
            marginBottom: '16px'
          })}>
            <h3 class={css({
              fontSize: '16px',
              fontWeight: '600',
              color: '#262626',
              margin: 0
            })}>
              最新资讯
            </h3>
            <span class={css({
              fontSize: '12px',
              color: '#1890ff',
              cursor: 'pointer'
            })}>
              查看更多 →
            </span>
          </div>

          <div class={css({
            display: 'flex',
            flexDirection: 'column',
            gap: '8px'
          })}>
            <For each={newsData}>
              {(item) => (
                <div class={css({
                  padding: '8px',
                  backgroundColor: '#fafafa',
                  borderRadius: '4px',
                  cursor: 'pointer',
                  transition: 'background-color 0.2s',
                  _hover: {
                    backgroundColor: '#f0f0f0'
                  }
                })}>
                  <div class={css({
                    fontSize: '12px',
                    fontWeight: '500',
                    color: '#262626',
                    marginBottom: '4px',
                    lineHeight: '1.4'
                  })}>
                    {item.title}
                  </div>
                  <div class={css({
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'space-between'
                  })}>
                    <span class={css({
                      fontSize: '11px',
                      color: '#8c8c8c'
                    })}>
                      {item.time}
                    </span>
                    <span class={css({
                      fontSize: '10px',
                      color: '#1890ff',
                      backgroundColor: '#e6f7ff',
                      padding: '2px 6px',
                      borderRadius: '2px'
                    })}>
                      {item.type}
                    </span>
                  </div>
                </div>
              )}
            </For>
          </div>
        </div>
      </div>

      {/* 页面底部信息 */}
      <div class={css({
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'space-between',
        padding: '12px 16px',
        backgroundColor: 'white',
        borderRadius: '8px',
        border: '1px solid #e8e8e8',
        fontSize: '12px',
        color: '#8c8c8c'
      })}>
        <div>
          当前时间: {currentTime()}
        </div>
        <div class={css({
          display: 'flex',
          alignItems: 'center',
          gap: '16px'
        })}>
          <span>数据来源: 模拟数据</span>
          <span>更新频率: 实时</span>
          <div class={css({
            display: 'flex',
            alignItems: 'center',
            gap: '4px'
          })}>
            <div class={css({
              width: '6px',
              height: '6px',
              borderRadius: '50%',
              backgroundColor: '#52c41a'
            })} />
            <span>系统正常</span>
          </div>
        </div>
      </div>
    </div>
  );
}