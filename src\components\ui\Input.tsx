import { JSX, splitProps, mergeProps, createSignal, Show } from 'solid-js'
import { css } from '../../../styled-system/css'
import clsx from 'clsx'

export interface InputProps extends JSX.InputHTMLAttributes<HTMLInputElement> {
  size?: 'large' | 'default' | 'small'
  disabled?: boolean
  clearable?: boolean
  prefixIcon?: JSX.Element
  suffixIcon?: JSX.Element
  showPassword?: boolean
  error?: boolean
  onClear?: () => void
}

export function Input(props: InputProps) {
  const merged = mergeProps(
    {
      size: 'default' as const,
      type: 'text' as const,
    },
    props
  )

  const [local, others] = splitProps(merged, [
    'size',
    'disabled',
    'clearable',
    'prefixIcon',
    'suffixIcon',
    'showPassword',
    'error',
    'onClear',
    'class',
    'value',
  ])

  const [showPasswordValue, setShowPasswordValue] = createSignal(false)
  const [focused, setFocused] = createSignal(false)

  const getContainerStyles = () => {
    const base = {
      position: 'relative',
      display: 'inline-flex',
      alignItems: 'center',
      width: '100%',
      borderRadius: '4px',
      border: '1px solid',
      backgroundColor: 'white',
      transition: 'all 0.3s',
      _focusWithin: {
        borderColor: 'primary.500',
        boxShadow: '0 0 0 2px rgba(64, 158, 255, 0.2)',
      },
    }

    const sizeStyles = {
      large: {
        height: '40px',
        fontSize: '14px',
      },
      default: {
        height: '32px',
        fontSize: '14px',
      },
      small: {
        height: '24px',
        fontSize: '12px',
      },
    }

    const stateStyles = {
      borderColor: local.error
        ? 'danger.500'
        : focused()
        ? 'primary.500'
        : 'border.base',
      _hover: !local.disabled
        ? {
            borderColor: local.error ? 'danger.600' : 'border.light',
          }
        : {},
      _disabled: {
        backgroundColor: 'bg.page',
        borderColor: 'border.lighter',
        cursor: 'not-allowed',
      },
    }

    return {
      ...base,
      ...sizeStyles[local.size],
      ...stateStyles,
    }
  }

  const getInputStyles = () => {
    const paddingLeft = local.prefixIcon ? '32px' : '12px'
    const paddingRight =
      local.clearable || local.suffixIcon || local.showPassword ? '32px' : '12px'

    return {
      width: '100%',
      height: '100%',
      border: 'none',
      outline: 'none',
      backgroundColor: 'transparent',
      color: 'text.primary',
      fontSize: 'inherit',
      paddingLeft,
      paddingRight,
      _placeholder: {
        color: 'text.placeholder',
      },
      _disabled: {
        cursor: 'not-allowed',
        color: 'text.placeholder',
      },
    }
  }

  const getIconStyles = (position: 'prefix' | 'suffix') => ({
    position: 'absolute',
    top: '50%',
    transform: 'translateY(-50%)',
    [position === 'prefix' ? 'left' : 'right']: '8px',
    color: 'text.secondary',
    fontSize: '14px',
    cursor: position === 'suffix' && local.clearable ? 'pointer' : 'default',
    zIndex: 1,
  })

  const handleClear = () => {
    local.onClear?.()
  }

  const togglePassword = () => {
    setShowPasswordValue(!showPasswordValue())
  }

  const inputType = () => {
    if (local.showPassword) {
      return showPasswordValue() ? 'text' : 'password'
    }
    return others.type || 'text'
  }

  return (
    <div class={clsx(css(getContainerStyles()), local.class)}>
      <Show when={local.prefixIcon}>
        <span class={css(getIconStyles('prefix'))}>{local.prefixIcon}</span>
      </Show>

      <input
        {...others}
        type={inputType()}
        class={css(getInputStyles())}
        disabled={local.disabled}
        value={local.value}
        onFocus={() => setFocused(true)}
        onBlur={() => setFocused(false)}
      />

      <Show when={local.clearable && local.value}>
        <span
          class={css({
            ...getIconStyles('suffix'),
            cursor: 'pointer',
            _hover: { color: 'text.primary' },
          })}
          onClick={handleClear}
        >
          ✕
        </span>
      </Show>

      <Show when={local.showPassword}>
        <span
          class={css({
            ...getIconStyles('suffix'),
            cursor: 'pointer',
            _hover: { color: 'text.primary' },
          })}
          onClick={togglePassword}
        >
          {showPasswordValue() ? '👁️' : '👁️‍🗨️'}
        </span>
      </Show>

      <Show when={local.suffixIcon && !local.clearable && !local.showPassword}>
        <span class={css(getIconStyles('suffix'))}>{local.suffixIcon}</span>
      </Show>
    </div>
  )
}
