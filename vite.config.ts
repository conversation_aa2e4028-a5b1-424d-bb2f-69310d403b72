import { defineConfig } from 'vite';
import solidPlugin from 'vite-plugin-solid';
import path from 'path';

export default defineConfig({
  plugins: [
    solidPlugin(),
  ],
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src'),
      '@/components': path.resolve(__dirname, './src/components'),
      '@/utils': path.resolve(__dirname, './src/utils'),
    },
  },
  build: {
    target: 'esnext',
    minify: 'esbuild',
    sourcemap: false,
    rollupOptions: {
      input: {
        main: path.resolve(__dirname, 'index.html')
      },
      external: ['@xenova/transformers'], // 将AI模型标记为外部依赖
      output: {
        manualChunks: {
          // 核心框架
          'vendor-solid': ['solid-js', '@solidjs/router'],
          // 图表库
          'vendor-charts': ['lightweight-charts'],
          // 代码编辑器
          'vendor-editor': [
            'monaco-editor',
            '@monaco-editor/loader'
          ],
          // 工具库
          'vendor-utils': ['date-fns', 'decimal.js', 'big.js', 'numeral', 'uuid'],
          // 网络通信
          'vendor-network': ['socket.io-client']
        }
      }
    }
  },

  server: {
    port: 3001,
    host: '0.0.0.0',
    open: true,
    cors: true,
    proxy: {
      '/api': {
        target: 'http://localhost:8000',
        changeOrigin: true,
        // 不需要重写路径，因为 http.ts 已经包含了 /api/v1
        // rewrite: (path) => path.replace(/^\/api/, '/api/v1'),
      },
    },
    // 为本地 Monaco Editor 资源配置静态文件服务
    fs: {
      allow: ['..']
    }
  },
  optimizeDeps: {
    include: [
      'solid-js',
      'decimal.js',
      'date-fns',
      'lightweight-charts',
      'socket.io-client'
    ],
    exclude: [
      '@xenova/transformers' // AI模型需要动态加载
    ]
  },
  define: {
    __DEV__: JSON.stringify(process.env.NODE_ENV === 'development'),
  },
  // 支持Web Workers和WASM
  worker: {
    format: 'es'
  },
  // 支持Transformers.js的WASM和模型文件
  assetsInclude: ['**/*.wasm', '**/*.onnx', '**/*.bin'],
});
