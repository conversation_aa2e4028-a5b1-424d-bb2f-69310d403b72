import { JSX, splitProps } from 'solid-js';
import { css, cva } from '../../styled-system/css';

export type ButtonVariant = 'default' | 'primary' | 'success' | 'warning' | 'danger' | 'text' | 'plain';
export type ButtonSize = 'sm' | 'md' | 'lg';

export interface ButtonProps extends JSX.ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: ButtonVariant;
  size?: ButtonSize;
  loading?: boolean;
  block?: boolean;
}

const buttonStyle = cva({
  base: {
    display: 'inline-flex',
    alignItems: 'center',
    justifyContent: 'center',
    gap: '6px',
    borderRadius: '4px',
    fontWeight: '500',
    cursor: 'pointer',
    transition: 'all 0.15s ease',
    borderWidth: '1px',
    borderStyle: 'solid',
    userSelect: 'none',
    _disabled: { opacity: 0.6, cursor: 'not-allowed' },
  },
  variants: {
    variant: {
      default: {
        backgroundColor: 'white',
        color: '#262626',
        borderColor: '#d9d9d9',
        _hover: { backgroundColor: '#f5f5f5' },
        _active: { backgroundColor: '#eee' },
      },
      primary: {
        backgroundColor: 'primary.500',
        color: 'white',
        borderColor: 'primary.500',
        _hover: { backgroundColor: 'primary.600', borderColor: 'primary.600' },
        _active: { backgroundColor: 'primary.700', borderColor: 'primary.700' },
      },
      success: {
        backgroundColor: 'success.500', color: 'white', borderColor: 'success.500',
        _hover: { backgroundColor: 'success.600', borderColor: 'success.600' },
        _active: { backgroundColor: 'success.700', borderColor: 'success.700' },
      },
      warning: {
        backgroundColor: 'warning.500', color: 'white', borderColor: 'warning.500',
      },
      danger: {
        backgroundColor: 'danger.500', color: 'white', borderColor: 'danger.500',
        _hover: { backgroundColor: 'danger.600', borderColor: 'danger.600' },
      },
      plain: {
        backgroundColor: 'white', color: 'primary.500', borderColor: 'primary.200',
        _hover: { backgroundColor: 'primary.50' },
      },
      text: {
        backgroundColor: 'transparent', color: 'primary.500', borderColor: 'transparent',
        _hover: { backgroundColor: 'primary.50' },
        _active: { backgroundColor: 'primary.100' },
      },
    },
    size: {
      sm: { height: '28px', fontSize: '12px', px: '10px' },
      md: { height: '32px', fontSize: '13px', px: '12px' },
      lg: { height: '36px', fontSize: '14px', px: '14px' },
    },
    block: {
      true: { width: '100%' },
      false: {},
    },
  },
  defaultVariants: { variant: 'default', size: 'md', block: false },
});

export default function Button(allProps: ButtonProps) {
  const [props, rest] = splitProps(allProps, ['class', 'children', 'variant', 'size', 'loading', 'block']);

  return (
    <button
      class={css(buttonStyle({ variant: props.variant, size: props.size, block: !!props.block }), props.class || '')}
      disabled={props.loading || rest.disabled}
      {...rest}
    >
      {props.loading && (
        <span class={css({
          width: '14px', height: '14px', borderRadius: '50%', borderWidth: '2px', borderStyle: 'solid',
          borderTopColor: 'white', borderLeftColor: 'white', borderRightColor: 'transparent', borderBottomColor: 'transparent',
          animation: 'spin 0.8s linear infinite',
        })} />
      )}
      {props.children}
    </button>
  );
}

// simple keyframes
const styleEl = (() => {
  if (typeof document === 'undefined') return null;
  const el = document.createElement('style');
  el.textContent = '@keyframes spin{to{transform:rotate(360deg)}}';
  document.head.appendChild(el);
  return el;
})();

