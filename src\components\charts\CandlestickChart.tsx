import { createSignal, createMemo, For } from 'solid-js'
import { EChartsWrapper } from './EChartsWrapper'
import { Button, Tag } from '../ui'
import { css } from '../../../styled-system/css'
import * as echarts from 'echarts'

export interface KlineData {
  date: string
  open: number
  close: number
  high: number
  low: number
  volume: number
}

export interface CandlestickChartProps {
  data: KlineData[]
  symbol: string
  loading?: boolean
  height?: string | number
  onPeriodChange?: (period: string) => void
}

export function CandlestickChart(props: CandlestickChartProps) {
  const [selectedPeriod, setSelectedPeriod] = createSignal('1D')
  const [showVolume, setShowVolume] = createSignal(true)
  const [showMA, setShowMA] = createSignal(true)

  const periods = [
    { key: '1m', label: '1分钟' },
    { key: '5m', label: '5分钟' },
    { key: '15m', label: '15分钟' },
    { key: '1H', label: '1小时' },
    { key: '1D', label: '日线' },
    { key: '1W', label: '周线' },
    { key: '1M', label: '月线' },
  ]

  // 计算移动平均线
  const calculateMA = (data: number[], period: number) => {
    const result: (number | null)[] = []
    for (let i = 0; i < data.length; i++) {
      if (i < period - 1) {
        result.push(null)
      } else {
        const sum = data.slice(i - period + 1, i + 1).reduce((a, b) => a + b, 0)
        result.push(sum / period)
      }
    }
    return result
  }

  const chartOption = createMemo(() => {
    if (!props.data || props.data.length === 0) {
      return {}
    }

    const dates = props.data.map(item => item.date)
    const klineData = props.data.map(item => [item.open, item.close, item.low, item.high])
    const volumeData = props.data.map(item => item.volume)
    const closeData = props.data.map(item => item.close)

    // 计算移动平均线
    const ma5 = showMA() ? calculateMA(closeData, 5) : []
    const ma10 = showMA() ? calculateMA(closeData, 10) : []
    const ma20 = showMA() ? calculateMA(closeData, 20) : []

    const option: echarts.EChartsOption = {
      backgroundColor: 'transparent',
      animation: false,
      legend: {
        data: ['K线', ...(showMA() ? ['MA5', 'MA10', 'MA20'] : []), ...(showVolume() ? ['成交量'] : [])],
        top: 10,
        textStyle: {
          color: '#606266',
        },
      },
      grid: [
        {
          left: '10%',
          right: '8%',
          top: '15%',
          height: showVolume() ? '50%' : '70%',
        },
        ...(showVolume() ? [{
          left: '10%',
          right: '8%',
          top: '70%',
          height: '20%',
        }] : []),
      ],
      xAxis: [
        {
          type: 'category',
          data: dates,
          boundaryGap: false,
          axisLine: { onZero: false },
          splitLine: { show: false },
          min: 'dataMin',
          max: 'dataMax',
          axisLabel: {
            formatter: (value: string) => {
              const date = new Date(value)
              return selectedPeriod().includes('m') || selectedPeriod().includes('H')
                ? `${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}`
                : `${date.getMonth() + 1}/${date.getDate()}`
            },
          },
        },
        ...(showVolume() ? [{
          type: 'category',
          gridIndex: 1,
          data: dates,
          boundaryGap: false,
          axisLine: { onZero: false },
          axisTick: { show: false },
          splitLine: { show: false },
          axisLabel: { show: false },
          min: 'dataMin',
          max: 'dataMax',
        }] : []),
      ],
      yAxis: [
        {
          scale: true,
          splitArea: {
            show: true,
          },
        },
        ...(showVolume() ? [{
          scale: true,
          gridIndex: 1,
          splitNumber: 2,
          axisLabel: { show: false },
          axisLine: { show: false },
          axisTick: { show: false },
          splitLine: { show: false },
        }] : []),
      ],
      dataZoom: [
        {
          type: 'inside',
          xAxisIndex: showVolume() ? [0, 1] : [0],
          start: 80,
          end: 100,
        },
        {
          show: true,
          xAxisIndex: showVolume() ? [0, 1] : [0],
          type: 'slider',
          top: '90%',
          start: 80,
          end: 100,
        },
      ],
      series: [
        {
          name: 'K线',
          type: 'candlestick',
          data: klineData,
          itemStyle: {
            color: '#ef4444', // 阳线颜色 (红色)
            color0: '#22c55e', // 阴线颜色 (绿色)
            borderColor: '#ef4444',
            borderColor0: '#22c55e',
          },
        },
        ...(showMA() ? [
          {
            name: 'MA5',
            type: 'line',
            data: ma5,
            smooth: true,
            lineStyle: {
              opacity: 0.8,
              width: 1,
              color: '#f59e0b',
            },
            showSymbol: false,
          },
          {
            name: 'MA10',
            type: 'line',
            data: ma10,
            smooth: true,
            lineStyle: {
              opacity: 0.8,
              width: 1,
              color: '#3b82f6',
            },
            showSymbol: false,
          },
          {
            name: 'MA20',
            type: 'line',
            data: ma20,
            smooth: true,
            lineStyle: {
              opacity: 0.8,
              width: 1,
              color: '#8b5cf6',
            },
            showSymbol: false,
          },
        ] : []),
        ...(showVolume() ? [{
          name: '成交量',
          type: 'bar',
          xAxisIndex: 1,
          yAxisIndex: 1,
          data: volumeData.map((vol, index) => ({
            value: vol,
            itemStyle: {
              color: klineData[index][1] >= klineData[index][0] ? '#ef4444' : '#22c55e',
            },
          })),
        }] : []),
      ],
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'cross',
        },
        formatter: (params: any) => {
          const dataIndex = params[0].dataIndex
          const kline = props.data[dataIndex]
          if (!kline) return ''

          return `
            <div style="font-size: 12px;">
              <div style="font-weight: bold; margin-bottom: 4px;">${kline.date}</div>
              <div>开盘: ${kline.open.toFixed(2)}</div>
              <div>收盘: ${kline.close.toFixed(2)}</div>
              <div>最高: ${kline.high.toFixed(2)}</div>
              <div>最低: ${kline.low.toFixed(2)}</div>
              <div>成交量: ${(kline.volume / 10000).toFixed(0)}万</div>
            </div>
          `
        },
      },
    }

    return option
  })

  const handlePeriodChange = (period: string) => {
    setSelectedPeriod(period)
    props.onPeriodChange?.(period)
  }

  return (
    <div class={css({ width: '100%' })}>
      {/* 工具栏 */}
      <div class={css({
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'space-between',
        marginBottom: '16px',
        padding: '12px 16px',
        backgroundColor: 'white',
        borderRadius: '4px',
        border: '1px solid',
        borderColor: 'border.base',
      })}>
        <div class={css({ display: 'flex', alignItems: 'center', gap: '16px' })}>
          <h3 class={css({ margin: 0, fontSize: '16px', fontWeight: '600', color: 'text.primary' })}>
            {props.symbol} K线图
          </h3>
          <div class={css({ display: 'flex', gap: '4px' })}>
            <For each={periods}>
              {(period) => (
                <Button
                  size="small"
                  variant={selectedPeriod() === period.key ? 'primary' : 'default'}
                  onClick={() => handlePeriodChange(period.key)}
                >
                  {period.label}
                </Button>
              )}
            </For>
          </div>
        </div>
        
        <div class={css({ display: 'flex', alignItems: 'center', gap: '12px' })}>
          <Tag
            type={showMA() ? 'primary' : 'info'}
            effect="light"
            onClick={() => setShowMA(!showMA())}
            class={css({ cursor: 'pointer' })}
          >
            均线
          </Tag>
          <Tag
            type={showVolume() ? 'primary' : 'info'}
            effect="light"
            onClick={() => setShowVolume(!showVolume())}
            class={css({ cursor: 'pointer' })}
          >
            成交量
          </Tag>
        </div>
      </div>

      {/* 图表 */}
      <div class={css({
        backgroundColor: 'white',
        borderRadius: '4px',
        border: '1px solid',
        borderColor: 'border.base',
        padding: '16px',
      })}>
        <EChartsWrapper
          option={chartOption()}
          height={props.height || '500px'}
          loading={props.loading}
        />
      </div>
    </div>
  )
}
