import { JSX, splitProps, mergeProps } from 'solid-js'
import { css } from '../../../styled-system/css'
import clsx from 'clsx'

export interface ButtonProps extends JSX.ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: 'primary' | 'success' | 'warning' | 'danger' | 'info' | 'text' | 'default'
  size?: 'large' | 'default' | 'small'
  loading?: boolean
  disabled?: boolean
  icon?: JSX.Element
  round?: boolean
  circle?: boolean
  plain?: boolean
}

export function Button(props: ButtonProps) {
  const merged = mergeProps(
    {
      variant: 'default' as const,
      size: 'default' as const,
      type: 'button' as const,
    },
    props
  )

  const [local, others] = splitProps(merged, [
    'variant',
    'size',
    'loading',
    'disabled',
    'icon',
    'round',
    'circle',
    'plain',
    'children',
    'class',
  ])

  const getVariantStyles = () => {
    const base = {
      display: 'inline-flex',
      alignItems: 'center',
      justifyContent: 'center',
      gap: '8px',
      fontWeight: '500',
      borderRadius: local.round ? '20px' : local.circle ? '50%' : '4px',
      border: '1px solid',
      cursor: 'pointer',
      transition: 'all 0.3s',
      outline: 'none',
      userSelect: 'none',
      verticalAlign: 'middle',
      whiteSpace: 'nowrap',
      textDecoration: 'none',
      _focus: {
        outline: '2px solid',
        outlineOffset: '2px',
      },
      _disabled: {
        cursor: 'not-allowed',
        opacity: '0.5',
      },
    }

    const sizeStyles = {
      large: {
        height: '40px',
        padding: local.circle ? '0' : '12px 20px',
        fontSize: '14px',
        minWidth: local.circle ? '40px' : 'auto',
      },
      default: {
        height: '32px',
        padding: local.circle ? '0' : '8px 16px',
        fontSize: '14px',
        minWidth: local.circle ? '32px' : 'auto',
      },
      small: {
        height: '24px',
        padding: local.circle ? '0' : '4px 12px',
        fontSize: '12px',
        minWidth: local.circle ? '24px' : 'auto',
      },
    }

    const variantStyles = {
      primary: local.plain
        ? {
            color: 'primary.500',
            backgroundColor: 'primary.50',
            borderColor: 'primary.200',
            _hover: {
              backgroundColor: 'primary.500',
              color: 'white',
              borderColor: 'primary.500',
            },
            _focus: {
              outlineColor: 'primary.500',
            },
          }
        : {
            color: 'white',
            backgroundColor: 'primary.500',
            borderColor: 'primary.500',
            _hover: {
              backgroundColor: 'primary.600',
              borderColor: 'primary.600',
            },
            _focus: {
              outlineColor: 'primary.500',
            },
          },
      success: local.plain
        ? {
            color: 'success.500',
            backgroundColor: 'success.50',
            borderColor: 'success.200',
            _hover: {
              backgroundColor: 'success.500',
              color: 'white',
              borderColor: 'success.500',
            },
            _focus: {
              outlineColor: 'success.500',
            },
          }
        : {
            color: 'white',
            backgroundColor: 'success.500',
            borderColor: 'success.500',
            _hover: {
              backgroundColor: 'success.600',
              borderColor: 'success.600',
            },
            _focus: {
              outlineColor: 'success.500',
            },
          },
      warning: local.plain
        ? {
            color: 'warning.500',
            backgroundColor: 'warning.50',
            borderColor: 'warning.200',
            _hover: {
              backgroundColor: 'warning.500',
              color: 'white',
              borderColor: 'warning.500',
            },
            _focus: {
              outlineColor: 'warning.500',
            },
          }
        : {
            color: 'white',
            backgroundColor: 'warning.500',
            borderColor: 'warning.500',
            _hover: {
              backgroundColor: 'warning.600',
              borderColor: 'warning.600',
            },
            _focus: {
              outlineColor: 'warning.500',
            },
          },
      danger: local.plain
        ? {
            color: 'danger.500',
            backgroundColor: 'danger.50',
            borderColor: 'danger.200',
            _hover: {
              backgroundColor: 'danger.500',
              color: 'white',
              borderColor: 'danger.500',
            },
            _focus: {
              outlineColor: 'danger.500',
            },
          }
        : {
            color: 'white',
            backgroundColor: 'danger.500',
            borderColor: 'danger.500',
            _hover: {
              backgroundColor: 'danger.600',
              borderColor: 'danger.600',
            },
            _focus: {
              outlineColor: 'danger.500',
            },
          },
      info: local.plain
        ? {
            color: 'info.500',
            backgroundColor: 'info.50',
            borderColor: 'info.200',
            _hover: {
              backgroundColor: 'info.500',
              color: 'white',
              borderColor: 'info.500',
            },
            _focus: {
              outlineColor: 'info.500',
            },
          }
        : {
            color: 'white',
            backgroundColor: 'info.500',
            borderColor: 'info.500',
            _hover: {
              backgroundColor: 'info.600',
              borderColor: 'info.600',
            },
            _focus: {
              outlineColor: 'info.500',
            },
          },
      text: {
        color: 'primary.500',
        backgroundColor: 'transparent',
        borderColor: 'transparent',
        _hover: {
          color: 'primary.600',
          backgroundColor: 'primary.50',
        },
        _focus: {
          outlineColor: 'primary.500',
        },
      },
      default: {
        color: 'text.regular',
        backgroundColor: 'white',
        borderColor: 'border.base',
        _hover: {
          color: 'primary.500',
          borderColor: 'primary.300',
        },
        _focus: {
          outlineColor: 'primary.500',
        },
      },
    }

    return {
      ...base,
      ...sizeStyles[local.size],
      ...variantStyles[local.variant],
    }
  }

  return (
    <button
      class={clsx(css(getVariantStyles()), local.class)}
      disabled={local.disabled || local.loading}
      {...others}
    >
      {local.loading && (
        <span class={css({ animation: 'spin 1s linear infinite' })}>⟳</span>
      )}
      {local.icon && !local.loading && local.icon}
      {local.children && !local.circle && local.children}
    </button>
  )
}
