import { createMemo, createSignal, For } from 'solid-js'
import { EChartsWrapper } from './EChartsWrapper'
import { Button, Tag } from '../ui'
import { css } from '../../../styled-system/css'
import * as echarts from 'echarts'

export interface EquityData {
  date: string
  totalAssets: number
  dailyReturn: number
  cumulativeReturn: number
  benchmark?: number
}

export interface EquityCurveChartProps {
  data: EquityData[]
  loading?: boolean
  height?: string | number
  showBenchmark?: boolean
  onPeriodChange?: (period: string) => void
}

export function EquityCurveChart(props: EquityCurveChartProps) {
  const [selectedPeriod, setSelectedPeriod] = createSignal('1M')
  const [showDrawdown, setShowDrawdown] = createSignal(false)

  const periods = [
    { key: '1W', label: '近一周' },
    { key: '1M', label: '近一月' },
    { key: '3M', label: '近三月' },
    { key: '6M', label: '近半年' },
    { key: '1Y', label: '近一年' },
    { key: 'ALL', label: '全部' },
  ]

  // 计算最大回撤
  const calculateDrawdown = (data: number[]) => {
    const drawdown: number[] = []
    let peak = data[0] || 0
    
    for (let i = 0; i < data.length; i++) {
      if (data[i] > peak) {
        peak = data[i]
      }
      drawdown.push(((data[i] - peak) / peak) * 100)
    }
    
    return drawdown
  }

  const chartOption = createMemo(() => {
    if (!props.data || props.data.length === 0) {
      return {}
    }

    const dates = props.data.map(item => item.date)
    const totalAssets = props.data.map(item => item.totalAssets)
    const cumulativeReturns = props.data.map(item => item.cumulativeReturn * 100)
    const benchmark = props.data.map(item => (item.benchmark || 0) * 100)
    const drawdown = showDrawdown() ? calculateDrawdown(totalAssets) : []

    const option: echarts.EChartsOption = {
      backgroundColor: 'transparent',
      legend: {
        data: [
          '总资产',
          '累计收益率',
          ...(props.showBenchmark ? ['基准收益'] : []),
          ...(showDrawdown() ? ['最大回撤'] : []),
        ],
        top: 10,
        textStyle: {
          color: '#606266',
        },
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '10%',
        top: '15%',
        containLabel: true,
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'cross',
          label: {
            backgroundColor: '#6a7985',
          },
        },
        formatter: (params: any) => {
          const dataIndex = params[0].dataIndex
          const data = props.data[dataIndex]
          if (!data) return ''

          return `
            <div style="font-size: 12px;">
              <div style="font-weight: bold; margin-bottom: 4px;">${data.date}</div>
              <div>总资产: ¥${data.totalAssets.toLocaleString()}</div>
              <div>日收益率: ${(data.dailyReturn * 100).toFixed(2)}%</div>
              <div>累计收益率: ${(data.cumulativeReturn * 100).toFixed(2)}%</div>
              ${props.showBenchmark && data.benchmark ? 
                `<div>基准收益: ${(data.benchmark * 100).toFixed(2)}%</div>` : ''
              }
            </div>
          `
        },
      },
      xAxis: {
        type: 'category',
        boundaryGap: false,
        data: dates,
        axisLabel: {
          formatter: (value: string) => {
            const date = new Date(value)
            return `${date.getMonth() + 1}/${date.getDate()}`
          },
        },
      },
      yAxis: [
        {
          type: 'value',
          name: '总资产 (¥)',
          position: 'left',
          axisLabel: {
            formatter: (value: number) => {
              if (value >= 10000) {
                return `${(value / 10000).toFixed(1)}万`
              }
              return value.toLocaleString()
            },
          },
        },
        {
          type: 'value',
          name: '收益率 (%)',
          position: 'right',
          axisLabel: {
            formatter: '{value}%',
          },
        },
      ],
      dataZoom: [
        {
          type: 'inside',
          start: 70,
          end: 100,
        },
        {
          show: true,
          type: 'slider',
          start: 70,
          end: 100,
        },
      ],
      series: [
        {
          name: '总资产',
          type: 'line',
          yAxisIndex: 0,
          data: totalAssets,
          smooth: true,
          symbol: 'none',
          lineStyle: {
            width: 2,
            color: '#409eff',
          },
          areaStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              { offset: 0, color: 'rgba(64, 158, 255, 0.3)' },
              { offset: 1, color: 'rgba(64, 158, 255, 0.1)' },
            ]),
          },
        },
        {
          name: '累计收益率',
          type: 'line',
          yAxisIndex: 1,
          data: cumulativeReturns,
          smooth: true,
          symbol: 'none',
          lineStyle: {
            width: 2,
            color: '#67c23a',
          },
        },
        ...(props.showBenchmark ? [{
          name: '基准收益',
          type: 'line',
          yAxisIndex: 1,
          data: benchmark,
          smooth: true,
          symbol: 'none',
          lineStyle: {
            width: 1,
            color: '#909399',
            type: 'dashed' as const,
          },
        }] : []),
        ...(showDrawdown() ? [{
          name: '最大回撤',
          type: 'line',
          yAxisIndex: 1,
          data: drawdown,
          smooth: true,
          symbol: 'none',
          lineStyle: {
            width: 1,
            color: '#f56c6c',
          },
          areaStyle: {
            color: 'rgba(245, 108, 108, 0.1)',
          },
        }] : []),
      ],
    }

    return option
  })

  const handlePeriodChange = (period: string) => {
    setSelectedPeriod(period)
    props.onPeriodChange?.(period)
  }

  // 计算统计数据
  const stats = createMemo(() => {
    if (!props.data || props.data.length === 0) return null

    const latestData = props.data[props.data.length - 1]
    const firstData = props.data[0]
    const totalReturn = latestData.cumulativeReturn * 100
    const totalAssets = latestData.totalAssets
    const dailyReturns = props.data.map(d => d.dailyReturn)
    
    // 计算夏普比率 (简化版，假设无风险利率为0)
    const avgReturn = dailyReturns.reduce((a, b) => a + b, 0) / dailyReturns.length
    const variance = dailyReturns.reduce((a, b) => a + Math.pow(b - avgReturn, 2), 0) / dailyReturns.length
    const volatility = Math.sqrt(variance * 252) // 年化波动率
    const sharpeRatio = (avgReturn * 252) / volatility

    // 最大回撤
    const drawdownData = calculateDrawdown(props.data.map(d => d.totalAssets))
    const maxDrawdown = Math.min(...drawdownData)

    return {
      totalAssets,
      totalReturn,
      sharpeRatio,
      maxDrawdown,
      volatility: volatility * 100,
    }
  })

  return (
    <div class={css({ width: '100%' })}>
      {/* 工具栏 */}
      <div class={css({
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'space-between',
        marginBottom: '16px',
        padding: '12px 16px',
        backgroundColor: 'white',
        borderRadius: '4px',
        border: '1px solid',
        borderColor: 'border.base',
      })}>
        <div class={css({ display: 'flex', alignItems: 'center', gap: '16px' })}>
          <h3 class={css({ margin: 0, fontSize: '16px', fontWeight: '600', color: 'text.primary' })}>
            资金曲线
          </h3>
          <div class={css({ display: 'flex', gap: '4px' })}>
            <For each={periods}>
              {(period) => (
                <Button
                  size="small"
                  variant={selectedPeriod() === period.key ? 'primary' : 'default'}
                  onClick={() => handlePeriodChange(period.key)}
                >
                  {period.label}
                </Button>
              )}
            </For>
          </div>
        </div>
        
        <div class={css({ display: 'flex', alignItems: 'center', gap: '12px' })}>
          <Tag
            type={showDrawdown() ? 'primary' : 'info'}
            effect="light"
            onClick={() => setShowDrawdown(!showDrawdown())}
            class={css({ cursor: 'pointer' })}
          >
            回撤
          </Tag>
        </div>
      </div>

      {/* 统计数据 */}
      {stats() && (
        <div class={css({
          display: 'grid',
          gridTemplateColumns: 'repeat(auto-fit, minmax(120px, 1fr))',
          gap: '12px',
          marginBottom: '16px',
        })}>
          <div class={css({
            backgroundColor: 'white',
            padding: '12px',
            borderRadius: '4px',
            border: '1px solid',
            borderColor: 'border.base',
            textAlign: 'center',
          })}>
            <div class={css({ fontSize: '12px', color: 'text.secondary' })}>总资产</div>
            <div class={css({ fontSize: '16px', fontWeight: '600', color: 'text.primary' })}>
              ¥{stats()!.totalAssets.toLocaleString()}
            </div>
          </div>
          <div class={css({
            backgroundColor: 'white',
            padding: '12px',
            borderRadius: '4px',
            border: '1px solid',
            borderColor: 'border.base',
            textAlign: 'center',
          })}>
            <div class={css({ fontSize: '12px', color: 'text.secondary' })}>累计收益</div>
            <div class={css({
              fontSize: '16px',
              fontWeight: '600',
              color: stats()!.totalReturn >= 0 ? 'success.500' : 'danger.500',
            })}>
              {stats()!.totalReturn >= 0 ? '+' : ''}{stats()!.totalReturn.toFixed(2)}%
            </div>
          </div>
          <div class={css({
            backgroundColor: 'white',
            padding: '12px',
            borderRadius: '4px',
            border: '1px solid',
            borderColor: 'border.base',
            textAlign: 'center',
          })}>
            <div class={css({ fontSize: '12px', color: 'text.secondary' })}>夏普比率</div>
            <div class={css({ fontSize: '16px', fontWeight: '600', color: 'text.primary' })}>
              {stats()!.sharpeRatio.toFixed(2)}
            </div>
          </div>
          <div class={css({
            backgroundColor: 'white',
            padding: '12px',
            borderRadius: '4px',
            border: '1px solid',
            borderColor: 'border.base',
            textAlign: 'center',
          })}>
            <div class={css({ fontSize: '12px', color: 'text.secondary' })}>最大回撤</div>
            <div class={css({ fontSize: '16px', fontWeight: '600', color: 'danger.500' })}>
              {stats()!.maxDrawdown.toFixed(2)}%
            </div>
          </div>
          <div class={css({
            backgroundColor: 'white',
            padding: '12px',
            borderRadius: '4px',
            border: '1px solid',
            borderColor: 'border.base',
            textAlign: 'center',
          })}>
            <div class={css({ fontSize: '12px', color: 'text.secondary' })}>年化波动</div>
            <div class={css({ fontSize: '16px', fontWeight: '600', color: 'text.primary' })}>
              {stats()!.volatility.toFixed(2)}%
            </div>
          </div>
        </div>
      )}

      {/* 图表 */}
      <div class={css({
        backgroundColor: 'white',
        borderRadius: '4px',
        border: '1px solid',
        borderColor: 'border.base',
        padding: '16px',
      })}>
        <EChartsWrapper
          option={chartOption()}
          height={props.height || '400px'}
          loading={props.loading}
        />
      </div>
    </div>
  )
}
