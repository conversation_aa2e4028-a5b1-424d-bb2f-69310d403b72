import{d as kt,c as ue,u as No,t as W,i as l,m as pe,a as A,b as t,e as B,A as Xo,o as ht,f as _t,s as q,F as Ge,g as Ao,h as Ko,j as It,k as Wt,l as Tt,P as Yo,S as He,n as Vo,R as Ye,p as Uo,r as Go}from"./vendor-solid-C4S8u6s-.js";import{l as _o}from"./vendor-editor-l7stcynF.js";(function(){const r=document.createElement("link").relList;if(r&&r.supports&&r.supports("modulepreload"))return;for(const a of document.querySelectorAll('link[rel="modulepreload"]'))b(a);new MutationObserver(a=>{for(const p of a)if(p.type==="childList")for(const S of p.addedNodes)S.tagName==="LINK"&&S.rel==="modulepreload"&&b(S)}).observe(document,{childList:!0,subtree:!0});function x(a){const p={};return a.integrity&&(p.integrity=a.integrity),a.referrerPolicy&&(p.referrerPolicy=a.referrerPolicy),a.crossOrigin==="use-credentials"?p.credentials="include":a.crossOrigin==="anonymous"?p.credentials="omit":p.credentials="same-origin",p}function b(a){if(a.ep)return;a.ep=!0;const p=x(a);fetch(a.href,p)}})();function wt(i){return typeof i=="object"&&i!=null&&!Array.isArray(i)}function Qo(i){return Object.fromEntries(Object.entries(i??{}).filter(([r,x])=>x!==void 0))}var Zo=i=>i==="base";function Jo(i){return i.slice().filter(r=>!Zo(r))}function zo(i){return String.fromCharCode(i+(i>25?39:97))}function ei(i){let r="",x;for(x=Math.abs(i);x>52;x=x/52|0)r=zo(x%52)+r;return zo(x%52)+r}function ti(i,r){let x=r.length;for(;x;)i=i*33^r.charCodeAt(--x);return i}function oi(i){return ei(ti(5381,i)>>>0)}var Bo=/\s*!(important)?/i;function ii(i){return typeof i=="string"?Bo.test(i):!1}function ri(i){return typeof i=="string"?i.replace(Bo,"").trim():i}function Po(i){return typeof i=="string"?i.replaceAll(" ","_"):i}var At=i=>{const r=new Map;return(...b)=>{const a=JSON.stringify(b);if(r.has(a))return r.get(a);const p=i(...b);return r.set(a,p),p}};function Lo(...i){return i.filter(Boolean).reduce((x,b)=>(Object.keys(b).forEach(a=>{const p=x[a],S=b[a];wt(p)&&wt(S)?x[a]=Lo(p,S):x[a]=S}),x),{})}var ni=i=>i!=null;function Do(i,r,x={}){const{stop:b,getKey:a}=x;function p(S,c=[]){if(wt(S)||Array.isArray(S)){const k={};for(const[T,P]of Object.entries(S)){const u=a?.(T,P)??T,m=[...c,u];if(b?.(S,m))return r(S,c);const R=p(P,m);ni(R)&&(k[u]=R)}return k}return r(S,c)}return p(i)}function li(i,r){return i.reduce((x,b,a)=>{const p=r[a];return b!=null&&(x[p]=b),x},{})}function Eo(i,r,x=!0){const{utility:b,conditions:a}=r,{hasShorthand:p,resolveShorthand:S}=b;return Do(i,c=>Array.isArray(c)?li(c,a.breakpoints.keys):c,{stop:c=>Array.isArray(c),getKey:x?c=>p?S(c):c:void 0})}var di={shift:i=>i,finalize:i=>i,breakpoints:{keys:[]}},ai=i=>typeof i=="string"?i.replaceAll(/[\n\s]+/g," "):i;function si(i){const{utility:r,hash:x,conditions:b=di}=i,a=S=>[r.prefix,S].filter(Boolean).join("-"),p=(S,c)=>{let k;if(x){const T=[...b.finalize(S),c];k=a(r.toHash(T,oi))}else k=[...b.finalize(S),a(c)].join(":");return k};return At(({base:S,...c}={})=>{const k=Object.assign(c,S),T=Eo(k,i),P=new Set;return Do(T,(u,m)=>{const R=ii(u);if(u==null)return;const[y,...I]=b.shift(m),Y=Jo(I),E=r.transform(y,ri(ai(u)));let M=p(Y,E.className);R&&(M=`${M}!`),P.add(M)}),Array.from(P).join(" ")})}function ci(...i){return i.flat().filter(r=>wt(r)&&Object.keys(Qo(r)).length>0)}function gi(i){function r(a){const p=ci(...a);return p.length===1?p:p.map(S=>Eo(S,i))}function x(...a){return Lo(...r(a))}function b(...a){return Object.assign({},...r(a))}return{mergeCss:At(x),assignCss:b}}var xi=/([A-Z])/g,ui=/^ms-/,pi=At(i=>i.startsWith("--")?i:i.replace(xi,"-$1").replace(ui,"-ms-").toLowerCase()),vi="cm,mm,Q,in,pc,pt,px,em,ex,ch,rem,lh,rlh,vw,vh,vmin,vmax,vb,vi,svw,svh,lvw,lvh,dvw,dvh,cqw,cqh,cqi,cqb,cqmin,cqmax,%";`${vi.split(",").join("|")}`;const bi="_dark,_light,_hover,_focus,_focusWithin,_focusVisible,_disabled,_active,_visited,_target,_readOnly,_readWrite,_empty,_checked,_enabled,_expanded,_highlighted,_before,_after,_firstLetter,_firstLine,_marker,_selection,_file,_backdrop,_first,_last,_only,_even,_odd,_firstOfType,_lastOfType,_onlyOfType,_peerFocus,_peerHover,_peerActive,_peerFocusWithin,_peerFocusVisible,_peerDisabled,_peerChecked,_peerInvalid,_peerExpanded,_peerPlaceholderShown,_groupFocus,_groupHover,_groupActive,_groupFocusWithin,_groupFocusVisible,_groupDisabled,_groupChecked,_groupExpanded,_groupInvalid,_indeterminate,_required,_valid,_invalid,_autofill,_inRange,_outOfRange,_placeholder,_placeholderShown,_pressed,_selected,_default,_optional,_open,_closed,_fullscreen,_loading,_currentPage,_currentStep,_motionReduce,_motionSafe,_print,_landscape,_portrait,_osDark,_osLight,_highContrast,_lessContrast,_moreContrast,_ltr,_rtl,_scrollbar,_scrollbarThumb,_scrollbarTrack,_horizontal,_vertical,_starting,sm,smOnly,smDown,md,mdOnly,mdDown,lg,lgOnly,lgDown,xl,xlOnly,xlDown,2xl,2xlOnly,2xlDown,smToMd,smToLg,smToXl,smTo2xl,mdToLg,mdToXl,mdTo2xl,lgToXl,lgTo2xl,xlTo2xl,@/xs,@/sm,@/md,@/lg,@/xl,@/2xl,@/3xl,@/4xl,@/5xl,@/6xl,@/7xl,@/8xl,base",Mo=new Set(bi.split(","));function $o(i){return Mo.has(i)||/^@|&|&$/.test(i)}const hi=/^_/,fi=/&|@/;function mi(i){return i.map(r=>Mo.has(r)?r.replace(hi,""):fi.test(r)?`[${Po(r.trim())}]`:r)}function Si(i){return i.sort((r,x)=>{const b=$o(r),a=$o(x);return b&&!a?1:!b&&a?-1:0})}const yi="aspectRatio:aspect,boxDecorationBreak:decoration,zIndex:z,boxSizing:box,objectPosition:obj-pos,objectFit:obj-fit,overscrollBehavior:overscroll,overscrollBehaviorX:overscroll-x,overscrollBehaviorY:overscroll-y,position:pos/1,top:top,left:left,insetInline:inset-x/insetX,insetBlock:inset-y/insetY,inset:inset,insetBlockEnd:inset-b,insetBlockStart:inset-t,insetInlineEnd:end/insetEnd/1,insetInlineStart:start/insetStart/1,right:right,bottom:bottom,float:float,visibility:vis,display:d,hideFrom:hide,hideBelow:show,flexBasis:basis,flex:flex,flexDirection:flex/flexDir,flexGrow:grow,flexShrink:shrink,gridTemplateColumns:grid-cols,gridTemplateRows:grid-rows,gridColumn:col-span,gridRow:row-span,gridColumnStart:col-start,gridColumnEnd:col-end,gridAutoFlow:grid-flow,gridAutoColumns:auto-cols,gridAutoRows:auto-rows,gap:gap,gridGap:gap,gridRowGap:gap-x,gridColumnGap:gap-y,rowGap:gap-x,columnGap:gap-y,justifyContent:justify,alignContent:content,alignItems:items,alignSelf:self,padding:p/1,paddingLeft:pl/1,paddingRight:pr/1,paddingTop:pt/1,paddingBottom:pb/1,paddingBlock:py/1/paddingY,paddingBlockEnd:pb,paddingBlockStart:pt,paddingInline:px/paddingX/1,paddingInlineEnd:pe/1/paddingEnd,paddingInlineStart:ps/1/paddingStart,marginLeft:ml/1,marginRight:mr/1,marginTop:mt/1,marginBottom:mb/1,margin:m/1,marginBlock:my/1/marginY,marginBlockEnd:mb,marginBlockStart:mt,marginInline:mx/1/marginX,marginInlineEnd:me/1/marginEnd,marginInlineStart:ms/1/marginStart,spaceX:space-x,spaceY:space-y,outlineWidth:ring-width/ringWidth,outlineColor:ring-color/ringColor,outline:ring/1,outlineOffset:ring-offset/ringOffset,divideX:divide-x,divideY:divide-y,divideColor:divide-color,divideStyle:divide-style,width:w/1,inlineSize:w,minWidth:min-w/minW,minInlineSize:min-w,maxWidth:max-w/maxW,maxInlineSize:max-w,height:h/1,blockSize:h,minHeight:min-h/minH,minBlockSize:min-h,maxHeight:max-h/maxH,maxBlockSize:max-b,color:text,fontFamily:font,fontSize:fs,fontWeight:fw,fontSmoothing:smoothing,fontVariantNumeric:numeric,letterSpacing:tracking,lineHeight:leading,textAlign:text-align,textDecoration:text-decor,textDecorationColor:text-decor-color,textEmphasisColor:text-emphasis-color,textDecorationStyle:decoration-style,textDecorationThickness:decoration-thickness,textUnderlineOffset:underline-offset,textTransform:text-transform,textIndent:indent,textShadow:text-shadow,textShadowColor:text-shadow/textShadowColor,textOverflow:text-overflow,verticalAlign:v-align,wordBreak:break,textWrap:text-wrap,truncate:truncate,lineClamp:clamp,listStyleType:list-type,listStylePosition:list-pos,listStyleImage:list-img,backgroundPosition:bg-pos/bgPosition,backgroundPositionX:bg-pos-x/bgPositionX,backgroundPositionY:bg-pos-y/bgPositionY,backgroundAttachment:bg-attach/bgAttachment,backgroundClip:bg-clip/bgClip,background:bg/1,backgroundColor:bg/bgColor,backgroundOrigin:bg-origin/bgOrigin,backgroundImage:bg-img/bgImage,backgroundRepeat:bg-repeat/bgRepeat,backgroundBlendMode:bg-blend/bgBlendMode,backgroundSize:bg-size/bgSize,backgroundGradient:bg-gradient/bgGradient,textGradient:text-gradient,gradientFromPosition:gradient-from-pos,gradientToPosition:gradient-to-pos,gradientFrom:gradient-from,gradientTo:gradient-to,gradientVia:gradient-via,gradientViaPosition:gradient-via-pos,borderRadius:rounded/1,borderTopLeftRadius:rounded-tl/roundedTopLeft,borderTopRightRadius:rounded-tr/roundedTopRight,borderBottomRightRadius:rounded-br/roundedBottomRight,borderBottomLeftRadius:rounded-bl/roundedBottomLeft,borderTopRadius:rounded-t/roundedTop,borderRightRadius:rounded-r/roundedRight,borderBottomRadius:rounded-b/roundedBottom,borderLeftRadius:rounded-l/roundedLeft,borderStartStartRadius:rounded-ss/roundedStartStart,borderStartEndRadius:rounded-se/roundedStartEnd,borderStartRadius:rounded-s/roundedStart,borderEndStartRadius:rounded-es/roundedEndStart,borderEndEndRadius:rounded-ee/roundedEndEnd,borderEndRadius:rounded-e/roundedEnd,border:border,borderWidth:border-w,borderTopWidth:border-tw,borderLeftWidth:border-lw,borderRightWidth:border-rw,borderBottomWidth:border-bw,borderColor:border,borderInline:border-x/borderX,borderInlineWidth:border-x/borderXWidth,borderInlineColor:border-x/borderXColor,borderBlock:border-y/borderY,borderBlockWidth:border-y/borderYWidth,borderBlockColor:border-y/borderYColor,borderLeft:border-l,borderLeftColor:border-l,borderInlineStart:border-s/borderStart,borderInlineStartWidth:border-s/borderStartWidth,borderInlineStartColor:border-s/borderStartColor,borderRight:border-r,borderRightColor:border-r,borderInlineEnd:border-e/borderEnd,borderInlineEndWidth:border-e/borderEndWidth,borderInlineEndColor:border-e/borderEndColor,borderTop:border-t,borderTopColor:border-t,borderBottom:border-b,borderBottomColor:border-b,borderBlockEnd:border-be,borderBlockEndColor:border-be,borderBlockStart:border-bs,borderBlockStartColor:border-bs,boxShadow:shadow/1,boxShadowColor:shadow-color/shadowColor,mixBlendMode:mix-blend,filter:filter,brightness:brightness,contrast:contrast,grayscale:grayscale,hueRotate:hue-rotate,invert:invert,saturate:saturate,sepia:sepia,dropShadow:drop-shadow,blur:blur,backdropFilter:backdrop,backdropBlur:backdrop-blur,backdropBrightness:backdrop-brightness,backdropContrast:backdrop-contrast,backdropGrayscale:backdrop-grayscale,backdropHueRotate:backdrop-hue-rotate,backdropInvert:backdrop-invert,backdropOpacity:backdrop-opacity,backdropSaturate:backdrop-saturate,backdropSepia:backdrop-sepia,borderCollapse:border,borderSpacing:border-spacing,borderSpacingX:border-spacing-x,borderSpacingY:border-spacing-y,tableLayout:table,transitionTimingFunction:ease,transitionDelay:delay,transitionDuration:duration,transitionProperty:transition-prop,transition:transition,animation:animation,animationName:animation-name,animationTimingFunction:animation-ease,animationDuration:animation-duration,animationDelay:animation-delay,transformOrigin:origin,rotate:rotate,rotateX:rotate-x,rotateY:rotate-y,rotateZ:rotate-z,scale:scale,scaleX:scale-x,scaleY:scale-y,translate:translate,translateX:translate-x/x,translateY:translate-y/y,translateZ:translate-z/z,accentColor:accent,caretColor:caret,scrollBehavior:scroll,scrollbar:scrollbar,scrollMargin:scroll-m,scrollMarginLeft:scroll-ml,scrollMarginRight:scroll-mr,scrollMarginTop:scroll-mt,scrollMarginBottom:scroll-mb,scrollMarginBlock:scroll-my/scrollMarginY,scrollMarginBlockEnd:scroll-mb,scrollMarginBlockStart:scroll-mt,scrollMarginInline:scroll-mx/scrollMarginX,scrollMarginInlineEnd:scroll-me,scrollMarginInlineStart:scroll-ms,scrollPadding:scroll-p,scrollPaddingBlock:scroll-pb/scrollPaddingY,scrollPaddingBlockStart:scroll-pt,scrollPaddingBlockEnd:scroll-pb,scrollPaddingInline:scroll-px/scrollPaddingX,scrollPaddingInlineEnd:scroll-pe,scrollPaddingInlineStart:scroll-ps,scrollPaddingLeft:scroll-pl,scrollPaddingRight:scroll-pr,scrollPaddingTop:scroll-pt,scrollPaddingBottom:scroll-pb,scrollSnapAlign:snap-align,scrollSnapStop:snap-stop,scrollSnapType:snap-type,scrollSnapStrictness:snap-strictness,scrollSnapMargin:snap-m,scrollSnapMarginTop:snap-mt,scrollSnapMarginBottom:snap-mb,scrollSnapMarginLeft:snap-ml,scrollSnapMarginRight:snap-mr,touchAction:touch,userSelect:select,fill:fill,stroke:stroke,strokeWidth:stroke-w,srOnly:sr,debug:debug,appearance:appearance,backfaceVisibility:backface,clipPath:clip-path,hyphens:hyphens,mask:mask,maskImage:mask-image,maskSize:mask-size,textSizeAdjust:text-adjust,container:cq,containerName:cq-name,containerType:cq-type,textStyle:textStyle",jo=new Map,Oo=new Map;yi.split(",").forEach(i=>{const[r,x]=i.split(":"),[b,...a]=x.split("/");jo.set(r,b),a.length&&a.forEach(p=>{Oo.set(p==="1"?b:p,r)})});const Ro=i=>Oo.get(i)||i,Fo={conditions:{shift:Si,finalize:mi,breakpoints:{keys:["base","sm","md","lg","xl","2xl"]}},utility:{transform:(i,r)=>{const x=Ro(i);return{className:`${jo.get(x)||pi(x)}_${Po(r)}`}},hasShorthand:!0,toHash:(i,r)=>r(i.join(":")),resolveShorthand:Ro}},Ci=si(Fo),e=(...i)=>Ci(Ho(...i));e.raw=(...i)=>Ho(...i);const{mergeCss:Ho}=gi(Fo);function Ve(){let i="",r=0,x;for(;r<arguments.length;)(x=arguments[r++])&&typeof x=="string"&&(i&&(i+=" "),i+=x);return i}var wi=W("<div><aside><div><div>量</div></div><nav></nav><div><button type=button></button></div></aside><main><header><div><h1></h1></div><div><button type=button>帮助</button><button type=button>设置</button><div>用</div></div></header><div>"),ki=W("<div><div>量化平台</div><div>专业版 v2.0"),Io=W("<span>");function _i(i){const[r,x]=ue(!1),b=No(),a=[{id:"dashboard",label:"仪表板",icon:"📊",path:"/dashboard"},{id:"market",label:"行情分析",icon:"📈",path:"/market"},{id:"strategy-editor",label:"策略编辑器",icon:"🧠",path:"/strategy-editor"},{id:"api-test",label:"API测试",icon:"🔧",path:"/api-test"}],p=c=>b.pathname===c||c==="/dashboard"&&b.pathname==="/",S=()=>a.find(k=>p(k.path))?.label||"仪表板";return(()=>{var c=wi(),k=c.firstChild,T=k.firstChild,P=T.firstChild,u=T.nextSibling,m=u.nextSibling,R=m.firstChild,y=k.nextSibling,I=y.firstChild,Y=I.firstChild,E=Y.firstChild,M=Y.nextSibling,h=M.firstChild,z=h.nextSibling,f=z.nextSibling,w=I.nextSibling;return l(T,(()=>{var g=pe(()=>!r());return()=>g()&&(()=>{var _=ki(),$=_.firstChild,V=$.nextSibling;return A(v=>{var d=e({fontSize:"14px",fontWeight:"600",color:"#262626",lineHeight:1.2}),C=e({fontSize:"11px",color:"#8c8c8c",lineHeight:1});return d!==v.e&&t($,v.e=d),C!==v.t&&t(V,v.t=C),v},{e:void 0,t:void 0}),_})()})(),null),l(u,()=>a.map(g=>B(Xo,{get href(){return g.path},get class(){return e({width:"100%",padding:r()?"12px 20px":"12px 16px",border:"none",backgroundColor:p(g.path)?"#e6f7ff":"transparent",color:p(g.path)?"#1890ff":"#595959",fontSize:"14px",textDecoration:"none",cursor:"pointer",transition:"all 0.2s",display:"flex",alignItems:"center",gap:"12px",borderLeft:p(g.path)?"3px solid #1890ff":"3px solid transparent",_hover:{backgroundColor:"#f5f5f5",color:"#1890ff"}})},get children(){return[(()=>{var _=Io();return l(_,()=>g.icon),A(()=>t(_,e({fontSize:"16px"}))),_})(),pe(()=>pe(()=>!r())()&&(()=>{var _=Io();return l(_,()=>g.label),A(()=>t(_,e({fontWeight:p(g.path)?"500":"400"}))),_})())]}}))),R.$$click=()=>x(!r()),l(R,()=>r()?"→":"←"),l(E,S),l(w,()=>i.children),A(g=>{var _=e({display:"flex",minHeight:"100vh",backgroundColor:"#f5f5f5"}),$=e({width:r()?"64px":"240px",backgroundColor:"white",borderRight:"1px solid #e8e8e8",transition:"width 0.3s ease",display:"flex",flexDirection:"column",position:"fixed",height:"100vh",zIndex:1e3}),V=e({padding:"12px 16px",borderBottom:"1px solid #e8e8e8",display:"flex",alignItems:"center",gap:"8px"}),v=e({width:"28px",height:"28px",backgroundColor:"#1890ff",borderRadius:"4px",display:"flex",alignItems:"center",justifyContent:"center",color:"white",fontSize:"14px",fontWeight:"bold"}),d=e({flex:1,padding:"8px 0",overflowY:"auto"}),C=e({padding:"16px",borderTop:"1px solid #e8e8e8"}),L=e({width:"100%",padding:"8px",border:"1px solid #d9d9d9",backgroundColor:"white",borderRadius:"4px",cursor:"pointer",fontSize:"12px",color:"#595959",_hover:{borderColor:"#1890ff",color:"#1890ff"}}),D=e({flex:1,marginLeft:r()?"64px":"240px",transition:"margin-left 0.3s ease",display:"flex",flexDirection:"column"}),F=e({backgroundColor:"white",borderBottom:"1px solid #e8e8e8",padding:"0 24px",height:"64px",display:"flex",alignItems:"center",justifyContent:"space-between"}),j=e({display:"flex",alignItems:"center",gap:"16px"}),X=e({fontSize:"18px",fontWeight:"500",color:"#262626",margin:0}),U=e({display:"flex",alignItems:"center",gap:"16px"}),H=e({padding:"6px 12px",border:"1px solid #d9d9d9",backgroundColor:"white",borderRadius:"4px",fontSize:"14px",cursor:"pointer",_hover:{borderColor:"#1890ff",color:"#1890ff"}}),n=e({padding:"6px 12px",border:"1px solid #d9d9d9",backgroundColor:"white",borderRadius:"4px",fontSize:"14px",cursor:"pointer",_hover:{borderColor:"#1890ff",color:"#1890ff"}}),K=e({width:"32px",height:"32px",backgroundColor:"#1890ff",borderRadius:"50%",display:"flex",alignItems:"center",justifyContent:"center",color:"white",fontSize:"14px",fontWeight:"500"}),ee=e({flex:1,backgroundColor:"#f5f5f5",overflow:"auto"});return _!==g.e&&t(c,g.e=_),$!==g.t&&t(k,g.t=$),V!==g.a&&t(T,g.a=V),v!==g.o&&t(P,g.o=v),d!==g.i&&t(u,g.i=d),C!==g.n&&t(m,g.n=C),L!==g.s&&t(R,g.s=L),D!==g.h&&t(y,g.h=D),F!==g.r&&t(I,g.r=F),j!==g.d&&t(Y,g.d=j),X!==g.l&&t(E,g.l=X),U!==g.u&&t(M,g.u=U),H!==g.c&&t(h,g.c=H),n!==g.w&&t(z,g.w=n),K!==g.m&&t(f,g.m=K),ee!==g.f&&t(w,g.f=ee),g},{e:void 0,t:void 0,a:void 0,o:void 0,i:void 0,n:void 0,s:void 0,h:void 0,r:void 0,d:void 0,l:void 0,u:void 0,c:void 0,w:void 0,m:void 0,f:void 0}),c})()}kt(["click"]);var zi=W('<div style=flex-direction:column;background-color:#f0f2f5;min-height:100vh><div style="background-color:#52c41a;border-radius:6px;text-align:center;font-size:16px;font-weight:600;box-shadow:0 2px 8px rgba(82, 196, 26, 0.3)">🎉 新版专业量化平台界面已成功加载！</div><div style=align-items:center;justify-content:space-between;margin-bottom:8px><h1 style=font-size:20px;font-weight:600>投资仪表盘</h1><div style=align-items:center><button style=background-color:#1890ff;border-radius:4px;font-size:12px>刷新</button><button style=background-color:white;border-radius:4px;font-size:12px>设置</button><button style=background-color:#52c41a;border-radius:4px;font-size:12px>新增策略</button></div></div><div style="grid-template-columns:repeat(auto-fit, minmax(200px, 1fr));margin-bottom:16px"></div><div><div><div><h3>资金曲线图</h3><div><button type=button>日</button><button type=button>周</button><button type=button>月</button></div></div><div><div>📈</div><div>资金曲线图表</div><div>显示策略收益走势</div></div></div><div><div><h3>持仓概览</h3><span>查看全部 →</span></div><div></div></div></div><div><div><div><h3>今日行情</h3><span>更新时间: 15:30</span></div><div></div></div><div><div><h3>最新资讯</h3><span>查看更多 →</span></div><div></div></div></div><div><div>当前时间: </div><div><span>数据来源: 模拟数据</span><span>更新频率: 实时</span><div><div></div><span>系统正常'),$i=W('<div style="background-color:white;border-radius:8px;flex-direction:column;align-items:center;text-align:center;min-height:120px;box-shadow:0 1px 3px rgba(0,0,0,0.1)"><div style=font-size:24px;margin-bottom:8px></div><div style=font-size:24px;font-weight:600;margin-bottom:4px></div><div style=font-size:12px;margin-bottom:8px></div><div style=font-size:12px;font-weight:500>'),Ri=W("<div><div><span></span><span>%</span></div><div><span></span><span>"),Ii=W("<div><div><div></div><span></span></div><div><div></div><div> (<!>)"),Wi=W("<div><div></div><div><span></span><span>");function Wo(){console.log("🔥 Dashboard组件已加载 - 新版本 - 时间戳:",Date.now());const[i,r]=ue(new Date().toLocaleString("zh-CN"));let x;ht(()=>{x=setInterval(()=>{r(new Date().toLocaleString("zh-CN"))},1e3)}),_t(()=>{x&&clearInterval(x)});const b=[{title:"总资产",value:"¥1,000,000",change:"+2.34%",trend:"up",icon:"💰",description:"总资产",subValue:"¥1,000,000"},{title:"今日盈亏",value:"0",change:"+0.00%",trend:"neutral",icon:"📊",description:"今日盈亏",subValue:"0.00%"},{title:"持仓市值",value:"¥50,000",change:"+0.00%",trend:"neutral",icon:"📈",description:"持仓市值",subValue:"¥50,000"},{title:"可用资金",value:"2",change:"+0.00%",trend:"neutral",icon:"🔒",description:"持仓数量",subValue:"2"}],a=[{code:"000725",name:"京东方A",price:3.45,change:-1.43,changePercent:-29.3,volume:7340.75,amount:-6046,status:"持仓"},{code:"300519",name:"新光药业",price:68.94,change:-1.05,changePercent:-1.5,volume:410.75,amount:-1796,status:"持仓"},{code:"000831",name:"五矿稀土",price:26.09,change:-1.37,changePercent:-5,volume:7558.72,amount:-7688,status:"持仓"}],p=[{name:"上证指数",value:"3,245.67",change:"+23.45",percent:"+0.73%",trend:"up"},{name:"深证成指",value:"10,567.23",change:"+45.67",percent:"+0.43%",trend:"up"},{name:"创业板指",value:"2,234.56",change:"-8.90",percent:"-0.40%",trend:"down"},{name:"科创50",value:"1,123.45",change:"+15.23",percent:"+1.37%",trend:"up"}],S=[{title:"A股市场今日表现强劲，科技股领涨",time:"刚刚发布",type:"market"},{title:"央行宣布降准0.25个百分点",time:"30分钟前",type:"policy"},{title:"新能源板块持续活跃，多只个股涨停",time:"1小时前",type:"sector"}];return(()=>{var c=zi(),k=c.firstChild,T=k.nextSibling,P=T.firstChild,u=P.nextSibling,m=u.firstChild,R=m.nextSibling,y=R.nextSibling,I=T.nextSibling,Y=I.nextSibling,E=Y.firstChild,M=E.firstChild,h=M.firstChild,z=h.nextSibling,f=z.firstChild,w=f.nextSibling,g=w.nextSibling,_=M.nextSibling,$=_.firstChild,V=$.nextSibling,v=V.nextSibling,d=E.nextSibling,C=d.firstChild,L=C.firstChild,D=L.nextSibling,F=C.nextSibling,j=Y.nextSibling,X=j.firstChild,U=X.firstChild,H=U.firstChild,n=H.nextSibling,K=U.nextSibling,ee=X.nextSibling,oe=ee.firstChild,ne=oe.firstChild,se=ne.nextSibling,he=oe.nextSibling,ve=j.nextSibling,me=ve.firstChild;me.firstChild;var Ce=me.nextSibling,Te=Ce.firstChild,we=Te.nextSibling,Se=we.nextSibling,qe=Se.firstChild;return q(c,"display","flex"),q(c,"gap","16px"),q(c,"width","100%"),q(c,"padding","16px"),q(k,"color","white"),q(k,"padding","12px 16px"),q(T,"display","flex"),q(P,"color","#262626"),q(P,"margin","0"),q(u,"display","flex"),q(u,"gap","8px"),q(m,"padding","6px 12px"),q(m,"color","white"),q(m,"border","none"),q(m,"cursor","pointer"),q(R,"padding","6px 12px"),q(R,"color","#262626"),q(R,"border","1px solid #d9d9d9"),q(R,"cursor","pointer"),q(y,"padding","6px 12px"),q(y,"color","white"),q(y,"border","none"),q(y,"cursor","pointer"),q(I,"display","grid"),q(I,"gap","16px"),l(I,B(Ge,{each:b,children:s=>(()=>{var Q=$i(),te=Q.firstChild,ie=te.nextSibling,J=ie.nextSibling,re=J.nextSibling;return q(Q,"padding","16px"),q(Q,"border","1px solid #e8e8e8"),q(Q,"display","flex"),q(Q,"transition","all 0.2s ease"),l(te,()=>s.icon),q(ie,"color","#262626"),l(ie,()=>s.value),q(J,"color","#8c8c8c"),l(J,()=>s.description),l(re,()=>s.change),A(G=>q(re,"color",s.trend==="up"?"#52c41a":s.trend==="down"?"#f5222d":"#8c8c8c")),Q})()})),l(F,B(Ge,{each:a,children:s=>(()=>{var Q=Ri(),te=Q.firstChild,ie=te.firstChild,J=ie.nextSibling,re=J.firstChild,G=te.nextSibling,de=G.firstChild,ce=de.nextSibling;return l(ie,()=>s.code),l(J,()=>s.changePercent>0?"+":"",re),l(J,()=>s.changePercent,re),l(de,()=>s.name),l(ce,()=>s.status),A(Z=>{var N=e({padding:"8px",backgroundColor:"#fafafa",borderRadius:"4px",fontSize:"12px"}),ae=e({display:"flex",justifyContent:"space-between",alignItems:"center",marginBottom:"4px"}),fe=e({fontWeight:"600",color:"#262626"}),be=e({color:s.changePercent>0?"#52c41a":"#f5222d",fontWeight:"500"}),ge=e({display:"flex",justifyContent:"space-between",color:"#8c8c8c"});return N!==Z.e&&t(Q,Z.e=N),ae!==Z.t&&t(te,Z.t=ae),fe!==Z.a&&t(ie,Z.a=fe),be!==Z.o&&t(J,Z.o=be),ge!==Z.i&&t(G,Z.i=ge),Z},{e:void 0,t:void 0,a:void 0,o:void 0,i:void 0}),Q})()})),l(K,B(Ge,{each:p,children:s=>(()=>{var Q=Ii(),te=Q.firstChild,ie=te.firstChild,J=ie.nextSibling,re=te.nextSibling,G=re.firstChild,de=G.nextSibling,ce=de.firstChild,Z=ce.nextSibling;return Z.nextSibling,l(J,()=>s.name),l(G,()=>s.value),l(de,()=>s.change,ce),l(de,()=>s.percent,Z),A(N=>{var ae=e({display:"flex",alignItems:"center",justifyContent:"space-between",padding:"8px",backgroundColor:"#fafafa",borderRadius:"4px",fontSize:"12px"}),fe=e({display:"flex",alignItems:"center",gap:"8px"}),be=e({width:"6px",height:"6px",borderRadius:"50%",backgroundColor:s.trend==="up"?"#52c41a":"#f5222d"}),ge=e({fontWeight:"500",color:"#262626"}),ze=e({textAlign:"right"}),ye=e({fontWeight:"600",color:"#262626",marginBottom:"2px"}),ke=e({color:s.trend==="up"?"#52c41a":"#f5222d",fontSize:"11px"});return ae!==N.e&&t(Q,N.e=ae),fe!==N.t&&t(te,N.t=fe),be!==N.a&&t(ie,N.a=be),ge!==N.o&&t(J,N.o=ge),ze!==N.i&&t(re,N.i=ze),ye!==N.n&&t(G,N.n=ye),ke!==N.s&&t(de,N.s=ke),N},{e:void 0,t:void 0,a:void 0,o:void 0,i:void 0,n:void 0,s:void 0}),Q})()})),l(he,B(Ge,{each:S,children:s=>(()=>{var Q=Wi(),te=Q.firstChild,ie=te.nextSibling,J=ie.firstChild,re=J.nextSibling;return l(te,()=>s.title),l(J,()=>s.time),l(re,()=>s.type),A(G=>{var de=e({padding:"8px",backgroundColor:"#fafafa",borderRadius:"4px",cursor:"pointer",transition:"background-color 0.2s",_hover:{backgroundColor:"#f0f0f0"}}),ce=e({fontSize:"12px",fontWeight:"500",color:"#262626",marginBottom:"4px",lineHeight:"1.4"}),Z=e({display:"flex",alignItems:"center",justifyContent:"space-between"}),N=e({fontSize:"11px",color:"#8c8c8c"}),ae=e({fontSize:"10px",color:"#1890ff",backgroundColor:"#e6f7ff",padding:"2px 6px",borderRadius:"2px"});return de!==G.e&&t(Q,G.e=de),ce!==G.t&&t(te,G.t=ce),Z!==G.a&&t(ie,G.a=Z),N!==G.o&&t(J,G.o=N),ae!==G.i&&t(re,G.i=ae),G},{e:void 0,t:void 0,a:void 0,o:void 0,i:void 0}),Q})()})),l(me,i,null),A(s=>{var Q=e({display:"grid",gridTemplateColumns:"2fr 1fr",gap:"16px",marginBottom:"16px","@media (max-width: 768px)":{gridTemplateColumns:"1fr"}}),te=e({backgroundColor:"white",borderRadius:"8px",border:"1px solid #e8e8e8",padding:"16px"}),ie=e({display:"flex",alignItems:"center",justifyContent:"space-between",marginBottom:"16px",flexWrap:"wrap",gap:"8px"}),J=e({fontSize:"16px",fontWeight:"600",color:"#262626",margin:0}),re=e({display:"flex",alignItems:"center",gap:"8px"}),G=e({padding:"4px 8px",backgroundColor:"#1890ff",color:"white",border:"none",borderRadius:"4px",fontSize:"12px",cursor:"pointer"}),de=e({padding:"4px 8px",backgroundColor:"white",color:"#262626",border:"1px solid #d9d9d9",borderRadius:"4px",fontSize:"12px",cursor:"pointer"}),ce=e({padding:"4px 8px",backgroundColor:"white",color:"#262626",border:"1px solid #d9d9d9",borderRadius:"4px",fontSize:"12px",cursor:"pointer"}),Z=e({height:"200px",backgroundColor:"#fafafa",borderRadius:"4px",display:"flex",alignItems:"center",justifyContent:"center",flexDirection:"column",gap:"8px"}),N=e({fontSize:"48px"}),ae=e({fontSize:"14px",color:"#8c8c8c"}),fe=e({fontSize:"12px",color:"#8c8c8c"}),be=e({backgroundColor:"white",borderRadius:"8px",border:"1px solid #e8e8e8",padding:"16px"}),ge=e({display:"flex",alignItems:"center",justifyContent:"space-between",marginBottom:"16px"}),ze=e({fontSize:"16px",fontWeight:"600",color:"#262626",margin:0}),ye=e({fontSize:"12px",color:"#8c8c8c"}),ke=e({display:"flex",flexDirection:"column",gap:"8px"}),Be=e({display:"grid",gridTemplateColumns:"1fr 1fr",gap:"16px","@media (max-width: 768px)":{gridTemplateColumns:"1fr"}}),Pe=e({backgroundColor:"white",borderRadius:"8px",border:"1px solid #e8e8e8",padding:"16px"}),Le=e({display:"flex",alignItems:"center",justifyContent:"space-between",marginBottom:"16px",flexWrap:"wrap",gap:"8px"}),De=e({fontSize:"16px",fontWeight:"600",color:"#262626",margin:0}),Qe=e({fontSize:"12px",color:"#8c8c8c"}),Ze=e({display:"flex",flexDirection:"column",gap:"8px"}),Je=e({backgroundColor:"white",borderRadius:"8px",border:"1px solid #e8e8e8",padding:"16px"}),et=e({display:"flex",alignItems:"center",justifyContent:"space-between",marginBottom:"16px"}),tt=e({fontSize:"16px",fontWeight:"600",color:"#262626",margin:0}),ot=e({fontSize:"12px",color:"#1890ff",cursor:"pointer"}),ft=e({display:"flex",flexDirection:"column",gap:"8px"}),mt=e({display:"flex",alignItems:"center",justifyContent:"space-between",padding:"12px 16px",backgroundColor:"white",borderRadius:"8px",border:"1px solid #e8e8e8",fontSize:"12px",color:"#8c8c8c"}),it=e({display:"flex",alignItems:"center",gap:"16px"}),Ne=e({display:"flex",alignItems:"center",gap:"4px"}),St=e({width:"6px",height:"6px",borderRadius:"50%",backgroundColor:"#52c41a"});return Q!==s.e&&t(Y,s.e=Q),te!==s.t&&t(E,s.t=te),ie!==s.a&&t(M,s.a=ie),J!==s.o&&t(h,s.o=J),re!==s.i&&t(z,s.i=re),G!==s.n&&t(f,s.n=G),de!==s.s&&t(w,s.s=de),ce!==s.h&&t(g,s.h=ce),Z!==s.r&&t(_,s.r=Z),N!==s.d&&t($,s.d=N),ae!==s.l&&t(V,s.l=ae),fe!==s.u&&t(v,s.u=fe),be!==s.c&&t(d,s.c=be),ge!==s.w&&t(C,s.w=ge),ze!==s.m&&t(L,s.m=ze),ye!==s.f&&t(D,s.f=ye),ke!==s.y&&t(F,s.y=ke),Be!==s.g&&t(j,s.g=Be),Pe!==s.p&&t(X,s.p=Pe),Le!==s.b&&t(U,s.b=Le),De!==s.T&&t(H,s.T=De),Qe!==s.A&&t(n,s.A=Qe),Ze!==s.O&&t(K,s.O=Ze),Je!==s.I&&t(ee,s.I=Je),et!==s.S&&t(oe,s.S=et),tt!==s.W&&t(ne,s.W=tt),ot!==s.C&&t(se,s.C=ot),ft!==s.B&&t(he,s.B=ft),mt!==s.v&&t(ve,s.v=mt),it!==s.k&&t(Ce,s.k=it),Ne!==s.x&&t(Se,s.x=Ne),St!==s.j&&t(qe,s.j=St),s},{e:void 0,t:void 0,a:void 0,o:void 0,i:void 0,n:void 0,s:void 0,h:void 0,r:void 0,d:void 0,l:void 0,u:void 0,c:void 0,w:void 0,m:void 0,f:void 0,y:void 0,g:void 0,p:void 0,b:void 0,T:void 0,A:void 0,O:void 0,I:void 0,S:void 0,W:void 0,C:void 0,B:void 0,v:void 0,k:void 0,x:void 0,j:void 0}),c})()}var Ti=W("<div><div><h1>🔧 API 连接测试</h1><p>测试前端与后端API的连接状态</p></div><div><button>开始测试</button></div><div><div><h2>测试结果</h2></div><div></div></div><div><div><h2>配置信息</h2></div><div><div><div><h3>API Base URL</h3><p></p></div><div><h3>环境模式</h3><p></p></div><div><h3>代理配置</h3><p>/api → localhost:8000"),Ai=W('<p>点击"开始测试"按钮运行API连接测试'),Bi=W("<div>"),Pi=W("<div><div><div></div><div><h3></h3><p>"),Li=W("<div>ms");const yt={get:async(i,r)=>{const x=r?"?"+new URLSearchParams(r).toString():"",a=await fetch("https://api.yourdomain.com"+i+x);if(!a.ok)throw new Error(`HTTP ${a.status}`);return a.json().catch(()=>({}))}},Ct={SYSTEM:{HEALTH:"/v1/health"},MARKET:{OVERVIEW:"/v1/market/overview",SEARCH:"/v1/market/search"},AUTH:{PROFILE:"/v1/auth/profile"}};function Di(){const[i,r]=ue([]),x=(a,p,S,c)=>{r(k=>[...k,{name:a,status:p,message:S,duration:c}])},b=async()=>{r([]);try{const a=Date.now();await yt.get(Ct.SYSTEM.HEALTH);const p=Date.now()-a;x("系统健康检查","success","连接成功",p)}catch(a){x("系统健康检查","error",a.message||"连接失败")}try{const a=Date.now();await yt.get(Ct.MARKET.OVERVIEW);const p=Date.now()-a;x("市场概览","success","数据获取成功",p)}catch(a){x("市场概览","error",a.message||"数据获取失败")}try{const a=Date.now();await yt.get(Ct.MARKET.SEARCH,{q:"AAPL"});const p=Date.now()-a;x("股票搜索","success","搜索成功",p)}catch(a){x("股票搜索","error",a.message||"搜索失败")}try{const a=Date.now();await yt.get(Ct.AUTH.PROFILE);const p=Date.now()-a;x("用户信息","success","获取成功",p)}catch(a){x("用户信息","error",a.message||"获取失败（预期，因为未登录）")}};return ht(()=>{console.log("ApiTest mounted")}),(()=>{var a=Ti(),p=a.firstChild,S=p.firstChild,c=S.nextSibling,k=p.nextSibling,T=k.firstChild,P=k.nextSibling,u=P.firstChild,m=u.firstChild,R=u.nextSibling,y=P.nextSibling,I=y.firstChild,Y=I.firstChild,E=I.nextSibling,M=E.firstChild,h=M.firstChild,z=h.firstChild,f=z.nextSibling,w=h.nextSibling,g=w.firstChild,_=g.nextSibling,$=w.nextSibling,V=$.firstChild,v=V.nextSibling;return T.$$click=b,l(R,(()=>{var d=pe(()=>i().length===0);return()=>d()?(()=>{var C=Ai();return A(()=>t(C,e({color:"gray.500",textAlign:"center",padding:"40px 0"}))),C})():(()=>{var C=Bi();return l(C,()=>i().map(L=>(()=>{var D=Pi(),F=D.firstChild,j=F.firstChild,X=j.nextSibling,U=X.firstChild,H=U.nextSibling;return l(j,()=>L.status==="success"?"✅":"❌"),l(U,()=>L.name),l(H,()=>L.message),l(D,(()=>{var n=pe(()=>!!L.duration);return()=>n()&&(()=>{var K=Li(),ee=K.firstChild;return l(K,()=>L.duration,ee),A(()=>t(K,e({fontSize:"12px",color:"gray.500",backgroundColor:"gray.100",padding:"4px 8px",borderRadius:"4px"}))),K})()})(),null),A(n=>{var K=e({display:"flex",alignItems:"center",justifyContent:"space-between",padding:"16px",borderRadius:"8px",border:"1px solid",borderColor:L.status==="success"?"green.200":"red.200",backgroundColor:L.status==="success"?"green.50":"red.50"}),ee=e({display:"flex",alignItems:"center",gap:"12px"}),oe=e({fontSize:"20px"}),ne=e({fontSize:"16px",fontWeight:"600",color:"gray.900",marginBottom:"4px"}),se=e({fontSize:"14px",color:"gray.600"});return K!==n.e&&t(D,n.e=K),ee!==n.t&&t(F,n.t=ee),oe!==n.a&&t(j,n.a=oe),ne!==n.o&&t(U,n.o=ne),se!==n.i&&t(H,n.i=se),n},{e:void 0,t:void 0,a:void 0,o:void 0,i:void 0}),D})())),A(()=>t(C,e({display:"flex",flexDirection:"column",gap:"16px"}))),C})()})()),l(f,()=>"https://api.yourdomain.com"),l(_,()=>"production"),A(d=>{var C=e({padding:"24px",maxWidth:"1200px",margin:"0 auto"}),L=e({marginBottom:"32px"}),D=e({fontSize:"32px",fontWeight:"bold",color:"gray.900",marginBottom:"8px"}),F=e({fontSize:"16px",color:"gray.600"}),j=e({marginBottom:"32px"}),X=e({padding:"12px 24px",backgroundColor:"blue.600",color:"white",border:"none",borderRadius:"8px",fontSize:"16px",fontWeight:"500",cursor:"pointer",transition:"all 0.2s",_hover:{backgroundColor:"blue.700"}}),U=e({backgroundColor:"white",borderRadius:"12px",boxShadow:"0 1px 3px rgba(0, 0, 0, 0.1)",border:"1px solid #e5e7eb",overflow:"hidden"}),H=e({padding:"24px",borderBottom:"1px solid #e5e7eb"}),n=e({fontSize:"20px",fontWeight:"bold",color:"gray.900"}),K=e({padding:"24px"}),ee=e({marginTop:"32px",backgroundColor:"white",borderRadius:"12px",boxShadow:"0 1px 3px rgba(0, 0, 0, 0.1)",border:"1px solid #e5e7eb",overflow:"hidden"}),oe=e({padding:"24px",borderBottom:"1px solid #e5e7eb"}),ne=e({fontSize:"20px",fontWeight:"bold",color:"gray.900"}),se=e({padding:"24px"}),he=e({display:"grid",gridTemplateColumns:"repeat(auto-fit, minmax(300px, 1fr))",gap:"16px"}),ve=e({fontSize:"14px",fontWeight:"600",color:"gray.700",marginBottom:"8px"}),me=e({fontSize:"14px",color:"gray.600",fontFamily:"monospace",backgroundColor:"gray.100",padding:"8px",borderRadius:"4px"}),Ce=e({fontSize:"14px",fontWeight:"600",color:"gray.700",marginBottom:"8px"}),Te=e({fontSize:"14px",color:"gray.600",fontFamily:"monospace",backgroundColor:"gray.100",padding:"8px",borderRadius:"4px"}),we=e({fontSize:"14px",fontWeight:"600",color:"gray.700",marginBottom:"8px"}),Se=e({fontSize:"14px",color:"gray.600",fontFamily:"monospace",backgroundColor:"gray.100",padding:"8px",borderRadius:"4px"});return C!==d.e&&t(a,d.e=C),L!==d.t&&t(p,d.t=L),D!==d.a&&t(S,d.a=D),F!==d.o&&t(c,d.o=F),j!==d.i&&t(k,d.i=j),X!==d.n&&t(T,d.n=X),U!==d.s&&t(P,d.s=U),H!==d.h&&t(u,d.h=H),n!==d.r&&t(m,d.r=n),K!==d.d&&t(R,d.d=K),ee!==d.l&&t(y,d.l=ee),oe!==d.u&&t(I,d.u=oe),ne!==d.c&&t(Y,d.c=ne),se!==d.w&&t(E,d.w=se),he!==d.m&&t(M,d.m=he),ve!==d.f&&t(z,d.f=ve),me!==d.y&&t(f,d.y=me),Ce!==d.g&&t(g,d.g=Ce),Te!==d.p&&t(_,d.p=Te),we!==d.b&&t(V,d.b=we),Se!==d.T&&t(v,d.T=Se),d},{e:void 0,t:void 0,a:void 0,o:void 0,i:void 0,n:void 0,s:void 0,h:void 0,r:void 0,d:void 0,l:void 0,u:void 0,c:void 0,w:void 0,m:void 0,f:void 0,y:void 0,g:void 0,p:void 0,b:void 0,T:void 0}),a})()}kt(["click"]);class Ei{constructor(){this.socket=null,this.reconnectAttempts=0,this.maxReconnectAttempts=5,this.reconnectDelay=1e3,this.connectionStatusSignal=ue("disconnected"),this.marketDataSignal=ue(new Map),this.connectionStatus=this.connectionStatusSignal[0],this.setConnectionStatus=this.connectionStatusSignal[1],this.marketData=this.marketDataSignal[0],this.setMarketData=this.marketDataSignal[1],this.connect()}connect(){try{this.setConnectionStatus("connecting"),console.log("尝试连接WebSocket服务器:","wss://api.yourdomain.com/ws"),this.socket=null}catch(r){console.error("WebSocket连接失败:",r),this.setConnectionStatus("error"),this.handleReconnect()}}handleReconnect(){this.reconnectAttempts<this.maxReconnectAttempts?(this.reconnectAttempts++,setTimeout(()=>{console.log(`尝试重连 (${this.reconnectAttempts}/${this.maxReconnectAttempts})`),this.connect()},this.reconnectDelay*this.reconnectAttempts)):(console.log("达到最大重连次数，停止重连"),this.setConnectionStatus("error"))}updateMarketData(r){this.setMarketData(x=>{const b=new Map(x);return b.set(r.symbol,r),b})}subscribeToMarketData(r){console.log("订阅市场数据:",r),this.socket&&this.socket.connected&&this.socket.emit("subscribe",{symbols:r})}unsubscribeFromMarketData(r){console.log("取消订阅市场数据:",r),this.socket&&this.socket.connected&&this.socket.emit("unsubscribe",{symbols:r})}reconnect(){console.log("手动重连WebSocket..."),this.disconnect(),this.reconnectAttempts=0,this.connect()}disconnect(){this.socket&&(this.socket.disconnect(),this.socket=null),this.setConnectionStatus("disconnected")}}const We=new Ei;function Mi(){return{connectionStatus:We.connectionStatus,marketData:We.marketData,subscribeToMarketData:We.subscribeToMarketData.bind(We),unsubscribeFromMarketData:We.unsubscribeFromMarketData.bind(We),disconnect:We.disconnect.bind(We),reconnect:We.reconnect.bind(We)}}var ji=W("<div><div><div><h1>行情分析</h1><div><span>沪深A股</span><span>数据更新: 15:30</span><div><div></div><span></span></div></div></div><div><button type=button>导出数据</button><button type=button>自选股</button></div></div><div><div><h2>市场概览</h2><div><button type=button>日</button><button type=button>周</button><button type=button>月</button></div></div><div><div><div>上证指数</div><div>3,247.89</div><div>-12.34 (-0.38%)</div></div><div><div>深证成指</div><div>10,567.23</div><div>+45.67 (+0.43%)</div></div><div><div>创业板指</div><div>2,234.56</div><div>-8.90 (-0.40%)</div></div><div><div>科创50</div><div>1,123.45</div><div>+15.23 (+1.37%)</div></div></div></div><div><div><h3>市场筛选</h3><span>共 <!> 只股票</span></div><div><div><input type=text placeholder=搜索股票代码或名称><button type=button>搜索</button></div><div><span>板块:</span><button type=button>全部</button><button type=button>沪A</button><button type=button>深A</button><button type=button>创业板</button></div></div></div><div><div><h2>股票列表</h2></div><div><table><thead><tr><th>代码</th><th>名称</th><th>现价</th><th>涨跌额</th><th>涨跌幅</th><th>成交量</th><th>最高价</th><th>最低价</th><th>操作</th></tr></thead><tbody></tbody></table></div><div><div>共 <!> 条数据</div><div><button type=button>上一页</button><span>1</span><button type=button>下一页"),Oi=W("<button type=button>重新连接"),Fi=W("<tr><td></td><td></td><td></td><td></td><td>%</td><td></td><td></td><td></td><td><div><button type=button>卖</button><button type=button>买"),Hi=W("<div><div><h3> 详细信息</h3></div><div><div>📊</div><p>K线图表和技术指标</p><p>这里将显示选中股票的详细分析图表");function qi(){const i=[{symbol:"000725",name:"京东方A",price:3.45,change:-1.43,changePercent:-29.3,volume:7340750,high:3.52,low:3.4,open:3.48,marketCap:12e10},{symbol:"300519",name:"新光药业",price:68.94,change:-1.05,changePercent:-1.5,volume:410750,high:70.1,low:68.5,open:69.9,marketCap:28e9},{symbol:"000831",name:"五矿稀土",price:26.09,change:-1.37,changePercent:-5,volume:7558720,high:27.5,low:25.8,open:27.2,marketCap:45e9},{symbol:"000001",name:"平安银行",price:12.45,change:.23,changePercent:1.88,volume:1568e4,high:12.58,low:12.2,open:12.3,marketCap:24e10},{symbol:"000002",name:"万科A",price:8.76,change:-.15,changePercent:-1.68,volume:895e4,high:8.95,low:8.65,open:8.85,marketCap:98e9}],[r,x]=ue(i),b=Mi(),[a,p]=ue("AAPL"),[S,c]=ue("");Ao(()=>{const u=b.marketData();u.size>0&&x(m=>m.map(R=>{const y=u.get(R.symbol);return y?{...R,price:y.price,change:y.change,changePercent:y.changePercent,volume:y.volume}:R}))}),ht(()=>{console.log("Market page mounted");const u=i.map(y=>y.symbol);b.subscribeToMarketData(u);let m;setTimeout(()=>{m=setInterval(()=>{b.connectionStatus()!=="connected"&&x(y=>y.map(I=>({...I,price:Math.max(.01,I.price+(Math.random()-.5)*2),change:I.change+(Math.random()-.5)*.5,changePercent:I.changePercent+(Math.random()-.5)*.2,volume:Math.max(0,I.volume+Math.floor((Math.random()-.5)*1e5))})))},3e3)},2e3),_t(()=>{m&&clearInterval(m),b.unsubscribeFromMarketData(u)})});const k=()=>{const u=S().toLowerCase();return r().filter(m=>m.symbol.toLowerCase().includes(u)||m.name.toLowerCase().includes(u))},T=(u,m=2)=>new Intl.NumberFormat("zh-CN",{minimumFractionDigits:m,maximumFractionDigits:m}).format(u),P=u=>u>=1e6?`${(u/1e6).toFixed(1)}M`:u>=1e3?`${(u/1e3).toFixed(1)}K`:u.toString();return(()=>{var u=ji(),m=u.firstChild,R=m.firstChild,y=R.firstChild,I=y.nextSibling,Y=I.firstChild,E=Y.nextSibling,M=E.nextSibling,h=M.firstChild,z=h.nextSibling,f=R.nextSibling,w=f.firstChild,g=w.nextSibling,_=m.nextSibling,$=_.firstChild,V=$.firstChild,v=V.nextSibling,d=v.firstChild,C=d.nextSibling,L=C.nextSibling,D=$.nextSibling,F=D.firstChild,j=F.firstChild,X=j.nextSibling,U=X.nextSibling,H=F.nextSibling,n=H.firstChild,K=n.nextSibling,ee=K.nextSibling,oe=H.nextSibling,ne=oe.firstChild,se=ne.nextSibling,he=se.nextSibling,ve=oe.nextSibling,me=ve.firstChild,Ce=me.nextSibling,Te=Ce.nextSibling,we=_.nextSibling,Se=we.firstChild,qe=Se.firstChild,s=qe.nextSibling,Q=s.firstChild,te=Q.nextSibling;te.nextSibling;var ie=Se.nextSibling,J=ie.firstChild,re=J.firstChild,G=re.nextSibling,de=J.nextSibling,ce=de.firstChild,Z=ce.nextSibling,N=Z.nextSibling,ae=N.nextSibling,fe=ae.nextSibling,be=we.nextSibling,ge=be.firstChild,ze=ge.firstChild,ye=ge.nextSibling,ke=ye.firstChild,Be=ke.firstChild,Pe=Be.firstChild,Le=Pe.firstChild,De=Le.nextSibling,Qe=De.nextSibling,Ze=Qe.nextSibling,Je=Ze.nextSibling,et=Je.nextSibling,tt=et.nextSibling,ot=tt.nextSibling,ft=ot.nextSibling,mt=Be.nextSibling,it=ye.nextSibling,Ne=it.firstChild,St=Ne.firstChild,Bt=St.nextSibling;Bt.nextSibling;var Pt=Ne.nextSibling,Lt=Pt.firstChild,Dt=Lt.nextSibling,qo=Dt.nextSibling;return l(z,()=>b.connectionStatus()==="connected"?"实时连接":"模拟数据"),l(f,(()=>{var o=pe(()=>b.connectionStatus()!=="connected");return()=>o()&&(()=>{var xe=Oi();return xe.$$click=()=>b.reconnect(),A(()=>t(xe,e({padding:"6px 12px",backgroundColor:"#1890ff",color:"white",border:"none",borderRadius:"4px",fontSize:"12px",cursor:"pointer"}))),xe})()})(),w),l(s,()=>k().length,te),re.$$input=o=>c(o.currentTarget.value),l(mt,()=>k().map(o=>(()=>{var xe=Fi(),$e=xe.firstChild,Re=$e.nextSibling,Ee=Re.nextSibling,Ie=Ee.nextSibling,_e=Ie.nextSibling,Xe=_e.firstChild,Me=_e.nextSibling,le=Me.nextSibling,Ae=le.nextSibling,je=Ae.nextSibling,Oe=je.firstChild,Fe=Oe.firstChild,Ke=Fe.nextSibling;return xe.$$click=()=>p(o.symbol),l($e,()=>o.symbol),l(Re,()=>o.name),l(Ee,()=>T(o.price)),l(Ie,()=>o.change>=0?"+":"",null),l(Ie,()=>T(o.change),null),l(_e,()=>o.changePercent>=0?"+":"",Xe),l(_e,()=>T(o.changePercent),Xe),l(Me,()=>P(o.volume)),l(le,()=>T(o.high)),l(Ae,()=>T(o.low)),A(O=>{var rt=e({borderBottom:"1px solid #f0f0f0",cursor:"pointer",transition:"background-color 0.2s",backgroundColor:a()===o.symbol?"#e6f7ff":"transparent",_hover:{backgroundColor:"#fafafa"}}),nt=e({padding:"12px 16px",fontSize:"14px",fontWeight:"600",color:"#262626"}),lt=e({padding:"12px 16px",fontSize:"14px",color:"#262626"}),dt=e({padding:"12px 16px",textAlign:"right",fontSize:"14px",fontWeight:"600",color:"#262626"}),at=e({padding:"12px 16px",textAlign:"right",fontSize:"14px",fontWeight:"500",color:o.change>=0?"#52c41a":"#f5222d"}),st=e({padding:"12px 16px",textAlign:"right",fontSize:"14px",fontWeight:"500",color:o.changePercent>=0?"#52c41a":"#f5222d"}),ct=e({padding:"12px 16px",textAlign:"right",fontSize:"14px",color:"#8c8c8c"}),gt=e({padding:"12px 16px",textAlign:"right",fontSize:"14px",color:"#8c8c8c"}),xt=e({padding:"12px 16px",textAlign:"right",fontSize:"14px",color:"#8c8c8c"}),ut=e({padding:"12px 16px",textAlign:"right"}),pt=e({display:"flex",gap:"4px",justifyContent:"flex-end"}),vt=e({padding:"4px 8px",backgroundColor:"#f5222d",color:"white",border:"none",borderRadius:"2px",fontSize:"12px",cursor:"pointer"}),bt=e({padding:"4px 8px",backgroundColor:"#52c41a",color:"white",border:"none",borderRadius:"2px",fontSize:"12px",cursor:"pointer"});return rt!==O.e&&t(xe,O.e=rt),nt!==O.t&&t($e,O.t=nt),lt!==O.a&&t(Re,O.a=lt),dt!==O.o&&t(Ee,O.o=dt),at!==O.i&&t(Ie,O.i=at),st!==O.n&&t(_e,O.n=st),ct!==O.s&&t(Me,O.s=ct),gt!==O.h&&t(le,O.h=gt),xt!==O.r&&t(Ae,O.r=xt),ut!==O.d&&t(je,O.d=ut),pt!==O.l&&t(Oe,O.l=pt),vt!==O.u&&t(Fe,O.u=vt),bt!==O.c&&t(Ke,O.c=bt),O},{e:void 0,t:void 0,a:void 0,o:void 0,i:void 0,n:void 0,s:void 0,h:void 0,r:void 0,d:void 0,l:void 0,u:void 0,c:void 0}),xe})())),l(Ne,()=>k().length,Bt),l(u,(()=>{var o=pe(()=>!!a());return()=>o()&&(()=>{var xe=Hi(),$e=xe.firstChild,Re=$e.firstChild,Ee=Re.firstChild,Ie=$e.nextSibling,_e=Ie.firstChild,Xe=_e.nextSibling,Me=Xe.nextSibling;return l(Re,a,Ee),A(le=>{var Ae=e({marginTop:"24px",backgroundColor:"white",borderRadius:"8px",border:"1px solid #e8e8e8",overflow:"hidden"}),je=e({padding:"20px",borderBottom:"1px solid #e8e8e8"}),Oe=e({fontSize:"16px",fontWeight:"600",color:"#262626",margin:"0"}),Fe=e({padding:"20px",textAlign:"center",color:"#8c8c8c"}),Ke=e({fontSize:"48px",marginBottom:"16px"}),O=e({fontSize:"12px"});return Ae!==le.e&&t(xe,le.e=Ae),je!==le.t&&t($e,le.t=je),Oe!==le.a&&t(Re,le.a=Oe),Fe!==le.o&&t(Ie,le.o=Fe),Ke!==le.i&&t(_e,le.i=Ke),O!==le.n&&t(Me,le.n=O),le},{e:void 0,t:void 0,a:void 0,o:void 0,i:void 0,n:void 0}),xe})()})(),null),A(o=>{var xe=e({padding:"16px",backgroundColor:"#f0f2f5",minHeight:"100%"}),$e=e({display:"flex",alignItems:"center",justifyContent:"space-between",marginBottom:"16px"}),Re=e({fontSize:"20px",fontWeight:"600",color:"#262626",margin:0,marginBottom:"4px"}),Ee=e({display:"flex",alignItems:"center",gap:"12px",fontSize:"12px",color:"#8c8c8c"}),Ie=e({padding:"2px 6px",backgroundColor:"#f6ffed",color:"#52c41a",borderRadius:"2px",fontSize:"11px"}),_e=e({display:"flex",alignItems:"center",gap:"4px"}),Xe=e({width:"6px",height:"6px",borderRadius:"50%",backgroundColor:b.connectionStatus()==="connected"?"#52c41a":"#faad14"}),Me=e({display:"flex",alignItems:"center",gap:"8px"}),le=e({padding:"6px 12px",backgroundColor:"white",color:"#262626",border:"1px solid #d9d9d9",borderRadius:"4px",fontSize:"12px",cursor:"pointer"}),Ae=e({padding:"6px 12px",backgroundColor:"#52c41a",color:"white",border:"none",borderRadius:"4px",fontSize:"12px",cursor:"pointer"}),je=e({marginBottom:"16px",backgroundColor:"white",borderRadius:"8px",border:"1px solid #e8e8e8",padding:"16px"}),Oe=e({display:"flex",alignItems:"center",justifyContent:"space-between",marginBottom:"12px"}),Fe=e({fontSize:"16px",fontWeight:"600",color:"#262626",margin:0}),Ke=e({display:"flex",alignItems:"center",gap:"8px"}),O=e({padding:"4px 8px",backgroundColor:"#1890ff",color:"white",border:"none",borderRadius:"4px",fontSize:"12px",cursor:"pointer"}),rt=e({padding:"4px 8px",backgroundColor:"white",color:"#262626",border:"1px solid #d9d9d9",borderRadius:"4px",fontSize:"12px",cursor:"pointer"}),nt=e({padding:"4px 8px",backgroundColor:"white",color:"#262626",border:"1px solid #d9d9d9",borderRadius:"4px",fontSize:"12px",cursor:"pointer"}),lt=e({display:"grid",gridTemplateColumns:"repeat(auto-fit, minmax(150px, 1fr))",gap:"16px"}),dt=e({padding:"12px",backgroundColor:"#fafafa",borderRadius:"4px",textAlign:"center"}),at=e({fontSize:"12px",color:"#8c8c8c",marginBottom:"4px"}),st=e({fontSize:"16px",fontWeight:"600",color:"#f5222d",marginBottom:"2px"}),ct=e({fontSize:"11px",color:"#f5222d"}),gt=e({padding:"12px",backgroundColor:"#fafafa",borderRadius:"4px",textAlign:"center"}),xt=e({fontSize:"12px",color:"#8c8c8c",marginBottom:"4px"}),ut=e({fontSize:"16px",fontWeight:"600",color:"#52c41a",marginBottom:"2px"}),pt=e({fontSize:"11px",color:"#52c41a"}),vt=e({padding:"12px",backgroundColor:"#fafafa",borderRadius:"4px",textAlign:"center"}),bt=e({fontSize:"12px",color:"#8c8c8c",marginBottom:"4px"}),Et=e({fontSize:"16px",fontWeight:"600",color:"#f5222d",marginBottom:"2px"}),Mt=e({fontSize:"11px",color:"#f5222d"}),jt=e({padding:"12px",backgroundColor:"#fafafa",borderRadius:"4px",textAlign:"center"}),Ot=e({fontSize:"12px",color:"#8c8c8c",marginBottom:"4px"}),Ft=e({fontSize:"16px",fontWeight:"600",color:"#52c41a",marginBottom:"2px"}),Ht=e({fontSize:"11px",color:"#52c41a"}),qt=e({marginBottom:"16px",backgroundColor:"white",borderRadius:"8px",border:"1px solid #e8e8e8",padding:"12px 16px"}),Nt=e({display:"flex",alignItems:"center",justifyContent:"space-between",marginBottom:"12px"}),Xt=e({fontSize:"14px",fontWeight:"600",color:"#262626",margin:0}),Kt=e({fontSize:"12px",color:"#8c8c8c"}),Yt=e({display:"flex",alignItems:"center",justifyContent:"space-between"}),Vt=e({display:"flex",alignItems:"center",gap:"8px"}),Ut=e({padding:"6px 12px",border:"1px solid #d9d9d9",borderRadius:"4px",fontSize:"12px",width:"180px",_focus:{outline:"none",borderColor:"#1890ff"}}),Gt=e({padding:"6px 12px",backgroundColor:"#1890ff",color:"white",border:"none",borderRadius:"4px",fontSize:"12px",cursor:"pointer"}),Qt=e({display:"flex",alignItems:"center",gap:"6px"}),Zt=e({fontSize:"12px",color:"#8c8c8c",marginRight:"4px"}),Jt=e({padding:"4px 8px",backgroundColor:"#1890ff",color:"white",border:"none",borderRadius:"4px",fontSize:"11px",cursor:"pointer"}),eo=e({padding:"4px 8px",backgroundColor:"white",color:"#262626",border:"1px solid #d9d9d9",borderRadius:"4px",fontSize:"11px",cursor:"pointer"}),to=e({padding:"4px 8px",backgroundColor:"white",color:"#262626",border:"1px solid #d9d9d9",borderRadius:"4px",fontSize:"11px",cursor:"pointer"}),oo=e({padding:"4px 8px",backgroundColor:"white",color:"#262626",border:"1px solid #d9d9d9",borderRadius:"4px",fontSize:"11px",cursor:"pointer"}),io=e({backgroundColor:"white",borderRadius:"8px",border:"1px solid #e8e8e8",overflow:"hidden"}),ro=e({padding:"16px 20px",borderBottom:"1px solid #e8e8e8",backgroundColor:"#fafafa"}),no=e({fontSize:"16px",fontWeight:"600",color:"#262626",margin:"0"}),lo=e({overflowX:"auto"}),ao=e({width:"100%",borderCollapse:"collapse"}),so=e({backgroundColor:"#fafafa"}),co=e({padding:"12px 16px",textAlign:"left",fontSize:"12px",fontWeight:"600",color:"#8c8c8c"}),go=e({padding:"12px 16px",textAlign:"left",fontSize:"12px",fontWeight:"600",color:"#8c8c8c"}),xo=e({padding:"12px 16px",textAlign:"right",fontSize:"12px",fontWeight:"600",color:"#8c8c8c"}),uo=e({padding:"12px 16px",textAlign:"right",fontSize:"12px",fontWeight:"600",color:"#8c8c8c"}),po=e({padding:"12px 16px",textAlign:"right",fontSize:"12px",fontWeight:"600",color:"#8c8c8c"}),vo=e({padding:"12px 16px",textAlign:"right",fontSize:"12px",fontWeight:"600",color:"#8c8c8c"}),bo=e({padding:"12px 16px",textAlign:"right",fontSize:"12px",fontWeight:"600",color:"#8c8c8c"}),ho=e({padding:"12px 16px",textAlign:"right",fontSize:"12px",fontWeight:"600",color:"#8c8c8c"}),fo=e({padding:"12px 16px",textAlign:"right",fontSize:"12px",fontWeight:"600",color:"#8c8c8c"}),mo=e({padding:"16px 20px",borderTop:"1px solid #f0f0f0",display:"flex",justifyContent:"space-between",alignItems:"center"}),So=e({fontSize:"14px",color:"#8c8c8c"}),yo=e({display:"flex",gap:"8px",alignItems:"center"}),Co=e({padding:"4px 8px",backgroundColor:"white",color:"#262626",border:"1px solid #d9d9d9",borderRadius:"4px",fontSize:"12px",cursor:"pointer"}),wo=e({padding:"4px 8px",backgroundColor:"#1890ff",color:"white",borderRadius:"4px",fontSize:"12px"}),ko=e({padding:"4px 8px",backgroundColor:"white",color:"#262626",border:"1px solid #d9d9d9",borderRadius:"4px",fontSize:"12px",cursor:"pointer"});return xe!==o.e&&t(u,o.e=xe),$e!==o.t&&t(m,o.t=$e),Re!==o.a&&t(y,o.a=Re),Ee!==o.o&&t(I,o.o=Ee),Ie!==o.i&&t(Y,o.i=Ie),_e!==o.n&&t(M,o.n=_e),Xe!==o.s&&t(h,o.s=Xe),Me!==o.h&&t(f,o.h=Me),le!==o.r&&t(w,o.r=le),Ae!==o.d&&t(g,o.d=Ae),je!==o.l&&t(_,o.l=je),Oe!==o.u&&t($,o.u=Oe),Fe!==o.c&&t(V,o.c=Fe),Ke!==o.w&&t(v,o.w=Ke),O!==o.m&&t(d,o.m=O),rt!==o.f&&t(C,o.f=rt),nt!==o.y&&t(L,o.y=nt),lt!==o.g&&t(D,o.g=lt),dt!==o.p&&t(F,o.p=dt),at!==o.b&&t(j,o.b=at),st!==o.T&&t(X,o.T=st),ct!==o.A&&t(U,o.A=ct),gt!==o.O&&t(H,o.O=gt),xt!==o.I&&t(n,o.I=xt),ut!==o.S&&t(K,o.S=ut),pt!==o.W&&t(ee,o.W=pt),vt!==o.C&&t(oe,o.C=vt),bt!==o.B&&t(ne,o.B=bt),Et!==o.v&&t(se,o.v=Et),Mt!==o.k&&t(he,o.k=Mt),jt!==o.x&&t(ve,o.x=jt),Ot!==o.j&&t(me,o.j=Ot),Ft!==o.q&&t(Ce,o.q=Ft),Ht!==o.z&&t(Te,o.z=Ht),qt!==o.P&&t(we,o.P=qt),Nt!==o.H&&t(Se,o.H=Nt),Xt!==o.F&&t(qe,o.F=Xt),Kt!==o.M&&t(s,o.M=Kt),Yt!==o.D&&t(ie,o.D=Yt),Vt!==o.R&&t(J,o.R=Vt),Ut!==o.E&&t(re,o.E=Ut),Gt!==o.L&&t(G,o.L=Gt),Qt!==o.N&&t(de,o.N=Qt),Zt!==o.G&&t(ce,o.G=Zt),Jt!==o.U&&t(Z,o.U=Jt),eo!==o.K&&t(N,o.K=eo),to!==o.V&&t(ae,o.V=to),oo!==o.Y&&t(fe,o.Y=oo),io!==o.J&&t(be,o.J=io),ro!==o.Q&&t(ge,o.Q=ro),no!==o.Z&&t(ze,o.Z=no),lo!==o.X&&t(ye,o.X=lo),ao!==o._&&t(ke,o._=ao),so!==o.$&&t(Pe,o.$=so),co!==o.te&&t(Le,o.te=co),go!==o.tt&&t(De,o.tt=go),xo!==o.ta&&t(Qe,o.ta=xo),uo!==o.to&&t(Ze,o.to=uo),po!==o.ti&&t(Je,o.ti=po),vo!==o.tn&&t(et,o.tn=vo),bo!==o.ts&&t(tt,o.ts=bo),ho!==o.th&&t(ot,o.th=ho),fo!==o.tr&&t(ft,o.tr=fo),mo!==o.td&&t(it,o.td=mo),So!==o.tl&&t(Ne,o.tl=So),yo!==o.tu&&t(Pt,o.tu=yo),Co!==o.tc&&t(Lt,o.tc=Co),wo!==o.tw&&t(Dt,o.tw=wo),ko!==o.tm&&t(qo,o.tm=ko),o},{e:void 0,t:void 0,a:void 0,o:void 0,i:void 0,n:void 0,s:void 0,h:void 0,r:void 0,d:void 0,l:void 0,u:void 0,c:void 0,w:void 0,m:void 0,f:void 0,y:void 0,g:void 0,p:void 0,b:void 0,T:void 0,A:void 0,O:void 0,I:void 0,S:void 0,W:void 0,C:void 0,B:void 0,v:void 0,k:void 0,x:void 0,j:void 0,q:void 0,z:void 0,P:void 0,H:void 0,F:void 0,M:void 0,D:void 0,R:void 0,E:void 0,L:void 0,N:void 0,G:void 0,U:void 0,K:void 0,V:void 0,Y:void 0,J:void 0,Q:void 0,Z:void 0,X:void 0,_:void 0,$:void 0,te:void 0,tt:void 0,ta:void 0,to:void 0,ti:void 0,tn:void 0,ts:void 0,th:void 0,tr:void 0,td:void 0,tl:void 0,tu:void 0,tc:void 0,tw:void 0,tm:void 0}),A(()=>re.value=S()),u})()}kt(["input","click"]);var Ni=W("<div><div>");function Xi(i){const[r,x]=ue();let b;return ht(async()=>{const a=r();if(a)try{const S="/libs/monaco-editor/min/vs";console.log(`Monaco Editor 配置: 本地模式, 路径: ${S}`),_o.config({paths:{vs:S}});const c=await _o.init();i.language==="python"&&c.languages.registerCompletionItemProvider("python",{provideCompletionItems:(k,T)=>{const P=k.getWordUntilPosition(T),u={startLineNumber:T.lineNumber,endLineNumber:T.lineNumber,startColumn:P.startColumn,endColumn:P.endColumn};return{suggestions:[{label:"def",kind:c.languages.CompletionItemKind.Keyword,insertText:"def ${1:function_name}(${2:parameters}):\n    ${3:pass}",insertTextRules:c.languages.CompletionItemInsertTextRule.InsertAsSnippet,documentation:"Define a function",range:u},{label:"initialize",kind:c.languages.CompletionItemKind.Function,insertText:"def initialize(context):\n    ${1:pass}",insertTextRules:c.languages.CompletionItemInsertTextRule.InsertAsSnippet,documentation:"策略初始化函数",range:u},{label:"handle_data",kind:c.languages.CompletionItemKind.Function,insertText:"def handle_data(context, data):\n    ${1:pass}",insertTextRules:c.languages.CompletionItemInsertTextRule.InsertAsSnippet,documentation:"主要的交易逻辑函数",range:u},{label:"order_target_percent",kind:c.languages.CompletionItemKind.Function,insertText:"order_target_percent(${1:security}, ${2:percent})",insertTextRules:c.languages.CompletionItemInsertTextRule.InsertAsSnippet,documentation:"下单到目标百分比",range:u},{label:"attribute_history",kind:c.languages.CompletionItemKind.Function,insertText:"attribute_history(${1:security}, ${2:count}, ${3:unit}, ${4:fields})",insertTextRules:c.languages.CompletionItemInsertTextRule.InsertAsSnippet,documentation:"获取历史数据",range:u},{label:"log.info",kind:c.languages.CompletionItemKind.Function,insertText:"log.info(${1:message})",insertTextRules:c.languages.CompletionItemInsertTextRule.InsertAsSnippet,documentation:"输出日志信息",range:u}]}}}),b=c.editor.create(a,{value:i.value||"",language:i.language||"python",theme:i.theme||"vs",fontSize:13,lineNumbers:"on",roundedSelection:!1,scrollBeyondLastLine:!1,minimap:{enabled:!0},automaticLayout:!0,tabSize:4,insertSpaces:!0,wordWrap:"on",folding:!0,renderLineHighlight:"all",selectOnLineNumbers:!0,matchBrackets:"always",...i.options}),b.onDidChangeModelContent(()=>{i.onChange&&b&&i.onChange(b.getValue())}),b.addCommand(c.KeyMod.CtrlCmd|c.KeyCode.KeyS,()=>{console.log("保存策略快捷键触发")}),b.addCommand(c.KeyMod.CtrlCmd|c.KeyCode.Enter,()=>{console.log("运行策略快捷键触发")})}catch(p){console.error("Monaco Editor 初始化失败:",p)}}),_t(()=>{b&&b.dispose()}),(()=>{var a=Ni(),p=a.firstChild;return Ko(x,p),A(S=>{var c=e({width:"100%",height:`${i.height||400}px`,border:"1px solid #e5e7eb",borderRadius:"6px",overflow:"hidden"}),k=e({width:"100%",height:"100%"});return c!==S.e&&t(a,S.e=c),k!==S.t&&t(p,S.t=k),S},{e:void 0,t:void 0}),a})()}var Ki=W('<div><div><h1>🧠 策略编辑器</h1><p>创建和编辑量化交易策略</p></div><div><div><div><h2>策略代码</h2><div><button>运行回测</button><button>保存策略</button></div></div><div></div></div><div><div><h2>回测结果</h2></div><div><div><div>📊</div><p>等待回测结果</p><p>点击"运行回测"开始策略测试</p></div></div></div></div><div><h3>策略模板</h3><div><button><div>双均线策略</div><div>基于移动平均线的经典策略</div></button><button><div>RSI策略</div><div>基于相对强弱指标的策略</div></button><button><div>布林带策略</div><div>利用布林带进行交易决策</div></button><button><div>机器学习策略</div><div>基于AI模型的量化策略');function Yi(){const[i,r]=ue(`# 量化策略示例
# 这是一个简单的移动平均线策略

def initialize(context):
    # 设置基准和股票
    g.benchmark = '000300.XSHG'
    g.stock = '000001.XSHE'
    
def handle_data(context, data):
    # 获取历史价格
    hist = attribute_history(g.stock, 20, '1d', ['close'])
    ma5 = hist['close'][-5:].mean()
    ma20 = hist['close'][-20:].mean()
    current_price = data[g.stock].close
    
    # 交易逻辑
    if ma5 > ma20 and current_price > ma5:
        # 金叉买入信号
        order_target_percent(g.stock, 0.8)
        log.info(f"买入信号，价格: {current_price}")
    elif ma5 < ma20:
        # 死叉卖出信号
        order_target_percent(g.stock, 0)
        log.info(f"卖出信号，价格: {current_price}")
`);return(()=>{var x=Ki(),b=x.firstChild,a=b.firstChild,p=a.nextSibling,S=b.nextSibling,c=S.firstChild,k=c.firstChild,T=k.firstChild,P=T.nextSibling,u=P.firstChild,m=u.nextSibling,R=k.nextSibling,y=c.nextSibling,I=y.firstChild,Y=I.firstChild,E=I.nextSibling,M=E.firstChild,h=M.firstChild,z=h.nextSibling,f=z.nextSibling,w=S.nextSibling,g=w.firstChild,_=g.nextSibling,$=_.firstChild,V=$.firstChild,v=V.nextSibling,d=$.nextSibling,C=d.firstChild,L=C.nextSibling,D=d.nextSibling,F=D.firstChild,j=F.nextSibling,X=D.nextSibling,U=X.firstChild,H=U.nextSibling;return l(R,B(Xi,{get value(){return i()},language:"python",theme:"vs",height:500,onChange:r,options:{minimap:{enabled:!0},fontSize:14,wordWrap:"on",automaticLayout:!0}})),A(n=>{var K=e({padding:"24px",maxWidth:"1400px",margin:"0 auto",height:"100%"}),ee=e({marginBottom:"32px"}),oe=e({fontSize:"32px",fontWeight:"bold",color:"gray.900",marginBottom:"8px"}),ne=e({fontSize:"16px",color:"gray.600"}),se=e({display:"grid",gridTemplateColumns:"1fr 1fr",gap:"24px",height:"calc(100vh - 200px)"}),he=e({backgroundColor:"white",borderRadius:"12px",boxShadow:"0 1px 3px rgba(0, 0, 0, 0.1)",border:"1px solid #e5e7eb",overflow:"hidden",display:"flex",flexDirection:"column"}),ve=e({padding:"16px 20px",borderBottom:"1px solid #e5e7eb",backgroundColor:"#f9fafb",display:"flex",justifyContent:"space-between",alignItems:"center"}),me=e({fontSize:"16px",fontWeight:"600",color:"gray.900"}),Ce=e({display:"flex",gap:"8px"}),Te=e({padding:"6px 12px",backgroundColor:"blue.600",color:"white",border:"none",borderRadius:"6px",fontSize:"12px",fontWeight:"500",cursor:"pointer",_hover:{backgroundColor:"blue.700"}}),we=e({padding:"6px 12px",backgroundColor:"green.600",color:"white",border:"none",borderRadius:"6px",fontSize:"12px",fontWeight:"500",cursor:"pointer",_hover:{backgroundColor:"green.700"}}),Se=e({flex:1,padding:"8px"}),qe=e({backgroundColor:"white",borderRadius:"12px",boxShadow:"0 1px 3px rgba(0, 0, 0, 0.1)",border:"1px solid #e5e7eb",overflow:"hidden",display:"flex",flexDirection:"column"}),s=e({padding:"16px 20px",borderBottom:"1px solid #e5e7eb",backgroundColor:"#f9fafb"}),Q=e({fontSize:"16px",fontWeight:"600",color:"gray.900"}),te=e({flex:1,padding:"20px",display:"flex",alignItems:"center",justifyContent:"center"}),ie=e({textAlign:"center",color:"gray.500"}),J=e({fontSize:"48px",marginBottom:"16px"}),re=e({fontSize:"16px",marginBottom:"8px"}),G=e({fontSize:"14px"}),de=e({marginTop:"24px",backgroundColor:"white",borderRadius:"12px",boxShadow:"0 1px 3px rgba(0, 0, 0, 0.1)",border:"1px solid #e5e7eb",padding:"20px"}),ce=e({fontSize:"18px",fontWeight:"600",color:"gray.900",marginBottom:"16px"}),Z=e({display:"grid",gridTemplateColumns:"repeat(auto-fit, minmax(200px, 1fr))",gap:"12px"}),N=e({padding:"12px 16px",border:"1px solid #e5e7eb",borderRadius:"8px",backgroundColor:"white",textAlign:"left",cursor:"pointer",transition:"all 0.2s",_hover:{borderColor:"blue.500",backgroundColor:"blue.50"}}),ae=e({fontSize:"14px",fontWeight:"500",color:"gray.900",marginBottom:"4px"}),fe=e({fontSize:"12px",color:"gray.600"}),be=e({padding:"12px 16px",border:"1px solid #e5e7eb",borderRadius:"8px",backgroundColor:"white",textAlign:"left",cursor:"pointer",transition:"all 0.2s",_hover:{borderColor:"blue.500",backgroundColor:"blue.50"}}),ge=e({fontSize:"14px",fontWeight:"500",color:"gray.900",marginBottom:"4px"}),ze=e({fontSize:"12px",color:"gray.600"}),ye=e({padding:"12px 16px",border:"1px solid #e5e7eb",borderRadius:"8px",backgroundColor:"white",textAlign:"left",cursor:"pointer",transition:"all 0.2s",_hover:{borderColor:"blue.500",backgroundColor:"blue.50"}}),ke=e({fontSize:"14px",fontWeight:"500",color:"gray.900",marginBottom:"4px"}),Be=e({fontSize:"12px",color:"gray.600"}),Pe=e({padding:"12px 16px",border:"1px solid #e5e7eb",borderRadius:"8px",backgroundColor:"white",textAlign:"left",cursor:"pointer",transition:"all 0.2s",_hover:{borderColor:"blue.500",backgroundColor:"blue.50"}}),Le=e({fontSize:"14px",fontWeight:"500",color:"gray.900",marginBottom:"4px"}),De=e({fontSize:"12px",color:"gray.600"});return K!==n.e&&t(x,n.e=K),ee!==n.t&&t(b,n.t=ee),oe!==n.a&&t(a,n.a=oe),ne!==n.o&&t(p,n.o=ne),se!==n.i&&t(S,n.i=se),he!==n.n&&t(c,n.n=he),ve!==n.s&&t(k,n.s=ve),me!==n.h&&t(T,n.h=me),Ce!==n.r&&t(P,n.r=Ce),Te!==n.d&&t(u,n.d=Te),we!==n.l&&t(m,n.l=we),Se!==n.u&&t(R,n.u=Se),qe!==n.c&&t(y,n.c=qe),s!==n.w&&t(I,n.w=s),Q!==n.m&&t(Y,n.m=Q),te!==n.f&&t(E,n.f=te),ie!==n.y&&t(M,n.y=ie),J!==n.g&&t(h,n.g=J),re!==n.p&&t(z,n.p=re),G!==n.b&&t(f,n.b=G),de!==n.T&&t(w,n.T=de),ce!==n.A&&t(g,n.A=ce),Z!==n.O&&t(_,n.O=Z),N!==n.I&&t($,n.I=N),ae!==n.S&&t(V,n.S=ae),fe!==n.W&&t(v,n.W=fe),be!==n.C&&t(d,n.C=be),ge!==n.B&&t(C,n.B=ge),ze!==n.v&&t(L,n.v=ze),ye!==n.k&&t(D,n.k=ye),ke!==n.x&&t(F,n.x=ke),Be!==n.j&&t(j,n.j=Be),Pe!==n.q&&t(X,n.q=Pe),Le!==n.z&&t(U,n.z=Le),De!==n.P&&t(H,n.P=De),n},{e:void 0,t:void 0,a:void 0,o:void 0,i:void 0,n:void 0,s:void 0,h:void 0,r:void 0,d:void 0,l:void 0,u:void 0,c:void 0,w:void 0,m:void 0,f:void 0,y:void 0,g:void 0,p:void 0,b:void 0,T:void 0,A:void 0,O:void 0,I:void 0,S:void 0,W:void 0,C:void 0,B:void 0,v:void 0,k:void 0,x:void 0,j:void 0,q:void 0,z:void 0,P:void 0}),x})()}var Vi=W("<button>"),Ui=W("<div>");const zt=i=>{const[r,x]=It(i,["variant","size","loading","icon","fullWidth","children","class","disabled"]),b=e({display:"inline-flex",alignItems:"center",justifyContent:"center",gap:"8px",borderRadius:"8px",fontWeight:"500",transition:"all 0.2s",cursor:"pointer",border:"none",outline:"none",textDecoration:"none",userSelect:"none",_focus:{boxShadow:"0 0 0 3px rgba(59, 130, 246, 0.1)"},_disabled:{opacity:.6,cursor:"not-allowed"}}),a={primary:e({backgroundColor:"blue.600",color:"white",_hover:{backgroundColor:"blue.700"},_active:{backgroundColor:"blue.800"}}),secondary:e({backgroundColor:"gray.100",color:"gray.900",_hover:{backgroundColor:"gray.200"},_active:{backgroundColor:"gray.300"}}),success:e({backgroundColor:"green.600",color:"white",_hover:{backgroundColor:"green.700"},_active:{backgroundColor:"green.800"}}),warning:e({backgroundColor:"yellow.500",color:"white",_hover:{backgroundColor:"yellow.600"},_active:{backgroundColor:"yellow.700"}}),danger:e({backgroundColor:"red.600",color:"white",_hover:{backgroundColor:"red.700"},_active:{backgroundColor:"red.800"}}),ghost:e({backgroundColor:"transparent",color:"gray.700",border:"1px solid",borderColor:"gray.300",_hover:{backgroundColor:"gray.50",borderColor:"gray.400"},_active:{backgroundColor:"gray.100"}})},p={sm:e({padding:"6px 12px",fontSize:"14px",minHeight:"32px"}),md:e({padding:"8px 16px",fontSize:"14px",minHeight:"40px"}),lg:e({padding:"12px 24px",fontSize:"16px",minHeight:"48px"})},S=e({width:"100%"}),c=e({width:"16px",height:"16px",border:"2px solid currentColor",borderTopColor:"transparent",borderRadius:"50%",animation:"spin 1s linear infinite"}),k=r.variant||"primary",T=r.size||"md";return(()=>{var P=Vi();return Wt(P,Tt({get class(){return Ve(b,a[k],p[T],r.fullWidth&&S,r.class)},get disabled(){return r.disabled||r.loading}},x),!1,!0),l(P,(()=>{var u=pe(()=>!!r.loading);return()=>u()&&(()=>{var m=Ui();return t(m,c),m})()})(),null),l(P,(()=>{var u=pe(()=>!!(!r.loading&&r.icon));return()=>u()&&r.icon})(),null),l(P,()=>r.children,null),P})()};var Gi=W("<div><div>"),Qi=W("<div><div><div>"),Zi=W("<h3>"),Ji=W("<p>");const Ue=i=>{const[r,x]=It(i,["title","subtitle","headerAction","padding","shadow","border","hover","children","class"]),b=e({backgroundColor:"white",borderRadius:"12px",overflow:"hidden",transition:"all 0.2s"}),a={none:"",sm:e({boxShadow:"0 1px 2px rgba(0, 0, 0, 0.05)"}),md:e({boxShadow:"0 4px 6px rgba(0, 0, 0, 0.07)"}),lg:e({boxShadow:"0 10px 15px rgba(0, 0, 0, 0.1)"})},p=e({border:"1px solid",borderColor:"gray.200"}),S=e({_hover:{transform:"translateY(-2px)",boxShadow:"0 8px 25px rgba(0, 0, 0, 0.1)"}}),c={none:"",sm:e({padding:"16px"}),md:e({padding:"24px"}),lg:e({padding:"32px"})},k=e({padding:"24px 24px 0 24px",marginBottom:"16px"}),T=e({fontSize:"18px",fontWeight:"600",color:"gray.900",marginBottom:"4px"}),P=e({fontSize:"14px",color:"gray.600"}),u=e({display:"flex",justifyContent:"space-between",alignItems:"flex-start"}),m=r.shadow||"md",R=r.padding||"md";return(()=>{var y=Gi(),I=y.firstChild;return Wt(y,Tt({get class(){return Ve(b,a[m],r.border&&p,r.hover&&S,r.class)}},x),!1,!0),l(y,(()=>{var Y=pe(()=>!!(r.title||r.subtitle||r.headerAction));return()=>Y()&&(()=>{var E=Qi(),M=E.firstChild,h=M.firstChild;return t(E,k),l(h,(()=>{var z=pe(()=>!!r.title);return()=>z()&&(()=>{var f=Zi();return t(f,T),l(f,()=>r.title),f})()})(),null),l(h,(()=>{var z=pe(()=>!!r.subtitle);return()=>z()&&(()=>{var f=Ji();return t(f,P),l(f,()=>r.subtitle),f})()})(),null),l(M,(()=>{var z=pe(()=>!!r.headerAction);return()=>z()&&r.headerAction})(),null),A(()=>t(M,r.headerAction?u:"")),E})()})(),I),l(I,()=>r.children),A(()=>t(I,c[R])),y})()};var er=W('<button aria-label="Close modal">×'),tr=W("<div><h2><span>"),or=W("<div>"),ir=W("<div><div><div>");const rr=i=>{Ao(()=>{if(i.isOpen){const u=m=>{m.key==="Escape"&&i.closable!==!1&&i.onClose()};document.addEventListener("keydown",u),document.body.style.overflow="hidden",_t(()=>{document.removeEventListener("keydown",u),document.body.style.overflow=""})}});const r=e({position:"fixed",top:0,left:0,right:0,bottom:0,backgroundColor:"rgba(0, 0, 0, 0.5)",display:"flex",alignItems:"center",justifyContent:"center",zIndex:1e3,padding:"16px"}),x=e({backgroundColor:"white",borderRadius:"12px",boxShadow:"0 20px 25px rgba(0, 0, 0, 0.1)",maxHeight:"90vh",overflow:"hidden",display:"flex",flexDirection:"column",animation:"modalEnter 0.2s ease-out"}),b={sm:e({width:"400px",maxWidth:"90vw"}),md:e({width:"600px",maxWidth:"90vw"}),lg:e({width:"800px",maxWidth:"90vw"}),xl:e({width:"1000px",maxWidth:"90vw"})},a=e({padding:"24px 24px 0 24px",borderBottom:"1px solid",borderColor:"gray.200",paddingBottom:"16px",marginBottom:"24px"}),p=e({fontSize:"20px",fontWeight:"600",color:"gray.900",margin:0,display:"flex",justifyContent:"space-between",alignItems:"center"}),S=e({background:"none",border:"none",fontSize:"24px",cursor:"pointer",color:"gray.400",padding:"4px",borderRadius:"4px",_hover:{color:"gray.600",backgroundColor:"gray.100"}}),c=e({padding:"0 24px",flex:1,overflow:"auto"}),k=e({padding:"16px 24px 24px 24px",borderTop:"1px solid",borderColor:"gray.200",marginTop:"24px",display:"flex",justifyContent:"flex-end",gap:"12px"}),T=i.size||"md",P=u=>{u.target===u.currentTarget&&i.maskClosable!==!1&&i.onClose()};return B(He,{get when(){return i.isOpen},get children(){return B(Yo,{get children(){var u=ir(),m=u.firstChild,R=m.firstChild;return u.$$click=P,t(u,r),m.$$click=y=>y.stopPropagation(),l(m,B(He,{get when(){return i.title||i.closable!==!1},get children(){var y=tr(),I=y.firstChild,Y=I.firstChild;return t(y,a),t(I,p),l(Y,()=>i.title),l(I,B(He,{get when(){return i.closable!==!1},get children(){var E=er();return Vo(E,"click",i.onClose,!0),t(E,S),E}}),null),y}}),R),t(R,c),l(R,()=>i.children),l(m,B(He,{get when(){return i.footer},get children(){var y=or();return t(y,k),l(y,()=>i.footer),y}}),null),A(()=>t(m,Ve(x,b[T],i.class))),u}})}})};kt(["click"]);var nr=W("<label>"),$t=W("<div>"),lr=W("<div><div><input>");const Rt=i=>{const[r,x]=It(i,["label","error","helperText","leftIcon","rightIcon","size","fullWidth","class"]),b=e({display:"flex",flexDirection:"column",gap:"6px"}),a=e({width:"100%"}),p=e({fontSize:"14px",fontWeight:"500",color:"gray.700"}),S=e({position:"relative",display:"flex",alignItems:"center"}),c=e({width:"100%",border:"1px solid",borderColor:"gray.300",borderRadius:"8px",transition:"all 0.2s",backgroundColor:"white",color:"gray.900",_focus:{outline:"none",borderColor:"blue.500",boxShadow:"0 0 0 3px rgba(59, 130, 246, 0.1)"},_disabled:{backgroundColor:"gray.100",color:"gray.500",cursor:"not-allowed"},_placeholder:{color:"gray.400"}}),k=e({borderColor:"red.500",_focus:{borderColor:"red.500",boxShadow:"0 0 0 3px rgba(239, 68, 68, 0.1)"}}),T={sm:e({padding:"8px 12px",fontSize:"14px",minHeight:"36px"}),md:e({padding:"10px 14px",fontSize:"14px",minHeight:"40px"}),lg:e({padding:"12px 16px",fontSize:"16px",minHeight:"48px"})},P=e({position:"absolute",top:"50%",transform:"translateY(-50%)",color:"gray.400",pointerEvents:"none",zIndex:1}),u=e({left:"12px"}),m=e({right:"12px"}),R=e({paddingLeft:"40px"}),y=e({paddingRight:"40px"}),I=e({fontSize:"12px",color:"gray.600"}),Y=e({fontSize:"12px",color:"red.600"}),E=r.size||"md";return(()=>{var M=lr(),h=M.firstChild,z=h.firstChild;return l(M,B(He,{get when(){return r.label},get children(){var f=nr();return t(f,p),l(f,()=>r.label),f}}),h),t(h,S),l(h,B(He,{get when(){return r.leftIcon},get children(){var f=$t();return l(f,()=>r.leftIcon),A(()=>t(f,Ve(P,u))),f}}),z),Wt(z,Tt({get class(){return Ve(c,T[E],r.error?k:void 0,r.leftIcon?R:void 0,r.rightIcon?y:void 0,r.class)}},x),!1,!1),l(h,B(He,{get when(){return r.rightIcon},get children(){var f=$t();return l(f,()=>r.rightIcon),A(()=>t(f,Ve(P,m))),f}}),null),l(M,B(He,{get when(){return r.error||r.helperText},get children(){var f=$t();return l(f,()=>r.error||r.helperText),A(()=>t(f,r.error?Y:I)),f}}),null),A(()=>t(M,Ve(b,r.fullWidth?a:void 0))),M})()};var dr=W("<div><h3>账户总值</h3><p>¥"),ar=W("<div><h3>总盈亏</h3><p>¥</p><p>%"),sr=W("<div><h3>可用资金</h3><p>¥"),cr=W("<div><h3>已用保证金</h3><p>¥"),gr=W("<div><table><thead><tr><th>代码</th><th>数量</th><th>盈亏</th></tr></thead><tbody>"),xr=W("<div><table><thead><tr><th>代码</th><th>类型</th><th>状态</th></tr></thead><tbody>"),ur=W("<div><div><div><label>交易类型</label><select aria-label=交易类型><option value=buy>买入</option><option value=sell>卖出</option></select></div><div><label>订单类型</label><select aria-label=订单类型><option value=market>市价单</option><option value=limit>限价单</option></select></div></div><div>"),pr=W("<div slot=footer>"),vr=W("<div><div><div><h1>💼 </h1><p>管理您的交易订单和持仓</p></div></div><div></div><div>"),br=W("<span>➕"),hr=W("<tr><td></td><td></td><td>¥<br><span>%"),fr=W("<tr><td><br><span>股 @ ¥</span></td><td><span></span></td><td><span>");const mr=()=>({t:i=>i});function Sr(){const{t:i}=mr(),[r,x]=ue([{id:"1",symbol:"AAPL",type:"buy",quantity:100,price:150.25,status:"filled",timestamp:new Date("2024-01-15T10:30:00")},{id:"2",symbol:"TSLA",type:"sell",quantity:50,price:245.8,status:"pending",timestamp:new Date("2024-01-15T11:15:00")},{id:"3",symbol:"MSFT",type:"buy",quantity:75,price:310.45,status:"filled",timestamp:new Date("2024-01-15T09:45:00")}]),[b,a]=ue([{symbol:"AAPL",quantity:100,avgPrice:150.25,currentPrice:152.3,unrealizedPnL:205,unrealizedPnLPercent:1.36},{symbol:"MSFT",quantity:75,avgPrice:310.45,currentPrice:308.9,unrealizedPnL:-116.25,unrealizedPnLPercent:-.5}]),[p,S]=ue(!1),[c,k]=ue({symbol:"",type:"buy",quantity:"",price:"",orderType:"limit"}),[T]=ue({totalValue:45678.9,totalPnL:88.75,totalPnLPercent:.19,buyingPower:12345.67,marginUsed:5432.1});ht(()=>{console.log("Trading page mounted");const m=setInterval(()=>{a(R=>R.map(y=>{const I=(Math.random()-.5)*2,Y=y.currentPrice+I,E=(Y-y.avgPrice)*y.quantity,M=E/(y.avgPrice*y.quantity)*100;return{...y,currentPrice:Y,unrealizedPnL:E,unrealizedPnLPercent:M}}))},3e3);return()=>clearInterval(m)});const P=()=>{const m=c();if(!m.symbol||!m.quantity||!m.price&&m.orderType==="limit"){alert("请填写完整的订单信息");return}const R={id:Date.now().toString(),symbol:m.symbol.toUpperCase(),type:m.type,quantity:parseInt(m.quantity),price:m.orderType==="market"?0:parseFloat(m.price),status:"pending",timestamp:new Date};x(y=>[R,...y]),S(!1),k({symbol:"",type:"buy",quantity:"",price:"",orderType:"limit"})},u=T();return(()=>{var m=vr(),R=m.firstChild,y=R.firstChild,I=y.firstChild;I.firstChild;var Y=I.nextSibling,E=R.nextSibling,M=E.nextSibling;return l(I,()=>i("nav.trading"),null),l(R,B(zt,{variant:"primary",size:"lg",get icon(){return br()},onClick:()=>S(!0),children:"新建订单"}),null),l(E,B(Ue,{padding:"md",shadow:"md",get children(){var h=dr(),z=h.firstChild,f=z.nextSibling;return f.firstChild,l(f,()=>u.totalValue.toLocaleString(),null),A(w=>{var g=e({textAlign:"center"}),_=e({fontSize:"14px",fontWeight:"500",color:"gray.600",marginBottom:"8px"}),$=e({fontSize:"24px",fontWeight:"bold",color:"gray.900"});return g!==w.e&&t(h,w.e=g),_!==w.t&&t(z,w.t=_),$!==w.a&&t(f,w.a=$),w},{e:void 0,t:void 0,a:void 0}),h}}),null),l(E,B(Ue,{padding:"md",shadow:"md",get children(){var h=ar(),z=h.firstChild,f=z.nextSibling,w=f.firstChild,g=f.nextSibling,_=g.firstChild;return l(f,()=>u.totalPnL>=0?"+":"",w),l(f,()=>Math.abs(u.totalPnL).toFixed(2),null),l(g,()=>u.totalPnL>=0?"+":"",_),l(g,()=>u.totalPnLPercent.toFixed(2),_),A($=>{var V=e({textAlign:"center"}),v=e({fontSize:"14px",fontWeight:"500",color:"gray.600",marginBottom:"8px"}),d=e({fontSize:"24px",fontWeight:"bold",color:u.totalPnL>=0?"green.600":"red.600"}),C=e({fontSize:"12px",color:u.totalPnL>=0?"green.600":"red.600"});return V!==$.e&&t(h,$.e=V),v!==$.t&&t(z,$.t=v),d!==$.a&&t(f,$.a=d),C!==$.o&&t(g,$.o=C),$},{e:void 0,t:void 0,a:void 0,o:void 0}),h}}),null),l(E,B(Ue,{padding:"md",shadow:"md",get children(){var h=sr(),z=h.firstChild,f=z.nextSibling;return f.firstChild,l(f,()=>u.buyingPower.toLocaleString(),null),A(w=>{var g=e({textAlign:"center"}),_=e({fontSize:"14px",fontWeight:"500",color:"gray.600",marginBottom:"8px"}),$=e({fontSize:"24px",fontWeight:"bold",color:"gray.900"});return g!==w.e&&t(h,w.e=g),_!==w.t&&t(z,w.t=_),$!==w.a&&t(f,w.a=$),w},{e:void 0,t:void 0,a:void 0}),h}}),null),l(E,B(Ue,{padding:"md",shadow:"md",get children(){var h=cr(),z=h.firstChild,f=z.nextSibling;return f.firstChild,l(f,()=>u.marginUsed.toLocaleString(),null),A(w=>{var g=e({textAlign:"center"}),_=e({fontSize:"14px",fontWeight:"500",color:"gray.600",marginBottom:"8px"}),$=e({fontSize:"24px",fontWeight:"bold",color:"orange.600"});return g!==w.e&&t(h,w.e=g),_!==w.t&&t(z,w.t=_),$!==w.a&&t(f,w.a=$),w},{e:void 0,t:void 0,a:void 0}),h}}),null),l(M,B(Ue,{title:"当前持仓",padding:"none",shadow:"md",get children(){var h=gr(),z=h.firstChild,f=z.firstChild,w=f.firstChild,g=w.firstChild,_=g.nextSibling,$=_.nextSibling,V=f.nextSibling;return l(V,B(Ge,{get each(){return b()},children:v=>(()=>{var d=hr(),C=d.firstChild,L=C.nextSibling,D=L.nextSibling,F=D.firstChild,j=F.nextSibling,X=j.nextSibling,U=X.firstChild;return l(C,()=>v.symbol),l(L,()=>v.quantity),l(D,()=>v.unrealizedPnL>=0?"+":"",F),l(D,()=>Math.abs(v.unrealizedPnL).toFixed(2),j),l(X,()=>v.unrealizedPnL>=0?"+":"",U),l(X,()=>v.unrealizedPnLPercent.toFixed(2),U),A(H=>{var n=e({borderBottom:"1px solid",borderColor:"gray.200"}),K=e({padding:"12px 16px",fontSize:"14px",fontWeight:"500",color:"gray.900"}),ee=e({padding:"12px 16px",textAlign:"right",fontSize:"14px",color:"gray.700"}),oe=e({padding:"12px 16px",textAlign:"right",fontSize:"14px",fontWeight:"500",color:v.unrealizedPnL>=0?"green.600":"red.600"}),ne=e({fontSize:"12px"});return n!==H.e&&t(d,H.e=n),K!==H.t&&t(C,H.t=K),ee!==H.a&&t(L,H.a=ee),oe!==H.o&&t(D,H.o=oe),ne!==H.i&&t(X,H.i=ne),H},{e:void 0,t:void 0,a:void 0,o:void 0,i:void 0}),d})()})),A(v=>{var d=e({overflowX:"auto"}),C=e({width:"100%",borderCollapse:"collapse"}),L=e({backgroundColor:"gray.50"}),D=e({padding:"12px 16px",textAlign:"left",fontSize:"12px",fontWeight:"600",color:"gray.600"}),F=e({padding:"12px 16px",textAlign:"right",fontSize:"12px",fontWeight:"600",color:"gray.600"}),j=e({padding:"12px 16px",textAlign:"right",fontSize:"12px",fontWeight:"600",color:"gray.600"});return d!==v.e&&t(h,v.e=d),C!==v.t&&t(z,v.t=C),L!==v.a&&t(w,v.a=L),D!==v.o&&t(g,v.o=D),F!==v.i&&t(_,v.i=F),j!==v.n&&t($,v.n=j),v},{e:void 0,t:void 0,a:void 0,o:void 0,i:void 0,n:void 0}),h}}),null),l(M,B(Ue,{title:"订单历史",padding:"none",shadow:"md",get children(){var h=xr(),z=h.firstChild,f=z.firstChild,w=f.firstChild,g=w.firstChild,_=g.nextSibling,$=_.nextSibling,V=f.nextSibling;return l(V,B(Ge,{get each(){return r()},children:v=>(()=>{var d=fr(),C=d.firstChild,L=C.firstChild,D=L.nextSibling,F=D.firstChild,j=C.nextSibling,X=j.firstChild,U=j.nextSibling,H=U.firstChild;return l(C,()=>v.symbol,L),l(D,()=>v.quantity,F),l(D,()=>v.price,null),l(X,()=>v.type==="buy"?"买入":"卖出"),l(H,(()=>{var n=pe(()=>v.status==="filled");return()=>n()?"已成交":v.status==="pending"?"待成交":"已取消"})()),A(n=>{var K=e({borderBottom:"1px solid",borderColor:"gray.200"}),ee=e({padding:"12px 16px",fontSize:"14px",fontWeight:"500",color:"gray.900"}),oe=e({fontSize:"12px",color:"gray.600"}),ne=e({padding:"12px 16px",textAlign:"center",fontSize:"14px"}),se=e({padding:"4px 8px",borderRadius:"4px",fontSize:"12px",fontWeight:"500",backgroundColor:v.type==="buy"?"green.100":"red.100",color:v.type==="buy"?"green.800":"red.800"}),he=e({padding:"12px 16px",textAlign:"right",fontSize:"14px"}),ve=e({padding:"4px 8px",borderRadius:"4px",fontSize:"12px",fontWeight:"500",backgroundColor:v.status==="filled"?"green.100":v.status==="pending"?"yellow.100":"red.100",color:v.status==="filled"?"green.800":v.status==="pending"?"yellow.800":"red.800"});return K!==n.e&&t(d,n.e=K),ee!==n.t&&t(C,n.t=ee),oe!==n.a&&t(D,n.a=oe),ne!==n.o&&t(j,n.o=ne),se!==n.i&&t(X,n.i=se),he!==n.n&&t(U,n.n=he),ve!==n.s&&t(H,n.s=ve),n},{e:void 0,t:void 0,a:void 0,o:void 0,i:void 0,n:void 0,s:void 0}),d})()})),A(v=>{var d=e({overflowX:"auto"}),C=e({width:"100%",borderCollapse:"collapse"}),L=e({backgroundColor:"gray.50"}),D=e({padding:"12px 16px",textAlign:"left",fontSize:"12px",fontWeight:"600",color:"gray.600"}),F=e({padding:"12px 16px",textAlign:"center",fontSize:"12px",fontWeight:"600",color:"gray.600"}),j=e({padding:"12px 16px",textAlign:"right",fontSize:"12px",fontWeight:"600",color:"gray.600"});return d!==v.e&&t(h,v.e=d),C!==v.t&&t(z,v.t=C),L!==v.a&&t(w,v.a=L),D!==v.o&&t(g,v.o=D),F!==v.i&&t(_,v.i=F),j!==v.n&&t($,v.n=j),v},{e:void 0,t:void 0,a:void 0,o:void 0,i:void 0,n:void 0}),h}}),null),l(m,B(rr,{get isOpen(){return p()},onClose:()=>S(!1),title:"新建订单",size:"md",get children(){return[(()=>{var h=ur(),z=h.firstChild,f=z.firstChild,w=f.firstChild,g=w.nextSibling,_=f.nextSibling,$=_.firstChild,V=$.nextSibling,v=z.nextSibling;return l(h,B(Rt,{label:"股票代码",placeholder:"例如: AAPL",get value(){return c().symbol},onInput:d=>k(C=>({...C,symbol:d.currentTarget.value}))}),z),g.addEventListener("change",d=>k(C=>({...C,type:d.currentTarget.value}))),V.addEventListener("change",d=>k(C=>({...C,orderType:d.currentTarget.value}))),l(v,B(Rt,{label:"数量",type:"number",placeholder:"100",get value(){return c().quantity},onInput:d=>k(C=>({...C,quantity:d.currentTarget.value}))}),null),l(v,(()=>{var d=pe(()=>c().orderType==="limit");return()=>d()&&B(Rt,{label:"价格",type:"number",step:"0.01",placeholder:"150.25",get value(){return c().price},onInput:C=>k(L=>({...L,price:C.currentTarget.value}))})})(),null),A(d=>{var C=e({display:"flex",flexDirection:"column",gap:"16px"}),L=e({display:"grid",gridTemplateColumns:"1fr 1fr",gap:"16px"}),D=e({display:"block",fontSize:"14px",fontWeight:"500",color:"gray.700",marginBottom:"8px"}),F=e({width:"100%",padding:"10px 14px",border:"1px solid",borderColor:"gray.300",borderRadius:"8px",backgroundColor:"white",color:"gray.900"}),j=e({display:"block",fontSize:"14px",fontWeight:"500",color:"gray.700",marginBottom:"8px"}),X=e({width:"100%",padding:"10px 14px",border:"1px solid",borderColor:"gray.300",borderRadius:"8px",backgroundColor:"white",color:"gray.900"}),U=e({display:"grid",gridTemplateColumns:"1fr 1fr",gap:"16px"});return C!==d.e&&t(h,d.e=C),L!==d.t&&t(z,d.t=L),D!==d.a&&t(w,d.a=D),F!==d.o&&t(g,d.o=F),j!==d.i&&t($,d.i=j),X!==d.n&&t(V,d.n=X),U!==d.s&&t(v,d.s=U),d},{e:void 0,t:void 0,a:void 0,o:void 0,i:void 0,n:void 0,s:void 0}),A(()=>g.value=c().type),A(()=>V.value=c().orderType),h})(),(()=>{var h=pr();return l(h,B(zt,{variant:"ghost",onClick:()=>S(!1),children:"取消"}),null),l(h,B(zt,{variant:"primary",onClick:P,children:"提交订单"}),null),h})()]}}),null),A(h=>{var z=e({padding:"24px",maxWidth:"1400px",margin:"0 auto",backgroundColor:"gray.50",minHeight:"100vh"}),f=e({marginBottom:"32px",display:"flex",justifyContent:"space-between",alignItems:"center"}),w=e({fontSize:"32px",fontWeight:"bold",color:"gray.900",marginBottom:"8px"}),g=e({fontSize:"16px",color:"gray.600"}),_=e({display:"grid",gridTemplateColumns:"repeat(auto-fit, minmax(200px, 1fr))",gap:"24px",marginBottom:"32px"}),$=e({display:"grid",gridTemplateColumns:"1fr 1fr",gap:"24px","@media (max-width: 1024px)":{gridTemplateColumns:"1fr"}});return z!==h.e&&t(m,h.e=z),f!==h.t&&t(R,h.t=f),w!==h.a&&t(I,h.a=w),g!==h.o&&t(Y,h.o=g),_!==h.i&&t(E,h.i=_),$!==h.n&&t(M,h.n=$),h},{e:void 0,t:void 0,a:void 0,o:void 0,i:void 0,n:void 0}),m})()}function yr(){return B(Uo,{get children(){return B(Ye,{path:"/",component:_i,get children(){return[B(Ye,{path:"/",component:Wo}),B(Ye,{path:"/dashboard",component:Wo}),B(Ye,{path:"/market",component:qi}),B(Ye,{path:"/trading",component:Sr}),B(Ye,{path:"/strategy-editor",component:Yi}),B(Ye,{path:"/api-test",component:Di})]}})}})}const To=document.getElementById("root");To&&Go(()=>B(yr,{}),To);console.log("🚀 量化交易前端平台启动成功"),console.log("📊 基于 SolidJS + Panda CSS"),console.log("⚡ 极致性能，专业体验");
