import{d as Rt,c as xe,u as qo,t as R,i as d,m as ue,a as T,b as t,e as B,A as No,o as St,f as It,F as Ue,g as To,h as Xo,s as Bt,j as Pt,k as Dt,P as Ko,S as Fe,l as Yo,R as Ke,n as Vo,r as Uo}from"./vendor-solid-BmRsd-Qu.js";import{l as _o}from"./vendor-editor-l7stcynF.js";(function(){const n=document.createElement("link").relList;if(n&&n.supports&&n.supports("modulepreload"))return;for(const s of document.querySelectorAll('link[rel="modulepreload"]'))b(s);new MutationObserver(s=>{for(const v of s)if(v.type==="childList")for(const m of v.addedNodes)m.tagName==="LINK"&&m.rel==="modulepreload"&&b(m)}).observe(document,{childList:!0,subtree:!0});function g(s){const v={};return s.integrity&&(v.integrity=s.integrity),s.referrerPolicy&&(v.referrerPolicy=s.referrerPolicy),s.crossOrigin==="use-credentials"?v.credentials="include":s.crossOrigin==="anonymous"?v.credentials="omit":v.credentials="same-origin",v}function b(s){if(s.ep)return;s.ep=!0;const v=g(s);fetch(s.href,v)}})();function $t(i){return typeof i=="object"&&i!=null&&!Array.isArray(i)}function Go(i){return Object.fromEntries(Object.entries(i??{}).filter(([n,g])=>g!==void 0))}var Qo=i=>i==="base";function Zo(i){return i.slice().filter(n=>!Qo(n))}function zo(i){return String.fromCharCode(i+(i>25?39:97))}function Jo(i){let n="",g;for(g=Math.abs(i);g>52;g=g/52|0)n=zo(g%52)+n;return zo(g%52)+n}function ei(i,n){let g=n.length;for(;g;)i=i*33^n.charCodeAt(--g);return i}function ti(i){return Jo(ei(5381,i)>>>0)}var Bo=/\s*!(important)?/i;function oi(i){return typeof i=="string"?Bo.test(i):!1}function ii(i){return typeof i=="string"?i.replace(Bo,"").trim():i}function Po(i){return typeof i=="string"?i.replaceAll(" ","_"):i}var Lt=i=>{const n=new Map;return(...b)=>{const s=JSON.stringify(b);if(n.has(s))return n.get(s);const v=i(...b);return n.set(s,v),v}};function Do(...i){return i.filter(Boolean).reduce((g,b)=>(Object.keys(b).forEach(s=>{const v=g[s],m=b[s];$t(v)&&$t(m)?g[s]=Do(v,m):g[s]=m}),g),{})}var ni=i=>i!=null;function Lo(i,n,g={}){const{stop:b,getKey:s}=g;function v(m,u=[]){if($t(m)||Array.isArray(m)){const k={};for(const[W,E]of Object.entries(m)){const x=s?.(W,E)??W,S=[...u,x];if(b?.(m,S))return n(m,u);const A=v(E,S);ni(A)&&(k[x]=A)}return k}return n(m,u)}return v(i)}function ri(i,n){return i.reduce((g,b,s)=>{const v=n[s];return b!=null&&(g[v]=b),g},{})}function Eo(i,n,g=!0){const{utility:b,conditions:s}=n,{hasShorthand:v,resolveShorthand:m}=b;return Lo(i,u=>Array.isArray(u)?ri(u,s.breakpoints.keys):u,{stop:u=>Array.isArray(u),getKey:g?u=>v?m(u):u:void 0})}var li={shift:i=>i,finalize:i=>i,breakpoints:{keys:[]}},di=i=>typeof i=="string"?i.replaceAll(/[\n\s]+/g," "):i;function ai(i){const{utility:n,hash:g,conditions:b=li}=i,s=m=>[n.prefix,m].filter(Boolean).join("-"),v=(m,u)=>{let k;if(g){const W=[...b.finalize(m),u];k=s(n.toHash(W,ti))}else k=[...b.finalize(m),s(u)].join(":");return k};return Lt(({base:m,...u}={})=>{const k=Object.assign(u,m),W=Eo(k,i),E=new Set;return Lo(W,(x,S)=>{const A=oi(x);if(x==null)return;const[C,...I]=b.shift(S),V=Zo(I),M=n.transform(C,ii(di(x)));let j=v(V,M.className);A&&(j=`${j}!`),E.add(j)}),Array.from(E).join(" ")})}function si(...i){return i.flat().filter(n=>$t(n)&&Object.keys(Go(n)).length>0)}function ci(i){function n(s){const v=si(...s);return v.length===1?v:v.map(m=>Eo(m,i))}function g(...s){return Do(...n(s))}function b(...s){return Object.assign({},...n(s))}return{mergeCss:Lt(g),assignCss:b}}var gi=/([A-Z])/g,xi=/^ms-/,ui=Lt(i=>i.startsWith("--")?i:i.replace(gi,"-$1").replace(xi,"-ms-").toLowerCase()),vi="cm,mm,Q,in,pc,pt,px,em,ex,ch,rem,lh,rlh,vw,vh,vmin,vmax,vb,vi,svw,svh,lvw,lvh,dvw,dvh,cqw,cqh,cqi,cqb,cqmin,cqmax,%";`${vi.split(",").join("|")}`;const pi="_dark,_light,_hover,_focus,_focusWithin,_focusVisible,_disabled,_active,_visited,_target,_readOnly,_readWrite,_empty,_checked,_enabled,_expanded,_highlighted,_before,_after,_firstLetter,_firstLine,_marker,_selection,_file,_backdrop,_first,_last,_only,_even,_odd,_firstOfType,_lastOfType,_onlyOfType,_peerFocus,_peerHover,_peerActive,_peerFocusWithin,_peerFocusVisible,_peerDisabled,_peerChecked,_peerInvalid,_peerExpanded,_peerPlaceholderShown,_groupFocus,_groupHover,_groupActive,_groupFocusWithin,_groupFocusVisible,_groupDisabled,_groupChecked,_groupExpanded,_groupInvalid,_indeterminate,_required,_valid,_invalid,_autofill,_inRange,_outOfRange,_placeholder,_placeholderShown,_pressed,_selected,_default,_optional,_open,_closed,_fullscreen,_loading,_currentPage,_currentStep,_motionReduce,_motionSafe,_print,_landscape,_portrait,_osDark,_osLight,_highContrast,_lessContrast,_moreContrast,_ltr,_rtl,_scrollbar,_scrollbarThumb,_scrollbarTrack,_horizontal,_vertical,_starting,sm,smOnly,smDown,md,mdOnly,mdDown,lg,lgOnly,lgDown,xl,xlOnly,xlDown,2xl,2xlOnly,2xlDown,smToMd,smToLg,smToXl,smTo2xl,mdToLg,mdToXl,mdTo2xl,lgToXl,lgTo2xl,xlTo2xl,@/xs,@/sm,@/md,@/lg,@/xl,@/2xl,@/3xl,@/4xl,@/5xl,@/6xl,@/7xl,@/8xl,base",Mo=new Set(pi.split(","));function $o(i){return Mo.has(i)||/^@|&|&$/.test(i)}const bi=/^_/,hi=/&|@/;function fi(i){return i.map(n=>Mo.has(n)?n.replace(bi,""):hi.test(n)?`[${Po(n.trim())}]`:n)}function mi(i){return i.sort((n,g)=>{const b=$o(n),s=$o(g);return b&&!s?1:!b&&s?-1:0})}const Si="aspectRatio:aspect,boxDecorationBreak:decoration,zIndex:z,boxSizing:box,objectPosition:obj-pos,objectFit:obj-fit,overscrollBehavior:overscroll,overscrollBehaviorX:overscroll-x,overscrollBehaviorY:overscroll-y,position:pos/1,top:top,left:left,insetInline:inset-x/insetX,insetBlock:inset-y/insetY,inset:inset,insetBlockEnd:inset-b,insetBlockStart:inset-t,insetInlineEnd:end/insetEnd/1,insetInlineStart:start/insetStart/1,right:right,bottom:bottom,float:float,visibility:vis,display:d,hideFrom:hide,hideBelow:show,flexBasis:basis,flex:flex,flexDirection:flex/flexDir,flexGrow:grow,flexShrink:shrink,gridTemplateColumns:grid-cols,gridTemplateRows:grid-rows,gridColumn:col-span,gridRow:row-span,gridColumnStart:col-start,gridColumnEnd:col-end,gridAutoFlow:grid-flow,gridAutoColumns:auto-cols,gridAutoRows:auto-rows,gap:gap,gridGap:gap,gridRowGap:gap-x,gridColumnGap:gap-y,rowGap:gap-x,columnGap:gap-y,justifyContent:justify,alignContent:content,alignItems:items,alignSelf:self,padding:p/1,paddingLeft:pl/1,paddingRight:pr/1,paddingTop:pt/1,paddingBottom:pb/1,paddingBlock:py/1/paddingY,paddingBlockEnd:pb,paddingBlockStart:pt,paddingInline:px/paddingX/1,paddingInlineEnd:pe/1/paddingEnd,paddingInlineStart:ps/1/paddingStart,marginLeft:ml/1,marginRight:mr/1,marginTop:mt/1,marginBottom:mb/1,margin:m/1,marginBlock:my/1/marginY,marginBlockEnd:mb,marginBlockStart:mt,marginInline:mx/1/marginX,marginInlineEnd:me/1/marginEnd,marginInlineStart:ms/1/marginStart,spaceX:space-x,spaceY:space-y,outlineWidth:ring-width/ringWidth,outlineColor:ring-color/ringColor,outline:ring/1,outlineOffset:ring-offset/ringOffset,divideX:divide-x,divideY:divide-y,divideColor:divide-color,divideStyle:divide-style,width:w/1,inlineSize:w,minWidth:min-w/minW,minInlineSize:min-w,maxWidth:max-w/maxW,maxInlineSize:max-w,height:h/1,blockSize:h,minHeight:min-h/minH,minBlockSize:min-h,maxHeight:max-h/maxH,maxBlockSize:max-b,color:text,fontFamily:font,fontSize:fs,fontWeight:fw,fontSmoothing:smoothing,fontVariantNumeric:numeric,letterSpacing:tracking,lineHeight:leading,textAlign:text-align,textDecoration:text-decor,textDecorationColor:text-decor-color,textEmphasisColor:text-emphasis-color,textDecorationStyle:decoration-style,textDecorationThickness:decoration-thickness,textUnderlineOffset:underline-offset,textTransform:text-transform,textIndent:indent,textShadow:text-shadow,textShadowColor:text-shadow/textShadowColor,textOverflow:text-overflow,verticalAlign:v-align,wordBreak:break,textWrap:text-wrap,truncate:truncate,lineClamp:clamp,listStyleType:list-type,listStylePosition:list-pos,listStyleImage:list-img,backgroundPosition:bg-pos/bgPosition,backgroundPositionX:bg-pos-x/bgPositionX,backgroundPositionY:bg-pos-y/bgPositionY,backgroundAttachment:bg-attach/bgAttachment,backgroundClip:bg-clip/bgClip,background:bg/1,backgroundColor:bg/bgColor,backgroundOrigin:bg-origin/bgOrigin,backgroundImage:bg-img/bgImage,backgroundRepeat:bg-repeat/bgRepeat,backgroundBlendMode:bg-blend/bgBlendMode,backgroundSize:bg-size/bgSize,backgroundGradient:bg-gradient/bgGradient,textGradient:text-gradient,gradientFromPosition:gradient-from-pos,gradientToPosition:gradient-to-pos,gradientFrom:gradient-from,gradientTo:gradient-to,gradientVia:gradient-via,gradientViaPosition:gradient-via-pos,borderRadius:rounded/1,borderTopLeftRadius:rounded-tl/roundedTopLeft,borderTopRightRadius:rounded-tr/roundedTopRight,borderBottomRightRadius:rounded-br/roundedBottomRight,borderBottomLeftRadius:rounded-bl/roundedBottomLeft,borderTopRadius:rounded-t/roundedTop,borderRightRadius:rounded-r/roundedRight,borderBottomRadius:rounded-b/roundedBottom,borderLeftRadius:rounded-l/roundedLeft,borderStartStartRadius:rounded-ss/roundedStartStart,borderStartEndRadius:rounded-se/roundedStartEnd,borderStartRadius:rounded-s/roundedStart,borderEndStartRadius:rounded-es/roundedEndStart,borderEndEndRadius:rounded-ee/roundedEndEnd,borderEndRadius:rounded-e/roundedEnd,border:border,borderWidth:border-w,borderTopWidth:border-tw,borderLeftWidth:border-lw,borderRightWidth:border-rw,borderBottomWidth:border-bw,borderColor:border,borderInline:border-x/borderX,borderInlineWidth:border-x/borderXWidth,borderInlineColor:border-x/borderXColor,borderBlock:border-y/borderY,borderBlockWidth:border-y/borderYWidth,borderBlockColor:border-y/borderYColor,borderLeft:border-l,borderLeftColor:border-l,borderInlineStart:border-s/borderStart,borderInlineStartWidth:border-s/borderStartWidth,borderInlineStartColor:border-s/borderStartColor,borderRight:border-r,borderRightColor:border-r,borderInlineEnd:border-e/borderEnd,borderInlineEndWidth:border-e/borderEndWidth,borderInlineEndColor:border-e/borderEndColor,borderTop:border-t,borderTopColor:border-t,borderBottom:border-b,borderBottomColor:border-b,borderBlockEnd:border-be,borderBlockEndColor:border-be,borderBlockStart:border-bs,borderBlockStartColor:border-bs,boxShadow:shadow/1,boxShadowColor:shadow-color/shadowColor,mixBlendMode:mix-blend,filter:filter,brightness:brightness,contrast:contrast,grayscale:grayscale,hueRotate:hue-rotate,invert:invert,saturate:saturate,sepia:sepia,dropShadow:drop-shadow,blur:blur,backdropFilter:backdrop,backdropBlur:backdrop-blur,backdropBrightness:backdrop-brightness,backdropContrast:backdrop-contrast,backdropGrayscale:backdrop-grayscale,backdropHueRotate:backdrop-hue-rotate,backdropInvert:backdrop-invert,backdropOpacity:backdrop-opacity,backdropSaturate:backdrop-saturate,backdropSepia:backdrop-sepia,borderCollapse:border,borderSpacing:border-spacing,borderSpacingX:border-spacing-x,borderSpacingY:border-spacing-y,tableLayout:table,transitionTimingFunction:ease,transitionDelay:delay,transitionDuration:duration,transitionProperty:transition-prop,transition:transition,animation:animation,animationName:animation-name,animationTimingFunction:animation-ease,animationDuration:animation-duration,animationDelay:animation-delay,transformOrigin:origin,rotate:rotate,rotateX:rotate-x,rotateY:rotate-y,rotateZ:rotate-z,scale:scale,scaleX:scale-x,scaleY:scale-y,translate:translate,translateX:translate-x/x,translateY:translate-y/y,translateZ:translate-z/z,accentColor:accent,caretColor:caret,scrollBehavior:scroll,scrollbar:scrollbar,scrollMargin:scroll-m,scrollMarginLeft:scroll-ml,scrollMarginRight:scroll-mr,scrollMarginTop:scroll-mt,scrollMarginBottom:scroll-mb,scrollMarginBlock:scroll-my/scrollMarginY,scrollMarginBlockEnd:scroll-mb,scrollMarginBlockStart:scroll-mt,scrollMarginInline:scroll-mx/scrollMarginX,scrollMarginInlineEnd:scroll-me,scrollMarginInlineStart:scroll-ms,scrollPadding:scroll-p,scrollPaddingBlock:scroll-pb/scrollPaddingY,scrollPaddingBlockStart:scroll-pt,scrollPaddingBlockEnd:scroll-pb,scrollPaddingInline:scroll-px/scrollPaddingX,scrollPaddingInlineEnd:scroll-pe,scrollPaddingInlineStart:scroll-ps,scrollPaddingLeft:scroll-pl,scrollPaddingRight:scroll-pr,scrollPaddingTop:scroll-pt,scrollPaddingBottom:scroll-pb,scrollSnapAlign:snap-align,scrollSnapStop:snap-stop,scrollSnapType:snap-type,scrollSnapStrictness:snap-strictness,scrollSnapMargin:snap-m,scrollSnapMarginTop:snap-mt,scrollSnapMarginBottom:snap-mb,scrollSnapMarginLeft:snap-ml,scrollSnapMarginRight:snap-mr,touchAction:touch,userSelect:select,fill:fill,stroke:stroke,strokeWidth:stroke-w,srOnly:sr,debug:debug,appearance:appearance,backfaceVisibility:backface,clipPath:clip-path,hyphens:hyphens,mask:mask,maskImage:mask-image,maskSize:mask-size,textSizeAdjust:text-adjust,container:cq,containerName:cq-name,containerType:cq-type,textStyle:textStyle",jo=new Map,Oo=new Map;Si.split(",").forEach(i=>{const[n,g]=i.split(":"),[b,...s]=g.split("/");jo.set(n,b),s.length&&s.forEach(v=>{Oo.set(v==="1"?b:v,n)})});const Ro=i=>Oo.get(i)||i,Fo={conditions:{shift:mi,finalize:fi,breakpoints:{keys:["base","sm","md","lg","xl","2xl"]}},utility:{transform:(i,n)=>{const g=Ro(i);return{className:`${jo.get(g)||ui(g)}_${Po(n)}`}},hasShorthand:!0,toHash:(i,n)=>n(i.join(":")),resolveShorthand:Ro}},yi=ai(Fo),e=(...i)=>yi(Ho(...i));e.raw=(...i)=>Ho(...i);const{mergeCss:Ho}=ci(Fo);function Ye(){let i="",n=0,g;for(;n<arguments.length;)(g=arguments[n++])&&typeof g=="string"&&(i&&(i+=" "),i+=g);return i}var Ci=R("<div><aside><div><div>量</div></div><nav></nav><div><button type=button></button></div></aside><main><header><div><h1></h1></div><div><button type=button>帮助</button><button type=button>设置</button><div>用</div></div></header><div>"),wi=R("<div><div>量化平台</div><div>专业版 v2.0"),Io=R("<span>");function ki(i){const[n,g]=xe(!1),b=qo(),s=[{id:"dashboard",label:"仪表板",icon:"📊",path:"/dashboard"},{id:"market",label:"行情分析",icon:"📈",path:"/market"},{id:"strategy-editor",label:"策略编辑器",icon:"🧠",path:"/strategy-editor"},{id:"api-test",label:"API测试",icon:"🔧",path:"/api-test"}],v=u=>b.pathname===u||u==="/dashboard"&&b.pathname==="/",m=()=>s.find(k=>v(k.path))?.label||"仪表板";return(()=>{var u=Ci(),k=u.firstChild,W=k.firstChild,E=W.firstChild,x=W.nextSibling,S=x.nextSibling,A=S.firstChild,C=k.nextSibling,I=C.firstChild,V=I.firstChild,M=V.firstChild,j=V.nextSibling,h=j.firstChild,z=h.nextSibling,f=z.nextSibling,w=I.nextSibling;return d(W,(()=>{var c=ue(()=>!n());return()=>c()&&(()=>{var _=wi(),$=_.firstChild,U=$.nextSibling;return T(p=>{var a=e({fontSize:"14px",fontWeight:"600",color:"#262626",lineHeight:1.2}),y=e({fontSize:"11px",color:"#8c8c8c",lineHeight:1});return a!==p.e&&t($,p.e=a),y!==p.t&&t(U,p.t=y),p},{e:void 0,t:void 0}),_})()})(),null),d(x,()=>s.map(c=>B(No,{get href(){return c.path},get class(){return e({width:"100%",padding:n()?"12px 20px":"12px 16px",border:"none",backgroundColor:v(c.path)?"#e6f7ff":"transparent",color:v(c.path)?"#1890ff":"#595959",fontSize:"14px",textDecoration:"none",cursor:"pointer",transition:"all 0.2s",display:"flex",alignItems:"center",gap:"12px",borderLeft:v(c.path)?"3px solid #1890ff":"3px solid transparent",_hover:{backgroundColor:"#f5f5f5",color:"#1890ff"}})},get children(){return[(()=>{var _=Io();return d(_,()=>c.icon),T(()=>t(_,e({fontSize:"16px"}))),_})(),ue(()=>ue(()=>!n())()&&(()=>{var _=Io();return d(_,()=>c.label),T(()=>t(_,e({fontWeight:v(c.path)?"500":"400"}))),_})())]}}))),A.$$click=()=>g(!n()),d(A,()=>n()?"→":"←"),d(M,m),d(w,()=>i.children),T(c=>{var _=e({display:"flex",minHeight:"100vh",backgroundColor:"#f5f5f5"}),$=e({width:n()?"64px":"240px",backgroundColor:"white",borderRight:"1px solid #e8e8e8",transition:"width 0.3s ease",display:"flex",flexDirection:"column",position:"fixed",height:"100vh",zIndex:1e3}),U=e({padding:"12px 16px",borderBottom:"1px solid #e8e8e8",display:"flex",alignItems:"center",gap:"8px"}),p=e({width:"28px",height:"28px",backgroundColor:"#1890ff",borderRadius:"4px",display:"flex",alignItems:"center",justifyContent:"center",color:"white",fontSize:"14px",fontWeight:"bold"}),a=e({flex:1,padding:"8px 0",overflowY:"auto"}),y=e({padding:"16px",borderTop:"1px solid #e8e8e8"}),P=e({width:"100%",padding:"8px",border:"1px solid #d9d9d9",backgroundColor:"white",borderRadius:"4px",cursor:"pointer",fontSize:"12px",color:"#595959",_hover:{borderColor:"#1890ff",color:"#1890ff"}}),D=e({flex:1,marginLeft:n()?"64px":"240px",transition:"margin-left 0.3s ease",display:"flex",flexDirection:"column"}),q=e({backgroundColor:"white",borderBottom:"1px solid #e8e8e8",padding:"0 24px",height:"64px",display:"flex",alignItems:"center",justifyContent:"space-between"}),O=e({display:"flex",alignItems:"center",gap:"16px"}),K=e({fontSize:"18px",fontWeight:"500",color:"#262626",margin:0}),G=e({display:"flex",alignItems:"center",gap:"16px"}),N=e({padding:"6px 12px",border:"1px solid #d9d9d9",backgroundColor:"white",borderRadius:"4px",fontSize:"14px",cursor:"pointer",_hover:{borderColor:"#1890ff",color:"#1890ff"}}),r=e({padding:"6px 12px",border:"1px solid #d9d9d9",backgroundColor:"white",borderRadius:"4px",fontSize:"14px",cursor:"pointer",_hover:{borderColor:"#1890ff",color:"#1890ff"}}),Y=e({width:"32px",height:"32px",backgroundColor:"#1890ff",borderRadius:"50%",display:"flex",alignItems:"center",justifyContent:"center",color:"white",fontSize:"14px",fontWeight:"500"}),Z=e({flex:1,backgroundColor:"#f5f5f5",overflow:"auto"});return _!==c.e&&t(u,c.e=_),$!==c.t&&t(k,c.t=$),U!==c.a&&t(W,c.a=U),p!==c.o&&t(E,c.o=p),a!==c.i&&t(x,c.i=a),y!==c.n&&t(S,c.n=y),P!==c.s&&t(A,c.s=P),D!==c.h&&t(C,c.h=D),q!==c.r&&t(I,c.r=q),O!==c.d&&t(V,c.d=O),K!==c.l&&t(M,c.l=K),G!==c.u&&t(j,c.u=G),N!==c.c&&t(h,c.c=N),r!==c.w&&t(z,c.w=r),Y!==c.m&&t(f,c.m=Y),Z!==c.f&&t(w,c.f=Z),c},{e:void 0,t:void 0,a:void 0,o:void 0,i:void 0,n:void 0,s:void 0,h:void 0,r:void 0,d:void 0,l:void 0,u:void 0,c:void 0,w:void 0,m:void 0,f:void 0}),u})()}Rt(["click"]);var _i=R("<div><div>🎉 新版专业量化平台界面已成功加载！</div><div><h1>投资仪表盘</h1><div><button>刷新</button><button>设置</button><button>新增策略</button></div></div><div></div><div><div><div><h3>资金曲线图</h3><div><button type=button>日</button><button type=button>周</button><button type=button>月</button></div></div><div><div>📈</div><div>资金曲线图表</div><div>显示策略收益走势</div></div></div><div><div><h3>持仓概览</h3><span>查看全部 →</span></div><div></div></div></div><div><div><div><h3>今日行情</h3><span>更新时间: 15:30</span></div><div></div></div><div><div><h3>最新资讯</h3><span>查看更多 →</span></div><div></div></div></div><div><div>当前时间: </div><div><span>数据来源: 模拟数据</span><span>更新频率: 实时</span><div><div></div><span>系统正常"),zi=R("<div><div></div><div></div><div></div><div>"),$i=R("<div><div><span></span><span>%</span></div><div><span></span><span>"),Ri=R("<div><div><div></div><span></span></div><div><div></div><div> (<!>)"),Ii=R("<div><div></div><div><span></span><span>");function Wo(){console.log("🔥 Dashboard组件已加载 - 新版本");const[i,n]=xe(new Date().toLocaleString("zh-CN"));let g;St(()=>{g=setInterval(()=>{n(new Date().toLocaleString("zh-CN"))},1e3)}),It(()=>{g&&clearInterval(g)});const b=[{title:"总资产",value:"¥1,000,000",change:"+2.34%",trend:"up",icon:"💰",description:"总资产",subValue:"¥1,000,000"},{title:"今日盈亏",value:"0",change:"+0.00%",trend:"neutral",icon:"📊",description:"今日盈亏",subValue:"0.00%"},{title:"持仓市值",value:"¥50,000",change:"+0.00%",trend:"neutral",icon:"📈",description:"持仓市值",subValue:"¥50,000"},{title:"可用资金",value:"2",change:"+0.00%",trend:"neutral",icon:"🔒",description:"持仓数量",subValue:"2"}],s=[{code:"000725",name:"京东方A",price:3.45,change:-1.43,changePercent:-29.3,volume:7340.75,amount:-6046,status:"持仓"},{code:"300519",name:"新光药业",price:68.94,change:-1.05,changePercent:-1.5,volume:410.75,amount:-1796,status:"持仓"},{code:"000831",name:"五矿稀土",price:26.09,change:-1.37,changePercent:-5,volume:7558.72,amount:-7688,status:"持仓"}],v=[{name:"上证指数",value:"3,245.67",change:"+23.45",percent:"+0.73%",trend:"up"},{name:"深证成指",value:"10,567.23",change:"+45.67",percent:"+0.43%",trend:"up"},{name:"创业板指",value:"2,234.56",change:"-8.90",percent:"-0.40%",trend:"down"},{name:"科创50",value:"1,123.45",change:"+15.23",percent:"+1.37%",trend:"up"}],m=[{title:"A股市场今日表现强劲，科技股领涨",time:"刚刚发布",type:"market"},{title:"央行宣布降准0.25个百分点",time:"30分钟前",type:"policy"},{title:"新能源板块持续活跃，多只个股涨停",time:"1小时前",type:"sector"}];return(()=>{var u=_i(),k=u.firstChild,W=k.nextSibling,E=W.firstChild,x=E.nextSibling,S=x.firstChild,A=S.nextSibling,C=A.nextSibling,I=W.nextSibling,V=I.nextSibling,M=V.firstChild,j=M.firstChild,h=j.firstChild,z=h.nextSibling,f=z.firstChild,w=f.nextSibling,c=w.nextSibling,_=j.nextSibling,$=_.firstChild,U=$.nextSibling,p=U.nextSibling,a=M.nextSibling,y=a.firstChild,P=y.firstChild,D=P.nextSibling,q=y.nextSibling,O=V.nextSibling,K=O.firstChild,G=K.firstChild,N=G.firstChild,r=N.nextSibling,Y=G.nextSibling,Z=K.nextSibling,te=Z.firstChild,re=te.firstChild,ce=re.nextSibling,be=te.nextSibling,ve=O.nextSibling,Se=ve.firstChild;Se.firstChild;var we=Se.nextSibling,We=we.firstChild,ke=We.nextSibling,ye=ke.nextSibling,He=ye.firstChild;return d(I,B(Ue,{each:b,children:l=>(()=>{var ee=zi(),J=ee.firstChild,oe=J.nextSibling,Q=oe.nextSibling,ie=Q.nextSibling;return d(J,()=>l.icon),d(oe,()=>l.value),d(Q,()=>l.description),d(ie,()=>l.change),T(L=>{var ne=e({backgroundColor:"white",borderRadius:"8px",padding:"16px",border:"1px solid #e8e8e8",display:"flex",flexDirection:"column",alignItems:"center",textAlign:"center",transition:"all 0.2s ease",minHeight:"120px",_hover:{boxShadow:"0 2px 8px rgba(0,0,0,0.1)"}}),ae=e({fontSize:"24px",marginBottom:"8px"}),X=e({fontSize:"24px",fontWeight:"600",color:"#262626",marginBottom:"4px"}),F=e({fontSize:"12px",color:"#8c8c8c",marginBottom:"8px"}),le=e({fontSize:"12px",color:l.trend==="up"?"#52c41a":l.trend==="down"?"#f5222d":"#8c8c8c",fontWeight:"500"});return ne!==L.e&&t(ee,L.e=ne),ae!==L.t&&t(J,L.t=ae),X!==L.a&&t(oe,L.a=X),F!==L.o&&t(Q,L.o=F),le!==L.i&&t(ie,L.i=le),L},{e:void 0,t:void 0,a:void 0,o:void 0,i:void 0}),ee})()})),d(q,B(Ue,{each:s,children:l=>(()=>{var ee=$i(),J=ee.firstChild,oe=J.firstChild,Q=oe.nextSibling,ie=Q.firstChild,L=J.nextSibling,ne=L.firstChild,ae=ne.nextSibling;return d(oe,()=>l.code),d(Q,()=>l.changePercent>0?"+":"",ie),d(Q,()=>l.changePercent,ie),d(ne,()=>l.name),d(ae,()=>l.status),T(X=>{var F=e({padding:"8px",backgroundColor:"#fafafa",borderRadius:"4px",fontSize:"12px"}),le=e({display:"flex",justifyContent:"space-between",alignItems:"center",marginBottom:"4px"}),he=e({fontWeight:"600",color:"#262626"}),pe=e({color:l.changePercent>0?"#52c41a":"#f5222d",fontWeight:"500"}),ge=e({display:"flex",justifyContent:"space-between",color:"#8c8c8c"});return F!==X.e&&t(ee,X.e=F),le!==X.t&&t(J,X.t=le),he!==X.a&&t(oe,X.a=he),pe!==X.o&&t(Q,X.o=pe),ge!==X.i&&t(L,X.i=ge),X},{e:void 0,t:void 0,a:void 0,o:void 0,i:void 0}),ee})()})),d(Y,B(Ue,{each:v,children:l=>(()=>{var ee=Ri(),J=ee.firstChild,oe=J.firstChild,Q=oe.nextSibling,ie=J.nextSibling,L=ie.firstChild,ne=L.nextSibling,ae=ne.firstChild,X=ae.nextSibling;return X.nextSibling,d(Q,()=>l.name),d(L,()=>l.value),d(ne,()=>l.change,ae),d(ne,()=>l.percent,X),T(F=>{var le=e({display:"flex",alignItems:"center",justifyContent:"space-between",padding:"8px",backgroundColor:"#fafafa",borderRadius:"4px",fontSize:"12px"}),he=e({display:"flex",alignItems:"center",gap:"8px"}),pe=e({width:"6px",height:"6px",borderRadius:"50%",backgroundColor:l.trend==="up"?"#52c41a":"#f5222d"}),ge=e({fontWeight:"500",color:"#262626"}),$e=e({textAlign:"right"}),Ce=e({fontWeight:"600",color:"#262626",marginBottom:"2px"}),_e=e({color:l.trend==="up"?"#52c41a":"#f5222d",fontSize:"11px"});return le!==F.e&&t(ee,F.e=le),he!==F.t&&t(J,F.t=he),pe!==F.a&&t(oe,F.a=pe),ge!==F.o&&t(Q,F.o=ge),$e!==F.i&&t(ie,F.i=$e),Ce!==F.n&&t(L,F.n=Ce),_e!==F.s&&t(ne,F.s=_e),F},{e:void 0,t:void 0,a:void 0,o:void 0,i:void 0,n:void 0,s:void 0}),ee})()})),d(be,B(Ue,{each:m,children:l=>(()=>{var ee=Ii(),J=ee.firstChild,oe=J.nextSibling,Q=oe.firstChild,ie=Q.nextSibling;return d(J,()=>l.title),d(Q,()=>l.time),d(ie,()=>l.type),T(L=>{var ne=e({padding:"8px",backgroundColor:"#fafafa",borderRadius:"4px",cursor:"pointer",transition:"background-color 0.2s",_hover:{backgroundColor:"#f0f0f0"}}),ae=e({fontSize:"12px",fontWeight:"500",color:"#262626",marginBottom:"4px",lineHeight:"1.4"}),X=e({display:"flex",alignItems:"center",justifyContent:"space-between"}),F=e({fontSize:"11px",color:"#8c8c8c"}),le=e({fontSize:"10px",color:"#1890ff",backgroundColor:"#e6f7ff",padding:"2px 6px",borderRadius:"2px"});return ne!==L.e&&t(ee,L.e=ne),ae!==L.t&&t(J,L.t=ae),X!==L.a&&t(oe,L.a=X),F!==L.o&&t(Q,L.o=F),le!==L.i&&t(ie,L.i=le),L},{e:void 0,t:void 0,a:void 0,o:void 0,i:void 0}),ee})()})),d(Se,i,null),T(l=>{var ee=e({display:"flex",flexDirection:"column",gap:"16px",width:"100%",padding:"16px",backgroundColor:"#f0f2f5",minHeight:"100%"}),J=e({backgroundColor:"#52c41a",color:"white",padding:"12px 16px",borderRadius:"6px",textAlign:"center",fontSize:"16px",fontWeight:"600",boxShadow:"0 2px 8px rgba(82, 196, 26, 0.3)"}),oe=e({display:"flex",alignItems:"center",justifyContent:"space-between",marginBottom:"8px"}),Q=e({fontSize:"20px",fontWeight:"600",color:"#262626",margin:0}),ie=e({display:"flex",alignItems:"center",gap:"8px"}),L=e({padding:"6px 12px",backgroundColor:"#1890ff",color:"white",border:"none",borderRadius:"4px",fontSize:"12px",cursor:"pointer"}),ne=e({padding:"6px 12px",backgroundColor:"white",color:"#262626",border:"1px solid #d9d9d9",borderRadius:"4px",fontSize:"12px",cursor:"pointer"}),ae=e({padding:"6px 12px",backgroundColor:"#52c41a",color:"white",border:"none",borderRadius:"4px",fontSize:"12px",cursor:"pointer"}),X=e({display:"grid",gridTemplateColumns:"repeat(auto-fit, minmax(200px, 1fr))",gap:"16px",marginBottom:"16px"}),F=e({display:"grid",gridTemplateColumns:"2fr 1fr",gap:"16px",marginBottom:"16px","@media (max-width: 768px)":{gridTemplateColumns:"1fr"}}),le=e({backgroundColor:"white",borderRadius:"8px",border:"1px solid #e8e8e8",padding:"16px"}),he=e({display:"flex",alignItems:"center",justifyContent:"space-between",marginBottom:"16px",flexWrap:"wrap",gap:"8px"}),pe=e({fontSize:"16px",fontWeight:"600",color:"#262626",margin:0}),ge=e({display:"flex",alignItems:"center",gap:"8px"}),$e=e({padding:"4px 8px",backgroundColor:"#1890ff",color:"white",border:"none",borderRadius:"4px",fontSize:"12px",cursor:"pointer"}),Ce=e({padding:"4px 8px",backgroundColor:"white",color:"#262626",border:"1px solid #d9d9d9",borderRadius:"4px",fontSize:"12px",cursor:"pointer"}),_e=e({padding:"4px 8px",backgroundColor:"white",color:"#262626",border:"1px solid #d9d9d9",borderRadius:"4px",fontSize:"12px",cursor:"pointer"}),Te=e({height:"200px",backgroundColor:"#fafafa",borderRadius:"4px",display:"flex",alignItems:"center",justifyContent:"center",flexDirection:"column",gap:"8px"}),Be=e({fontSize:"48px"}),Pe=e({fontSize:"14px",color:"#8c8c8c"}),De=e({fontSize:"12px",color:"#8c8c8c"}),Ge=e({backgroundColor:"white",borderRadius:"8px",border:"1px solid #e8e8e8",padding:"16px"}),Qe=e({display:"flex",alignItems:"center",justifyContent:"space-between",marginBottom:"16px"}),Ze=e({fontSize:"16px",fontWeight:"600",color:"#262626",margin:0}),Je=e({fontSize:"12px",color:"#8c8c8c"}),et=e({display:"flex",flexDirection:"column",gap:"8px"}),tt=e({display:"grid",gridTemplateColumns:"1fr 1fr",gap:"16px","@media (max-width: 768px)":{gridTemplateColumns:"1fr"}}),yt=e({backgroundColor:"white",borderRadius:"8px",border:"1px solid #e8e8e8",padding:"16px"}),Ct=e({display:"flex",alignItems:"center",justifyContent:"space-between",marginBottom:"16px",flexWrap:"wrap",gap:"8px"}),ot=e({fontSize:"16px",fontWeight:"600",color:"#262626",margin:0}),qe=e({fontSize:"12px",color:"#8c8c8c"}),wt=e({display:"flex",flexDirection:"column",gap:"8px"}),it=e({backgroundColor:"white",borderRadius:"8px",border:"1px solid #e8e8e8",padding:"16px"}),nt=e({display:"flex",alignItems:"center",justifyContent:"space-between",marginBottom:"16px"}),rt=e({fontSize:"16px",fontWeight:"600",color:"#262626",margin:0}),lt=e({fontSize:"12px",color:"#1890ff",cursor:"pointer"}),kt=e({display:"flex",flexDirection:"column",gap:"8px"}),o=e({display:"flex",alignItems:"center",justifyContent:"space-between",padding:"12px 16px",backgroundColor:"white",borderRadius:"8px",border:"1px solid #e8e8e8",fontSize:"12px",color:"#8c8c8c"}),se=e({display:"flex",alignItems:"center",gap:"16px"}),fe=e({display:"flex",alignItems:"center",gap:"4px"}),me=e({width:"6px",height:"6px",borderRadius:"50%",backgroundColor:"#52c41a"});return ee!==l.e&&t(u,l.e=ee),J!==l.t&&t(k,l.t=J),oe!==l.a&&t(W,l.a=oe),Q!==l.o&&t(E,l.o=Q),ie!==l.i&&t(x,l.i=ie),L!==l.n&&t(S,l.n=L),ne!==l.s&&t(A,l.s=ne),ae!==l.h&&t(C,l.h=ae),X!==l.r&&t(I,l.r=X),F!==l.d&&t(V,l.d=F),le!==l.l&&t(M,l.l=le),he!==l.u&&t(j,l.u=he),pe!==l.c&&t(h,l.c=pe),ge!==l.w&&t(z,l.w=ge),$e!==l.m&&t(f,l.m=$e),Ce!==l.f&&t(w,l.f=Ce),_e!==l.y&&t(c,l.y=_e),Te!==l.g&&t(_,l.g=Te),Be!==l.p&&t($,l.p=Be),Pe!==l.b&&t(U,l.b=Pe),De!==l.T&&t(p,l.T=De),Ge!==l.A&&t(a,l.A=Ge),Qe!==l.O&&t(y,l.O=Qe),Ze!==l.I&&t(P,l.I=Ze),Je!==l.S&&t(D,l.S=Je),et!==l.W&&t(q,l.W=et),tt!==l.C&&t(O,l.C=tt),yt!==l.B&&t(K,l.B=yt),Ct!==l.v&&t(G,l.v=Ct),ot!==l.k&&t(N,l.k=ot),qe!==l.x&&t(r,l.x=qe),wt!==l.j&&t(Y,l.j=wt),it!==l.q&&t(Z,l.q=it),nt!==l.z&&t(te,l.z=nt),rt!==l.P&&t(re,l.P=rt),lt!==l.H&&t(ce,l.H=lt),kt!==l.F&&t(be,l.F=kt),o!==l.M&&t(ve,l.M=o),se!==l.D&&t(we,l.D=se),fe!==l.R&&t(ye,l.R=fe),me!==l.E&&t(He,l.E=me),l},{e:void 0,t:void 0,a:void 0,o:void 0,i:void 0,n:void 0,s:void 0,h:void 0,r:void 0,d:void 0,l:void 0,u:void 0,c:void 0,w:void 0,m:void 0,f:void 0,y:void 0,g:void 0,p:void 0,b:void 0,T:void 0,A:void 0,O:void 0,I:void 0,S:void 0,W:void 0,C:void 0,B:void 0,v:void 0,k:void 0,x:void 0,j:void 0,q:void 0,z:void 0,P:void 0,H:void 0,F:void 0,M:void 0,D:void 0,R:void 0,E:void 0}),u})()}var Wi=R("<div><div><h1>🔧 API 连接测试</h1><p>测试前端与后端API的连接状态</p></div><div><button>开始测试</button></div><div><div><h2>测试结果</h2></div><div></div></div><div><div><h2>配置信息</h2></div><div><div><div><h3>API Base URL</h3><p></p></div><div><h3>环境模式</h3><p></p></div><div><h3>代理配置</h3><p>/api → localhost:8000"),Ai=R('<p>点击"开始测试"按钮运行API连接测试'),Ti=R("<div>"),Bi=R("<div><div><div></div><div><h3></h3><p>"),Pi=R("<div>ms");const _t={get:async(i,n)=>{const g=n?"?"+new URLSearchParams(n).toString():"",s=await fetch("https://api.yourdomain.com"+i+g);if(!s.ok)throw new Error(`HTTP ${s.status}`);return s.json().catch(()=>({}))}},zt={SYSTEM:{HEALTH:"/v1/health"},MARKET:{OVERVIEW:"/v1/market/overview",SEARCH:"/v1/market/search"},AUTH:{PROFILE:"/v1/auth/profile"}};function Di(){const[i,n]=xe([]),g=(s,v,m,u)=>{n(k=>[...k,{name:s,status:v,message:m,duration:u}])},b=async()=>{n([]);try{const s=Date.now();await _t.get(zt.SYSTEM.HEALTH);const v=Date.now()-s;g("系统健康检查","success","连接成功",v)}catch(s){g("系统健康检查","error",s.message||"连接失败")}try{const s=Date.now();await _t.get(zt.MARKET.OVERVIEW);const v=Date.now()-s;g("市场概览","success","数据获取成功",v)}catch(s){g("市场概览","error",s.message||"数据获取失败")}try{const s=Date.now();await _t.get(zt.MARKET.SEARCH,{q:"AAPL"});const v=Date.now()-s;g("股票搜索","success","搜索成功",v)}catch(s){g("股票搜索","error",s.message||"搜索失败")}try{const s=Date.now();await _t.get(zt.AUTH.PROFILE);const v=Date.now()-s;g("用户信息","success","获取成功",v)}catch(s){g("用户信息","error",s.message||"获取失败（预期，因为未登录）")}};return St(()=>{console.log("ApiTest mounted")}),(()=>{var s=Wi(),v=s.firstChild,m=v.firstChild,u=m.nextSibling,k=v.nextSibling,W=k.firstChild,E=k.nextSibling,x=E.firstChild,S=x.firstChild,A=x.nextSibling,C=E.nextSibling,I=C.firstChild,V=I.firstChild,M=I.nextSibling,j=M.firstChild,h=j.firstChild,z=h.firstChild,f=z.nextSibling,w=h.nextSibling,c=w.firstChild,_=c.nextSibling,$=w.nextSibling,U=$.firstChild,p=U.nextSibling;return W.$$click=b,d(A,(()=>{var a=ue(()=>i().length===0);return()=>a()?(()=>{var y=Ai();return T(()=>t(y,e({color:"gray.500",textAlign:"center",padding:"40px 0"}))),y})():(()=>{var y=Ti();return d(y,()=>i().map(P=>(()=>{var D=Bi(),q=D.firstChild,O=q.firstChild,K=O.nextSibling,G=K.firstChild,N=G.nextSibling;return d(O,()=>P.status==="success"?"✅":"❌"),d(G,()=>P.name),d(N,()=>P.message),d(D,(()=>{var r=ue(()=>!!P.duration);return()=>r()&&(()=>{var Y=Pi(),Z=Y.firstChild;return d(Y,()=>P.duration,Z),T(()=>t(Y,e({fontSize:"12px",color:"gray.500",backgroundColor:"gray.100",padding:"4px 8px",borderRadius:"4px"}))),Y})()})(),null),T(r=>{var Y=e({display:"flex",alignItems:"center",justifyContent:"space-between",padding:"16px",borderRadius:"8px",border:"1px solid",borderColor:P.status==="success"?"green.200":"red.200",backgroundColor:P.status==="success"?"green.50":"red.50"}),Z=e({display:"flex",alignItems:"center",gap:"12px"}),te=e({fontSize:"20px"}),re=e({fontSize:"16px",fontWeight:"600",color:"gray.900",marginBottom:"4px"}),ce=e({fontSize:"14px",color:"gray.600"});return Y!==r.e&&t(D,r.e=Y),Z!==r.t&&t(q,r.t=Z),te!==r.a&&t(O,r.a=te),re!==r.o&&t(G,r.o=re),ce!==r.i&&t(N,r.i=ce),r},{e:void 0,t:void 0,a:void 0,o:void 0,i:void 0}),D})())),T(()=>t(y,e({display:"flex",flexDirection:"column",gap:"16px"}))),y})()})()),d(f,()=>"https://api.yourdomain.com"),d(_,()=>"production"),T(a=>{var y=e({padding:"24px",maxWidth:"1200px",margin:"0 auto"}),P=e({marginBottom:"32px"}),D=e({fontSize:"32px",fontWeight:"bold",color:"gray.900",marginBottom:"8px"}),q=e({fontSize:"16px",color:"gray.600"}),O=e({marginBottom:"32px"}),K=e({padding:"12px 24px",backgroundColor:"blue.600",color:"white",border:"none",borderRadius:"8px",fontSize:"16px",fontWeight:"500",cursor:"pointer",transition:"all 0.2s",_hover:{backgroundColor:"blue.700"}}),G=e({backgroundColor:"white",borderRadius:"12px",boxShadow:"0 1px 3px rgba(0, 0, 0, 0.1)",border:"1px solid #e5e7eb",overflow:"hidden"}),N=e({padding:"24px",borderBottom:"1px solid #e5e7eb"}),r=e({fontSize:"20px",fontWeight:"bold",color:"gray.900"}),Y=e({padding:"24px"}),Z=e({marginTop:"32px",backgroundColor:"white",borderRadius:"12px",boxShadow:"0 1px 3px rgba(0, 0, 0, 0.1)",border:"1px solid #e5e7eb",overflow:"hidden"}),te=e({padding:"24px",borderBottom:"1px solid #e5e7eb"}),re=e({fontSize:"20px",fontWeight:"bold",color:"gray.900"}),ce=e({padding:"24px"}),be=e({display:"grid",gridTemplateColumns:"repeat(auto-fit, minmax(300px, 1fr))",gap:"16px"}),ve=e({fontSize:"14px",fontWeight:"600",color:"gray.700",marginBottom:"8px"}),Se=e({fontSize:"14px",color:"gray.600",fontFamily:"monospace",backgroundColor:"gray.100",padding:"8px",borderRadius:"4px"}),we=e({fontSize:"14px",fontWeight:"600",color:"gray.700",marginBottom:"8px"}),We=e({fontSize:"14px",color:"gray.600",fontFamily:"monospace",backgroundColor:"gray.100",padding:"8px",borderRadius:"4px"}),ke=e({fontSize:"14px",fontWeight:"600",color:"gray.700",marginBottom:"8px"}),ye=e({fontSize:"14px",color:"gray.600",fontFamily:"monospace",backgroundColor:"gray.100",padding:"8px",borderRadius:"4px"});return y!==a.e&&t(s,a.e=y),P!==a.t&&t(v,a.t=P),D!==a.a&&t(m,a.a=D),q!==a.o&&t(u,a.o=q),O!==a.i&&t(k,a.i=O),K!==a.n&&t(W,a.n=K),G!==a.s&&t(E,a.s=G),N!==a.h&&t(x,a.h=N),r!==a.r&&t(S,a.r=r),Y!==a.d&&t(A,a.d=Y),Z!==a.l&&t(C,a.l=Z),te!==a.u&&t(I,a.u=te),re!==a.c&&t(V,a.c=re),ce!==a.w&&t(M,a.w=ce),be!==a.m&&t(j,a.m=be),ve!==a.f&&t(z,a.f=ve),Se!==a.y&&t(f,a.y=Se),we!==a.g&&t(c,a.g=we),We!==a.p&&t(_,a.p=We),ke!==a.b&&t(U,a.b=ke),ye!==a.T&&t(p,a.T=ye),a},{e:void 0,t:void 0,a:void 0,o:void 0,i:void 0,n:void 0,s:void 0,h:void 0,r:void 0,d:void 0,l:void 0,u:void 0,c:void 0,w:void 0,m:void 0,f:void 0,y:void 0,g:void 0,p:void 0,b:void 0,T:void 0}),s})()}Rt(["click"]);class Li{constructor(){this.socket=null,this.reconnectAttempts=0,this.maxReconnectAttempts=5,this.reconnectDelay=1e3,this.connectionStatusSignal=xe("disconnected"),this.marketDataSignal=xe(new Map),this.connectionStatus=this.connectionStatusSignal[0],this.setConnectionStatus=this.connectionStatusSignal[1],this.marketData=this.marketDataSignal[0],this.setMarketData=this.marketDataSignal[1],this.connect()}connect(){try{this.setConnectionStatus("connecting"),console.log("尝试连接WebSocket服务器:","wss://api.yourdomain.com/ws"),this.socket=null}catch(n){console.error("WebSocket连接失败:",n),this.setConnectionStatus("error"),this.handleReconnect()}}handleReconnect(){this.reconnectAttempts<this.maxReconnectAttempts?(this.reconnectAttempts++,setTimeout(()=>{console.log(`尝试重连 (${this.reconnectAttempts}/${this.maxReconnectAttempts})`),this.connect()},this.reconnectDelay*this.reconnectAttempts)):(console.log("达到最大重连次数，停止重连"),this.setConnectionStatus("error"))}updateMarketData(n){this.setMarketData(g=>{const b=new Map(g);return b.set(n.symbol,n),b})}subscribeToMarketData(n){console.log("订阅市场数据:",n),this.socket&&this.socket.connected&&this.socket.emit("subscribe",{symbols:n})}unsubscribeFromMarketData(n){console.log("取消订阅市场数据:",n),this.socket&&this.socket.connected&&this.socket.emit("unsubscribe",{symbols:n})}reconnect(){console.log("手动重连WebSocket..."),this.disconnect(),this.reconnectAttempts=0,this.connect()}disconnect(){this.socket&&(this.socket.disconnect(),this.socket=null),this.setConnectionStatus("disconnected")}}const Ie=new Li;function Ei(){return{connectionStatus:Ie.connectionStatus,marketData:Ie.marketData,subscribeToMarketData:Ie.subscribeToMarketData.bind(Ie),unsubscribeFromMarketData:Ie.unsubscribeFromMarketData.bind(Ie),disconnect:Ie.disconnect.bind(Ie),reconnect:Ie.reconnect.bind(Ie)}}var Mi=R("<div><div><div><h1>行情分析</h1><div><span>沪深A股</span><span>数据更新: 15:30</span><div><div></div><span></span></div></div></div><div><button type=button>导出数据</button><button type=button>自选股</button></div></div><div><div><h2>市场概览</h2><div><button type=button>日</button><button type=button>周</button><button type=button>月</button></div></div><div><div><div>上证指数</div><div>3,247.89</div><div>-12.34 (-0.38%)</div></div><div><div>深证成指</div><div>10,567.23</div><div>+45.67 (+0.43%)</div></div><div><div>创业板指</div><div>2,234.56</div><div>-8.90 (-0.40%)</div></div><div><div>科创50</div><div>1,123.45</div><div>+15.23 (+1.37%)</div></div></div></div><div><div><h3>市场筛选</h3><span>共 <!> 只股票</span></div><div><div><input type=text placeholder=搜索股票代码或名称><button type=button>搜索</button></div><div><span>板块:</span><button type=button>全部</button><button type=button>沪A</button><button type=button>深A</button><button type=button>创业板</button></div></div></div><div><div><h2>股票列表</h2></div><div><table><thead><tr><th>代码</th><th>名称</th><th>现价</th><th>涨跌额</th><th>涨跌幅</th><th>成交量</th><th>最高价</th><th>最低价</th><th>操作</th></tr></thead><tbody></tbody></table></div><div><div>共 <!> 条数据</div><div><button type=button>上一页</button><span>1</span><button type=button>下一页"),ji=R("<button type=button>重新连接"),Oi=R("<tr><td></td><td></td><td></td><td></td><td>%</td><td></td><td></td><td></td><td><div><button type=button>卖</button><button type=button>买"),Fi=R("<div><div><h3> 详细信息</h3></div><div><div>📊</div><p>K线图表和技术指标</p><p>这里将显示选中股票的详细分析图表");function Hi(){const i=[{symbol:"000725",name:"京东方A",price:3.45,change:-1.43,changePercent:-29.3,volume:7340750,high:3.52,low:3.4,open:3.48,marketCap:12e10},{symbol:"300519",name:"新光药业",price:68.94,change:-1.05,changePercent:-1.5,volume:410750,high:70.1,low:68.5,open:69.9,marketCap:28e9},{symbol:"000831",name:"五矿稀土",price:26.09,change:-1.37,changePercent:-5,volume:7558720,high:27.5,low:25.8,open:27.2,marketCap:45e9},{symbol:"000001",name:"平安银行",price:12.45,change:.23,changePercent:1.88,volume:1568e4,high:12.58,low:12.2,open:12.3,marketCap:24e10},{symbol:"000002",name:"万科A",price:8.76,change:-.15,changePercent:-1.68,volume:895e4,high:8.95,low:8.65,open:8.85,marketCap:98e9}],[n,g]=xe(i),b=Ei(),[s,v]=xe("AAPL"),[m,u]=xe("");To(()=>{const x=b.marketData();x.size>0&&g(S=>S.map(A=>{const C=x.get(A.symbol);return C?{...A,price:C.price,change:C.change,changePercent:C.changePercent,volume:C.volume}:A}))}),St(()=>{console.log("Market page mounted");const x=i.map(C=>C.symbol);b.subscribeToMarketData(x);let S;setTimeout(()=>{S=setInterval(()=>{b.connectionStatus()!=="connected"&&g(C=>C.map(I=>({...I,price:Math.max(.01,I.price+(Math.random()-.5)*2),change:I.change+(Math.random()-.5)*.5,changePercent:I.changePercent+(Math.random()-.5)*.2,volume:Math.max(0,I.volume+Math.floor((Math.random()-.5)*1e5))})))},3e3)},2e3),It(()=>{S&&clearInterval(S),b.unsubscribeFromMarketData(x)})});const k=()=>{const x=m().toLowerCase();return n().filter(S=>S.symbol.toLowerCase().includes(x)||S.name.toLowerCase().includes(x))},W=(x,S=2)=>new Intl.NumberFormat("zh-CN",{minimumFractionDigits:S,maximumFractionDigits:S}).format(x),E=x=>x>=1e6?`${(x/1e6).toFixed(1)}M`:x>=1e3?`${(x/1e3).toFixed(1)}K`:x.toString();return(()=>{var x=Mi(),S=x.firstChild,A=S.firstChild,C=A.firstChild,I=C.nextSibling,V=I.firstChild,M=V.nextSibling,j=M.nextSibling,h=j.firstChild,z=h.nextSibling,f=A.nextSibling,w=f.firstChild,c=w.nextSibling,_=S.nextSibling,$=_.firstChild,U=$.firstChild,p=U.nextSibling,a=p.firstChild,y=a.nextSibling,P=y.nextSibling,D=$.nextSibling,q=D.firstChild,O=q.firstChild,K=O.nextSibling,G=K.nextSibling,N=q.nextSibling,r=N.firstChild,Y=r.nextSibling,Z=Y.nextSibling,te=N.nextSibling,re=te.firstChild,ce=re.nextSibling,be=ce.nextSibling,ve=te.nextSibling,Se=ve.firstChild,we=Se.nextSibling,We=we.nextSibling,ke=_.nextSibling,ye=ke.firstChild,He=ye.firstChild,l=He.nextSibling,ee=l.firstChild,J=ee.nextSibling;J.nextSibling;var oe=ye.nextSibling,Q=oe.firstChild,ie=Q.firstChild,L=ie.nextSibling,ne=Q.nextSibling,ae=ne.firstChild,X=ae.nextSibling,F=X.nextSibling,le=F.nextSibling,he=le.nextSibling,pe=ke.nextSibling,ge=pe.firstChild,$e=ge.firstChild,Ce=ge.nextSibling,_e=Ce.firstChild,Te=_e.firstChild,Be=Te.firstChild,Pe=Be.firstChild,De=Pe.nextSibling,Ge=De.nextSibling,Qe=Ge.nextSibling,Ze=Qe.nextSibling,Je=Ze.nextSibling,et=Je.nextSibling,tt=et.nextSibling,yt=tt.nextSibling,Ct=Te.nextSibling,ot=Ce.nextSibling,qe=ot.firstChild,wt=qe.firstChild,it=wt.nextSibling;it.nextSibling;var nt=qe.nextSibling,rt=nt.firstChild,lt=rt.nextSibling,kt=lt.nextSibling;return d(z,()=>b.connectionStatus()==="connected"?"实时连接":"模拟数据"),d(f,(()=>{var o=ue(()=>b.connectionStatus()!=="connected");return()=>o()&&(()=>{var se=ji();return se.$$click=()=>b.reconnect(),T(()=>t(se,e({padding:"6px 12px",backgroundColor:"#1890ff",color:"white",border:"none",borderRadius:"4px",fontSize:"12px",cursor:"pointer"}))),se})()})(),w),d(l,()=>k().length,J),ie.$$input=o=>u(o.currentTarget.value),d(Ct,()=>k().map(o=>(()=>{var se=Oi(),fe=se.firstChild,me=fe.nextSibling,Le=me.nextSibling,Re=Le.nextSibling,ze=Re.nextSibling,Ne=ze.firstChild,Ee=ze.nextSibling,de=Ee.nextSibling,Ae=de.nextSibling,Me=Ae.nextSibling,je=Me.firstChild,Oe=je.firstChild,Xe=Oe.nextSibling;return se.$$click=()=>v(o.symbol),d(fe,()=>o.symbol),d(me,()=>o.name),d(Le,()=>W(o.price)),d(Re,()=>o.change>=0?"+":"",null),d(Re,()=>W(o.change),null),d(ze,()=>o.changePercent>=0?"+":"",Ne),d(ze,()=>W(o.changePercent),Ne),d(Ee,()=>E(o.volume)),d(de,()=>W(o.high)),d(Ae,()=>W(o.low)),T(H=>{var dt=e({borderBottom:"1px solid #f0f0f0",cursor:"pointer",transition:"background-color 0.2s",backgroundColor:s()===o.symbol?"#e6f7ff":"transparent",_hover:{backgroundColor:"#fafafa"}}),at=e({padding:"12px 16px",fontSize:"14px",fontWeight:"600",color:"#262626"}),st=e({padding:"12px 16px",fontSize:"14px",color:"#262626"}),ct=e({padding:"12px 16px",textAlign:"right",fontSize:"14px",fontWeight:"600",color:"#262626"}),gt=e({padding:"12px 16px",textAlign:"right",fontSize:"14px",fontWeight:"500",color:o.change>=0?"#52c41a":"#f5222d"}),xt=e({padding:"12px 16px",textAlign:"right",fontSize:"14px",fontWeight:"500",color:o.changePercent>=0?"#52c41a":"#f5222d"}),ut=e({padding:"12px 16px",textAlign:"right",fontSize:"14px",color:"#8c8c8c"}),vt=e({padding:"12px 16px",textAlign:"right",fontSize:"14px",color:"#8c8c8c"}),pt=e({padding:"12px 16px",textAlign:"right",fontSize:"14px",color:"#8c8c8c"}),bt=e({padding:"12px 16px",textAlign:"right"}),ht=e({display:"flex",gap:"4px",justifyContent:"flex-end"}),ft=e({padding:"4px 8px",backgroundColor:"#f5222d",color:"white",border:"none",borderRadius:"2px",fontSize:"12px",cursor:"pointer"}),mt=e({padding:"4px 8px",backgroundColor:"#52c41a",color:"white",border:"none",borderRadius:"2px",fontSize:"12px",cursor:"pointer"});return dt!==H.e&&t(se,H.e=dt),at!==H.t&&t(fe,H.t=at),st!==H.a&&t(me,H.a=st),ct!==H.o&&t(Le,H.o=ct),gt!==H.i&&t(Re,H.i=gt),xt!==H.n&&t(ze,H.n=xt),ut!==H.s&&t(Ee,H.s=ut),vt!==H.h&&t(de,H.h=vt),pt!==H.r&&t(Ae,H.r=pt),bt!==H.d&&t(Me,H.d=bt),ht!==H.l&&t(je,H.l=ht),ft!==H.u&&t(Oe,H.u=ft),mt!==H.c&&t(Xe,H.c=mt),H},{e:void 0,t:void 0,a:void 0,o:void 0,i:void 0,n:void 0,s:void 0,h:void 0,r:void 0,d:void 0,l:void 0,u:void 0,c:void 0}),se})())),d(qe,()=>k().length,it),d(x,(()=>{var o=ue(()=>!!s());return()=>o()&&(()=>{var se=Fi(),fe=se.firstChild,me=fe.firstChild,Le=me.firstChild,Re=fe.nextSibling,ze=Re.firstChild,Ne=ze.nextSibling,Ee=Ne.nextSibling;return d(me,s,Le),T(de=>{var Ae=e({marginTop:"24px",backgroundColor:"white",borderRadius:"8px",border:"1px solid #e8e8e8",overflow:"hidden"}),Me=e({padding:"20px",borderBottom:"1px solid #e8e8e8"}),je=e({fontSize:"16px",fontWeight:"600",color:"#262626",margin:"0"}),Oe=e({padding:"20px",textAlign:"center",color:"#8c8c8c"}),Xe=e({fontSize:"48px",marginBottom:"16px"}),H=e({fontSize:"12px"});return Ae!==de.e&&t(se,de.e=Ae),Me!==de.t&&t(fe,de.t=Me),je!==de.a&&t(me,de.a=je),Oe!==de.o&&t(Re,de.o=Oe),Xe!==de.i&&t(ze,de.i=Xe),H!==de.n&&t(Ee,de.n=H),de},{e:void 0,t:void 0,a:void 0,o:void 0,i:void 0,n:void 0}),se})()})(),null),T(o=>{var se=e({padding:"16px",backgroundColor:"#f0f2f5",minHeight:"100%"}),fe=e({display:"flex",alignItems:"center",justifyContent:"space-between",marginBottom:"16px"}),me=e({fontSize:"20px",fontWeight:"600",color:"#262626",margin:0,marginBottom:"4px"}),Le=e({display:"flex",alignItems:"center",gap:"12px",fontSize:"12px",color:"#8c8c8c"}),Re=e({padding:"2px 6px",backgroundColor:"#f6ffed",color:"#52c41a",borderRadius:"2px",fontSize:"11px"}),ze=e({display:"flex",alignItems:"center",gap:"4px"}),Ne=e({width:"6px",height:"6px",borderRadius:"50%",backgroundColor:b.connectionStatus()==="connected"?"#52c41a":"#faad14"}),Ee=e({display:"flex",alignItems:"center",gap:"8px"}),de=e({padding:"6px 12px",backgroundColor:"white",color:"#262626",border:"1px solid #d9d9d9",borderRadius:"4px",fontSize:"12px",cursor:"pointer"}),Ae=e({padding:"6px 12px",backgroundColor:"#52c41a",color:"white",border:"none",borderRadius:"4px",fontSize:"12px",cursor:"pointer"}),Me=e({marginBottom:"16px",backgroundColor:"white",borderRadius:"8px",border:"1px solid #e8e8e8",padding:"16px"}),je=e({display:"flex",alignItems:"center",justifyContent:"space-between",marginBottom:"12px"}),Oe=e({fontSize:"16px",fontWeight:"600",color:"#262626",margin:0}),Xe=e({display:"flex",alignItems:"center",gap:"8px"}),H=e({padding:"4px 8px",backgroundColor:"#1890ff",color:"white",border:"none",borderRadius:"4px",fontSize:"12px",cursor:"pointer"}),dt=e({padding:"4px 8px",backgroundColor:"white",color:"#262626",border:"1px solid #d9d9d9",borderRadius:"4px",fontSize:"12px",cursor:"pointer"}),at=e({padding:"4px 8px",backgroundColor:"white",color:"#262626",border:"1px solid #d9d9d9",borderRadius:"4px",fontSize:"12px",cursor:"pointer"}),st=e({display:"grid",gridTemplateColumns:"repeat(auto-fit, minmax(150px, 1fr))",gap:"16px"}),ct=e({padding:"12px",backgroundColor:"#fafafa",borderRadius:"4px",textAlign:"center"}),gt=e({fontSize:"12px",color:"#8c8c8c",marginBottom:"4px"}),xt=e({fontSize:"16px",fontWeight:"600",color:"#f5222d",marginBottom:"2px"}),ut=e({fontSize:"11px",color:"#f5222d"}),vt=e({padding:"12px",backgroundColor:"#fafafa",borderRadius:"4px",textAlign:"center"}),pt=e({fontSize:"12px",color:"#8c8c8c",marginBottom:"4px"}),bt=e({fontSize:"16px",fontWeight:"600",color:"#52c41a",marginBottom:"2px"}),ht=e({fontSize:"11px",color:"#52c41a"}),ft=e({padding:"12px",backgroundColor:"#fafafa",borderRadius:"4px",textAlign:"center"}),mt=e({fontSize:"12px",color:"#8c8c8c",marginBottom:"4px"}),Et=e({fontSize:"16px",fontWeight:"600",color:"#f5222d",marginBottom:"2px"}),Mt=e({fontSize:"11px",color:"#f5222d"}),jt=e({padding:"12px",backgroundColor:"#fafafa",borderRadius:"4px",textAlign:"center"}),Ot=e({fontSize:"12px",color:"#8c8c8c",marginBottom:"4px"}),Ft=e({fontSize:"16px",fontWeight:"600",color:"#52c41a",marginBottom:"2px"}),Ht=e({fontSize:"11px",color:"#52c41a"}),qt=e({marginBottom:"16px",backgroundColor:"white",borderRadius:"8px",border:"1px solid #e8e8e8",padding:"12px 16px"}),Nt=e({display:"flex",alignItems:"center",justifyContent:"space-between",marginBottom:"12px"}),Xt=e({fontSize:"14px",fontWeight:"600",color:"#262626",margin:0}),Kt=e({fontSize:"12px",color:"#8c8c8c"}),Yt=e({display:"flex",alignItems:"center",justifyContent:"space-between"}),Vt=e({display:"flex",alignItems:"center",gap:"8px"}),Ut=e({padding:"6px 12px",border:"1px solid #d9d9d9",borderRadius:"4px",fontSize:"12px",width:"180px",_focus:{outline:"none",borderColor:"#1890ff"}}),Gt=e({padding:"6px 12px",backgroundColor:"#1890ff",color:"white",border:"none",borderRadius:"4px",fontSize:"12px",cursor:"pointer"}),Qt=e({display:"flex",alignItems:"center",gap:"6px"}),Zt=e({fontSize:"12px",color:"#8c8c8c",marginRight:"4px"}),Jt=e({padding:"4px 8px",backgroundColor:"#1890ff",color:"white",border:"none",borderRadius:"4px",fontSize:"11px",cursor:"pointer"}),eo=e({padding:"4px 8px",backgroundColor:"white",color:"#262626",border:"1px solid #d9d9d9",borderRadius:"4px",fontSize:"11px",cursor:"pointer"}),to=e({padding:"4px 8px",backgroundColor:"white",color:"#262626",border:"1px solid #d9d9d9",borderRadius:"4px",fontSize:"11px",cursor:"pointer"}),oo=e({padding:"4px 8px",backgroundColor:"white",color:"#262626",border:"1px solid #d9d9d9",borderRadius:"4px",fontSize:"11px",cursor:"pointer"}),io=e({backgroundColor:"white",borderRadius:"8px",border:"1px solid #e8e8e8",overflow:"hidden"}),no=e({padding:"16px 20px",borderBottom:"1px solid #e8e8e8",backgroundColor:"#fafafa"}),ro=e({fontSize:"16px",fontWeight:"600",color:"#262626",margin:"0"}),lo=e({overflowX:"auto"}),ao=e({width:"100%",borderCollapse:"collapse"}),so=e({backgroundColor:"#fafafa"}),co=e({padding:"12px 16px",textAlign:"left",fontSize:"12px",fontWeight:"600",color:"#8c8c8c"}),go=e({padding:"12px 16px",textAlign:"left",fontSize:"12px",fontWeight:"600",color:"#8c8c8c"}),xo=e({padding:"12px 16px",textAlign:"right",fontSize:"12px",fontWeight:"600",color:"#8c8c8c"}),uo=e({padding:"12px 16px",textAlign:"right",fontSize:"12px",fontWeight:"600",color:"#8c8c8c"}),vo=e({padding:"12px 16px",textAlign:"right",fontSize:"12px",fontWeight:"600",color:"#8c8c8c"}),po=e({padding:"12px 16px",textAlign:"right",fontSize:"12px",fontWeight:"600",color:"#8c8c8c"}),bo=e({padding:"12px 16px",textAlign:"right",fontSize:"12px",fontWeight:"600",color:"#8c8c8c"}),ho=e({padding:"12px 16px",textAlign:"right",fontSize:"12px",fontWeight:"600",color:"#8c8c8c"}),fo=e({padding:"12px 16px",textAlign:"right",fontSize:"12px",fontWeight:"600",color:"#8c8c8c"}),mo=e({padding:"16px 20px",borderTop:"1px solid #f0f0f0",display:"flex",justifyContent:"space-between",alignItems:"center"}),So=e({fontSize:"14px",color:"#8c8c8c"}),yo=e({display:"flex",gap:"8px",alignItems:"center"}),Co=e({padding:"4px 8px",backgroundColor:"white",color:"#262626",border:"1px solid #d9d9d9",borderRadius:"4px",fontSize:"12px",cursor:"pointer"}),wo=e({padding:"4px 8px",backgroundColor:"#1890ff",color:"white",borderRadius:"4px",fontSize:"12px"}),ko=e({padding:"4px 8px",backgroundColor:"white",color:"#262626",border:"1px solid #d9d9d9",borderRadius:"4px",fontSize:"12px",cursor:"pointer"});return se!==o.e&&t(x,o.e=se),fe!==o.t&&t(S,o.t=fe),me!==o.a&&t(C,o.a=me),Le!==o.o&&t(I,o.o=Le),Re!==o.i&&t(V,o.i=Re),ze!==o.n&&t(j,o.n=ze),Ne!==o.s&&t(h,o.s=Ne),Ee!==o.h&&t(f,o.h=Ee),de!==o.r&&t(w,o.r=de),Ae!==o.d&&t(c,o.d=Ae),Me!==o.l&&t(_,o.l=Me),je!==o.u&&t($,o.u=je),Oe!==o.c&&t(U,o.c=Oe),Xe!==o.w&&t(p,o.w=Xe),H!==o.m&&t(a,o.m=H),dt!==o.f&&t(y,o.f=dt),at!==o.y&&t(P,o.y=at),st!==o.g&&t(D,o.g=st),ct!==o.p&&t(q,o.p=ct),gt!==o.b&&t(O,o.b=gt),xt!==o.T&&t(K,o.T=xt),ut!==o.A&&t(G,o.A=ut),vt!==o.O&&t(N,o.O=vt),pt!==o.I&&t(r,o.I=pt),bt!==o.S&&t(Y,o.S=bt),ht!==o.W&&t(Z,o.W=ht),ft!==o.C&&t(te,o.C=ft),mt!==o.B&&t(re,o.B=mt),Et!==o.v&&t(ce,o.v=Et),Mt!==o.k&&t(be,o.k=Mt),jt!==o.x&&t(ve,o.x=jt),Ot!==o.j&&t(Se,o.j=Ot),Ft!==o.q&&t(we,o.q=Ft),Ht!==o.z&&t(We,o.z=Ht),qt!==o.P&&t(ke,o.P=qt),Nt!==o.H&&t(ye,o.H=Nt),Xt!==o.F&&t(He,o.F=Xt),Kt!==o.M&&t(l,o.M=Kt),Yt!==o.D&&t(oe,o.D=Yt),Vt!==o.R&&t(Q,o.R=Vt),Ut!==o.E&&t(ie,o.E=Ut),Gt!==o.L&&t(L,o.L=Gt),Qt!==o.N&&t(ne,o.N=Qt),Zt!==o.G&&t(ae,o.G=Zt),Jt!==o.U&&t(X,o.U=Jt),eo!==o.K&&t(F,o.K=eo),to!==o.V&&t(le,o.V=to),oo!==o.Y&&t(he,o.Y=oo),io!==o.J&&t(pe,o.J=io),no!==o.Q&&t(ge,o.Q=no),ro!==o.Z&&t($e,o.Z=ro),lo!==o.X&&t(Ce,o.X=lo),ao!==o._&&t(_e,o._=ao),so!==o.$&&t(Be,o.$=so),co!==o.te&&t(Pe,o.te=co),go!==o.tt&&t(De,o.tt=go),xo!==o.ta&&t(Ge,o.ta=xo),uo!==o.to&&t(Qe,o.to=uo),vo!==o.ti&&t(Ze,o.ti=vo),po!==o.tn&&t(Je,o.tn=po),bo!==o.ts&&t(et,o.ts=bo),ho!==o.th&&t(tt,o.th=ho),fo!==o.tr&&t(yt,o.tr=fo),mo!==o.td&&t(ot,o.td=mo),So!==o.tl&&t(qe,o.tl=So),yo!==o.tu&&t(nt,o.tu=yo),Co!==o.tc&&t(rt,o.tc=Co),wo!==o.tw&&t(lt,o.tw=wo),ko!==o.tm&&t(kt,o.tm=ko),o},{e:void 0,t:void 0,a:void 0,o:void 0,i:void 0,n:void 0,s:void 0,h:void 0,r:void 0,d:void 0,l:void 0,u:void 0,c:void 0,w:void 0,m:void 0,f:void 0,y:void 0,g:void 0,p:void 0,b:void 0,T:void 0,A:void 0,O:void 0,I:void 0,S:void 0,W:void 0,C:void 0,B:void 0,v:void 0,k:void 0,x:void 0,j:void 0,q:void 0,z:void 0,P:void 0,H:void 0,F:void 0,M:void 0,D:void 0,R:void 0,E:void 0,L:void 0,N:void 0,G:void 0,U:void 0,K:void 0,V:void 0,Y:void 0,J:void 0,Q:void 0,Z:void 0,X:void 0,_:void 0,$:void 0,te:void 0,tt:void 0,ta:void 0,to:void 0,ti:void 0,tn:void 0,ts:void 0,th:void 0,tr:void 0,td:void 0,tl:void 0,tu:void 0,tc:void 0,tw:void 0,tm:void 0}),T(()=>ie.value=m()),x})()}Rt(["input","click"]);var qi=R("<div><div>");function Ni(i){const[n,g]=xe();let b;return St(async()=>{const s=n();if(s)try{const m="/libs/monaco-editor/min/vs";console.log(`Monaco Editor 配置: 本地模式, 路径: ${m}`),_o.config({paths:{vs:m}});const u=await _o.init();i.language==="python"&&u.languages.registerCompletionItemProvider("python",{provideCompletionItems:(k,W)=>{const E=k.getWordUntilPosition(W),x={startLineNumber:W.lineNumber,endLineNumber:W.lineNumber,startColumn:E.startColumn,endColumn:E.endColumn};return{suggestions:[{label:"def",kind:u.languages.CompletionItemKind.Keyword,insertText:"def ${1:function_name}(${2:parameters}):\n    ${3:pass}",insertTextRules:u.languages.CompletionItemInsertTextRule.InsertAsSnippet,documentation:"Define a function",range:x},{label:"initialize",kind:u.languages.CompletionItemKind.Function,insertText:"def initialize(context):\n    ${1:pass}",insertTextRules:u.languages.CompletionItemInsertTextRule.InsertAsSnippet,documentation:"策略初始化函数",range:x},{label:"handle_data",kind:u.languages.CompletionItemKind.Function,insertText:"def handle_data(context, data):\n    ${1:pass}",insertTextRules:u.languages.CompletionItemInsertTextRule.InsertAsSnippet,documentation:"主要的交易逻辑函数",range:x},{label:"order_target_percent",kind:u.languages.CompletionItemKind.Function,insertText:"order_target_percent(${1:security}, ${2:percent})",insertTextRules:u.languages.CompletionItemInsertTextRule.InsertAsSnippet,documentation:"下单到目标百分比",range:x},{label:"attribute_history",kind:u.languages.CompletionItemKind.Function,insertText:"attribute_history(${1:security}, ${2:count}, ${3:unit}, ${4:fields})",insertTextRules:u.languages.CompletionItemInsertTextRule.InsertAsSnippet,documentation:"获取历史数据",range:x},{label:"log.info",kind:u.languages.CompletionItemKind.Function,insertText:"log.info(${1:message})",insertTextRules:u.languages.CompletionItemInsertTextRule.InsertAsSnippet,documentation:"输出日志信息",range:x}]}}}),b=u.editor.create(s,{value:i.value||"",language:i.language||"python",theme:i.theme||"vs",fontSize:13,lineNumbers:"on",roundedSelection:!1,scrollBeyondLastLine:!1,minimap:{enabled:!0},automaticLayout:!0,tabSize:4,insertSpaces:!0,wordWrap:"on",folding:!0,renderLineHighlight:"all",selectOnLineNumbers:!0,matchBrackets:"always",...i.options}),b.onDidChangeModelContent(()=>{i.onChange&&b&&i.onChange(b.getValue())}),b.addCommand(u.KeyMod.CtrlCmd|u.KeyCode.KeyS,()=>{console.log("保存策略快捷键触发")}),b.addCommand(u.KeyMod.CtrlCmd|u.KeyCode.Enter,()=>{console.log("运行策略快捷键触发")})}catch(v){console.error("Monaco Editor 初始化失败:",v)}}),It(()=>{b&&b.dispose()}),(()=>{var s=qi(),v=s.firstChild;return Xo(g,v),T(m=>{var u=e({width:"100%",height:`${i.height||400}px`,border:"1px solid #e5e7eb",borderRadius:"6px",overflow:"hidden"}),k=e({width:"100%",height:"100%"});return u!==m.e&&t(s,m.e=u),k!==m.t&&t(v,m.t=k),m},{e:void 0,t:void 0}),s})()}var Xi=R('<div><div><h1>🧠 策略编辑器</h1><p>创建和编辑量化交易策略</p></div><div><div><div><h2>策略代码</h2><div><button>运行回测</button><button>保存策略</button></div></div><div></div></div><div><div><h2>回测结果</h2></div><div><div><div>📊</div><p>等待回测结果</p><p>点击"运行回测"开始策略测试</p></div></div></div></div><div><h3>策略模板</h3><div><button><div>双均线策略</div><div>基于移动平均线的经典策略</div></button><button><div>RSI策略</div><div>基于相对强弱指标的策略</div></button><button><div>布林带策略</div><div>利用布林带进行交易决策</div></button><button><div>机器学习策略</div><div>基于AI模型的量化策略');function Ki(){const[i,n]=xe(`# 量化策略示例
# 这是一个简单的移动平均线策略

def initialize(context):
    # 设置基准和股票
    g.benchmark = '000300.XSHG'
    g.stock = '000001.XSHE'
    
def handle_data(context, data):
    # 获取历史价格
    hist = attribute_history(g.stock, 20, '1d', ['close'])
    ma5 = hist['close'][-5:].mean()
    ma20 = hist['close'][-20:].mean()
    current_price = data[g.stock].close
    
    # 交易逻辑
    if ma5 > ma20 and current_price > ma5:
        # 金叉买入信号
        order_target_percent(g.stock, 0.8)
        log.info(f"买入信号，价格: {current_price}")
    elif ma5 < ma20:
        # 死叉卖出信号
        order_target_percent(g.stock, 0)
        log.info(f"卖出信号，价格: {current_price}")
`);return(()=>{var g=Xi(),b=g.firstChild,s=b.firstChild,v=s.nextSibling,m=b.nextSibling,u=m.firstChild,k=u.firstChild,W=k.firstChild,E=W.nextSibling,x=E.firstChild,S=x.nextSibling,A=k.nextSibling,C=u.nextSibling,I=C.firstChild,V=I.firstChild,M=I.nextSibling,j=M.firstChild,h=j.firstChild,z=h.nextSibling,f=z.nextSibling,w=m.nextSibling,c=w.firstChild,_=c.nextSibling,$=_.firstChild,U=$.firstChild,p=U.nextSibling,a=$.nextSibling,y=a.firstChild,P=y.nextSibling,D=a.nextSibling,q=D.firstChild,O=q.nextSibling,K=D.nextSibling,G=K.firstChild,N=G.nextSibling;return d(A,B(Ni,{get value(){return i()},language:"python",theme:"vs",height:500,onChange:n,options:{minimap:{enabled:!0},fontSize:14,wordWrap:"on",automaticLayout:!0}})),T(r=>{var Y=e({padding:"24px",maxWidth:"1400px",margin:"0 auto",height:"100%"}),Z=e({marginBottom:"32px"}),te=e({fontSize:"32px",fontWeight:"bold",color:"gray.900",marginBottom:"8px"}),re=e({fontSize:"16px",color:"gray.600"}),ce=e({display:"grid",gridTemplateColumns:"1fr 1fr",gap:"24px",height:"calc(100vh - 200px)"}),be=e({backgroundColor:"white",borderRadius:"12px",boxShadow:"0 1px 3px rgba(0, 0, 0, 0.1)",border:"1px solid #e5e7eb",overflow:"hidden",display:"flex",flexDirection:"column"}),ve=e({padding:"16px 20px",borderBottom:"1px solid #e5e7eb",backgroundColor:"#f9fafb",display:"flex",justifyContent:"space-between",alignItems:"center"}),Se=e({fontSize:"16px",fontWeight:"600",color:"gray.900"}),we=e({display:"flex",gap:"8px"}),We=e({padding:"6px 12px",backgroundColor:"blue.600",color:"white",border:"none",borderRadius:"6px",fontSize:"12px",fontWeight:"500",cursor:"pointer",_hover:{backgroundColor:"blue.700"}}),ke=e({padding:"6px 12px",backgroundColor:"green.600",color:"white",border:"none",borderRadius:"6px",fontSize:"12px",fontWeight:"500",cursor:"pointer",_hover:{backgroundColor:"green.700"}}),ye=e({flex:1,padding:"8px"}),He=e({backgroundColor:"white",borderRadius:"12px",boxShadow:"0 1px 3px rgba(0, 0, 0, 0.1)",border:"1px solid #e5e7eb",overflow:"hidden",display:"flex",flexDirection:"column"}),l=e({padding:"16px 20px",borderBottom:"1px solid #e5e7eb",backgroundColor:"#f9fafb"}),ee=e({fontSize:"16px",fontWeight:"600",color:"gray.900"}),J=e({flex:1,padding:"20px",display:"flex",alignItems:"center",justifyContent:"center"}),oe=e({textAlign:"center",color:"gray.500"}),Q=e({fontSize:"48px",marginBottom:"16px"}),ie=e({fontSize:"16px",marginBottom:"8px"}),L=e({fontSize:"14px"}),ne=e({marginTop:"24px",backgroundColor:"white",borderRadius:"12px",boxShadow:"0 1px 3px rgba(0, 0, 0, 0.1)",border:"1px solid #e5e7eb",padding:"20px"}),ae=e({fontSize:"18px",fontWeight:"600",color:"gray.900",marginBottom:"16px"}),X=e({display:"grid",gridTemplateColumns:"repeat(auto-fit, minmax(200px, 1fr))",gap:"12px"}),F=e({padding:"12px 16px",border:"1px solid #e5e7eb",borderRadius:"8px",backgroundColor:"white",textAlign:"left",cursor:"pointer",transition:"all 0.2s",_hover:{borderColor:"blue.500",backgroundColor:"blue.50"}}),le=e({fontSize:"14px",fontWeight:"500",color:"gray.900",marginBottom:"4px"}),he=e({fontSize:"12px",color:"gray.600"}),pe=e({padding:"12px 16px",border:"1px solid #e5e7eb",borderRadius:"8px",backgroundColor:"white",textAlign:"left",cursor:"pointer",transition:"all 0.2s",_hover:{borderColor:"blue.500",backgroundColor:"blue.50"}}),ge=e({fontSize:"14px",fontWeight:"500",color:"gray.900",marginBottom:"4px"}),$e=e({fontSize:"12px",color:"gray.600"}),Ce=e({padding:"12px 16px",border:"1px solid #e5e7eb",borderRadius:"8px",backgroundColor:"white",textAlign:"left",cursor:"pointer",transition:"all 0.2s",_hover:{borderColor:"blue.500",backgroundColor:"blue.50"}}),_e=e({fontSize:"14px",fontWeight:"500",color:"gray.900",marginBottom:"4px"}),Te=e({fontSize:"12px",color:"gray.600"}),Be=e({padding:"12px 16px",border:"1px solid #e5e7eb",borderRadius:"8px",backgroundColor:"white",textAlign:"left",cursor:"pointer",transition:"all 0.2s",_hover:{borderColor:"blue.500",backgroundColor:"blue.50"}}),Pe=e({fontSize:"14px",fontWeight:"500",color:"gray.900",marginBottom:"4px"}),De=e({fontSize:"12px",color:"gray.600"});return Y!==r.e&&t(g,r.e=Y),Z!==r.t&&t(b,r.t=Z),te!==r.a&&t(s,r.a=te),re!==r.o&&t(v,r.o=re),ce!==r.i&&t(m,r.i=ce),be!==r.n&&t(u,r.n=be),ve!==r.s&&t(k,r.s=ve),Se!==r.h&&t(W,r.h=Se),we!==r.r&&t(E,r.r=we),We!==r.d&&t(x,r.d=We),ke!==r.l&&t(S,r.l=ke),ye!==r.u&&t(A,r.u=ye),He!==r.c&&t(C,r.c=He),l!==r.w&&t(I,r.w=l),ee!==r.m&&t(V,r.m=ee),J!==r.f&&t(M,r.f=J),oe!==r.y&&t(j,r.y=oe),Q!==r.g&&t(h,r.g=Q),ie!==r.p&&t(z,r.p=ie),L!==r.b&&t(f,r.b=L),ne!==r.T&&t(w,r.T=ne),ae!==r.A&&t(c,r.A=ae),X!==r.O&&t(_,r.O=X),F!==r.I&&t($,r.I=F),le!==r.S&&t(U,r.S=le),he!==r.W&&t(p,r.W=he),pe!==r.C&&t(a,r.C=pe),ge!==r.B&&t(y,r.B=ge),$e!==r.v&&t(P,r.v=$e),Ce!==r.k&&t(D,r.k=Ce),_e!==r.x&&t(q,r.x=_e),Te!==r.j&&t(O,r.j=Te),Be!==r.q&&t(K,r.q=Be),Pe!==r.z&&t(G,r.z=Pe),De!==r.P&&t(N,r.P=De),r},{e:void 0,t:void 0,a:void 0,o:void 0,i:void 0,n:void 0,s:void 0,h:void 0,r:void 0,d:void 0,l:void 0,u:void 0,c:void 0,w:void 0,m:void 0,f:void 0,y:void 0,g:void 0,p:void 0,b:void 0,T:void 0,A:void 0,O:void 0,I:void 0,S:void 0,W:void 0,C:void 0,B:void 0,v:void 0,k:void 0,x:void 0,j:void 0,q:void 0,z:void 0,P:void 0}),g})()}var Yi=R("<button>"),Vi=R("<div>");const Wt=i=>{const[n,g]=Bt(i,["variant","size","loading","icon","fullWidth","children","class","disabled"]),b=e({display:"inline-flex",alignItems:"center",justifyContent:"center",gap:"8px",borderRadius:"8px",fontWeight:"500",transition:"all 0.2s",cursor:"pointer",border:"none",outline:"none",textDecoration:"none",userSelect:"none",_focus:{boxShadow:"0 0 0 3px rgba(59, 130, 246, 0.1)"},_disabled:{opacity:.6,cursor:"not-allowed"}}),s={primary:e({backgroundColor:"blue.600",color:"white",_hover:{backgroundColor:"blue.700"},_active:{backgroundColor:"blue.800"}}),secondary:e({backgroundColor:"gray.100",color:"gray.900",_hover:{backgroundColor:"gray.200"},_active:{backgroundColor:"gray.300"}}),success:e({backgroundColor:"green.600",color:"white",_hover:{backgroundColor:"green.700"},_active:{backgroundColor:"green.800"}}),warning:e({backgroundColor:"yellow.500",color:"white",_hover:{backgroundColor:"yellow.600"},_active:{backgroundColor:"yellow.700"}}),danger:e({backgroundColor:"red.600",color:"white",_hover:{backgroundColor:"red.700"},_active:{backgroundColor:"red.800"}}),ghost:e({backgroundColor:"transparent",color:"gray.700",border:"1px solid",borderColor:"gray.300",_hover:{backgroundColor:"gray.50",borderColor:"gray.400"},_active:{backgroundColor:"gray.100"}})},v={sm:e({padding:"6px 12px",fontSize:"14px",minHeight:"32px"}),md:e({padding:"8px 16px",fontSize:"14px",minHeight:"40px"}),lg:e({padding:"12px 24px",fontSize:"16px",minHeight:"48px"})},m=e({width:"100%"}),u=e({width:"16px",height:"16px",border:"2px solid currentColor",borderTopColor:"transparent",borderRadius:"50%",animation:"spin 1s linear infinite"}),k=n.variant||"primary",W=n.size||"md";return(()=>{var E=Yi();return Pt(E,Dt({get class(){return Ye(b,s[k],v[W],n.fullWidth&&m,n.class)},get disabled(){return n.disabled||n.loading}},g),!1,!0),d(E,(()=>{var x=ue(()=>!!n.loading);return()=>x()&&(()=>{var S=Vi();return t(S,u),S})()})(),null),d(E,(()=>{var x=ue(()=>!!(!n.loading&&n.icon));return()=>x()&&n.icon})(),null),d(E,()=>n.children,null),E})()};var Ui=R("<div><div>"),Gi=R("<div><div><div>"),Qi=R("<h3>"),Zi=R("<p>");const Ve=i=>{const[n,g]=Bt(i,["title","subtitle","headerAction","padding","shadow","border","hover","children","class"]),b=e({backgroundColor:"white",borderRadius:"12px",overflow:"hidden",transition:"all 0.2s"}),s={none:"",sm:e({boxShadow:"0 1px 2px rgba(0, 0, 0, 0.05)"}),md:e({boxShadow:"0 4px 6px rgba(0, 0, 0, 0.07)"}),lg:e({boxShadow:"0 10px 15px rgba(0, 0, 0, 0.1)"})},v=e({border:"1px solid",borderColor:"gray.200"}),m=e({_hover:{transform:"translateY(-2px)",boxShadow:"0 8px 25px rgba(0, 0, 0, 0.1)"}}),u={none:"",sm:e({padding:"16px"}),md:e({padding:"24px"}),lg:e({padding:"32px"})},k=e({padding:"24px 24px 0 24px",marginBottom:"16px"}),W=e({fontSize:"18px",fontWeight:"600",color:"gray.900",marginBottom:"4px"}),E=e({fontSize:"14px",color:"gray.600"}),x=e({display:"flex",justifyContent:"space-between",alignItems:"flex-start"}),S=n.shadow||"md",A=n.padding||"md";return(()=>{var C=Ui(),I=C.firstChild;return Pt(C,Dt({get class(){return Ye(b,s[S],n.border&&v,n.hover&&m,n.class)}},g),!1,!0),d(C,(()=>{var V=ue(()=>!!(n.title||n.subtitle||n.headerAction));return()=>V()&&(()=>{var M=Gi(),j=M.firstChild,h=j.firstChild;return t(M,k),d(h,(()=>{var z=ue(()=>!!n.title);return()=>z()&&(()=>{var f=Qi();return t(f,W),d(f,()=>n.title),f})()})(),null),d(h,(()=>{var z=ue(()=>!!n.subtitle);return()=>z()&&(()=>{var f=Zi();return t(f,E),d(f,()=>n.subtitle),f})()})(),null),d(j,(()=>{var z=ue(()=>!!n.headerAction);return()=>z()&&n.headerAction})(),null),T(()=>t(j,n.headerAction?x:"")),M})()})(),I),d(I,()=>n.children),T(()=>t(I,u[A])),C})()};var Ji=R('<button aria-label="Close modal">×'),en=R("<div><h2><span>"),tn=R("<div>"),on=R("<div><div><div>");const nn=i=>{To(()=>{if(i.isOpen){const x=S=>{S.key==="Escape"&&i.closable!==!1&&i.onClose()};document.addEventListener("keydown",x),document.body.style.overflow="hidden",It(()=>{document.removeEventListener("keydown",x),document.body.style.overflow=""})}});const n=e({position:"fixed",top:0,left:0,right:0,bottom:0,backgroundColor:"rgba(0, 0, 0, 0.5)",display:"flex",alignItems:"center",justifyContent:"center",zIndex:1e3,padding:"16px"}),g=e({backgroundColor:"white",borderRadius:"12px",boxShadow:"0 20px 25px rgba(0, 0, 0, 0.1)",maxHeight:"90vh",overflow:"hidden",display:"flex",flexDirection:"column",animation:"modalEnter 0.2s ease-out"}),b={sm:e({width:"400px",maxWidth:"90vw"}),md:e({width:"600px",maxWidth:"90vw"}),lg:e({width:"800px",maxWidth:"90vw"}),xl:e({width:"1000px",maxWidth:"90vw"})},s=e({padding:"24px 24px 0 24px",borderBottom:"1px solid",borderColor:"gray.200",paddingBottom:"16px",marginBottom:"24px"}),v=e({fontSize:"20px",fontWeight:"600",color:"gray.900",margin:0,display:"flex",justifyContent:"space-between",alignItems:"center"}),m=e({background:"none",border:"none",fontSize:"24px",cursor:"pointer",color:"gray.400",padding:"4px",borderRadius:"4px",_hover:{color:"gray.600",backgroundColor:"gray.100"}}),u=e({padding:"0 24px",flex:1,overflow:"auto"}),k=e({padding:"16px 24px 24px 24px",borderTop:"1px solid",borderColor:"gray.200",marginTop:"24px",display:"flex",justifyContent:"flex-end",gap:"12px"}),W=i.size||"md",E=x=>{x.target===x.currentTarget&&i.maskClosable!==!1&&i.onClose()};return B(Fe,{get when(){return i.isOpen},get children(){return B(Ko,{get children(){var x=on(),S=x.firstChild,A=S.firstChild;return x.$$click=E,t(x,n),S.$$click=C=>C.stopPropagation(),d(S,B(Fe,{get when(){return i.title||i.closable!==!1},get children(){var C=en(),I=C.firstChild,V=I.firstChild;return t(C,s),t(I,v),d(V,()=>i.title),d(I,B(Fe,{get when(){return i.closable!==!1},get children(){var M=Ji();return Yo(M,"click",i.onClose,!0),t(M,m),M}}),null),C}}),A),t(A,u),d(A,()=>i.children),d(S,B(Fe,{get when(){return i.footer},get children(){var C=tn();return t(C,k),d(C,()=>i.footer),C}}),null),T(()=>t(S,Ye(g,b[W],i.class))),x}})}})};Rt(["click"]);var rn=R("<label>"),At=R("<div>"),ln=R("<div><div><input>");const Tt=i=>{const[n,g]=Bt(i,["label","error","helperText","leftIcon","rightIcon","size","fullWidth","class"]),b=e({display:"flex",flexDirection:"column",gap:"6px"}),s=e({width:"100%"}),v=e({fontSize:"14px",fontWeight:"500",color:"gray.700"}),m=e({position:"relative",display:"flex",alignItems:"center"}),u=e({width:"100%",border:"1px solid",borderColor:"gray.300",borderRadius:"8px",transition:"all 0.2s",backgroundColor:"white",color:"gray.900",_focus:{outline:"none",borderColor:"blue.500",boxShadow:"0 0 0 3px rgba(59, 130, 246, 0.1)"},_disabled:{backgroundColor:"gray.100",color:"gray.500",cursor:"not-allowed"},_placeholder:{color:"gray.400"}}),k=e({borderColor:"red.500",_focus:{borderColor:"red.500",boxShadow:"0 0 0 3px rgba(239, 68, 68, 0.1)"}}),W={sm:e({padding:"8px 12px",fontSize:"14px",minHeight:"36px"}),md:e({padding:"10px 14px",fontSize:"14px",minHeight:"40px"}),lg:e({padding:"12px 16px",fontSize:"16px",minHeight:"48px"})},E=e({position:"absolute",top:"50%",transform:"translateY(-50%)",color:"gray.400",pointerEvents:"none",zIndex:1}),x=e({left:"12px"}),S=e({right:"12px"}),A=e({paddingLeft:"40px"}),C=e({paddingRight:"40px"}),I=e({fontSize:"12px",color:"gray.600"}),V=e({fontSize:"12px",color:"red.600"}),M=n.size||"md";return(()=>{var j=ln(),h=j.firstChild,z=h.firstChild;return d(j,B(Fe,{get when(){return n.label},get children(){var f=rn();return t(f,v),d(f,()=>n.label),f}}),h),t(h,m),d(h,B(Fe,{get when(){return n.leftIcon},get children(){var f=At();return d(f,()=>n.leftIcon),T(()=>t(f,Ye(E,x))),f}}),z),Pt(z,Dt({get class(){return Ye(u,W[M],n.error?k:void 0,n.leftIcon?A:void 0,n.rightIcon?C:void 0,n.class)}},g),!1,!1),d(h,B(Fe,{get when(){return n.rightIcon},get children(){var f=At();return d(f,()=>n.rightIcon),T(()=>t(f,Ye(E,S))),f}}),null),d(j,B(Fe,{get when(){return n.error||n.helperText},get children(){var f=At();return d(f,()=>n.error||n.helperText),T(()=>t(f,n.error?V:I)),f}}),null),T(()=>t(j,Ye(b,n.fullWidth?s:void 0))),j})()};var dn=R("<div><h3>账户总值</h3><p>¥"),an=R("<div><h3>总盈亏</h3><p>¥</p><p>%"),sn=R("<div><h3>可用资金</h3><p>¥"),cn=R("<div><h3>已用保证金</h3><p>¥"),gn=R("<div><table><thead><tr><th>代码</th><th>数量</th><th>盈亏</th></tr></thead><tbody>"),xn=R("<div><table><thead><tr><th>代码</th><th>类型</th><th>状态</th></tr></thead><tbody>"),un=R("<div><div><div><label>交易类型</label><select aria-label=交易类型><option value=buy>买入</option><option value=sell>卖出</option></select></div><div><label>订单类型</label><select aria-label=订单类型><option value=market>市价单</option><option value=limit>限价单</option></select></div></div><div>"),vn=R("<div slot=footer>"),pn=R("<div><div><div><h1>💼 </h1><p>管理您的交易订单和持仓</p></div></div><div></div><div>"),bn=R("<span>➕"),hn=R("<tr><td></td><td></td><td>¥<br><span>%"),fn=R("<tr><td><br><span>股 @ ¥</span></td><td><span></span></td><td><span>");const mn=()=>({t:i=>i});function Sn(){const{t:i}=mn(),[n,g]=xe([{id:"1",symbol:"AAPL",type:"buy",quantity:100,price:150.25,status:"filled",timestamp:new Date("2024-01-15T10:30:00")},{id:"2",symbol:"TSLA",type:"sell",quantity:50,price:245.8,status:"pending",timestamp:new Date("2024-01-15T11:15:00")},{id:"3",symbol:"MSFT",type:"buy",quantity:75,price:310.45,status:"filled",timestamp:new Date("2024-01-15T09:45:00")}]),[b,s]=xe([{symbol:"AAPL",quantity:100,avgPrice:150.25,currentPrice:152.3,unrealizedPnL:205,unrealizedPnLPercent:1.36},{symbol:"MSFT",quantity:75,avgPrice:310.45,currentPrice:308.9,unrealizedPnL:-116.25,unrealizedPnLPercent:-.5}]),[v,m]=xe(!1),[u,k]=xe({symbol:"",type:"buy",quantity:"",price:"",orderType:"limit"}),[W]=xe({totalValue:45678.9,totalPnL:88.75,totalPnLPercent:.19,buyingPower:12345.67,marginUsed:5432.1});St(()=>{console.log("Trading page mounted");const S=setInterval(()=>{s(A=>A.map(C=>{const I=(Math.random()-.5)*2,V=C.currentPrice+I,M=(V-C.avgPrice)*C.quantity,j=M/(C.avgPrice*C.quantity)*100;return{...C,currentPrice:V,unrealizedPnL:M,unrealizedPnLPercent:j}}))},3e3);return()=>clearInterval(S)});const E=()=>{const S=u();if(!S.symbol||!S.quantity||!S.price&&S.orderType==="limit"){alert("请填写完整的订单信息");return}const A={id:Date.now().toString(),symbol:S.symbol.toUpperCase(),type:S.type,quantity:parseInt(S.quantity),price:S.orderType==="market"?0:parseFloat(S.price),status:"pending",timestamp:new Date};g(C=>[A,...C]),m(!1),k({symbol:"",type:"buy",quantity:"",price:"",orderType:"limit"})},x=W();return(()=>{var S=pn(),A=S.firstChild,C=A.firstChild,I=C.firstChild;I.firstChild;var V=I.nextSibling,M=A.nextSibling,j=M.nextSibling;return d(I,()=>i("nav.trading"),null),d(A,B(Wt,{variant:"primary",size:"lg",get icon(){return bn()},onClick:()=>m(!0),children:"新建订单"}),null),d(M,B(Ve,{padding:"md",shadow:"md",get children(){var h=dn(),z=h.firstChild,f=z.nextSibling;return f.firstChild,d(f,()=>x.totalValue.toLocaleString(),null),T(w=>{var c=e({textAlign:"center"}),_=e({fontSize:"14px",fontWeight:"500",color:"gray.600",marginBottom:"8px"}),$=e({fontSize:"24px",fontWeight:"bold",color:"gray.900"});return c!==w.e&&t(h,w.e=c),_!==w.t&&t(z,w.t=_),$!==w.a&&t(f,w.a=$),w},{e:void 0,t:void 0,a:void 0}),h}}),null),d(M,B(Ve,{padding:"md",shadow:"md",get children(){var h=an(),z=h.firstChild,f=z.nextSibling,w=f.firstChild,c=f.nextSibling,_=c.firstChild;return d(f,()=>x.totalPnL>=0?"+":"",w),d(f,()=>Math.abs(x.totalPnL).toFixed(2),null),d(c,()=>x.totalPnL>=0?"+":"",_),d(c,()=>x.totalPnLPercent.toFixed(2),_),T($=>{var U=e({textAlign:"center"}),p=e({fontSize:"14px",fontWeight:"500",color:"gray.600",marginBottom:"8px"}),a=e({fontSize:"24px",fontWeight:"bold",color:x.totalPnL>=0?"green.600":"red.600"}),y=e({fontSize:"12px",color:x.totalPnL>=0?"green.600":"red.600"});return U!==$.e&&t(h,$.e=U),p!==$.t&&t(z,$.t=p),a!==$.a&&t(f,$.a=a),y!==$.o&&t(c,$.o=y),$},{e:void 0,t:void 0,a:void 0,o:void 0}),h}}),null),d(M,B(Ve,{padding:"md",shadow:"md",get children(){var h=sn(),z=h.firstChild,f=z.nextSibling;return f.firstChild,d(f,()=>x.buyingPower.toLocaleString(),null),T(w=>{var c=e({textAlign:"center"}),_=e({fontSize:"14px",fontWeight:"500",color:"gray.600",marginBottom:"8px"}),$=e({fontSize:"24px",fontWeight:"bold",color:"gray.900"});return c!==w.e&&t(h,w.e=c),_!==w.t&&t(z,w.t=_),$!==w.a&&t(f,w.a=$),w},{e:void 0,t:void 0,a:void 0}),h}}),null),d(M,B(Ve,{padding:"md",shadow:"md",get children(){var h=cn(),z=h.firstChild,f=z.nextSibling;return f.firstChild,d(f,()=>x.marginUsed.toLocaleString(),null),T(w=>{var c=e({textAlign:"center"}),_=e({fontSize:"14px",fontWeight:"500",color:"gray.600",marginBottom:"8px"}),$=e({fontSize:"24px",fontWeight:"bold",color:"orange.600"});return c!==w.e&&t(h,w.e=c),_!==w.t&&t(z,w.t=_),$!==w.a&&t(f,w.a=$),w},{e:void 0,t:void 0,a:void 0}),h}}),null),d(j,B(Ve,{title:"当前持仓",padding:"none",shadow:"md",get children(){var h=gn(),z=h.firstChild,f=z.firstChild,w=f.firstChild,c=w.firstChild,_=c.nextSibling,$=_.nextSibling,U=f.nextSibling;return d(U,B(Ue,{get each(){return b()},children:p=>(()=>{var a=hn(),y=a.firstChild,P=y.nextSibling,D=P.nextSibling,q=D.firstChild,O=q.nextSibling,K=O.nextSibling,G=K.firstChild;return d(y,()=>p.symbol),d(P,()=>p.quantity),d(D,()=>p.unrealizedPnL>=0?"+":"",q),d(D,()=>Math.abs(p.unrealizedPnL).toFixed(2),O),d(K,()=>p.unrealizedPnL>=0?"+":"",G),d(K,()=>p.unrealizedPnLPercent.toFixed(2),G),T(N=>{var r=e({borderBottom:"1px solid",borderColor:"gray.200"}),Y=e({padding:"12px 16px",fontSize:"14px",fontWeight:"500",color:"gray.900"}),Z=e({padding:"12px 16px",textAlign:"right",fontSize:"14px",color:"gray.700"}),te=e({padding:"12px 16px",textAlign:"right",fontSize:"14px",fontWeight:"500",color:p.unrealizedPnL>=0?"green.600":"red.600"}),re=e({fontSize:"12px"});return r!==N.e&&t(a,N.e=r),Y!==N.t&&t(y,N.t=Y),Z!==N.a&&t(P,N.a=Z),te!==N.o&&t(D,N.o=te),re!==N.i&&t(K,N.i=re),N},{e:void 0,t:void 0,a:void 0,o:void 0,i:void 0}),a})()})),T(p=>{var a=e({overflowX:"auto"}),y=e({width:"100%",borderCollapse:"collapse"}),P=e({backgroundColor:"gray.50"}),D=e({padding:"12px 16px",textAlign:"left",fontSize:"12px",fontWeight:"600",color:"gray.600"}),q=e({padding:"12px 16px",textAlign:"right",fontSize:"12px",fontWeight:"600",color:"gray.600"}),O=e({padding:"12px 16px",textAlign:"right",fontSize:"12px",fontWeight:"600",color:"gray.600"});return a!==p.e&&t(h,p.e=a),y!==p.t&&t(z,p.t=y),P!==p.a&&t(w,p.a=P),D!==p.o&&t(c,p.o=D),q!==p.i&&t(_,p.i=q),O!==p.n&&t($,p.n=O),p},{e:void 0,t:void 0,a:void 0,o:void 0,i:void 0,n:void 0}),h}}),null),d(j,B(Ve,{title:"订单历史",padding:"none",shadow:"md",get children(){var h=xn(),z=h.firstChild,f=z.firstChild,w=f.firstChild,c=w.firstChild,_=c.nextSibling,$=_.nextSibling,U=f.nextSibling;return d(U,B(Ue,{get each(){return n()},children:p=>(()=>{var a=fn(),y=a.firstChild,P=y.firstChild,D=P.nextSibling,q=D.firstChild,O=y.nextSibling,K=O.firstChild,G=O.nextSibling,N=G.firstChild;return d(y,()=>p.symbol,P),d(D,()=>p.quantity,q),d(D,()=>p.price,null),d(K,()=>p.type==="buy"?"买入":"卖出"),d(N,(()=>{var r=ue(()=>p.status==="filled");return()=>r()?"已成交":p.status==="pending"?"待成交":"已取消"})()),T(r=>{var Y=e({borderBottom:"1px solid",borderColor:"gray.200"}),Z=e({padding:"12px 16px",fontSize:"14px",fontWeight:"500",color:"gray.900"}),te=e({fontSize:"12px",color:"gray.600"}),re=e({padding:"12px 16px",textAlign:"center",fontSize:"14px"}),ce=e({padding:"4px 8px",borderRadius:"4px",fontSize:"12px",fontWeight:"500",backgroundColor:p.type==="buy"?"green.100":"red.100",color:p.type==="buy"?"green.800":"red.800"}),be=e({padding:"12px 16px",textAlign:"right",fontSize:"14px"}),ve=e({padding:"4px 8px",borderRadius:"4px",fontSize:"12px",fontWeight:"500",backgroundColor:p.status==="filled"?"green.100":p.status==="pending"?"yellow.100":"red.100",color:p.status==="filled"?"green.800":p.status==="pending"?"yellow.800":"red.800"});return Y!==r.e&&t(a,r.e=Y),Z!==r.t&&t(y,r.t=Z),te!==r.a&&t(D,r.a=te),re!==r.o&&t(O,r.o=re),ce!==r.i&&t(K,r.i=ce),be!==r.n&&t(G,r.n=be),ve!==r.s&&t(N,r.s=ve),r},{e:void 0,t:void 0,a:void 0,o:void 0,i:void 0,n:void 0,s:void 0}),a})()})),T(p=>{var a=e({overflowX:"auto"}),y=e({width:"100%",borderCollapse:"collapse"}),P=e({backgroundColor:"gray.50"}),D=e({padding:"12px 16px",textAlign:"left",fontSize:"12px",fontWeight:"600",color:"gray.600"}),q=e({padding:"12px 16px",textAlign:"center",fontSize:"12px",fontWeight:"600",color:"gray.600"}),O=e({padding:"12px 16px",textAlign:"right",fontSize:"12px",fontWeight:"600",color:"gray.600"});return a!==p.e&&t(h,p.e=a),y!==p.t&&t(z,p.t=y),P!==p.a&&t(w,p.a=P),D!==p.o&&t(c,p.o=D),q!==p.i&&t(_,p.i=q),O!==p.n&&t($,p.n=O),p},{e:void 0,t:void 0,a:void 0,o:void 0,i:void 0,n:void 0}),h}}),null),d(S,B(nn,{get isOpen(){return v()},onClose:()=>m(!1),title:"新建订单",size:"md",get children(){return[(()=>{var h=un(),z=h.firstChild,f=z.firstChild,w=f.firstChild,c=w.nextSibling,_=f.nextSibling,$=_.firstChild,U=$.nextSibling,p=z.nextSibling;return d(h,B(Tt,{label:"股票代码",placeholder:"例如: AAPL",get value(){return u().symbol},onInput:a=>k(y=>({...y,symbol:a.currentTarget.value}))}),z),c.addEventListener("change",a=>k(y=>({...y,type:a.currentTarget.value}))),U.addEventListener("change",a=>k(y=>({...y,orderType:a.currentTarget.value}))),d(p,B(Tt,{label:"数量",type:"number",placeholder:"100",get value(){return u().quantity},onInput:a=>k(y=>({...y,quantity:a.currentTarget.value}))}),null),d(p,(()=>{var a=ue(()=>u().orderType==="limit");return()=>a()&&B(Tt,{label:"价格",type:"number",step:"0.01",placeholder:"150.25",get value(){return u().price},onInput:y=>k(P=>({...P,price:y.currentTarget.value}))})})(),null),T(a=>{var y=e({display:"flex",flexDirection:"column",gap:"16px"}),P=e({display:"grid",gridTemplateColumns:"1fr 1fr",gap:"16px"}),D=e({display:"block",fontSize:"14px",fontWeight:"500",color:"gray.700",marginBottom:"8px"}),q=e({width:"100%",padding:"10px 14px",border:"1px solid",borderColor:"gray.300",borderRadius:"8px",backgroundColor:"white",color:"gray.900"}),O=e({display:"block",fontSize:"14px",fontWeight:"500",color:"gray.700",marginBottom:"8px"}),K=e({width:"100%",padding:"10px 14px",border:"1px solid",borderColor:"gray.300",borderRadius:"8px",backgroundColor:"white",color:"gray.900"}),G=e({display:"grid",gridTemplateColumns:"1fr 1fr",gap:"16px"});return y!==a.e&&t(h,a.e=y),P!==a.t&&t(z,a.t=P),D!==a.a&&t(w,a.a=D),q!==a.o&&t(c,a.o=q),O!==a.i&&t($,a.i=O),K!==a.n&&t(U,a.n=K),G!==a.s&&t(p,a.s=G),a},{e:void 0,t:void 0,a:void 0,o:void 0,i:void 0,n:void 0,s:void 0}),T(()=>c.value=u().type),T(()=>U.value=u().orderType),h})(),(()=>{var h=vn();return d(h,B(Wt,{variant:"ghost",onClick:()=>m(!1),children:"取消"}),null),d(h,B(Wt,{variant:"primary",onClick:E,children:"提交订单"}),null),h})()]}}),null),T(h=>{var z=e({padding:"24px",maxWidth:"1400px",margin:"0 auto",backgroundColor:"gray.50",minHeight:"100vh"}),f=e({marginBottom:"32px",display:"flex",justifyContent:"space-between",alignItems:"center"}),w=e({fontSize:"32px",fontWeight:"bold",color:"gray.900",marginBottom:"8px"}),c=e({fontSize:"16px",color:"gray.600"}),_=e({display:"grid",gridTemplateColumns:"repeat(auto-fit, minmax(200px, 1fr))",gap:"24px",marginBottom:"32px"}),$=e({display:"grid",gridTemplateColumns:"1fr 1fr",gap:"24px","@media (max-width: 1024px)":{gridTemplateColumns:"1fr"}});return z!==h.e&&t(S,h.e=z),f!==h.t&&t(A,h.t=f),w!==h.a&&t(I,h.a=w),c!==h.o&&t(V,h.o=c),_!==h.i&&t(M,h.i=_),$!==h.n&&t(j,h.n=$),h},{e:void 0,t:void 0,a:void 0,o:void 0,i:void 0,n:void 0}),S})()}function yn(){return B(Vo,{get children(){return B(Ke,{path:"/",component:ki,get children(){return[B(Ke,{path:"/",component:Wo}),B(Ke,{path:"/dashboard",component:Wo}),B(Ke,{path:"/market",component:Hi}),B(Ke,{path:"/trading",component:Sn}),B(Ke,{path:"/strategy-editor",component:Ki}),B(Ke,{path:"/api-test",component:Di})]}})}})}const Ao=document.getElementById("root");Ao&&Uo(()=>B(yn,{}),Ao);console.log("🚀 量化交易前端平台启动成功"),console.log("📊 基于 SolidJS + Panda CSS"),console.log("⚡ 极致性能，专业体验");
