import { createSignal, onMount, onCleanup, createEffect } from 'solid-js';
import { css } from '../../styled-system/css';
import { useWebSocket } from '../utils/websocket-client';
import { Button, Input, Card } from '@/ui';


interface MarketData {
  symbol: string;
  name: string;
  price: number;
  change: number;
  changePercent: number;
  volume: number;
  high: number;
  low: number;
  open: number;
  marketCap?: number;
}

export default function Market() {
  // 初始化A股数据 - 更符合中国市场
  const initialData: MarketData[] = [
    {
      symbol: '000725',
      name: '京东方A',
      price: 3.45,
      change: -1.43,
      changePercent: -29.3,
      volume: 7340750,
      high: 3.52,
      low: 3.40,
      open: 3.48,
      marketCap: 120000000000
    },
    {
      symbol: '300519',
      name: '新光药业',
      price: 68.94,
      change: -1.05,
      changePercent: -1.5,
      volume: 410750,
      high: 70.10,
      low: 68.50,
      open: 69.90,
      marketCap: 28000000000
    },
    {
      symbol: '000831',
      name: '五矿稀土',
      price: 26.09,
      change: -1.37,
      changePercent: -5.0,
      volume: 7558720,
      high: 27.50,
      low: 25.80,
      open: 27.20,
      marketCap: 45000000000
    },
    {
      symbol: '000001',
      name: '平安银行',
      price: 12.45,
      change: 0.23,
      changePercent: 1.88,
      volume: 15680000,
      high: 12.58,
      low: 12.20,
      open: 12.30,
      marketCap: 240000000000
    },
    {
      symbol: '000002',
      name: '万科A',
      price: 8.76,
      change: -0.15,
      changePercent: -1.68,
      volume: 8950000,
      high: 8.95,
      low: 8.65,
      open: 8.85,
      marketCap: 98000000000
    }
  ];

  const [marketData, setMarketData] = createSignal<MarketData[]>(initialData);

  // 使用WebSocket钩子
  const ws = useWebSocket();

  const [selectedSymbol, setSelectedSymbol] = createSignal('AAPL');
  const [searchQuery, setSearchQuery] = createSignal('');

  // WebSocket实时数据更新
  createEffect(() => {
    const wsData = ws.marketData();
    if (wsData.size > 0) {
      setMarketData(prev =>
        prev.map(item => {
          const update = wsData.get(item.symbol);
          if (update) {
            return {
              ...item,
              price: update.price,
              change: update.change,
              changePercent: update.changePercent,
              volume: update.volume
            };
          }
          return item;
        })
      );
    }
  });

  onMount(() => {
    console.log('Market page mounted');
    // 订阅所有股票的市场数据
    const symbols = initialData.map(item => item.symbol);
    ws.subscribeToMarketData(symbols);

    // 如果WebSocket未连接，则使用模拟数据
    let mockDataInterval: NodeJS.Timeout;

    const startMockData = () => {
      mockDataInterval = setInterval(() => {
        if (ws.connectionStatus() !== 'connected') {
          setMarketData(prev => prev.map(item => ({
            ...item,
            price: Math.max(0.01, item.price + (Math.random() - 0.5) * 2),
            change: item.change + (Math.random() - 0.5) * 0.5,
            changePercent: item.changePercent + (Math.random() - 0.5) * 0.2,
            volume: Math.max(0, item.volume + Math.floor((Math.random() - 0.5) * 100000))
          })));
        }
      }, 3000);
    };

    // 延迟启动模拟数据，给WebSocket连接时间
    setTimeout(startMockData, 2000);

    onCleanup(() => {
      if (mockDataInterval) {
        clearInterval(mockDataInterval);
      }
      // 取消订阅
      ws.unsubscribeFromMarketData(symbols);
    });
  });

  const filteredData = () => {
    const query = searchQuery().toLowerCase();
    return marketData().filter(item =>
      item.symbol.toLowerCase().includes(query) ||
      item.name.toLowerCase().includes(query)
    );
  };

  const formatNumber = (num: number, decimals = 2) => {
    return new Intl.NumberFormat('zh-CN', {
      minimumFractionDigits: decimals,
      maximumFractionDigits: decimals
    }).format(num);
  };

  const formatVolume = (volume: number) => {
    if (volume >= 1000000) {
      return `${(volume / 1000000).toFixed(1)}M`;
    } else if (volume >= 1000) {
      return `${(volume / 1000).toFixed(1)}K`;
    }
    return volume.toString();
  };


  return (
    <div class={css({
      padding: '16px',
      backgroundColor: '#f0f2f5',
      minHeight: '100%'
    })}>
      {/* 页面标题和操作栏 */}
      <div class={css({
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'space-between',
        marginBottom: '16px'
      })}>
        <div>
          <h1 class={css({
            fontSize: '20px',
            fontWeight: '600',
            color: '#262626',
            margin: 0,
            marginBottom: '4px'
          })}>
            行情分析
          </h1>
          <div class={css({
            display: 'flex',
            alignItems: 'center',
            gap: '12px',
            fontSize: '12px',
            color: '#8c8c8c'
          })}>
            <span class={css({
              padding: '2px 6px',
              backgroundColor: '#f6ffed',
              color: '#52c41a',
              borderRadius: '2px',
              fontSize: '11px'
            })}>
              沪深A股
            </span>
            <span>数据更新: 15:30</span>
            <div class={css({
              display: 'flex',
              alignItems: 'center',
              gap: '4px'
            })}>
              <div class={css({
                width: '6px',
                height: '6px',
                borderRadius: '50%',
                backgroundColor: ws.connectionStatus() === 'connected' ? '#52c41a' : '#faad14'
              })} />
              <span>
                {ws.connectionStatus() === 'connected' ? '实时连接' : '模拟数据'}
              </span>
            </div>
          </div>
        </div>

        <div class={css({ display: 'flex', alignItems: 'center', gap: '8px' })}>
          {ws.connectionStatus() !== 'connected' && (
            <Button variant="primary" onClick={() => ws.reconnect()}>重新连接</Button>
          )}
          <Button>导出数据</Button>
          <Button variant="success">自选股</Button>
        </div>
      </div>

      {/* 市场概览 */}
      <div class={css({
        marginBottom: '16px',
        backgroundColor: 'white',
        borderRadius: '8px',
        border: '1px solid #e8e8e8',
        padding: '16px'
      })}>
        <div class={css({
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          marginBottom: '12px'
        })}>
          <h2 class={css({
            fontSize: '16px',
            fontWeight: '600',
            color: '#262626',
            margin: 0
          })}>
            市场概览
          </h2>
          <div class={css({ display: 'flex', alignItems: 'center', gap: '8px' })}>
            <Button size="sm" variant="primary">日</Button>
            <Button size="sm">周</Button>
            <Button size="sm">月</Button>
          </div>
        </div>

        <div class={css({
          display: 'grid',
          gridTemplateColumns: 'repeat(auto-fit, minmax(150px, 1fr))',
          gap: '16px'
        })}>
          <div class={css({
            padding: '12px',
            backgroundColor: '#fafafa',
            borderRadius: '4px',
            textAlign: 'center'
          })}>
            <div class={css({
              fontSize: '12px',
              color: '#8c8c8c',
              marginBottom: '4px'
            })}>
              上证指数
            </div>
            <div class={css({
              fontSize: '16px',
              fontWeight: '600',
              color: '#f5222d',
              marginBottom: '2px'
            })}>
              3,247.89
            </div>
            <div class={css({
              fontSize: '11px',
              color: '#f5222d'
            })}>
              -12.34 (-0.38%)
            </div>
          </div>
          <div class={css({
            padding: '12px',
            backgroundColor: '#fafafa',
            borderRadius: '4px',
            textAlign: 'center'
          })}>
            <div class={css({
              fontSize: '12px',
              color: '#8c8c8c',
              marginBottom: '4px'
            })}>
              深证成指
            </div>
            <div class={css({
              fontSize: '16px',
              fontWeight: '600',
              color: '#52c41a',
              marginBottom: '2px'
            })}>
              10,567.23
            </div>
            <div class={css({
              fontSize: '11px',
              color: '#52c41a'
            })}>
              +45.67 (+0.43%)
            </div>
          </div>
          <div class={css({
            padding: '12px',
            backgroundColor: '#fafafa',
            borderRadius: '4px',
            textAlign: 'center'
          })}>
            <div class={css({
              fontSize: '12px',
              color: '#8c8c8c',
              marginBottom: '4px'
            })}>
              创业板指
            </div>
            <div class={css({
              fontSize: '16px',
              fontWeight: '600',
              color: '#f5222d',
              marginBottom: '2px'
            })}>
              2,234.56
            </div>
            <div class={css({
              fontSize: '11px',
              color: '#f5222d'
            })}>
              -8.90 (-0.40%)
            </div>
          </div>
          <div class={css({
            padding: '12px',
            backgroundColor: '#fafafa',
            borderRadius: '4px',
            textAlign: 'center'
          })}>
            <div class={css({
              fontSize: '12px',
              color: '#8c8c8c',
              marginBottom: '4px'
            })}>
              科创50
            </div>
            <div class={css({
              fontSize: '16px',
              fontWeight: '600',
              color: '#52c41a',
              marginBottom: '2px'
            })}>
              1,123.45
            </div>
            <div class={css({
              fontSize: '11px',
              color: '#52c41a'
            })}>
              +15.23 (+1.37%)
            </div>
          </div>
        </div>
      </div>

      {/* 搜索和筛选 */}
      <div class={css({
        marginBottom: '16px',
        backgroundColor: 'white',
        borderRadius: '8px',
        border: '1px solid #e8e8e8',
        padding: '12px 16px'
      })}>
        <div class={css({
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          marginBottom: '12px'
        })}>
          <h3 class={css({
            fontSize: '14px',
            fontWeight: '600',
            color: '#262626',
            margin: 0
          })}>
            市场筛选
          </h3>
          <span class={css({
            fontSize: '12px',
            color: '#8c8c8c'
          })}>
            共 {filteredData().length} 只股票
          </span>
        </div>

        <div class={css({
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between'
        })}>
          <div class={css({
            display: 'flex',
            alignItems: 'center',
            gap: '8px'
          })}>
            <Input placeholder="搜索股票代码或名称" value={searchQuery()} onInput={(e) => setSearchQuery((e.currentTarget as HTMLInputElement).value)} />
            <Button variant="primary">搜索</Button>
          </div>

          <div class={css({
            display: 'flex',
            alignItems: 'center',
            gap: '6px'
          })}>
            <span class={css({
              fontSize: '12px',
              color: '#8c8c8c',
              marginRight: '4px'
            })}>
              板块:
            </span>
            <Button size="sm" variant="primary">全部</Button>
            <Button size="sm">沪A</Button>
            <Button size="sm">深A</Button>
            <Button size="sm">创业板</Button>
          </div>
        </div>
      </div>

      {/* 股票列表 */}
      <div class={css({
        backgroundColor: 'white',
        borderRadius: '8px',
        border: '1px solid #e8e8e8',
        overflow: 'hidden'
      })}>
        <div class={css({
          padding: '16px 20px',
          borderBottom: '1px solid #e8e8e8',
          backgroundColor: '#fafafa'
        })}>
          <h2 class={css({
            fontSize: '16px',
            fontWeight: '600',
            color: '#262626',
            margin: '0'
          })}>
            股票列表
          </h2>
        </div>

        <div class={css({
          overflowX: 'auto'
        })}>
          <table class={css({
            width: '100%',
            borderCollapse: 'collapse'
          })}>
            <thead>
              <tr class={css({
                backgroundColor: '#fafafa'
              })}>
                <th class={css({
                  padding: '12px 16px',
                  textAlign: 'left',
                  fontSize: '12px',
                  fontWeight: '600',
                  color: '#8c8c8c'
                })}>
                  代码
                </th>
                <th class={css({
                  padding: '12px 16px',
                  textAlign: 'left',
                  fontSize: '12px',
                  fontWeight: '600',
                  color: '#8c8c8c'
                })}>
                  名称
                </th>
                <th class={css({
                  padding: '12px 16px',
                  textAlign: 'right',
                  fontSize: '12px',
                  fontWeight: '600',
                  color: '#8c8c8c'
                })}>
                  现价
                </th>
                <th class={css({
                  padding: '12px 16px',
                  textAlign: 'right',
                  fontSize: '12px',
                  fontWeight: '600',
                  color: '#8c8c8c'
                })}>
                  涨跌额
                </th>
                <th class={css({
                  padding: '12px 16px',
                  textAlign: 'right',
                  fontSize: '12px',
                  fontWeight: '600',
                  color: '#8c8c8c'
                })}>
                  涨跌幅
                </th>
                <th class={css({
                  padding: '12px 16px',
                  textAlign: 'right',
                  fontSize: '12px',
                  fontWeight: '600',
                  color: '#8c8c8c'
                })}>
                  成交量
                </th>
                <th class={css({
                  padding: '12px 16px',
                  textAlign: 'right',
                  fontSize: '12px',
                  fontWeight: '600',
                  color: '#8c8c8c'
                })}>
                  最高价
                </th>
                <th class={css({
                  padding: '12px 16px',
                  textAlign: 'right',
                  fontSize: '12px',
                  fontWeight: '600',
                  color: '#8c8c8c'
                })}>
                  最低价
                </th>
                <th class={css({
                  padding: '12px 16px',
                  textAlign: 'right',
                  fontSize: '12px',
                  fontWeight: '600',
                  color: '#8c8c8c'
                })}>
                  操作
                </th>
              </tr>
            </thead>
            <tbody>
              {filteredData().map((item) => (
                <tr
                  class={css({
                    borderBottom: '1px solid #f0f0f0',
                    cursor: 'pointer',
                    transition: 'background-color 0.2s',
                    backgroundColor: selectedSymbol() === item.symbol ? '#e6f7ff' : 'transparent',
                    _hover: {
                      backgroundColor: '#fafafa'
                    }
                  })}
                  onClick={() => setSelectedSymbol(item.symbol)}
                >
                  <td class={css({
                    padding: '12px 16px',
                    fontSize: '14px',
                    fontWeight: '600',
                    color: '#262626'
                  })}>
                    {item.symbol}
                  </td>
                  <td class={css({
                    padding: '12px 16px',
                    fontSize: '14px',
                    color: '#262626'
                  })}>
                    {item.name}
                  </td>
                  <td class={css({
                    padding: '12px 16px',
                    textAlign: 'right',
                    fontSize: '14px',
                    fontWeight: '600',
                    color: '#262626'
                  })}>
                    {formatNumber(item.price)}
                  </td>
                  <td class={css({
                    padding: '12px 16px',
                    textAlign: 'right',
                    fontSize: '14px',
                    fontWeight: '500',
                    color: item.change >= 0 ? '#52c41a' : '#f5222d'
                  })}>
                    {item.change >= 0 ? '+' : ''}{formatNumber(item.change)}
                  </td>
                  <td class={css({
                    padding: '12px 16px',
                    textAlign: 'right',
                    fontSize: '14px',
                    fontWeight: '500',
                    color: item.changePercent >= 0 ? '#52c41a' : '#f5222d'
                  })}>
                    {item.changePercent >= 0 ? '+' : ''}{formatNumber(item.changePercent)}%
                  </td>
                  <td class={css({
                    padding: '12px 16px',
                    textAlign: 'right',
                    fontSize: '14px',
                    color: '#8c8c8c'
                  })}>
                    {formatVolume(item.volume)}
                  </td>
                  <td class={css({
                    padding: '12px 16px',
                    textAlign: 'right',
                    fontSize: '14px',
                    color: '#8c8c8c'
                  })}>
                    {formatNumber(item.high)}
                  </td>
                  <td class={css({
                    padding: '12px 16px',
                    textAlign: 'right',
                    fontSize: '14px',
                    color: '#8c8c8c'
                  })}>
                    {formatNumber(item.low)}
                  </td>
                  <td class={css({
                    padding: '12px 16px',
                    textAlign: 'right'
                  })}>
                    <div class={css({
                      display: 'flex',
                      gap: '4px',
                      justifyContent: 'flex-end'
                    })}>
                      <Button size="sm" variant="danger">卖</Button>
                      <Button size="sm" variant="success">买</Button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        {/* 分页 */}
        <div class={css({
          padding: '16px 20px',
          borderTop: '1px solid #f0f0f0',
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center'
        })}>
          <div class={css({
            fontSize: '14px',
            color: '#8c8c8c'
          })}>
            共 {filteredData().length} 条数据
          </div>
          <div class={css({
            display: 'flex',
            gap: '8px',
            alignItems: 'center'
          })}>
            <Button size="sm">上一页</Button>
            <span class={css({ padding: '4px 8px', backgroundColor: 'primary.500', color: 'white', borderRadius: '4px', fontSize: '12px' })}>1</span>
            <Button size="sm">下一页</Button>
          </div>
        </div>
      </div>

      {/* 选中股票的详细信息 */}
      {selectedSymbol() && (
        <div class={css({
          marginTop: '24px',
          backgroundColor: 'white',
          borderRadius: '8px',
          border: '1px solid #e8e8e8',
          overflow: 'hidden'
        })}>
          <div class={css({
            padding: '20px',
            borderBottom: '1px solid #e8e8e8'
          })}>
            <h3 class={css({
              fontSize: '16px',
              fontWeight: '600',
              color: '#262626',
              margin: '0'
            })}>
              {selectedSymbol()} 详细信息
            </h3>
          </div>
          <div class={css({
            padding: '20px',
            textAlign: 'center',
            color: '#8c8c8c'
          })}>
            <div class={css({ fontSize: '48px', marginBottom: '16px' })}>📊</div>
            <p>K线图表和技术指标</p>
            <p class={css({ fontSize: '12px' })}>这里将显示选中股票的详细分析图表</p>
          </div>
        </div>
      )}
    </div>
  );
}
