import { createSignal } from 'solid-js';

export default function Market() {
  const [currentTime, setCurrentTime] = createSignal(new Date().toLocaleString('zh-CN'));

  return (
    <div style={{
      padding: '20px',
      'min-height': '100vh',
      'background-color': '#f5f5f5',
    }}>
      <div style={{
        'background-color': 'white',
        padding: '20px',
        'border-radius': '8px',
        'text-align': 'center',
      }}>
        <h1 style={{ color: '#1890ff', 'font-size': '24px', margin: '0 0 10px 0' }}>
          📊 行情中心
        </h1>
        <p style={{ color: '#666', margin: '0 0 20px 0' }}>
          实时行情数据将在里程碑 2 中完整实现
        </p>
        <div style={{
          padding: '40px',
          'background-color': '#f9f9f9',
          'border-radius': '8px',
          'border': '2px dashed #d9d9d9',
        }}>
          <div style={{ 'font-size': '48px', 'margin-bottom': '16px' }}>🚧</div>
          <div style={{ 'font-size': '16px', color: '#666' }}>功能开发中...</div>
          <div style={{ 'font-size': '14px', color: '#999', 'margin-top': '8px' }}>
            将包含：K线图表、实时行情、技术指标、市场深度等
          </div>
        </div>
      </div>
    </div>
  );
}
