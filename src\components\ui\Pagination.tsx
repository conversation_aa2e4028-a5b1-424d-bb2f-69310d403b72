import { JSX, splitProps, For, createMemo } from 'solid-js'
import { css } from '../../../styled-system/css'
import clsx from 'clsx'

export interface PaginationProps extends JSX.HTMLAttributes<HTMLDivElement> {
  total: number
  pageSize?: number
  current?: number
  onChange?: (page: number) => void
}

export function Pagination(props: PaginationProps) {
  const [local, others] = splitProps(props, ['total', 'pageSize', 'current', 'onChange', 'class'])
  const pageSize = () => local.pageSize ?? 10
  const totalPages = createMemo(() => Math.max(1, Math.ceil(local.total / pageSize())))
  const current = () => Math.min(local.current ?? 1, totalPages())

  const btn = (active?: boolean) => css({
    padding: '4px 8px',
    backgroundColor: active ? 'primary.500' : 'white',
    color: active ? 'white' : 'text.regular',
    border: '1px solid',
    borderColor: active ? 'primary.500' : 'border.base',
    borderRadius: '4px',
    fontSize: '12px',
    cursor: 'pointer'
  })

  const handlePrev = () => local.onChange?.(Math.max(1, current() - 1))
  const handleNext = () => local.onChange?.(Math.min(totalPages(), current() + 1))
  const handleClick = (p: number) => local.onChange?.(p)

  const pages = createMemo(() => {
    const t = totalPages()
    if (t <= 5) return Array.from({ length: t }, (_, i) => i + 1)
    const c = current()
    const arr = new Set<number>([1, t, c])
    if (c - 1 > 1) arr.add(c - 1)
    if (c + 1 < t) arr.add(c + 1)
    return Array.from(arr).sort((a, b) => a - b)
  })

  return (
    <div class={clsx(css({ display: 'flex', gap: '8px', alignItems: 'center' }), local.class)} {...others}>
      <button class={btn(false)} onClick={handlePrev}>上一页</button>
      <For each={pages()}>
        {(p) => (
          <button class={btn(p === current())} onClick={() => handleClick(p)}>{p}</button>
        )}
      </For>
      <button class={btn(false)} onClick={handleNext}>下一页</button>
    </div>
  )
}

