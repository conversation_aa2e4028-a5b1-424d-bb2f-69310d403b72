{"license": "MIT", "name": "onnxruntime-node", "repository": {"url": "https://github.com/Microsoft/onnxruntime.git", "type": "git"}, "author": "fs-eire", "binary": {"module_path": "./bin", "host": "https://onnxruntimetestdata.blob.core.windows.net/onnxruntime-node-prebuild/", "napi_versions": [3]}, "version": "1.14.0", "dependencies": {"onnxruntime-common": "~1.14.0"}, "scripts": {"buildr": "tsc && node ./script/build --config=RelWithDebInfo", "prepare": "tsc --build script test .", "rebuild": "tsc && node ./script/build --rebuild", "rebuildd": "tsc && node ./script/build --rebuild --config=Debug", "buildd": "tsc && node ./script/build --config=Debug", "build": "tsc && node ./script/build", "test": "tsc --build ../scripts && node ../scripts/prepare-onnx-node-tests && mocha ./test/test-main", "prepack": "node ./script/prepack", "rebuildr": "tsc && node ./script/build --rebuild --config=RelWithDebInfo"}, "keywords": ["ONNX", "ONNXRuntime", "ONNX Runtime"], "devDependencies": {"@types/fs-extra": "^9.0.6", "@types/minimist": "^1.2.2", "@types/mocha": "^8.2.2", "@types/node": "^14.14.37", "cmake-js": "^6.2.1", "fs-extra": "^9.1.0", "jsonc": "^2.0.0", "minimist": "^1.2.7", "mocha": "^10.2.0", "node-addon-api": "^3.1.0", "onnx-proto": "^4.0.4", "typescript": "^4.9.4"}, "main": "dist/index.js", "os": ["win32", "darwin", "linux"], "types": "dist/index.d.ts", "description": "ONNXRuntime Node.js binding"}