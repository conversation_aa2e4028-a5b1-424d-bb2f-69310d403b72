import { JSX, createSignal, onMount, onCleanup, Show, For } from 'solid-js'
import { css } from '../../../styled-system/css'
import { Portal } from 'solid-js/web'

export interface MessageOptions {
  type?: 'success' | 'warning' | 'info' | 'error'
  message: string
  duration?: number
  showClose?: boolean
  onClose?: () => void
}

interface MessageInstance extends MessageOptions {
  id: string
  visible: boolean
}

const [messages, setMessages] = createSignal<MessageInstance[]>([])

let messageId = 0

export const Message = {
  success: (message: string, options?: Partial<MessageOptions>) =>
    showMessage({ ...options, message, type: 'success' }),
  warning: (message: string, options?: Partial<MessageOptions>) =>
    showMessage({ ...options, message, type: 'warning' }),
  info: (message: string, options?: Partial<MessageOptions>) =>
    showMessage({ ...options, message, type: 'info' }),
  error: (message: string, options?: Partial<MessageOptions>) =>
    showMessage({ ...options, message, type: 'error' }),
  show: showMessage,
  clear: () => setMessages([]),
}

function showMessage(options: MessageOptions) {
  const id = `message-${++messageId}`
  const instance: MessageInstance = {
    id,
    type: 'info',
    duration: 3000,
    showClose: false,
    visible: true,
    ...options,
  }

  setMessages((prev) => [...prev, instance])

  if (instance.duration && instance.duration > 0) {
    setTimeout(() => {
      closeMessage(id)
    }, instance.duration)
  }

  return id
}

function closeMessage(id: string) {
  setMessages((prev) => prev.filter((msg) => msg.id !== id))
}

function MessageItem(props: { message: MessageInstance; index: number }) {
  const getMessageStyles = () => {
    const base = {
      display: 'flex',
      alignItems: 'center',
      gap: '8px',
      padding: '12px 16px',
      borderRadius: '4px',
      border: '1px solid',
      backgroundColor: 'white',
      boxShadow: 'light',
      fontSize: '14px',
      fontWeight: '400',
      minWidth: '300px',
      maxWidth: '500px',
      marginBottom: '8px',
      transform: 'translateX(0)',
      opacity: 1,
      transition: 'all 0.3s ease',
      animation: 'slideInRight 0.3s ease',
    }

    const typeStyles = {
      success: {
        color: 'success.600',
        backgroundColor: 'success.50',
        borderColor: 'success.200',
      },
      warning: {
        color: 'warning.600',
        backgroundColor: 'warning.50',
        borderColor: 'warning.200',
      },
      info: {
        color: 'info.600',
        backgroundColor: 'info.50',
        borderColor: 'info.200',
      },
      error: {
        color: 'danger.600',
        backgroundColor: 'danger.50',
        borderColor: 'danger.200',
      },
    }

    return {
      ...base,
      ...typeStyles[props.message.type || 'info'],
    }
  }

  const getIconStyles = () => ({
    fontSize: '16px',
    flexShrink: 0,
  })

  const getCloseButtonStyles = () => ({
    marginLeft: 'auto',
    cursor: 'pointer',
    fontSize: '12px',
    padding: '2px',
    borderRadius: '2px',
    _hover: {
      backgroundColor: 'rgba(0, 0, 0, 0.1)',
    },
  })

  const getIcon = () => {
    switch (props.message.type) {
      case 'success':
        return '✓'
      case 'warning':
        return '⚠'
      case 'error':
        return '✕'
      default:
        return 'ℹ'
    }
  }

  const handleClose = () => {
    props.message.onClose?.()
    closeMessage(props.message.id)
  }

  return (
    <div
      class={css(getMessageStyles())}
      style={{
        top: `${20 + props.index * 60}px`,
      }}
    >
      <span class={css(getIconStyles())}>{getIcon()}</span>
      <span class={css({ flex: 1 })}>{props.message.message}</span>
      <Show when={props.message.showClose}>
        <span class={css(getCloseButtonStyles())} onClick={handleClose}>
          ✕
        </span>
      </Show>
    </div>
  )
}

export function MessageContainer() {
  const getContainerStyles = () => ({
    position: 'fixed',
    top: 0,
    right: '20px',
    zIndex: 9999,
    pointerEvents: 'none',
  })

  return (
    <Portal>
      <div class={css(getContainerStyles())}>
        <For each={messages()}>
          {(message, index) => (
            <div style={{ 'pointer-events': 'auto' }}>
              <MessageItem message={message} index={index()} />
            </div>
          )}
        </For>
      </div>
    </Portal>
  )
}

// 添加动画样式到全局
const style = document.createElement('style')
style.textContent = `
  @keyframes slideInRight {
    from {
      transform: translateX(100%);
      opacity: 0;
    }
    to {
      transform: translateX(0);
      opacity: 1;
    }
  }
  
  @keyframes spin {
    from {
      transform: rotate(0deg);
    }
    to {
      transform: rotate(360deg);
    }
  }
`
document.head.appendChild(style)
