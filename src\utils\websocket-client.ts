import type { Socket } from 'socket.io-client';
import { createSignal } from 'solid-js';

export interface MarketDataUpdate {
  symbol: string;
  price: number;
  change: number;
  changePercent: number;
  volume: number;
  timestamp: number;
}

class WebSocketClient {
  private socket: Socket | null = null;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  private reconnectDelay = 1000;
  
  // 创建响应式信号
  private connectionStatusSignal = createSignal<'connecting' | 'connected' | 'disconnected' | 'error'>('disconnected');
  private marketDataSignal = createSignal<Map<string, MarketDataUpdate>>(new Map());

  public connectionStatus = this.connectionStatusSignal[0];
  private setConnectionStatus = this.connectionStatusSignal[1];
  public marketData = this.marketDataSignal[0];
  private setMarketData = this.marketDataSignal[1];

  constructor() {
    this.connect();
  }

  private connect() {
    try {
      this.setConnectionStatus('connecting');
      
      // 尝试连接真实的WebSocket服务器
      const wsUrl = import.meta.env.VITE_WS_URL || 'ws://localhost:8000';
      console.log('尝试连接WebSocket服务器:', wsUrl);
      
      // 这里原本连接真实服务器，为了前端自足性，先禁用真实 socket 连接
      // 并在 Market 页面使用模拟数据。
      this.socket = null;

    } catch (error) {
      console.error('WebSocket连接失败:', error);
      this.setConnectionStatus('error');
      this.handleReconnect();
    }
  }

  private handleReconnect() {
    if (this.reconnectAttempts < this.maxReconnectAttempts) {
      this.reconnectAttempts++;
      setTimeout(() => {
        console.log(`尝试重连 (${this.reconnectAttempts}/${this.maxReconnectAttempts})`);
        this.connect();
      }, this.reconnectDelay * this.reconnectAttempts);
    } else {
      console.log('达到最大重连次数，停止重连');
      this.setConnectionStatus('error');
    }
  }

  private updateMarketData(data: MarketDataUpdate) {
    this.setMarketData(prev => {
      const newMap = new Map(prev);
      newMap.set(data.symbol, data);
      return newMap;
    });
  }

  // 订阅市场数据
  public subscribeToMarketData(symbols: string[]) {
    console.log('订阅市场数据:', symbols);
    if (this.socket && this.socket.connected) {
      this.socket.emit('subscribe', { symbols });
    }
  }

  // 取消订阅市场数据
  public unsubscribeFromMarketData(symbols: string[]) {
    console.log('取消订阅市场数据:', symbols);
    if (this.socket && this.socket.connected) {
      this.socket.emit('unsubscribe', { symbols });
    }
  }

  // 手动重连
  public reconnect() {
    console.log('手动重连WebSocket...');
    this.disconnect();
    this.reconnectAttempts = 0;
    this.connect();
  }

  // 断开连接
  public disconnect() {
    if (this.socket) {
      this.socket.disconnect();
      this.socket = null;
    }
    this.setConnectionStatus('disconnected');
  }
}

// 创建单例实例
export const wsClient = new WebSocketClient();

// 导出钩子函数供组件使用
export function useWebSocket() {
  return {
    connectionStatus: wsClient.connectionStatus,
    marketData: wsClient.marketData,
    
    // 方法
    subscribeToMarketData: wsClient.subscribeToMarketData.bind(wsClient),
    unsubscribeFromMarketData: wsClient.unsubscribeFromMarketData.bind(wsClient),
    disconnect: wsClient.disconnect.bind(wsClient),
    reconnect: wsClient.reconnect.bind(wsClient)
  };
}