import { JSX, For, Show, splitProps } from 'solid-js'
import { DropdownMenu } from '@kobalte/core/dropdown-menu'
import { css } from '../../../styled-system/css'
import { Button } from './Button'
import clsx from 'clsx'

export interface DropdownItem {
  key: string
  label: string
  icon?: JSX.Element
  disabled?: boolean
  divided?: boolean
  onClick?: () => void
}

export interface DropdownProps {
  items: DropdownItem[]
  trigger?: JSX.Element
  placement?: 'bottom-start' | 'bottom-end' | 'top-start' | 'top-end'
  disabled?: boolean
  onSelect?: (key: string) => void
  class?: string
  children?: JSX.Element
}

export function Dropdown(props: DropdownProps) {
  const [local, others] = splitProps(props, [
    'items',
    'trigger',
    'placement',
    'disabled',
    'onSelect',
    'class',
    'children',
  ])

  const getTriggerStyles = () => ({
    display: 'inline-flex',
    alignItems: 'center',
    gap: '4px',
  })

  const getContentStyles = () => ({
    backgroundColor: 'white',
    borderRadius: '4px',
    border: '1px solid',
    borderColor: 'border.base',
    boxShadow: 'light',
    padding: '4px 0',
    minWidth: '120px',
    maxWidth: '300px',
    zIndex: 1000,
    animation: 'fadeIn 0.2s ease',
  })

  const getItemStyles = (item: DropdownItem) => ({
    display: 'flex',
    alignItems: 'center',
    gap: '8px',
    padding: '8px 16px',
    fontSize: '14px',
    color: item.disabled ? 'text.placeholder' : 'text.regular',
    cursor: item.disabled ? 'not-allowed' : 'pointer',
    borderTop: item.divided ? '1px solid' : 'none',
    borderColor: 'border.lighter',
    marginTop: item.divided ? '4px' : '0',
    paddingTop: item.divided ? '12px' : '8px',
    transition: 'all 0.2s',
    _hover: !item.disabled
      ? {
          backgroundColor: 'primary.50',
          color: 'primary.600',
        }
      : {},
    _focus: {
      backgroundColor: 'primary.50',
      color: 'primary.600',
      outline: 'none',
    },
  })

  const handleItemClick = (item: DropdownItem) => {
    if (item.disabled) return
    item.onClick?.()
    local.onSelect?.(item.key)
  }

  return (
    <DropdownMenu placement={local.placement || 'bottom-start'} disabled={local.disabled}>
      <DropdownMenu.Trigger class={css(getTriggerStyles())}>
        {local.trigger || (
          <Button variant="default" size="small">
            操作 ▼
          </Button>
        )}
      </DropdownMenu.Trigger>

      <DropdownMenu.Portal>
        <DropdownMenu.Content class={clsx(css(getContentStyles()), local.class)}>
          <For each={local.items}>
            {(item) => (
              <DropdownMenu.Item
                class={css(getItemStyles(item))}
                disabled={item.disabled}
                onSelect={() => handleItemClick(item)}
              >
                <Show when={item.icon}>
                  <span class={css({ fontSize: '14px' })}>{item.icon}</span>
                </Show>
                <span>{item.label}</span>
              </DropdownMenu.Item>
            )}
          </For>
          {local.children}
        </DropdownMenu.Content>
      </DropdownMenu.Portal>
    </DropdownMenu>
  )
}

// 添加动画样式
const dropdownStyle = document.createElement('style')
dropdownStyle.textContent = `
  @keyframes fadeIn {
    from {
      opacity: 0;
      transform: translateY(-4px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
`
if (!document.head.querySelector('style[data-dropdown]')) {
  dropdownStyle.setAttribute('data-dropdown', 'true')
  document.head.appendChild(dropdownStyle)
}
