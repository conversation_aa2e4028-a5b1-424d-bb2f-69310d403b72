import { JSX, splitProps } from 'solid-js';
import { css, cva } from '../../styled-system/css';

export interface InputProps extends JSX.InputHTMLAttributes<HTMLInputElement> {
  status?: 'default' | 'success' | 'warning' | 'danger';
  size?: 'sm' | 'md' | 'lg';
  clearable?: boolean;
  prefix?: JSX.Element;
  suffix?: JSX.Element;
}

const root = cva({
  base: {
    display: 'inline-flex', alignItems: 'center',
    borderWidth: '1px', borderStyle: 'solid', borderColor: '#d9d9d9',
    borderRadius: '4px', backgroundColor: 'white',
    transition: 'all 0.15s ease',
    _focusWithin: { borderColor: 'primary.500', boxShadow: '0 0 0 1px token(colors.primary.500 / 20%)' },
  },
  variants: {
    size: {
      sm: { height: '28px', fontSize: '12px', px: '8px', gap: '6px' },
      md: { height: '32px', fontSize: '13px', px: '10px', gap: '6px' },
      lg: { height: '36px', fontSize: '14px', px: '12px', gap: '8px' },
    },
    status: {
      default: {},
      success: { borderColor: 'success.500' },
      warning: { borderColor: 'warning.500' },
      danger: { borderColor: 'danger.500' },
    },
  },
  defaultVariants: { size: 'md', status: 'default' }
});

const inputCss = cva({
  base: {
    flex: 1, border: 'none', outline: 'none', background: 'transparent',
    color: '#262626', height: '100%',
    '::placeholder': { color: '#bfbfbf' },
  }
});

export default function Input(allProps: InputProps) {
  const [props, rest] = splitProps(allProps, ['class', 'status', 'size', 'clearable', 'prefix', 'suffix']);
  return (
    <div class={css(root({ status: props.status, size: props.size }), props.class || '')}>
      {props.prefix}
      <input class={css(inputCss())} {...rest} />
      {props.clearable && (
        <button type="button"
          onClick={() => (rest.onInput as any)?.({ currentTarget: { value: '' } })}
          class={css({ color: '#8c8c8c', _hover: { color: '#595959' } })}
        >×</button>
      )}
      {props.suffix}
    </div>
  );
}

