import { createSignal, createMemo, For } from 'solid-js'
import { css } from '../../../styled-system/css'
import { Card, Button, Tag, Table } from '../ui'
import { ColumnDef } from '@tanstack/solid-table'

export interface Position {
  symbol: string
  name: string
  quantity: number
  availableQuantity: number
  avgCost: number
  currentPrice: number
  marketValue: number
  unrealizedPnL: number
  unrealizedPnLPercent: number
  todayPnL: number
  totalPnL: number
  lastUpdate: Date
}

export interface PositionManagerProps {
  positions: Position[]
  onSell?: (symbol: string, quantity: number) => void
  onSetAlert?: (symbol: string) => void
}

export function PositionManager(props: PositionManagerProps) {
  const [selectedPositions, setSelectedPositions] = createSignal<string[]>([])
  const [sortBy, setSortBy] = createSignal<'pnl' | 'percent' | 'value'>('pnl')

  // 计算总体统计
  const portfolioStats = createMemo(() => {
    const positions = props.positions
    const totalMarketValue = positions.reduce((sum, pos) => sum + pos.marketValue, 0)
    const totalCost = positions.reduce((sum, pos) => sum + pos.avgCost * pos.quantity, 0)
    const totalUnrealizedPnL = positions.reduce((sum, pos) => sum + pos.unrealizedPnL, 0)
    const totalTodayPnL = positions.reduce((sum, pos) => sum + pos.todayPnL, 0)
    
    return {
      totalMarketValue,
      totalCost,
      totalUnrealizedPnL,
      totalUnrealizedPnLPercent: totalCost > 0 ? (totalUnrealizedPnL / totalCost) * 100 : 0,
      totalTodayPnL,
      positionCount: positions.length,
    }
  })

  // 表格列定义
  const columns: ColumnDef<Position, any>[] = [
    {
      accessorKey: 'symbol',
      header: '代码',
      cell: (info) => (
        <div class={css({ display: 'flex', flexDirection: 'column' })}>
          <span class={css({ fontWeight: '600', fontFamily: 'monospace' })}>
            {info.getValue()}
          </span>
          <span class={css({ fontSize: '12px', color: 'text.secondary' })}>
            {info.row.original.name}
          </span>
        </div>
      ),
    },
    {
      accessorKey: 'quantity',
      header: '持仓',
      cell: (info) => (
        <div class={css({ display: 'flex', flexDirection: 'column', textAlign: 'right' })}>
          <span class={css({ fontWeight: '500' })}>{info.getValue().toLocaleString()}</span>
          <span class={css({ fontSize: '12px', color: 'text.secondary' })}>
            可卖: {info.row.original.availableQuantity.toLocaleString()}
          </span>
        </div>
      ),
    },
    {
      accessorKey: 'avgCost',
      header: '成本价',
      cell: (info) => (
        <span class={css({ fontFamily: 'monospace', fontWeight: '500' })}>
          ¥{info.getValue().toFixed(2)}
        </span>
      ),
    },
    {
      accessorKey: 'currentPrice',
      header: '现价',
      cell: (info) => {
        const position = info.row.original
        const change = position.currentPrice - position.avgCost
        const color = change > 0 ? 'danger.500' : change < 0 ? 'success.500' : 'text.regular'
        return (
          <span class={css({ fontFamily: 'monospace', fontWeight: '500', color })}>
            ¥{info.getValue().toFixed(2)}
          </span>
        )
      },
    },
    {
      accessorKey: 'marketValue',
      header: '市值',
      cell: (info) => (
        <span class={css({ fontWeight: '500' })}>
          ¥{info.getValue().toLocaleString()}
        </span>
      ),
    },
    {
      accessorKey: 'unrealizedPnL',
      header: '浮动盈亏',
      cell: (info) => {
        const pnl = info.getValue() as number
        const percent = info.row.original.unrealizedPnLPercent
        const color = pnl > 0 ? 'danger.500' : pnl < 0 ? 'success.500' : 'text.regular'
        return (
          <div class={css({ display: 'flex', flexDirection: 'column', textAlign: 'right' })}>
            <span class={css({ fontWeight: '600', color })}>
              {pnl > 0 ? '+' : ''}¥{pnl.toFixed(2)}
            </span>
            <span class={css({ fontSize: '12px', color })}>
              {percent > 0 ? '+' : ''}{percent.toFixed(2)}%
            </span>
          </div>
        )
      },
    },
    {
      accessorKey: 'todayPnL',
      header: '今日盈亏',
      cell: (info) => {
        const pnl = info.getValue() as number
        const color = pnl > 0 ? 'danger.500' : pnl < 0 ? 'success.500' : 'text.regular'
        return (
          <span class={css({ fontWeight: '500', color })}>
            {pnl > 0 ? '+' : ''}¥{pnl.toFixed(2)}
          </span>
        )
      },
    },
    {
      id: 'actions',
      header: '操作',
      cell: (info) => (
        <div class={css({ display: 'flex', gap: '4px' })}>
          <Button
            size="small"
            variant="success"
            onClick={() => props.onSell?.(info.row.original.symbol, info.row.original.availableQuantity)}
          >
            卖出
          </Button>
          <Button
            size="small"
            variant="default"
            onClick={() => props.onSetAlert?.(info.row.original.symbol)}
          >
            提醒
          </Button>
        </div>
      ),
    },
  ]

  const handleBatchSell = () => {
    // 批量卖出逻辑
    console.log('批量卖出:', selectedPositions())
  }

  const stats = portfolioStats()

  return (
    <div class={css({ display: 'flex', flexDirection: 'column', gap: '16px' })}>
      {/* 持仓统计 */}
      <div class={css({
        display: 'grid',
        gridTemplateColumns: 'repeat(auto-fit, minmax(150px, 1fr))',
        gap: '12px',
      })}>
        <Card bodyStyle={{ padding: '12px', textAlign: 'center' }}>
          <div class={css({ fontSize: '12px', color: 'text.secondary', marginBottom: '4px' })}>
            总市值
          </div>
          <div class={css({ fontSize: '18px', fontWeight: '600', color: 'text.primary' })}>
            ¥{stats.totalMarketValue.toLocaleString()}
          </div>
        </Card>

        <Card bodyStyle={{ padding: '12px', textAlign: 'center' }}>
          <div class={css({ fontSize: '12px', color: 'text.secondary', marginBottom: '4px' })}>
            总成本
          </div>
          <div class={css({ fontSize: '18px', fontWeight: '600', color: 'text.primary' })}>
            ¥{stats.totalCost.toLocaleString()}
          </div>
        </Card>

        <Card bodyStyle={{ padding: '12px', textAlign: 'center' }}>
          <div class={css({ fontSize: '12px', color: 'text.secondary', marginBottom: '4px' })}>
            浮动盈亏
          </div>
          <div class={css({
            fontSize: '18px',
            fontWeight: '600',
            color: stats.totalUnrealizedPnL > 0 ? 'danger.500' : stats.totalUnrealizedPnL < 0 ? 'success.500' : 'text.primary',
          })}>
            {stats.totalUnrealizedPnL > 0 ? '+' : ''}¥{stats.totalUnrealizedPnL.toFixed(2)}
          </div>
          <div class={css({
            fontSize: '12px',
            color: stats.totalUnrealizedPnL > 0 ? 'danger.500' : stats.totalUnrealizedPnL < 0 ? 'success.500' : 'text.secondary',
          })}>
            {stats.totalUnrealizedPnLPercent > 0 ? '+' : ''}{stats.totalUnrealizedPnLPercent.toFixed(2)}%
          </div>
        </Card>

        <Card bodyStyle={{ padding: '12px', textAlign: 'center' }}>
          <div class={css({ fontSize: '12px', color: 'text.secondary', marginBottom: '4px' })}>
            今日盈亏
          </div>
          <div class={css({
            fontSize: '18px',
            fontWeight: '600',
            color: stats.totalTodayPnL > 0 ? 'danger.500' : stats.totalTodayPnL < 0 ? 'success.500' : 'text.primary',
          })}>
            {stats.totalTodayPnL > 0 ? '+' : ''}¥{stats.totalTodayPnL.toFixed(2)}
          </div>
        </Card>

        <Card bodyStyle={{ padding: '12px', textAlign: 'center' }}>
          <div class={css({ fontSize: '12px', color: 'text.secondary', marginBottom: '4px' })}>
            持仓数量
          </div>
          <div class={css({ fontSize: '18px', fontWeight: '600', color: 'text.primary' })}>
            {stats.positionCount} 只
          </div>
        </Card>
      </div>

      {/* 操作工具栏 */}
      <div class={css({
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'space-between',
        padding: '12px 16px',
        backgroundColor: 'white',
        borderRadius: '4px',
        border: '1px solid',
        borderColor: 'border.base',
      })}>
        <div class={css({ display: 'flex', alignItems: 'center', gap: '12px' })}>
          <span class={css({ fontSize: '14px', color: 'text.secondary' })}>
            已选择 {selectedPositions().length} 只股票
          </span>
          {selectedPositions().length > 0 && (
            <Button size="small" variant="success" onClick={handleBatchSell}>
              批量卖出
            </Button>
          )}
        </div>

        <div class={css({ display: 'flex', alignItems: 'center', gap: '8px' })}>
          <span class={css({ fontSize: '14px', color: 'text.secondary' })}>排序:</span>
          <Button
            size="small"
            variant={sortBy() === 'pnl' ? 'primary' : 'default'}
            onClick={() => setSortBy('pnl')}
          >
            盈亏
          </Button>
          <Button
            size="small"
            variant={sortBy() === 'percent' ? 'primary' : 'default'}
            onClick={() => setSortBy('percent')}
          >
            涨幅
          </Button>
          <Button
            size="small"
            variant={sortBy() === 'value' ? 'primary' : 'default'}
            onClick={() => setSortBy('value')}
          >
            市值
          </Button>
        </div>
      </div>

      {/* 持仓列表 */}
      <Card header="持仓明细" shadow="always">
        <Table
          data={props.positions}
          columns={columns}
          pagination={true}
          pageSize={10}
          sortable={true}
          selectable={true}
          onRowSelect={(rows) => setSelectedPositions(rows.map(r => r.symbol))}
          height="400px"
        />
      </Card>
    </div>
  )
}
