import { createSignal, onMount } from 'solid-js';
import { css } from '../../styled-system/css';

// 极简 http 客户端与路径常量，避免未定义引用
const httpClient = {
  get: async (url: string, params?: Record<string, string>) => {
    const query = params
      ? '?' + new URLSearchParams(params).toString()
      : '';
    const base = import.meta.env?.VITE_API_BASE_URL || '/api';
    const res = await fetch(base + url + query);
    if (!res.ok) throw new Error(`HTTP ${res.status}`);
    return res.json().catch(() => ({}));
  },
};

const API_PATHS = {
  SYSTEM: { HEALTH: '/v1/health' },
  MARKET: { OVERVIEW: '/v1/market/overview', SEARCH: '/v1/market/search' },
  AUTH: { PROFILE: '/v1/auth/profile' },
};

export default function ApiTest() {
  const [testResults, setTestResults] = createSignal<Array<{
    name: string;
    status: 'pending' | 'success' | 'error';
    message: string;
    duration?: number;
  }>>([]);

  const addTestResult = (name: string, status: 'success' | 'error', message: string, duration?: number) => {
    setTestResults(prev => [...prev, { name, status, message, duration }]);
  };

  const runApiTests = async () => {
    setTestResults([]);
    const base = import.meta.env?.VITE_API_BASE_URL || '/api';
    const isProxyUnavailable = base.startsWith('/api') && (location.hostname === 'localhost' || location.hostname === '127.0.0.1');
    
    // 测试1: 健康检查
    try {
      const start = Date.now();
      if (isProxyUnavailable) throw new Error('后端未启动或代理不可用');
      await httpClient.get(API_PATHS.SYSTEM.HEALTH);
      const duration = Date.now() - start;
      addTestResult('系统健康检查', 'success', '连接成功', duration);
    } catch (error: any) {
      addTestResult('系统健康检查', 'error', error.message || '连接失败');
    }

    // 测试2: 市场数据
    try {
      const start = Date.now();
      if (isProxyUnavailable) throw new Error('后端未启动或代理不可用');
      await httpClient.get(API_PATHS.MARKET.OVERVIEW);
      const duration = Date.now() - start;
      addTestResult('市场概览', 'success', '数据获取成功', duration);
    } catch (error: any) {
      addTestResult('市场概览', 'error', error.message || '数据获取失败');
    }

    // 测试3: 股票搜索
    try {
      const start = Date.now();
      if (isProxyUnavailable) throw new Error('后端未启动或代理不可用');
      await httpClient.get(API_PATHS.MARKET.SEARCH, { q: 'AAPL' });
      const duration = Date.now() - start;
      addTestResult('股票搜索', 'success', '搜索成功', duration);
    } catch (error: any) {
      addTestResult('股票搜索', 'error', error.message || '搜索失败');
    }

    // 测试4: 用户信息（需要认证）
    try {
      const start = Date.now();
      if (isProxyUnavailable) throw new Error('后端未启动或代理不可用');
      await httpClient.get(API_PATHS.AUTH.PROFILE);
      const duration = Date.now() - start;
      addTestResult('用户信息', 'success', '获取成功', duration);
    } catch (error: any) {
      addTestResult('用户信息', 'error', error.message || '获取失败（预期，因为未登录）');
    }
  };

  onMount(() => {
    console.log('ApiTest mounted');
  });

  return (
    <div class={css({
      padding: '24px',
      maxWidth: '1200px',
      margin: '0 auto'
    })}>
      {/* 页面标题 */}
      <div class={css({
        marginBottom: '32px'
      })}>
        <h1 class={css({
          fontSize: '32px',
          fontWeight: 'bold',
          color: 'gray.900',
          marginBottom: '8px'
        })}>
          🔧 API 连接测试
        </h1>
        <p class={css({
          fontSize: '16px',
          color: 'gray.600'
        })}>
          测试前端与后端API的连接状态
        </p>
      </div>

      {/* 测试按钮 */}
      <div class={css({
        marginBottom: '32px'
      })}>
        <button
          onClick={runApiTests}
          class={css({
            padding: '12px 24px',
            backgroundColor: 'blue.600',
            color: 'white',
            border: 'none',
            borderRadius: '8px',
            fontSize: '16px',
            fontWeight: '500',
            cursor: 'pointer',
            transition: 'all 0.2s',
            _hover: {
              backgroundColor: 'blue.700'
            }
          })}
        >
          开始测试
        </button>
      </div>

      {/* 测试结果 */}
      <div class={css({
        backgroundColor: 'white',
        borderRadius: '12px',
        boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)',
        border: '1px solid #e5e7eb',
        overflow: 'hidden'
      })}>
        <div class={css({
          padding: '24px',
          borderBottom: '1px solid #e5e7eb'
        })}>
          <h2 class={css({
            fontSize: '20px',
            fontWeight: 'bold',
            color: 'gray.900'
          })}>
            测试结果
          </h2>
        </div>

        <div class={css({
          padding: '24px'
        })}>
          {testResults().length === 0 ? (
            <p class={css({
              color: 'gray.500',
              textAlign: 'center',
              padding: '40px 0'
            })}>
              点击"开始测试"按钮运行API连接测试
            </p>
          ) : (
            <div class={css({
              display: 'flex',
              flexDirection: 'column',
              gap: '16px'
            })}>
              {testResults().map((result) => (
                <div
                  class={css({
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'space-between',
                    padding: '16px',
                    borderRadius: '8px',
                    border: '1px solid',
                    borderColor: result.status === 'success' ? 'green.200' : 'red.200',
                    backgroundColor: result.status === 'success' ? 'green.50' : 'red.50'
                  })}
                >
                  <div class={css({
                    display: 'flex',
                    alignItems: 'center',
                    gap: '12px'
                  })}>
                    <div class={css({
                      fontSize: '20px'
                    })}>
                      {result.status === 'success' ? '✅' : '❌'}
                    </div>
                    <div>
                      <h3 class={css({
                        fontSize: '16px',
                        fontWeight: '600',
                        color: 'gray.900',
                        marginBottom: '4px'
                      })}>
                        {result.name}
                      </h3>
                      <p class={css({
                        fontSize: '14px',
                        color: 'gray.600'
                      })}>
                        {result.message}
                      </p>
                    </div>
                  </div>
                  {result.duration && (
                    <div class={css({
                      fontSize: '12px',
                      color: 'gray.500',
                      backgroundColor: 'gray.100',
                      padding: '4px 8px',
                      borderRadius: '4px'
                    })}>
                      {result.duration}ms
                    </div>
                  )}
                </div>
              ))}
            </div>
          )}
        </div>
      </div>

      {/* 配置信息 */}
      <div class={css({
        marginTop: '32px',
        backgroundColor: 'white',
        borderRadius: '12px',
        boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)',
        border: '1px solid #e5e7eb',
        overflow: 'hidden'
      })}>
        <div class={css({
          padding: '24px',
          borderBottom: '1px solid #e5e7eb'
        })}>
          <h2 class={css({
            fontSize: '20px',
            fontWeight: 'bold',
            color: 'gray.900'
          })}>
            配置信息
          </h2>
        </div>

        <div class={css({
          padding: '24px'
        })}>
          <div class={css({
            display: 'grid',
            gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))',
            gap: '16px'
          })}>
            <div>
              <h3 class={css({
                fontSize: '14px',
                fontWeight: '600',
                color: 'gray.700',
                marginBottom: '8px'
              })}>
                API Base URL
              </h3>
              <p class={css({
                fontSize: '14px',
                color: 'gray.600',
                fontFamily: 'monospace',
                backgroundColor: 'gray.100',
                padding: '8px',
                borderRadius: '4px'
              })}>
                {import.meta.env?.VITE_API_BASE_URL || '/api/v1 (代理)'}
              </p>
            </div>

            <div>
              <h3 class={css({
                fontSize: '14px',
                fontWeight: '600',
                color: 'gray.700',
                marginBottom: '8px'
              })}>
                环境模式
              </h3>
              <p class={css({
                fontSize: '14px',
                color: 'gray.600',
                fontFamily: 'monospace',
                backgroundColor: 'gray.100',
                padding: '8px',
                borderRadius: '4px'
              })}>
                {import.meta.env?.MODE || 'development'}
              </p>
            </div>

            <div>
              <h3 class={css({
                fontSize: '14px',
                fontWeight: '600',
                color: 'gray.700',
                marginBottom: '8px'
              })}>
                代理配置
              </h3>
              <p class={css({
                fontSize: '14px',
                color: 'gray.600',
                fontFamily: 'monospace',
                backgroundColor: 'gray.100',
                padding: '8px',
                borderRadius: '4px'
              })}>
                /api → localhost:8000
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
