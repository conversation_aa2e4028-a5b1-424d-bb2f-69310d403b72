const S={context:void 0,registry:void 0,effects:void 0,done:!1,getContextId(){return Be(this.context.count)},getNextContextId(){return Be(this.context.count++)}};function Be(e){const t=String(e),n=t.length-1;return S.context.id+(n?String.fromCharCode(96+n):"")+t}function vt(e){S.context=e}const Ct=!0,$t=(e,t)=>e===t,k=Symbol("solid-proxy"),ze=typeof Proxy=="function",Ae=Symbol("solid-track"),Xe=Symbol("solid-dev-component"),oe={equals:$t};let Ye=nt;const j=1,ie=2,Lt={};var w=null;let ye=null,Rt=null,A=null,C=null,D=null,he=0;const kt={afterUpdate:null,afterCreateOwner:null,afterCreateSignal:null,afterRegisterGraph:null};function q(e,t){const n=A,r=w,s=e.length===0,o=t===void 0?r:t,i=s?{owned:null,cleanups:null,context:null,owner:null}:{owned:null,cleanups:null,context:o?o.context:null,owner:o},l=s?()=>e(()=>{throw new Error("Dispose method must be an explicit argument to createRoot function")}):()=>e(()=>L(()=>J(i)));w=i,A=null;try{return F(l,!0)}finally{A=n,w=r}}function N(e,t){t=t?Object.assign({},oe,t):oe;const n={value:e,observers:null,observerSlots:null,comparator:t.equals||void 0};t.name&&(n.name=t.name),t.internal?n.internal=!0:Ze(n);const r=s=>(typeof s=="function"&&(s=s(n.value)),De(n,s));return[tt.bind(n),r]}function Zn(e,t,n){const r=te(e,t,!0,j,n);K(r)}function I(e,t,n){const r=te(e,t,!1,j,n);K(r)}function Je(e,t,n){Ye=It;const r=te(e,t,!1,j,n);(!n||!n.render)&&(r.user=!0),D?D.push(r):K(r)}function P(e,t,n){n=n?Object.assign({},oe,n):oe;const r=te(e,t,!0,0,n);return r.observers=null,r.observerSlots=null,r.comparator=n.equals||void 0,K(r),tt.bind(r)}function Qe(e){return F(e,!1)}function L(e){if(A===null)return e();const t=A;A=null;try{return e()}finally{A=t}}function Re(e,t,n){const r=Array.isArray(e);let s,o=n&&n.defer;return i=>{let l;if(r){l=Array(e.length);for(let c=0;c<e.length;c++)l[c]=e[c]()}else l=e();if(o)return o=!1,i;const a=L(()=>t(l,s,i));return s=l,a}}function er(e){Je(()=>L(e))}function Y(e){return w===null?console.warn("cleanups created outside a `createRoot` or `render` will never be run"):w.cleanups===null?w.cleanups=[e]:w.cleanups.push(e),e}function Se(){return A}function le(){return w}function ke(e,t){const n=w,r=A;w=e,A=null;try{return F(t,!0)}catch(s){je(s)}finally{w=n,A=r}}function Tt(e){const t=A,n=w;return Promise.resolve().then(()=>{A=t,w=n;let r;return F(e,!1),A=w=null,r?r.done:void 0})}const[tr,nr]=N(!1);function Nt(e,t){const n=te(()=>L(()=>(Object.assign(e,{[Xe]:!0}),e(t))),void 0,!0,0);return n.props=t,n.observers=null,n.observerSlots=null,n.name=e.name,n.component=e,K(n),n.tValue!==void 0?n.tValue:n.value}function Ze(e){w&&(w.sourceMap?w.sourceMap.push(e):w.sourceMap=[e],e.graph=w)}function et(e,t){const n=Symbol("context");return{id:n,Provider:Ft(n,t),defaultValue:e}}function Te(e){let t;return w&&w.context&&(t=w.context[e.id])!==void 0?t:e.defaultValue}function Ne(e){const t=P(e),n=P(()=>Pe(t()),void 0,{name:"children"});return n.toArray=()=>{const r=n();return Array.isArray(r)?r:r!=null?[r]:[]},n}function tt(){if(this.sources&&this.state)if(this.state===j)K(this);else{const e=C;C=null,F(()=>ce(this),!1),C=e}if(A){const e=this.observers?this.observers.length:0;A.sources?(A.sources.push(this),A.sourceSlots.push(e)):(A.sources=[this],A.sourceSlots=[e]),this.observers?(this.observers.push(A),this.observerSlots.push(A.sources.length-1)):(this.observers=[A],this.observerSlots=[A.sources.length-1])}return this.value}function De(e,t,n){let r=e.value;return(!e.comparator||!e.comparator(r,t))&&(e.value=t,e.observers&&e.observers.length&&F(()=>{for(let s=0;s<e.observers.length;s+=1){const o=e.observers[s],i=ye&&ye.running;i&&ye.disposed.has(o),(i?!o.tState:!o.state)&&(o.pure?C.push(o):D.push(o),o.observers&&rt(o)),i||(o.state=j)}if(C.length>1e6)throw C=[],Ct?new Error("Potential Infinite Loop Detected."):new Error},!1)),t}function K(e){if(!e.fn)return;J(e);const t=he;Dt(e,e.value,t)}function Dt(e,t,n){let r;const s=w,o=A;A=w=e;try{r=e.fn(t)}catch(i){return e.pure&&(e.state=j,e.owned&&e.owned.forEach(J),e.owned=null),e.updatedAt=n+1,je(i)}finally{A=o,w=s}(!e.updatedAt||e.updatedAt<=n)&&(e.updatedAt!=null&&"observers"in e?De(e,r):e.value=r,e.updatedAt=n)}function te(e,t,n,r=j,s){const o={fn:e,state:r,updatedAt:null,owned:null,sources:null,sourceSlots:null,cleanups:null,value:t,owner:w,context:w?w.context:null,pure:n};return w===null?console.warn("computations created outside a `createRoot` or `render` will never be disposed"):w!==Lt&&(w.owned?w.owned.push(o):w.owned=[o]),s&&s.name&&(o.name=s.name),o}function ae(e){if(e.state===0)return;if(e.state===ie)return ce(e);if(e.suspense&&L(e.suspense.inFallback))return e.suspense.effects.push(e);const t=[e];for(;(e=e.owner)&&(!e.updatedAt||e.updatedAt<he);)e.state&&t.push(e);for(let n=t.length-1;n>=0;n--)if(e=t[n],e.state===j)K(e);else if(e.state===ie){const r=C;C=null,F(()=>ce(e,t[0]),!1),C=r}}function F(e,t){if(C)return e();let n=!1;t||(C=[]),D?n=!0:D=[],he++;try{const r=e();return jt(n),r}catch(r){n||(D=null),C=null,je(r)}}function jt(e){if(C&&(nt(C),C=null),e)return;const t=D;D=null,t.length&&F(()=>Ye(t),!1)}function nt(e){for(let t=0;t<e.length;t++)ae(e[t])}function It(e){let t,n=0;for(t=0;t<e.length;t++){const r=e[t];r.user?e[n++]=r:ae(r)}if(S.context){if(S.count){S.effects||(S.effects=[]),S.effects.push(...e.slice(0,n));return}vt()}for(S.effects&&(S.done||!S.count)&&(e=[...S.effects,...e],n+=S.effects.length,delete S.effects),t=0;t<n;t++)ae(e[t])}function ce(e,t){e.state=0;for(let n=0;n<e.sources.length;n+=1){const r=e.sources[n];if(r.sources){const s=r.state;s===j?r!==t&&(!r.updatedAt||r.updatedAt<he)&&ae(r):s===ie&&ce(r,t)}}}function rt(e){for(let t=0;t<e.observers.length;t+=1){const n=e.observers[t];n.state||(n.state=ie,n.pure?C.push(n):D.push(n),n.observers&&rt(n))}}function J(e){let t;if(e.sources)for(;e.sources.length;){const n=e.sources.pop(),r=e.sourceSlots.pop(),s=n.observers;if(s&&s.length){const o=s.pop(),i=n.observerSlots.pop();r<s.length&&(o.sourceSlots[i]=r,s[r]=o,n.observerSlots[r]=i)}}if(e.tOwned){for(t=e.tOwned.length-1;t>=0;t--)J(e.tOwned[t]);delete e.tOwned}if(e.owned){for(t=e.owned.length-1;t>=0;t--)J(e.owned[t]);e.owned=null}if(e.cleanups){for(t=e.cleanups.length-1;t>=0;t--)e.cleanups[t]();e.cleanups=null}e.state=0,delete e.sourceMap}function Mt(e){return e instanceof Error?e:new Error(typeof e=="string"?e:"Unknown error",{cause:e})}function je(e,t=w){throw Mt(e)}function Pe(e){if(typeof e=="function"&&!e.length)return Pe(e());if(Array.isArray(e)){const t=[];for(let n=0;n<e.length;n++){const r=Pe(e[n]);Array.isArray(r)?t.push.apply(t,r):t.push(r)}return t}return e}function Ft(e,t){return function(r){let s;return I(()=>s=L(()=>(w.context={...w.context,[e]:r.value},Ne(()=>r.children))),void 0,t),s}}const _t=Symbol("fallback");function Ve(e){for(let t=0;t<e.length;t++)e[t]()}function Ut(e,t,n={}){let r=[],s=[],o=[],i=0,l=t.length>1?[]:null;return Y(()=>Ve(o)),()=>{let a=e()||[],c=a.length,f,u;return a[Ae],L(()=>{let g,O,h,m,b,y,v,x,$;if(c===0)i!==0&&(Ve(o),o=[],r=[],s=[],i=0,l&&(l=[])),n.fallback&&(r=[_t],s[0]=q(U=>(o[0]=U,n.fallback())),i=1);else if(i===0){for(s=new Array(c),u=0;u<c;u++)r[u]=a[u],s[u]=q(d);i=c}else{for(h=new Array(c),m=new Array(c),l&&(b=new Array(c)),y=0,v=Math.min(i,c);y<v&&r[y]===a[y];y++);for(v=i-1,x=c-1;v>=y&&x>=y&&r[v]===a[x];v--,x--)h[x]=s[v],m[x]=o[v],l&&(b[x]=l[v]);for(g=new Map,O=new Array(x+1),u=x;u>=y;u--)$=a[u],f=g.get($),O[u]=f===void 0?-1:f,g.set($,u);for(f=y;f<=v;f++)$=r[f],u=g.get($),u!==void 0&&u!==-1?(h[u]=s[f],m[u]=o[f],l&&(b[u]=l[f]),u=O[u],g.set($,u)):o[f]();for(u=y;u<c;u++)u in h?(s[u]=h[u],o[u]=m[u],l&&(l[u]=b[u],l[u](u))):s[u]=q(d);s=s.slice(0,i=c),r=a.slice(0)}return s});function d(g){if(o[u]=g,l){const[O,h]=N(u,{name:"index"});return l[u]=h,t(a[u],O)}return t(a[u])}}}function M(e,t){return Nt(e,t||{})}function re(){return!0}const Ee={get(e,t,n){return t===k?n:e.get(t)},has(e,t){return t===k?!0:e.has(t)},set:re,deleteProperty:re,getOwnPropertyDescriptor(e,t){return{configurable:!0,enumerable:!0,get(){return e.get(t)},set:re,deleteProperty:re}},ownKeys(e){return e.keys()}};function we(e){return(e=typeof e=="function"?e():e)?e:{}}function Bt(){for(let e=0,t=this.length;e<t;++e){const n=this[e]();if(n!==void 0)return n}}function Oe(...e){let t=!1;for(let i=0;i<e.length;i++){const l=e[i];t=t||!!l&&k in l,e[i]=typeof l=="function"?(t=!0,P(l)):l}if(ze&&t)return new Proxy({get(i){for(let l=e.length-1;l>=0;l--){const a=we(e[l])[i];if(a!==void 0)return a}},has(i){for(let l=e.length-1;l>=0;l--)if(i in we(e[l]))return!0;return!1},keys(){const i=[];for(let l=0;l<e.length;l++)i.push(...Object.keys(we(e[l])));return[...new Set(i)]}},Ee);const n={},r=Object.create(null);for(let i=e.length-1;i>=0;i--){const l=e[i];if(!l)continue;const a=Object.getOwnPropertyNames(l);for(let c=a.length-1;c>=0;c--){const f=a[c];if(f==="__proto__"||f==="constructor")continue;const u=Object.getOwnPropertyDescriptor(l,f);if(!r[f])r[f]=u.get?{enumerable:!0,configurable:!0,get:Bt.bind(n[f]=[u.get.bind(l)])}:u.value!==void 0?u:void 0;else{const d=n[f];d&&(u.get?d.push(u.get.bind(l)):u.value!==void 0&&d.push(()=>u.value))}}}const s={},o=Object.keys(r);for(let i=o.length-1;i>=0;i--){const l=o[i],a=r[l];a&&a.get?Object.defineProperty(s,l,a):s[l]=a?a.value:void 0}return s}function st(e,...t){if(ze&&k in e){const s=new Set(t.length>1?t.flat():t[0]),o=t.map(i=>new Proxy({get(l){return i.includes(l)?e[l]:void 0},has(l){return i.includes(l)&&l in e},keys(){return i.filter(l=>l in e)}},Ee));return o.push(new Proxy({get(i){return s.has(i)?void 0:e[i]},has(i){return s.has(i)?!1:i in e},keys(){return Object.keys(e).filter(i=>!s.has(i))}},Ee)),o}const n={},r=t.map(()=>({}));for(const s of Object.getOwnPropertyNames(e)){const o=Object.getOwnPropertyDescriptor(e,s),i=!o.get&&!o.set&&o.enumerable&&o.writable&&o.configurable;let l=!1,a=0;for(const c of t)c.includes(s)&&(l=!0,i?r[a][s]=o.value:Object.defineProperty(r[a],s,o)),++a;l||(i?n[s]=o.value:Object.defineProperty(n,s,o))}return[...r,n]}let Vt=0;function rr(){return S.context?S.getNextContextId():`cl-${Vt++}`}const Gt=e=>`Attempting to access a stale value from <${e}> that could possibly be undefined. This may occur because you are reading the accessor returned from the component at a time where it has already been unmounted. We recommend cleaning up any stale timers or async, or reading from the initial condition.`;function sr(e){const t="fallback"in e&&{fallback:()=>e.fallback};return P(Ut(()=>e.each,e.children,t||void 0),void 0,{name:"value"})}function ot(e){const t=e.keyed,n=P(()=>e.when,void 0,{name:"condition value"}),r=t?n:P(n,void 0,{equals:(s,o)=>!s==!o,name:"condition"});return P(()=>{const s=r();if(s){const o=e.children;return typeof o=="function"&&o.length>0?L(()=>o(t?s:()=>{if(!L(r))throw Gt("Show");return n()})):o}return e.fallback},void 0,{name:"value"})}const Kt={hooks:kt,writeSignal:De,registerGraph:Ze};globalThis&&(globalThis.Solid$$?console.warn("You appear to have multiple instances of Solid. This can lead to unexpected behavior."):globalThis.Solid$$=!0);const Ht=["allowfullscreen","async","alpha","autofocus","autoplay","checked","controls","default","disabled","formnovalidate","hidden","indeterminate","inert","ismap","loop","multiple","muted","nomodule","novalidate","open","playsinline","readonly","required","reversed","seamless","selected","adauctionheaders","browsingtopics","credentialless","defaultchecked","defaultmuted","defaultselected","defer","disablepictureinpicture","disableremoteplayback","preservespitch","shadowrootclonable","shadowrootcustomelementregistry","shadowrootdelegatesfocus","shadowrootserializable","sharedstoragewritable"],Wt=new Set(["className","value","readOnly","noValidate","formNoValidate","isMap","noModule","playsInline","adAuctionHeaders","allowFullscreen","browsingTopics","defaultChecked","defaultMuted","defaultSelected","disablePictureInPicture","disableRemotePlayback","preservesPitch","shadowRootClonable","shadowRootCustomElementRegistry","shadowRootDelegatesFocus","shadowRootSerializable","sharedStorageWritable",...Ht]),qt=new Set(["innerHTML","textContent","innerText","children"]),zt=Object.assign(Object.create(null),{className:"class",htmlFor:"for"}),Xt=Object.assign(Object.create(null),{class:"className",novalidate:{$:"noValidate",FORM:1},formnovalidate:{$:"formNoValidate",BUTTON:1,INPUT:1},ismap:{$:"isMap",IMG:1},nomodule:{$:"noModule",SCRIPT:1},playsinline:{$:"playsInline",VIDEO:1},readonly:{$:"readOnly",INPUT:1,TEXTAREA:1},adauctionheaders:{$:"adAuctionHeaders",IFRAME:1},allowfullscreen:{$:"allowFullscreen",IFRAME:1},browsingtopics:{$:"browsingTopics",IMG:1},defaultchecked:{$:"defaultChecked",INPUT:1},defaultmuted:{$:"defaultMuted",AUDIO:1,VIDEO:1},defaultselected:{$:"defaultSelected",OPTION:1},disablepictureinpicture:{$:"disablePictureInPicture",VIDEO:1},disableremoteplayback:{$:"disableRemotePlayback",AUDIO:1,VIDEO:1},preservespitch:{$:"preservesPitch",AUDIO:1,VIDEO:1},shadowrootclonable:{$:"shadowRootClonable",TEMPLATE:1},shadowrootdelegatesfocus:{$:"shadowRootDelegatesFocus",TEMPLATE:1},shadowrootserializable:{$:"shadowRootSerializable",TEMPLATE:1},sharedstoragewritable:{$:"sharedStorageWritable",IFRAME:1,IMG:1}});function Yt(e,t){const n=Xt[e];return typeof n=="object"?n[t]?n.$:void 0:n}const Jt=new Set(["beforeinput","click","dblclick","contextmenu","focusin","focusout","input","keydown","keyup","mousedown","mousemove","mouseout","mouseover","mouseup","pointerdown","pointermove","pointerout","pointerover","pointerup","touchend","touchmove","touchstart"]),Qt=new Set(["altGlyph","altGlyphDef","altGlyphItem","animate","animateColor","animateMotion","animateTransform","circle","clipPath","color-profile","cursor","defs","desc","ellipse","feBlend","feColorMatrix","feComponentTransfer","feComposite","feConvolveMatrix","feDiffuseLighting","feDisplacementMap","feDistantLight","feDropShadow","feFlood","feFuncA","feFuncB","feFuncG","feFuncR","feGaussianBlur","feImage","feMerge","feMergeNode","feMorphology","feOffset","fePointLight","feSpecularLighting","feSpotLight","feTile","feTurbulence","filter","font","font-face","font-face-format","font-face-name","font-face-src","font-face-uri","foreignObject","g","glyph","glyphRef","hkern","image","line","linearGradient","marker","mask","metadata","missing-glyph","mpath","path","pattern","polygon","polyline","radialGradient","rect","set","stop","svg","switch","symbol","text","textPath","tref","tspan","use","view","vkern"]),Zt={xlink:"http://www.w3.org/1999/xlink",xml:"http://www.w3.org/XML/1998/namespace"},en=e=>P(()=>e());function tn(e,t,n){let r=n.length,s=t.length,o=r,i=0,l=0,a=t[s-1].nextSibling,c=null;for(;i<s||l<o;){if(t[i]===n[l]){i++,l++;continue}for(;t[s-1]===n[o-1];)s--,o--;if(s===i){const f=o<r?l?n[l-1].nextSibling:n[o-l]:a;for(;l<o;)e.insertBefore(n[l++],f)}else if(o===l)for(;i<s;)(!c||!c.has(t[i]))&&t[i].remove(),i++;else if(t[i]===n[o-1]&&n[l]===t[s-1]){const f=t[--s].nextSibling;e.insertBefore(n[l++],t[i++].nextSibling),e.insertBefore(n[--o],f),t[s]=n[o]}else{if(!c){c=new Map;let u=l;for(;u<o;)c.set(n[u],u++)}const f=c.get(t[i]);if(f!=null)if(l<f&&f<o){let u=i,d=1,g;for(;++u<s&&u<o&&!((g=c.get(t[u]))==null||g!==f+d);)d++;if(d>f-l){const O=t[i];for(;l<f;)e.insertBefore(n[l++],O)}else e.replaceChild(n[l++],t[i++])}else i++;else t[i++].remove()}}}const Ge="_$DX_DELEGATE";function or(e,t,n,r={}){if(!t)throw new Error("The `element` passed to `render(..., element)` doesn't exist. Make sure `element` exists in the document.");let s;return q(o=>{s=o,t===document?e():ve(t,e(),t.firstChild?null:void 0,n)},r.owner),()=>{s(),t.textContent=""}}function nn(e,t,n,r){let s;const o=()=>{if(_())throw new Error("Failed attempt to create new DOM elements during hydration. Check that the libraries you are using support hydration.");const l=document.createElement("template");return l.innerHTML=e,l.content.firstChild},i=()=>(s||(s=o())).cloneNode(!0);return i.cloneNode=i,i}function it(e,t=window.document){const n=t[Ge]||(t[Ge]=new Set);for(let r=0,s=e.length;r<s;r++){const o=e[r];n.has(o)||(n.add(o),t.addEventListener(o,gn))}}function xe(e,t,n){_(e)||(n==null?e.removeAttribute(t):e.setAttribute(t,n))}function rn(e,t,n,r){_(e)||(r==null?e.removeAttributeNS(t,n):e.setAttributeNS(t,n,r))}function sn(e,t,n){_(e)||(n?e.setAttribute(t,""):e.removeAttribute(t))}function on(e,t){_(e)||(t==null?e.removeAttribute("class"):e.className=t)}function ln(e,t,n,r){if(r)Array.isArray(n)?(e[`$$${t}`]=n[0],e[`$$${t}Data`]=n[1]):e[`$$${t}`]=n;else if(Array.isArray(n)){const s=n[0];e.addEventListener(t,n[0]=o=>s.call(e,n[1],o))}else e.addEventListener(t,n,typeof n!="function"&&n)}function an(e,t,n={}){const r=Object.keys(t||{}),s=Object.keys(n);let o,i;for(o=0,i=s.length;o<i;o++){const l=s[o];!l||l==="undefined"||t[l]||(Ke(e,l,!1),delete n[l])}for(o=0,i=r.length;o<i;o++){const l=r[o],a=!!t[l];!l||l==="undefined"||n[l]===a||!a||(Ke(e,l,!0),n[l]=a)}return n}function cn(e,t,n){if(!t)return n?xe(e,"style"):t;const r=e.style;if(typeof t=="string")return r.cssText=t;typeof n=="string"&&(r.cssText=n=void 0),n||(n={}),t||(t={});let s,o;for(o in n)t[o]==null&&r.removeProperty(o),delete n[o];for(o in t)s=t[o],s!==n[o]&&(r.setProperty(o,s),n[o]=s);return n}function ir(e,t,n){n!=null?e.style.setProperty(t,n):e.style.removeProperty(t)}function lt(e,t={},n,r){const s={};return r||I(()=>s.children=Q(e,t.children,s.children)),I(()=>typeof t.ref=="function"&&un(t.ref,e)),I(()=>fn(e,t,n,!0,s,!0)),s}function un(e,t,n){return L(()=>e(t,n))}function ve(e,t,n,r){if(n!==void 0&&!r&&(r=[]),typeof t!="function")return Q(e,t,r,n);I(s=>Q(e,t(),s,n),r)}function fn(e,t,n,r,s={},o=!1){t||(t={});for(const i in s)if(!(i in t)){if(i==="children")continue;s[i]=He(e,i,null,s[i],n,o,t)}for(const i in t){if(i==="children")continue;const l=t[i];s[i]=He(e,i,l,s[i],n,o,t)}}function dn(e){let t,n,r=_();if(!r||!(t=S.registry.get(n=mn()))){if(r)throw S.done=!0,new Error(`Hydration Mismatch. Unable to find DOM nodes for hydration key: ${n}
`);return e()}return S.completed&&S.completed.add(t),S.registry.delete(n),t}function _(e){return!!S.context&&!S.done&&(!e||e.isConnected)}function hn(e){return e.toLowerCase().replace(/-([a-z])/g,(t,n)=>n.toUpperCase())}function Ke(e,t,n){const r=t.trim().split(/\s+/);for(let s=0,o=r.length;s<o;s++)e.classList.toggle(r[s],n)}function He(e,t,n,r,s,o,i){let l,a,c,f,u;if(t==="style")return cn(e,n,r);if(t==="classList")return an(e,n,r);if(n===r)return r;if(t==="ref")o||n(e);else if(t.slice(0,3)==="on:"){const d=t.slice(3);r&&e.removeEventListener(d,r,typeof r!="function"&&r),n&&e.addEventListener(d,n,typeof n!="function"&&n)}else if(t.slice(0,10)==="oncapture:"){const d=t.slice(10);r&&e.removeEventListener(d,r,!0),n&&e.addEventListener(d,n,!0)}else if(t.slice(0,2)==="on"){const d=t.slice(2).toLowerCase(),g=Jt.has(d);if(!g&&r){const O=Array.isArray(r)?r[0]:r;e.removeEventListener(d,O)}(g||n)&&(ln(e,d,n,g),g&&it([d]))}else if(t.slice(0,5)==="attr:")xe(e,t.slice(5),n);else if(t.slice(0,5)==="bool:")sn(e,t.slice(5),n);else if((u=t.slice(0,5)==="prop:")||(c=qt.has(t))||!s&&((f=Yt(t,e.tagName))||(a=Wt.has(t)))||(l=e.nodeName.includes("-")||"is"in i)){if(u)t=t.slice(5),a=!0;else if(_(e))return n;t==="class"||t==="className"?on(e,n):l&&!a&&!c?e[hn(t)]=n:e[f||t]=n}else{const d=s&&t.indexOf(":")>-1&&Zt[t.split(":")[0]];d?rn(e,d,t,n):xe(e,zt[t]||t,n)}return n}function gn(e){let t=e.target;const n=`$$${e.type}`,r=e.target,s=e.currentTarget,o=a=>Object.defineProperty(e,"target",{configurable:!0,value:a}),i=()=>{const a=t[n];if(a&&!t.disabled){const c=t[`${n}Data`];if(c!==void 0?a.call(t,c,e):a.call(t,e),e.cancelBubble)return}return t.host&&typeof t.host!="string"&&!t.host._$host&&t.contains(e.target)&&o(t.host),!0},l=()=>{for(;i()&&(t=t._$host||t.parentNode||t.host););};if(Object.defineProperty(e,"currentTarget",{configurable:!0,get(){return t||document}}),e.composedPath){const a=e.composedPath();o(a[0]);for(let c=0;c<a.length-2&&(t=a[c],!!i());c++){if(t._$host){t=t._$host,l();break}if(t.parentNode===s)break}}else l();o(r)}function Q(e,t,n,r,s){const o=_(e);if(o){!n&&(n=[...e.childNodes]);let a=[];for(let c=0;c<n.length;c++){const f=n[c];f.nodeType===8&&f.data.slice(0,2)==="!$"?f.remove():a.push(f)}n=a}for(;typeof n=="function";)n=n();if(t===n)return n;const i=typeof t,l=r!==void 0;if(e=l&&n[0]&&n[0].parentNode||e,i==="string"||i==="number"){if(o||i==="number"&&(t=t.toString(),t===n))return n;if(l){let a=n[0];a&&a.nodeType===3?a.data!==t&&(a.data=t):a=document.createTextNode(t),n=W(e,n,r,a)}else n!==""&&typeof n=="string"?n=e.firstChild.data=t:n=e.textContent=t}else if(t==null||i==="boolean"){if(o)return n;n=W(e,n,r)}else{if(i==="function")return I(()=>{let a=t();for(;typeof a=="function";)a=a();n=Q(e,a,n,r)}),()=>n;if(Array.isArray(t)){const a=[],c=n&&Array.isArray(n);if(Ce(a,t,n,s))return I(()=>n=Q(e,a,n,r,!0)),()=>n;if(o){if(!a.length)return n;if(r===void 0)return n=[...e.childNodes];let f=a[0];if(f.parentNode!==e)return n;const u=[f];for(;(f=f.nextSibling)!==r;)u.push(f);return n=u}if(a.length===0){if(n=W(e,n,r),l)return n}else c?n.length===0?We(e,a,r):tn(e,n,a):(n&&W(e),We(e,a));n=a}else if(t.nodeType){if(o&&t.parentNode)return n=l?[t]:t;if(Array.isArray(n)){if(l)return n=W(e,n,r,t);W(e,n,null,t)}else n==null||n===""||!e.firstChild?e.appendChild(t):e.replaceChild(t,e.firstChild);n=t}else console.warn("Unrecognized value. Skipped inserting",t)}return n}function Ce(e,t,n,r){let s=!1;for(let o=0,i=t.length;o<i;o++){let l=t[o],a=n&&n[e.length],c;if(!(l==null||l===!0||l===!1))if((c=typeof l)=="object"&&l.nodeType)e.push(l);else if(Array.isArray(l))s=Ce(e,l,a)||s;else if(c==="function")if(r){for(;typeof l=="function";)l=l();s=Ce(e,Array.isArray(l)?l:[l],Array.isArray(a)?a:[a])||s}else e.push(l),s=!0;else{const f=String(l);a&&a.nodeType===3&&a.data===f?e.push(a):e.push(document.createTextNode(f))}}return s}function We(e,t,n=null){for(let r=0,s=t.length;r<s;r++)e.insertBefore(t[r],n)}function W(e,t,n,r){if(n===void 0)return e.textContent="";const s=r||document.createTextNode("");if(t.length){let o=!1;for(let i=t.length-1;i>=0;i--){const l=t[i];if(s!==l){const a=l.parentNode===e;!o&&!i?a?e.replaceChild(s,l):e.insertBefore(s,n):a&&l.remove()}else o=!0}}else e.insertBefore(s,n);return[s]}function mn(){return S.getNextContextId()}const yn=!1,wn="http://www.w3.org/2000/svg";function at(e,t=!1,n=void 0){return t?document.createElementNS(wn,e):document.createElement(e,{is:n})}function lr(e){const{useShadow:t}=e,n=document.createTextNode(""),r=()=>e.mount||document.body,s=le();let o,i=!!S.context;return Je(()=>{i&&(le().user=i=!1),o||(o=ke(s,()=>P(()=>e.children)));const l=r();if(l instanceof HTMLHeadElement){const[a,c]=N(!1),f=()=>c(!0);q(u=>ve(l,()=>a()?u():o(),null)),Y(f)}else{const a=at(e.isSVG?"g":"div",e.isSVG),c=t&&a.attachShadow?a.attachShadow({mode:"open"}):a;Object.defineProperty(a,"_$host",{get(){return n.parentNode},configurable:!0}),ve(c,o),l.appendChild(a),e.ref&&e.ref(a),Y(()=>l.removeChild(a))}},void 0,{render:!i}),n}function bn(e,t){const n=P(e);return P(()=>{const r=n();switch(typeof r){case"function":return Object.assign(r,{[Xe]:!0}),L(()=>r(t));case"string":const s=Qt.has(r),o=S.context?dn():at(r,s,t.is);return lt(o,t,s),o}})}function ar(e){const[,t]=st(e,["component"]);return bn(()=>e.component,t)}function ct(){let e=new Set;function t(s){return e.add(s),()=>e.delete(s)}let n=!1;function r(s,o){if(n)return!(n=!1);const i={to:s,options:o,defaultPrevented:!1,preventDefault:()=>i.defaultPrevented=!0};for(const l of e)l.listener({...i,from:l.location,retry:a=>{a&&(n=!0),l.navigate(s,{...o,resolve:!1})}});return!i.defaultPrevented}return{subscribe:t,confirm:r}}let $e;function Ie(){(!window.history.state||window.history.state._depth==null)&&window.history.replaceState({...window.history.state,_depth:window.history.length-1},""),$e=window.history.state._depth}Ie();function pn(e){return{...e,_depth:window.history.state&&window.history.state._depth}}function An(e,t){let n=!1;return()=>{const r=$e;Ie();const s=r==null?null:$e-r;if(n){n=!1;return}s&&t(s)?(n=!0,window.history.go(-s)):e()}}const Sn=/^(?:[a-z0-9]+:)?\/\//i,Pn=/^\/+|(\/)\/+$/g,ut="http://sr";function G(e,t=!1){const n=e.replace(Pn,"$1");return n?t||/^[?#]/.test(n)?n:"/"+n:""}function se(e,t,n){if(Sn.test(t))return;const r=G(e),s=n&&G(n);let o="";return!s||t.startsWith("/")?o=r:s.toLowerCase().indexOf(r.toLowerCase())!==0?o=r+s:o=s,(o||"/")+G(t,!o)}function En(e,t){if(e==null)throw new Error(t);return e}function On(e,t){return G(e).replace(/\/*(\*.*)?$/g,"")+G(t)}function ft(e){const t={};return e.searchParams.forEach((n,r)=>{t[r]=n}),t}function xn(e,t,n){const[r,s]=e.split("/*",2),o=r.split("/").filter(Boolean),i=o.length;return l=>{const a=l.split("/").filter(Boolean),c=a.length-i;if(c<0||c>0&&s===void 0&&!t)return null;const f={path:i?"":"/",params:{}},u=d=>n===void 0?void 0:n[d];for(let d=0;d<i;d++){const g=o[d],O=a[d],h=g[0]===":",m=h?g.slice(1):g;if(h&&be(O,u(m)))f.params[m]=O;else if(h||!be(O,g))return null;f.path+=`/${O}`}if(s){const d=c?a.slice(-c).join("/"):"";if(be(d,u(s)))f.params[s]=d;else return null}return f}}function be(e,t){const n=r=>r.localeCompare(e,void 0,{sensitivity:"base"})===0;return t===void 0?!0:typeof t=="string"?n(t):typeof t=="function"?t(e):Array.isArray(t)?t.some(n):t instanceof RegExp?t.test(e):!1}function vn(e){const[t,n]=e.pattern.split("/*",2),r=t.split("/").filter(Boolean);return r.reduce((s,o)=>s+(o.startsWith(":")?2:3),r.length-(n===void 0?0:1))}function dt(e){const t=new Map,n=le();return new Proxy({},{get(r,s){return t.has(s)||ke(n,()=>t.set(s,P(()=>e()[s]))),t.get(s)()},getOwnPropertyDescriptor(){return{enumerable:!0,configurable:!0}},ownKeys(){return Reflect.ownKeys(e())}})}function ht(e){let t=/(\/?\:[^\/]+)\?/.exec(e);if(!t)return[e];let n=e.slice(0,t.index),r=e.slice(t.index+t[0].length);const s=[n,n+=t[1]];for(;t=/^(\/\:[^\/]+)\?/.exec(r);)s.push(n+=t[1]),r=r.slice(t[0].length);return ht(r).reduce((o,i)=>[...o,...s.map(l=>l+i)],[])}const Cn=100,gt=et(),Me=et(),Fe=()=>En(Te(gt),"<A> and 'use' router primitives can be only used inside a Route."),$n=()=>Te(Me)||Fe().base,Ln=e=>{const t=$n();return P(()=>t.resolvePath(e()))},Rn=e=>{const t=Fe();return P(()=>{const n=e();return n!==void 0?t.renderPath(n):n})},kn=()=>Fe().location;function Tn(e,t=""){const{component:n,load:r,children:s,info:o}=e,i=!s||Array.isArray(s)&&!s.length,l={key:e,component:n,load:r,info:o};return mt(e.path).reduce((a,c)=>{for(const f of ht(c)){const u=On(t,f);let d=i?u:u.split("/*",1)[0];d=d.split("/").map(g=>g.startsWith(":")||g.startsWith("*")?g:encodeURIComponent(g)).join("/"),a.push({...l,originalPath:c,pattern:d,matcher:xn(d,!i,e.matchFilters)})}return a},[])}function Nn(e,t=0){return{routes:e,score:vn(e[e.length-1])*1e4-t,matcher(n){const r=[];for(let s=e.length-1;s>=0;s--){const o=e[s],i=o.matcher(n);if(!i)return null;r.unshift({...i,route:o})}return r}}}function mt(e){return Array.isArray(e)?e:[e]}function yt(e,t="",n=[],r=[]){const s=mt(e);for(let o=0,i=s.length;o<i;o++){const l=s[o];if(l&&typeof l=="object"){l.hasOwnProperty("path")||(l.path="");const a=Tn(l,t);for(const c of a){n.push(c);const f=Array.isArray(l.children)&&l.children.length===0;if(l.children&&!f)yt(l.children,c.pattern,n,r);else{const u=Nn([...n],r.length);r.push(u)}n.pop()}}}return n.length?r:r.sort((o,i)=>i.score-o.score)}function pe(e,t){for(let n=0,r=e.length;n<r;n++){const s=e[n].matcher(t);if(s)return s}return[]}function Dn(e,t){const n=new URL(ut),r=P(a=>{const c=e();try{return new URL(c,n)}catch{return console.error(`Invalid path ${c}`),a}},n,{equals:(a,c)=>a.href===c.href}),s=P(()=>r().pathname),o=P(()=>r().search,!0),i=P(()=>r().hash),l=()=>"";return{get pathname(){return s()},get search(){return o()},get hash(){return i()},get state(){return t()},get key(){return l()},query:dt(Re(o,()=>ft(r())))}}let V;function jn(){return V}function In(e,t,n,r={}){const{signal:[s,o],utils:i={}}=e,l=i.parsePath||(p=>p),a=i.renderPath||(p=>p),c=i.beforeLeave||ct(),f=se("",r.base||"");if(f===void 0)throw new Error(`${f} is not a valid base path`);f&&!s().value&&o({value:f,replace:!0,scroll:!1});const[u,d]=N(!1);let g;const O=(p,E)=>{E.value===h()&&E.state===b()||(g===void 0&&d(!0),V=p,g=E,Tt(()=>{g===E&&(m(g.value),y(g.state),$[1]([]))}).finally(()=>{g===E&&Qe(()=>{V=void 0,p==="navigate"&&Ot(g),d(!1),g=void 0})}))},[h,m]=N(s().value),[b,y]=N(s().state),v=Dn(h,b),x=[],$=N([]),U=P(()=>typeof r.transformUrl=="function"?pe(t(),r.transformUrl(v.pathname)):pe(t(),v.pathname)),St=dt(()=>{const p=U(),E={};for(let R=0;R<p.length;R++)Object.assign(E,p[R].params);return E}),_e={pattern:f,path:()=>f,outlet:()=>null,resolvePath(p){return se(f,p)}};return I(Re(s,p=>O("native",p),{defer:!0})),{base:_e,location:v,params:St,isRouting:u,renderPath:a,parsePath:l,navigatorFactory:Et,matches:U,beforeLeave:c,preloadRoute:xt,singleFlight:r.singleFlight===void 0?!0:r.singleFlight,submissions:$};function Pt(p,E,R){L(()=>{if(typeof E=="number"){E&&(i.go?i.go(E):console.warn("Router integration does not support relative routing"));return}const{replace:ge,resolve:me,scroll:H,state:ne}={replace:!1,resolve:!0,scroll:!0,...R},B=me?p.resolvePath(E):se("",E);if(B===void 0)throw new Error(`Path '${E}' is not a routable path`);if(x.length>=Cn)throw new Error("Too many redirects");const Ue=h();(B!==Ue||ne!==b())&&(yn||c.confirm(B,R)&&(x.push({value:Ue,replace:ge,scroll:H,state:b()}),O("navigate",{value:B,state:ne})))})}function Et(p){return p=p||Te(Me)||_e,(E,R)=>Pt(p,E,R)}function Ot(p){const E=x[0];E&&(o({...p,replace:E.replace,scroll:E.scroll}),x.length=0)}function xt(p,E={}){const R=pe(t(),p.pathname),ge=V;V="preload";for(let me in R){const{route:H,params:ne}=R[me];H.component&&H.component.preload&&H.component.preload();const{load:B}=H;E.preloadData&&B&&ke(n(),()=>B({params:ne,location:{pathname:p.pathname,search:p.search,hash:p.hash,query:ft(p),state:null,key:""},intent:"preload"}))}V=ge}}function Mn(e,t,n,r){const{base:s,location:o,params:i}=e,{pattern:l,component:a,load:c}=r().route,f=P(()=>r().path);a&&a.preload&&a.preload();const u=c?c({params:i,location:o,intent:V||"initial"}):void 0;return{parent:t,pattern:l,path:f,outlet:()=>a?M(a,{params:i,location:o,data:u,get children(){return n()}}):n(),resolvePath(g){return se(s.path(),g,f())}}}const Fn=e=>t=>{const{base:n}=t,r=Ne(()=>t.children),s=P(()=>yt(r(),t.base||""));let o;const i=In(e,s,()=>o,{base:n,singleFlight:t.singleFlight,transformUrl:t.transformUrl});return e.create&&e.create(i),M(gt.Provider,{value:i,get children(){return M(_n,{routerState:i,get root(){return t.root},get load(){return t.rootLoad},get children(){return[en(()=>(o=le())&&null),M(Un,{routerState:i,get branches(){return s()}})]}})}})};function _n(e){const t=e.routerState.location,n=e.routerState.params,r=P(()=>e.load&&L(()=>{e.load({params:n,location:t,intent:jn()||"initial"})}));return M(ot,{get when(){return e.root},keyed:!0,get fallback(){return e.children},children:s=>M(s,{params:n,location:t,get data(){return r()},get children(){return e.children}})})}function Un(e){const t=[];let n;const r=P(Re(e.routerState.matches,(s,o,i)=>{let l=o&&s.length===o.length;const a=[];for(let c=0,f=s.length;c<f;c++){const u=o&&o[c],d=s[c];i&&u&&d.route.key===u.route.key?a[c]=i[c]:(l=!1,t[c]&&t[c](),q(g=>{t[c]=g,a[c]=Mn(e.routerState,a[c-1]||e.routerState.base,qe(()=>r()[c+1]),()=>e.routerState.matches()[c])}))}return t.splice(s.length).forEach(c=>c()),i&&l?i:(n=a[0],a)}));return qe(()=>r()&&n)()}const qe=e=>()=>M(ot,{get when(){return e()},keyed:!0,children:t=>M(Me.Provider,{value:t,get children(){return t.outlet()}})}),cr=e=>{const t=Ne(()=>e.children);return Oe(e,{get children(){return t()}})};function Bn([e,t],n,r){return[e,r?s=>t(r(s)):t]}function Vn(e){if(e==="#")return null;try{return document.querySelector(e)}catch{return null}}function Gn(e){let t=!1;const n=s=>typeof s=="string"?{value:s}:s,r=Bn(N(n(e.get()),{equals:(s,o)=>s.value===o.value&&s.state===o.state}),void 0,s=>(!t&&e.set(s),s));return e.init&&Y(e.init((s=e.get())=>{t=!0,r[1](n(s)),t=!1})),Fn({signal:r,create:e.create,utils:e.utils})}function Kn(e,t,n){return e.addEventListener(t,n),()=>e.removeEventListener(t,n)}function Hn(e,t){const n=Vn(`#${e}`);n?n.scrollIntoView():t&&window.scrollTo(0,0)}const Wn=new Map;function qn(e=!0,t=!1,n="/_server",r){return s=>{const o=s.base.path(),i=s.navigatorFactory(s.base);let l={};function a(h){return h.namespaceURI==="http://www.w3.org/2000/svg"}function c(h){if(h.defaultPrevented||h.button!==0||h.metaKey||h.altKey||h.ctrlKey||h.shiftKey)return;const m=h.composedPath().find(U=>U instanceof Node&&U.nodeName.toUpperCase()==="A");if(!m||t&&!m.hasAttribute("link"))return;const b=a(m),y=b?m.href.baseVal:m.href;if((b?m.target.baseVal:m.target)||!y&&!m.hasAttribute("state"))return;const x=(m.getAttribute("rel")||"").split(/\s+/);if(m.hasAttribute("download")||x&&x.includes("external"))return;const $=b?new URL(y,document.baseURI):new URL(y);if(!($.origin!==window.location.origin||o&&$.pathname&&!$.pathname.toLowerCase().startsWith(o.toLowerCase())))return[m,$]}function f(h){const m=c(h);if(!m)return;const[b,y]=m,v=s.parsePath(y.pathname+y.search+y.hash),x=b.getAttribute("state");h.preventDefault(),i(v,{resolve:!1,replace:b.hasAttribute("replace"),scroll:!b.hasAttribute("noscroll"),state:x&&JSON.parse(x)})}function u(h){const m=c(h);if(!m)return;const[b,y]=m;typeof r=="function"&&(y.pathname=r(y.pathname)),l[y.pathname]||s.preloadRoute(y,{preloadData:b.getAttribute("preload")!=="false"})}function d(h){const m=c(h);if(!m)return;const[b,y]=m;typeof r=="function"&&(y.pathname=r(y.pathname)),!l[y.pathname]&&(l[y.pathname]=setTimeout(()=>{s.preloadRoute(y,{preloadData:b.getAttribute("preload")!=="false"}),delete l[y.pathname]},200))}function g(h){const m=c(h);if(!m)return;const[,b]=m;typeof r=="function"&&(b.pathname=r(b.pathname)),l[b.pathname]&&(clearTimeout(l[b.pathname]),delete l[b.pathname])}function O(h){if(h.defaultPrevented)return;let m=h.submitter&&h.submitter.hasAttribute("formaction")?h.submitter.getAttribute("formaction"):h.target.getAttribute("action");if(!m)return;if(!m.startsWith("https://action/")){const y=new URL(m,ut);if(m=s.parsePath(y.pathname+y.search),!m.startsWith(n))return}if(h.target.method.toUpperCase()!=="POST")throw new Error("Only POST forms are supported for Actions");const b=Wn.get(m);if(b){h.preventDefault();const y=new FormData(h.target);h.submitter&&h.submitter.name&&y.append(h.submitter.name,h.submitter.value),b.call({r:s,f:h.target},y)}}it(["click","submit"]),document.addEventListener("click",f),e&&(document.addEventListener("mouseover",d),document.addEventListener("mouseout",g),document.addEventListener("focusin",u),document.addEventListener("touchstart",u)),document.addEventListener("submit",O),Y(()=>{document.removeEventListener("click",f),e&&(document.removeEventListener("mouseover",d),document.removeEventListener("mouseout",g),document.removeEventListener("focusin",u),document.removeEventListener("touchstart",u)),document.removeEventListener("submit",O)})}}function ur(e){const t=()=>{const r=window.location.pathname+window.location.search;return{value:e.transformUrl?e.transformUrl(r)+window.location.hash:r+window.location.hash,state:window.history.state}},n=ct();return Gn({get:t,set({value:r,replace:s,scroll:o,state:i}){s?window.history.replaceState(pn(i),"",r):window.history.pushState(i,"",r),Hn(decodeURIComponent(window.location.hash.slice(1)),o),Ie()},init:r=>Kn(window,"popstate",An(r,s=>{if(s&&s<0)return!n.confirm(s);{const o=t();return!n.confirm(o.value,{state:o.state})}})),create:qn(e.preload,e.explicitLinks,e.actionBase,e.transformUrl),utils:{go:r=>window.history.go(r),beforeLeave:n}})(e)}var zn=nn("<a>");function fr(e){e=Oe({inactiveClass:"inactive",activeClass:"active"},e);const[,t]=st(e,["href","state","class","activeClass","inactiveClass","end"]),n=Ln(()=>e.href),r=Rn(n),s=kn(),o=P(()=>{const i=n();if(i===void 0)return[!1,!1];const l=G(i.split(/[?#]/,1)[0]).toLowerCase(),a=G(s.pathname).toLowerCase();return[e.end?l===a:a.startsWith(l+"/")||a===l,l===a]});return(()=>{var i=zn();return lt(i,Oe(t,{get href(){return r()||e.href},get state(){return JSON.stringify(e.state)},get classList(){return{...e.class&&{[e.class]:!0},[e.inactiveClass]:!o()[0],[e.activeClass]:o()[0],...t.classList}},link:"",get"aria-current"(){return o()[1]?"page":void 0}}),!1,!1),i})()}const Le=Symbol("store-raw"),z=Symbol("store-node"),T=Symbol("store-has"),wt=Symbol("store-self");function bt(e){let t=e[k];if(!t&&(Object.defineProperty(e,k,{value:t=new Proxy(e,Jn)}),!Array.isArray(e))){const n=Object.keys(e),r=Object.getOwnPropertyDescriptors(e);for(let s=0,o=n.length;s<o;s++){const i=n[s];r[i].get&&Object.defineProperty(e,i,{enumerable:r[i].enumerable,get:r[i].get.bind(t)})}}return t}function ue(e){let t;return e!=null&&typeof e=="object"&&(e[k]||!(t=Object.getPrototypeOf(e))||t===Object.prototype||Array.isArray(e))}function Z(e,t=new Set){let n,r,s,o;if(n=e!=null&&e[Le])return n;if(!ue(e)||t.has(e))return e;if(Array.isArray(e)){Object.isFrozen(e)?e=e.slice(0):t.add(e);for(let i=0,l=e.length;i<l;i++)s=e[i],(r=Z(s,t))!==s&&(e[i]=r)}else{Object.isFrozen(e)?e=Object.assign({},e):t.add(e);const i=Object.keys(e),l=Object.getOwnPropertyDescriptors(e);for(let a=0,c=i.length;a<c;a++)o=i[a],!l[o].get&&(s=e[o],(r=Z(s,t))!==s&&(e[o]=r))}return e}function fe(e,t){let n=e[t];return n||Object.defineProperty(e,t,{value:n=Object.create(null)}),n}function ee(e,t,n){if(e[t])return e[t];const[r,s]=N(n,{equals:!1,internal:!0});return r.$=s,e[t]=r}function Xn(e,t){const n=Reflect.getOwnPropertyDescriptor(e,t);return!n||n.get||!n.configurable||t===k||t===z||(delete n.value,delete n.writable,n.get=()=>e[k][t]),n}function pt(e){Se()&&ee(fe(e,z),wt)()}function Yn(e){return pt(e),Reflect.ownKeys(e)}const Jn={get(e,t,n){if(t===Le)return e;if(t===k)return n;if(t===Ae)return pt(e),n;const r=fe(e,z),s=r[t];let o=s?s():e[t];if(t===z||t===T||t==="__proto__")return o;if(!s){const i=Object.getOwnPropertyDescriptor(e,t);Se()&&(typeof o!="function"||e.hasOwnProperty(t))&&!(i&&i.get)&&(o=ee(r,t,o)())}return ue(o)?bt(o):o},has(e,t){return t===Le||t===k||t===Ae||t===z||t===T||t==="__proto__"?!0:(Se()&&ee(fe(e,T),t)(),t in e)},set(){return console.warn("Cannot mutate a Store directly"),!0},deleteProperty(){return console.warn("Cannot mutate a Store directly"),!0},ownKeys:Yn,getOwnPropertyDescriptor:Xn};function de(e,t,n,r=!1){if(!r&&e[t]===n)return;const s=e[t],o=e.length;n===void 0?(delete e[t],e[T]&&e[T][t]&&s!==void 0&&e[T][t].$()):(e[t]=n,e[T]&&e[T][t]&&s===void 0&&e[T][t].$());let i=fe(e,z),l;if((l=ee(i,t,s))&&l.$(()=>n),Array.isArray(e)&&e.length!==o){for(let a=e.length;a<o;a++)(l=i[a])&&l.$();(l=ee(i,"length",o))&&l.$(e.length)}(l=i[wt])&&l.$()}function At(e,t){const n=Object.keys(t);for(let r=0;r<n.length;r+=1){const s=n[r];de(e,s,t[s])}}function Qn(e,t){if(typeof t=="function"&&(t=t(e)),t=Z(t),Array.isArray(t)){if(e===t)return;let n=0,r=t.length;for(;n<r;n++){const s=t[n];e[n]!==s&&de(e,n,s)}de(e,"length",r)}else At(e,t)}function X(e,t,n=[]){let r,s=e;if(t.length>1){r=t.shift();const i=typeof r,l=Array.isArray(e);if(Array.isArray(r)){for(let a=0;a<r.length;a++)X(e,[r[a]].concat(t),n);return}else if(l&&i==="function"){for(let a=0;a<e.length;a++)r(e[a],a)&&X(e,[a].concat(t),n);return}else if(l&&i==="object"){const{from:a=0,to:c=e.length-1,by:f=1}=r;for(let u=a;u<=c;u+=f)X(e,[u].concat(t),n);return}else if(t.length>1){X(e[r],t,[r].concat(n));return}s=e[r],n=[r].concat(n)}let o=t[0];typeof o=="function"&&(o=o(s,n),o===s)||r===void 0&&o==null||(o=Z(o),r===void 0||ue(s)&&ue(o)&&!Array.isArray(o)?At(s,o):de(e,r,o))}function dr(...[e,t]){const n=Z(e||{}),r=Array.isArray(n);if(typeof n!="object"&&typeof n!="function")throw new Error(`Unexpected type ${typeof n} received when initializing 'createStore'. Expected an object.`);const s=bt(n);Kt.registerGraph({value:n,name:t&&t.name});function o(...i){Qe(()=>{r&&i.length===1?Qn(n,i[0]):X(n,i)})}return[s,o]}export{rr as A,kn as B,fr as C,Kt as D,un as E,sr as F,ur as G,or as H,lr as P,cr as R,ot as S,lt as a,en as b,I as c,on as d,it as e,N as f,M as g,P as h,ve as i,dr as j,Zn as k,ln as l,Oe as m,ir as n,Y as o,le as p,Je as q,et as r,st as s,nn as t,Te as u,er as v,ar as w,xe as x,L as y,Re as z};
