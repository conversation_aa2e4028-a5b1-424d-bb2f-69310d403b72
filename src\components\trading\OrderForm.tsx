import { createSignal, createMemo, For } from 'solid-js'
import { css } from '../../../styled-system/css'
import { Button, Input, Card, Tag } from '../ui'

export interface OrderFormData {
  symbol: string
  side: 'buy' | 'sell'
  orderType: 'market' | 'limit' | 'stop'
  quantity: number
  price?: number
  stopPrice?: number
  timeInForce: 'GTC' | 'IOC' | 'FOK'
  stopLoss?: number
  takeProfit?: number
}

export interface OrderFormProps {
  symbol?: string
  currentPrice?: number
  onSubmit: (order: OrderFormData) => void
  onCancel: () => void
}

export function OrderForm(props: OrderFormProps) {
  const [formData, setFormData] = createSignal<Partial<OrderFormData>>({
    symbol: props.symbol || '',
    side: 'buy',
    orderType: 'limit',
    quantity: 0,
    timeInForce: 'GTC',
  })

  const [availableFunds] = createSignal(100000) // 模拟可用资金
  const [position] = createSignal(0) // 模拟持仓

  // 计算预估成交金额
  const estimatedAmount = createMemo(() => {
    const data = formData()
    const price = data.orderType === 'market' ? props.currentPrice : data.price
    return (data.quantity || 0) * (price || 0)
  })

  // 计算手续费
  const commission = createMemo(() => {
    return estimatedAmount() * 0.0003 // 万三手续费
  })

  // 验证订单
  const isValidOrder = createMemo(() => {
    const data = formData()
    if (!data.symbol || !data.quantity || data.quantity <= 0) return false
    if (data.orderType !== 'market' && (!data.price || data.price <= 0)) return false
    if (data.side === 'buy' && estimatedAmount() + commission() > availableFunds()) return false
    if (data.side === 'sell' && data.quantity > position()) return false
    return true
  })

  const updateField = (field: keyof OrderFormData, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }))
  }

  const handleSubmit = () => {
    if (!isValidOrder()) return
    props.onSubmit(formData() as OrderFormData)
  }

  const orderTypes = [
    { key: 'market', label: '市价单', desc: '立即成交' },
    { key: 'limit', label: '限价单', desc: '指定价格' },
    { key: 'stop', label: '止损单', desc: '触发价格' },
  ]

  const timeInForceOptions = [
    { key: 'GTC', label: '撤销前有效' },
    { key: 'IOC', label: '立即成交或撤销' },
    { key: 'FOK', label: '全部成交或撤销' },
  ]

  return (
    <Card header="下单交易" class={css({ width: '400px' })}>
      <div class={css({ padding: '16px', display: 'flex', flexDirection: 'column', gap: '16px' })}>
        {/* 股票信息 */}
        <div class={css({ display: 'flex', alignItems: 'center', gap: '12px' })}>
          <Input
            placeholder="股票代码"
            value={formData().symbol}
            onInput={(e) => updateField('symbol', e.currentTarget.value.toUpperCase())}
            class={css({ flex: 1 })}
          />
          {props.currentPrice && (
            <div class={css({ fontSize: '14px', color: 'text.secondary' })}>
              现价: ¥{props.currentPrice.toFixed(2)}
            </div>
          )}
        </div>

        {/* 买卖方向 */}
        <div class={css({ display: 'flex', gap: '8px' })}>
          <Button
            variant={formData().side === 'buy' ? 'danger' : 'default'}
            onClick={() => updateField('side', 'buy')}
            class={css({ flex: 1 })}
          >
            买入
          </Button>
          <Button
            variant={formData().side === 'sell' ? 'success' : 'default'}
            onClick={() => updateField('side', 'sell')}
            class={css({ flex: 1 })}
          >
            卖出
          </Button>
        </div>

        {/* 订单类型 */}
        <div>
          <div class={css({ fontSize: '14px', color: 'text.secondary', marginBottom: '8px' })}>
            订单类型
          </div>
          <div class={css({ display: 'flex', gap: '8px' })}>
            <For each={orderTypes}>
              {(type) => (
                <Button
                  size="small"
                  variant={formData().orderType === type.key ? 'primary' : 'default'}
                  onClick={() => updateField('orderType', type.key)}
                  title={type.desc}
                >
                  {type.label}
                </Button>
              )}
            </For>
          </div>
        </div>

        {/* 数量和价格 */}
        <div class={css({ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '12px' })}>
          <div>
            <div class={css({ fontSize: '14px', color: 'text.secondary', marginBottom: '4px' })}>
              数量 (股)
            </div>
            <Input
              type="number"
              placeholder="0"
              value={formData().quantity?.toString() || ''}
              onInput={(e) => updateField('quantity', parseInt(e.currentTarget.value) || 0)}
            />
          </div>
          
          {formData().orderType !== 'market' && (
            <div>
              <div class={css({ fontSize: '14px', color: 'text.secondary', marginBottom: '4px' })}>
                价格 (¥)
              </div>
              <Input
                type="number"
                step="0.01"
                placeholder="0.00"
                value={formData().price?.toString() || ''}
                onInput={(e) => updateField('price', parseFloat(e.currentTarget.value) || 0)}
              />
            </div>
          )}
        </div>

        {/* 止损止盈 */}
        <div class={css({ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '12px' })}>
          <div>
            <div class={css({ fontSize: '14px', color: 'text.secondary', marginBottom: '4px' })}>
              止损价 (可选)
            </div>
            <Input
              type="number"
              step="0.01"
              placeholder="0.00"
              value={formData().stopLoss?.toString() || ''}
              onInput={(e) => updateField('stopLoss', parseFloat(e.currentTarget.value) || undefined)}
            />
          </div>
          
          <div>
            <div class={css({ fontSize: '14px', color: 'text.secondary', marginBottom: '4px' })}>
              止盈价 (可选)
            </div>
            <Input
              type="number"
              step="0.01"
              placeholder="0.00"
              value={formData().takeProfit?.toString() || ''}
              onInput={(e) => updateField('takeProfit', parseFloat(e.currentTarget.value) || undefined)}
            />
          </div>
        </div>

        {/* 订单信息汇总 */}
        <div class={css({
          backgroundColor: 'bg.page',
          padding: '12px',
          borderRadius: '4px',
          fontSize: '12px',
        })}>
          <div class={css({ display: 'flex', justifyContent: 'space-between', marginBottom: '4px' })}>
            <span>预估成交金额:</span>
            <span class={css({ fontWeight: '600' })}>¥{estimatedAmount().toLocaleString()}</span>
          </div>
          <div class={css({ display: 'flex', justifyContent: 'space-between', marginBottom: '4px' })}>
            <span>预估手续费:</span>
            <span>¥{commission().toFixed(2)}</span>
          </div>
          <div class={css({ display: 'flex', justifyContent: 'space-between', marginBottom: '4px' })}>
            <span>可用资金:</span>
            <span>¥{availableFunds().toLocaleString()}</span>
          </div>
          {formData().side === 'sell' && (
            <div class={css({ display: 'flex', justifyContent: 'space-between' })}>
              <span>可卖数量:</span>
              <span>{position()} 股</span>
            </div>
          )}
        </div>

        {/* 操作按钮 */}
        <div class={css({ display: 'flex', gap: '12px' })}>
          <Button variant="default" onClick={props.onCancel} class={css({ flex: 1 })}>
            取消
          </Button>
          <Button
            variant={formData().side === 'buy' ? 'danger' : 'success'}
            onClick={handleSubmit}
            disabled={!isValidOrder()}
            class={css({ flex: 1 })}
          >
            确认{formData().side === 'buy' ? '买入' : '卖出'}
          </Button>
        </div>

        {/* 风险提示 */}
        {!isValidOrder() && (
          <div class={css({ fontSize: '12px', color: 'danger.500', textAlign: 'center' })}>
            {!formData().symbol && '请输入股票代码'}
            {!formData().quantity && '请输入交易数量'}
            {formData().side === 'buy' && estimatedAmount() + commission() > availableFunds() && '资金不足'}
            {formData().side === 'sell' && formData().quantity! > position() && '持仓不足'}
          </div>
        )}
      </div>
    </Card>
  )
}
