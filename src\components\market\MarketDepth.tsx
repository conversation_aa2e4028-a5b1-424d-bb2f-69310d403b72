import { createSignal, createMemo, For } from 'solid-js'
import { css } from '../../../styled-system/css'
import { Card } from '../ui'

export interface DepthLevel {
  price: number
  volume: number
  amount: number
}

export interface MarketDepthData {
  symbol: string
  bids: DepthLevel[] // 买盘
  asks: DepthLevel[] // 卖盘
  lastPrice: number
  change: number
  changePercent: number
  timestamp: Date
}

export interface MarketDepthProps {
  data: MarketDepthData
  levels?: number
  onPriceClick?: (price: number) => void
}

export function MarketDepth(props: MarketDepthProps) {
  const levels = () => props.levels || 5

  // 计算最大成交量用于绘制深度条
  const maxVolume = createMemo(() => {
    const allLevels = [...props.data.bids, ...props.data.asks]
    return Math.max(...allLevels.map(level => level.volume))
  })

  // 计算累计量
  const cumulativeBids = createMemo(() => {
    let cumulative = 0
    return props.data.bids.map(bid => {
      cumulative += bid.volume
      return { ...bid, cumulative }
    })
  })

  const cumulativeAsks = createMemo(() => {
    let cumulative = 0
    return props.data.asks.map(ask => {
      cumulative += ask.volume
      return { ...ask, cumulative }
    })
  })

  const formatVolume = (volume: number) => {
    if (volume >= 10000) {
      return `${(volume / 10000).toFixed(1)}万`
    }
    return volume.toLocaleString()
  }

  const formatAmount = (amount: number) => {
    if (amount >= 100000000) {
      return `${(amount / 100000000).toFixed(2)}亿`
    }
    if (amount >= 10000) {
      return `${(amount / 10000).toFixed(1)}万`
    }
    return amount.toLocaleString()
  }

  const getDepthBarWidth = (volume: number) => {
    return `${(volume / maxVolume()) * 100}%`
  }

  const handlePriceClick = (price: number) => {
    props.onPriceClick?.(price)
  }

  return (
    <Card header={`${props.data.symbol} 买卖盘口`} class={css({ width: '300px' })}>
      <div class={css({ padding: '8px' })}>
        {/* 当前价格信息 */}
        <div class={css({
          textAlign: 'center',
          padding: '12px',
          marginBottom: '12px',
          backgroundColor: 'bg.page',
          borderRadius: '4px',
        })}>
          <div class={css({
            fontSize: '20px',
            fontWeight: '600',
            fontFamily: 'monospace',
            color: props.data.change > 0 ? 'danger.500' : props.data.change < 0 ? 'success.500' : 'text.primary',
          })}>
            ¥{props.data.lastPrice.toFixed(2)}
          </div>
          <div class={css({
            fontSize: '12px',
            color: props.data.change > 0 ? 'danger.500' : props.data.change < 0 ? 'success.500' : 'text.secondary',
          })}>
            {props.data.change > 0 ? '+' : ''}{props.data.change.toFixed(2)} ({props.data.changePercent > 0 ? '+' : ''}{props.data.changePercent.toFixed(2)}%)
          </div>
        </div>

        {/* 表头 */}
        <div class={css({
          display: 'grid',
          gridTemplateColumns: '1fr 1fr 1fr',
          gap: '4px',
          padding: '4px 8px',
          fontSize: '12px',
          color: 'text.secondary',
          borderBottom: '1px solid',
          borderColor: 'border.lighter',
          marginBottom: '4px',
        })}>
          <div>价格</div>
          <div class={css({ textAlign: 'right' })}>数量</div>
          <div class={css({ textAlign: 'right' })}>累计</div>
        </div>

        {/* 卖盘 (从高到低) */}
        <div class={css({ marginBottom: '8px' })}>
          <For each={cumulativeAsks().slice(0, levels()).reverse()}>
            {(ask, index) => (
              <div
                class={css({
                  position: 'relative',
                  display: 'grid',
                  gridTemplateColumns: '1fr 1fr 1fr',
                  gap: '4px',
                  padding: '2px 8px',
                  fontSize: '12px',
                  cursor: 'pointer',
                  _hover: {
                    backgroundColor: 'success.50',
                  },
                })}
                onClick={() => handlePriceClick(ask.price)}
              >
                {/* 深度背景条 */}
                <div
                  class={css({
                    position: 'absolute',
                    top: 0,
                    right: 0,
                    bottom: 0,
                    backgroundColor: 'success.100',
                    opacity: 0.3,
                    zIndex: 0,
                  })}
                  style={{ width: getDepthBarWidth(ask.volume) }}
                />
                
                <div class={css({ position: 'relative', zIndex: 1, color: 'success.600', fontFamily: 'monospace' })}>
                  {ask.price.toFixed(2)}
                </div>
                <div class={css({ position: 'relative', zIndex: 1, textAlign: 'right' })}>
                  {formatVolume(ask.volume)}
                </div>
                <div class={css({ position: 'relative', zIndex: 1, textAlign: 'right', color: 'text.secondary' })}>
                  {formatVolume(ask.cumulative)}
                </div>
              </div>
            )}
          </For>
        </div>

        {/* 分隔线 */}
        <div class={css({
          height: '1px',
          backgroundColor: 'border.base',
          margin: '8px 0',
        })} />

        {/* 买盘 (从高到低) */}
        <div>
          <For each={cumulativeBids().slice(0, levels())}>
            {(bid, index) => (
              <div
                class={css({
                  position: 'relative',
                  display: 'grid',
                  gridTemplateColumns: '1fr 1fr 1fr',
                  gap: '4px',
                  padding: '2px 8px',
                  fontSize: '12px',
                  cursor: 'pointer',
                  _hover: {
                    backgroundColor: 'danger.50',
                  },
                })}
                onClick={() => handlePriceClick(bid.price)}
              >
                {/* 深度背景条 */}
                <div
                  class={css({
                    position: 'absolute',
                    top: 0,
                    right: 0,
                    bottom: 0,
                    backgroundColor: 'danger.100',
                    opacity: 0.3,
                    zIndex: 0,
                  })}
                  style={{ width: getDepthBarWidth(bid.volume) }}
                />
                
                <div class={css({ position: 'relative', zIndex: 1, color: 'danger.600', fontFamily: 'monospace' })}>
                  {bid.price.toFixed(2)}
                </div>
                <div class={css({ position: 'relative', zIndex: 1, textAlign: 'right' })}>
                  {formatVolume(bid.volume)}
                </div>
                <div class={css({ position: 'relative', zIndex: 1, textAlign: 'right', color: 'text.secondary' })}>
                  {formatVolume(bid.cumulative)}
                </div>
              </div>
            )}
          </For>
        </div>

        {/* 统计信息 */}
        <div class={css({
          marginTop: '12px',
          padding: '8px',
          backgroundColor: 'bg.page',
          borderRadius: '4px',
          fontSize: '11px',
          color: 'text.secondary',
        })}>
          <div class={css({ display: 'flex', justifyContent: 'space-between', marginBottom: '2px' })}>
            <span>买盘总量:</span>
            <span>{formatVolume(cumulativeBids()[cumulativeBids().length - 1]?.cumulative || 0)}</span>
          </div>
          <div class={css({ display: 'flex', justifyContent: 'space-between', marginBottom: '2px' })}>
            <span>卖盘总量:</span>
            <span>{formatVolume(cumulativeAsks()[cumulativeAsks().length - 1]?.cumulative || 0)}</span>
          </div>
          <div class={css({ display: 'flex', justifyContent: 'space-between' })}>
            <span>更新时间:</span>
            <span>{props.data.timestamp.toLocaleTimeString()}</span>
          </div>
        </div>
      </div>
    </Card>
  )
}
