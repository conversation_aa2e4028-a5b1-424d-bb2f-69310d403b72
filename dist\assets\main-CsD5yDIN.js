import{d as st,c as re,u as co,t as X,i as v,m as le,a as F,b as t,e as U,A as go,o as lt,f as ct,F as at,g as xo,h as uo,R as we,j as vo,r as fo}from"./vendor-solid-DG9tx6GZ.js";import{l as Kt}from"./vendor-editor-l7stcynF.js";(function(){const l=document.createElement("link").relList;if(l&&l.supports&&l.supports("modulepreload"))return;for(const r of document.querySelectorAll('link[rel="modulepreload"]'))s(r);new MutationObserver(r=>{for(const d of r)if(d.type==="childList")for(const u of d.addedNodes)u.tagName==="LINK"&&u.rel==="modulepreload"&&s(u)}).observe(document,{childList:!0,subtree:!0});function g(r){const d={};return r.integrity&&(d.integrity=r.integrity),r.referrerPolicy&&(d.referrerPolicy=r.referrerPolicy),r.crossOrigin==="use-credentials"?d.credentials="include":r.crossOrigin==="anonymous"?d.credentials="omit":d.credentials="same-origin",d}function s(r){if(r.ep)return;r.ep=!0;const d=g(r);fetch(r.href,d)}})();function dt(i){return typeof i=="object"&&i!=null&&!Array.isArray(i)}function po(i){return Object.fromEntries(Object.entries(i??{}).filter(([l,g])=>g!==void 0))}var bo=i=>i==="base";function ho(i){return i.slice().filter(l=>!bo(l))}function Yt(i){return String.fromCharCode(i+(i>25?39:97))}function mo(i){let l="",g;for(g=Math.abs(i);g>52;g=g/52|0)l=Yt(g%52)+l;return Yt(g%52)+l}function So(i,l){let g=l.length;for(;g;)i=i*33^l.charCodeAt(--g);return i}function Co(i){return mo(So(5381,i)>>>0)}var Qt=/\s*!(important)?/i;function yo(i){return typeof i=="string"?Qt.test(i):!1}function ko(i){return typeof i=="string"?i.replace(Qt,"").trim():i}function Jt(i){return typeof i=="string"?i.replaceAll(" ","_"):i}var gt=i=>{const l=new Map;return(...s)=>{const r=JSON.stringify(s);if(l.has(r))return l.get(r);const d=i(...s);return l.set(r,d),d}};function eo(...i){return i.filter(Boolean).reduce((g,s)=>(Object.keys(s).forEach(r=>{const d=g[r],u=s[r];dt(d)&&dt(u)?g[r]=eo(d,u):g[r]=u}),g),{})}var wo=i=>i!=null;function to(i,l,g={}){const{stop:s,getKey:r}=g;function d(u,p=[]){if(dt(u)||Array.isArray(u)){const m={};for(const[z,j]of Object.entries(u)){const b=r?.(z,j)??z,_=[...p,b];if(s?.(u,_))return l(u,p);const I=d(j,_);wo(I)&&(m[b]=I)}return m}return l(u,p)}return d(i)}function _o(i,l){return i.reduce((g,s,r)=>{const d=l[r];return s!=null&&(g[d]=s),g},{})}function oo(i,l,g=!0){const{utility:s,conditions:r}=l,{hasShorthand:d,resolveShorthand:u}=s;return to(i,p=>Array.isArray(p)?_o(p,r.breakpoints.keys):p,{stop:p=>Array.isArray(p),getKey:g?p=>d?u(p):p:void 0})}var zo={shift:i=>i,finalize:i=>i,breakpoints:{keys:[]}},Ro=i=>typeof i=="string"?i.replaceAll(/[\n\s]+/g," "):i;function Io(i){const{utility:l,hash:g,conditions:s=zo}=i,r=u=>[l.prefix,u].filter(Boolean).join("-"),d=(u,p)=>{let m;if(g){const z=[...s.finalize(u),p];m=r(l.toHash(z,Co))}else m=[...s.finalize(u),r(p)].join(":");return m};return gt(({base:u,...p}={})=>{const m=Object.assign(p,u),z=oo(m,i),j=new Set;return to(z,(b,_)=>{const I=yo(b);if(b==null)return;const[A,...R]=s.shift(_),K=ho(R),V=l.transform(A,ko(Ro(b)));let H=d(K,V.className);I&&(H=`${H}!`),j.add(H)}),Array.from(j).join(" ")})}function Ao(...i){return i.flat().filter(l=>dt(l)&&Object.keys(po(l)).length>0)}function To(i){function l(r){const d=Ao(...r);return d.length===1?d:d.map(u=>oo(u,i))}function g(...r){return eo(...l(r))}function s(...r){return Object.assign({},...l(r))}return{mergeCss:gt(g),assignCss:s}}var Bo=/([A-Z])/g,$o=/^ms-/,Wo=gt(i=>i.startsWith("--")?i:i.replace(Bo,"-$1").replace($o,"-ms-").toLowerCase()),Do="cm,mm,Q,in,pc,pt,px,em,ex,ch,rem,lh,rlh,vw,vh,vmin,vmax,vb,vi,svw,svh,lvw,lvh,dvw,dvh,cqw,cqh,cqi,cqb,cqmin,cqmax,%";`${Do.split(",").join("|")}`;const Po="_dark,_light,_hover,_focus,_focusWithin,_focusVisible,_disabled,_active,_visited,_target,_readOnly,_readWrite,_empty,_checked,_enabled,_expanded,_highlighted,_before,_after,_firstLetter,_firstLine,_marker,_selection,_file,_backdrop,_first,_last,_only,_even,_odd,_firstOfType,_lastOfType,_onlyOfType,_peerFocus,_peerHover,_peerActive,_peerFocusWithin,_peerFocusVisible,_peerDisabled,_peerChecked,_peerInvalid,_peerExpanded,_peerPlaceholderShown,_groupFocus,_groupHover,_groupActive,_groupFocusWithin,_groupFocusVisible,_groupDisabled,_groupChecked,_groupExpanded,_groupInvalid,_indeterminate,_required,_valid,_invalid,_autofill,_inRange,_outOfRange,_placeholder,_placeholderShown,_pressed,_selected,_default,_optional,_open,_closed,_fullscreen,_loading,_currentPage,_currentStep,_motionReduce,_motionSafe,_print,_landscape,_portrait,_osDark,_osLight,_highContrast,_lessContrast,_moreContrast,_ltr,_rtl,_scrollbar,_scrollbarThumb,_scrollbarTrack,_horizontal,_vertical,_starting,sm,smOnly,smDown,md,mdOnly,mdDown,lg,lgOnly,lgDown,xl,xlOnly,xlDown,2xl,2xlOnly,2xlDown,smToMd,smToLg,smToXl,smTo2xl,mdToLg,mdToXl,mdTo2xl,lgToXl,lgTo2xl,xlTo2xl,@/xs,@/sm,@/md,@/lg,@/xl,@/2xl,@/3xl,@/4xl,@/5xl,@/6xl,@/7xl,@/8xl,base",io=new Set(Po.split(","));function qt(i){return io.has(i)||/^@|&|&$/.test(i)}const Eo=/^_/,Mo=/&|@/;function Lo(i){return i.map(l=>io.has(l)?l.replace(Eo,""):Mo.test(l)?`[${Jt(l.trim())}]`:l)}function jo(i){return i.sort((l,g)=>{const s=qt(l),r=qt(g);return s&&!r?1:!s&&r?-1:0})}const Oo="aspectRatio:aspect,boxDecorationBreak:decoration,zIndex:z,boxSizing:box,objectPosition:obj-pos,objectFit:obj-fit,overscrollBehavior:overscroll,overscrollBehaviorX:overscroll-x,overscrollBehaviorY:overscroll-y,position:pos/1,top:top,left:left,insetInline:inset-x/insetX,insetBlock:inset-y/insetY,inset:inset,insetBlockEnd:inset-b,insetBlockStart:inset-t,insetInlineEnd:end/insetEnd/1,insetInlineStart:start/insetStart/1,right:right,bottom:bottom,float:float,visibility:vis,display:d,hideFrom:hide,hideBelow:show,flexBasis:basis,flex:flex,flexDirection:flex/flexDir,flexGrow:grow,flexShrink:shrink,gridTemplateColumns:grid-cols,gridTemplateRows:grid-rows,gridColumn:col-span,gridRow:row-span,gridColumnStart:col-start,gridColumnEnd:col-end,gridAutoFlow:grid-flow,gridAutoColumns:auto-cols,gridAutoRows:auto-rows,gap:gap,gridGap:gap,gridRowGap:gap-x,gridColumnGap:gap-y,rowGap:gap-x,columnGap:gap-y,justifyContent:justify,alignContent:content,alignItems:items,alignSelf:self,padding:p/1,paddingLeft:pl/1,paddingRight:pr/1,paddingTop:pt/1,paddingBottom:pb/1,paddingBlock:py/1/paddingY,paddingBlockEnd:pb,paddingBlockStart:pt,paddingInline:px/paddingX/1,paddingInlineEnd:pe/1/paddingEnd,paddingInlineStart:ps/1/paddingStart,marginLeft:ml/1,marginRight:mr/1,marginTop:mt/1,marginBottom:mb/1,margin:m/1,marginBlock:my/1/marginY,marginBlockEnd:mb,marginBlockStart:mt,marginInline:mx/1/marginX,marginInlineEnd:me/1/marginEnd,marginInlineStart:ms/1/marginStart,spaceX:space-x,spaceY:space-y,outlineWidth:ring-width/ringWidth,outlineColor:ring-color/ringColor,outline:ring/1,outlineOffset:ring-offset/ringOffset,divideX:divide-x,divideY:divide-y,divideColor:divide-color,divideStyle:divide-style,width:w/1,inlineSize:w,minWidth:min-w/minW,minInlineSize:min-w,maxWidth:max-w/maxW,maxInlineSize:max-w,height:h/1,blockSize:h,minHeight:min-h/minH,minBlockSize:min-h,maxHeight:max-h/maxH,maxBlockSize:max-b,color:text,fontFamily:font,fontSize:fs,fontWeight:fw,fontSmoothing:smoothing,fontVariantNumeric:numeric,letterSpacing:tracking,lineHeight:leading,textAlign:text-align,textDecoration:text-decor,textDecorationColor:text-decor-color,textEmphasisColor:text-emphasis-color,textDecorationStyle:decoration-style,textDecorationThickness:decoration-thickness,textUnderlineOffset:underline-offset,textTransform:text-transform,textIndent:indent,textShadow:text-shadow,textShadowColor:text-shadow/textShadowColor,textOverflow:text-overflow,verticalAlign:v-align,wordBreak:break,textWrap:text-wrap,truncate:truncate,lineClamp:clamp,listStyleType:list-type,listStylePosition:list-pos,listStyleImage:list-img,backgroundPosition:bg-pos/bgPosition,backgroundPositionX:bg-pos-x/bgPositionX,backgroundPositionY:bg-pos-y/bgPositionY,backgroundAttachment:bg-attach/bgAttachment,backgroundClip:bg-clip/bgClip,background:bg/1,backgroundColor:bg/bgColor,backgroundOrigin:bg-origin/bgOrigin,backgroundImage:bg-img/bgImage,backgroundRepeat:bg-repeat/bgRepeat,backgroundBlendMode:bg-blend/bgBlendMode,backgroundSize:bg-size/bgSize,backgroundGradient:bg-gradient/bgGradient,textGradient:text-gradient,gradientFromPosition:gradient-from-pos,gradientToPosition:gradient-to-pos,gradientFrom:gradient-from,gradientTo:gradient-to,gradientVia:gradient-via,gradientViaPosition:gradient-via-pos,borderRadius:rounded/1,borderTopLeftRadius:rounded-tl/roundedTopLeft,borderTopRightRadius:rounded-tr/roundedTopRight,borderBottomRightRadius:rounded-br/roundedBottomRight,borderBottomLeftRadius:rounded-bl/roundedBottomLeft,borderTopRadius:rounded-t/roundedTop,borderRightRadius:rounded-r/roundedRight,borderBottomRadius:rounded-b/roundedBottom,borderLeftRadius:rounded-l/roundedLeft,borderStartStartRadius:rounded-ss/roundedStartStart,borderStartEndRadius:rounded-se/roundedStartEnd,borderStartRadius:rounded-s/roundedStart,borderEndStartRadius:rounded-es/roundedEndStart,borderEndEndRadius:rounded-ee/roundedEndEnd,borderEndRadius:rounded-e/roundedEnd,border:border,borderWidth:border-w,borderTopWidth:border-tw,borderLeftWidth:border-lw,borderRightWidth:border-rw,borderBottomWidth:border-bw,borderColor:border,borderInline:border-x/borderX,borderInlineWidth:border-x/borderXWidth,borderInlineColor:border-x/borderXColor,borderBlock:border-y/borderY,borderBlockWidth:border-y/borderYWidth,borderBlockColor:border-y/borderYColor,borderLeft:border-l,borderLeftColor:border-l,borderInlineStart:border-s/borderStart,borderInlineStartWidth:border-s/borderStartWidth,borderInlineStartColor:border-s/borderStartColor,borderRight:border-r,borderRightColor:border-r,borderInlineEnd:border-e/borderEnd,borderInlineEndWidth:border-e/borderEndWidth,borderInlineEndColor:border-e/borderEndColor,borderTop:border-t,borderTopColor:border-t,borderBottom:border-b,borderBottomColor:border-b,borderBlockEnd:border-be,borderBlockEndColor:border-be,borderBlockStart:border-bs,borderBlockStartColor:border-bs,boxShadow:shadow/1,boxShadowColor:shadow-color/shadowColor,mixBlendMode:mix-blend,filter:filter,brightness:brightness,contrast:contrast,grayscale:grayscale,hueRotate:hue-rotate,invert:invert,saturate:saturate,sepia:sepia,dropShadow:drop-shadow,blur:blur,backdropFilter:backdrop,backdropBlur:backdrop-blur,backdropBrightness:backdrop-brightness,backdropContrast:backdrop-contrast,backdropGrayscale:backdrop-grayscale,backdropHueRotate:backdrop-hue-rotate,backdropInvert:backdrop-invert,backdropOpacity:backdrop-opacity,backdropSaturate:backdrop-saturate,backdropSepia:backdrop-sepia,borderCollapse:border,borderSpacing:border-spacing,borderSpacingX:border-spacing-x,borderSpacingY:border-spacing-y,tableLayout:table,transitionTimingFunction:ease,transitionDelay:delay,transitionDuration:duration,transitionProperty:transition-prop,transition:transition,animation:animation,animationName:animation-name,animationTimingFunction:animation-ease,animationDuration:animation-duration,animationDelay:animation-delay,transformOrigin:origin,rotate:rotate,rotateX:rotate-x,rotateY:rotate-y,rotateZ:rotate-z,scale:scale,scaleX:scale-x,scaleY:scale-y,translate:translate,translateX:translate-x/x,translateY:translate-y/y,translateZ:translate-z/z,accentColor:accent,caretColor:caret,scrollBehavior:scroll,scrollbar:scrollbar,scrollMargin:scroll-m,scrollMarginLeft:scroll-ml,scrollMarginRight:scroll-mr,scrollMarginTop:scroll-mt,scrollMarginBottom:scroll-mb,scrollMarginBlock:scroll-my/scrollMarginY,scrollMarginBlockEnd:scroll-mb,scrollMarginBlockStart:scroll-mt,scrollMarginInline:scroll-mx/scrollMarginX,scrollMarginInlineEnd:scroll-me,scrollMarginInlineStart:scroll-ms,scrollPadding:scroll-p,scrollPaddingBlock:scroll-pb/scrollPaddingY,scrollPaddingBlockStart:scroll-pt,scrollPaddingBlockEnd:scroll-pb,scrollPaddingInline:scroll-px/scrollPaddingX,scrollPaddingInlineEnd:scroll-pe,scrollPaddingInlineStart:scroll-ps,scrollPaddingLeft:scroll-pl,scrollPaddingRight:scroll-pr,scrollPaddingTop:scroll-pt,scrollPaddingBottom:scroll-pb,scrollSnapAlign:snap-align,scrollSnapStop:snap-stop,scrollSnapType:snap-type,scrollSnapStrictness:snap-strictness,scrollSnapMargin:snap-m,scrollSnapMarginTop:snap-mt,scrollSnapMarginBottom:snap-mb,scrollSnapMarginLeft:snap-ml,scrollSnapMarginRight:snap-mr,touchAction:touch,userSelect:select,fill:fill,stroke:stroke,strokeWidth:stroke-w,srOnly:sr,debug:debug,appearance:appearance,backfaceVisibility:backface,clipPath:clip-path,hyphens:hyphens,mask:mask,maskImage:mask-image,maskSize:mask-size,textSizeAdjust:text-adjust,container:cq,containerName:cq-name,containerType:cq-type,textStyle:textStyle",no=new Map,ro=new Map;Oo.split(",").forEach(i=>{const[l,g]=i.split(":"),[s,...r]=g.split("/");no.set(l,s),r.length&&r.forEach(d=>{ro.set(d==="1"?s:d,l)})});const Vt=i=>ro.get(i)||i,lo={conditions:{shift:jo,finalize:Lo,breakpoints:{keys:["base","sm","md","lg","xl","2xl"]}},utility:{transform:(i,l)=>{const g=Vt(i);return{className:`${no.get(g)||Wo(g)}_${Jt(l)}`}},hasShorthand:!0,toHash:(i,l)=>l(i.join(":")),resolveShorthand:Vt}},Fo=Io(lo),e=(...i)=>Fo(ao(...i));e.raw=(...i)=>ao(...i);const{mergeCss:ao}=To(lo);var Ho=X("<div><aside><div><div>量</div></div><nav></nav><div><button></button></div></aside><main><header><div><h1></h1></div><div><button>帮助</button><button>设置</button><div>用</div></div></header><div>"),No=X("<span>量化交易平台"),Gt=X("<span>");function Xo(i){const[l,g]=re(!1),s=co(),r=[{id:"dashboard",label:"仪表板",icon:"📊",path:"/dashboard"},{id:"market",label:"行情分析",icon:"📈",path:"/market"},{id:"strategy-editor",label:"策略编辑器",icon:"🧠",path:"/strategy-editor"},{id:"api-test",label:"API测试",icon:"🔧",path:"/api-test"}],d=p=>s.pathname===p||p==="/dashboard"&&s.pathname==="/",u=()=>r.find(m=>d(m.path))?.label||"仪表板";return(()=>{var p=Ho(),m=p.firstChild,z=m.firstChild,j=z.firstChild,b=z.nextSibling,_=b.nextSibling,I=_.firstChild,A=m.nextSibling,R=A.firstChild,K=R.firstChild,V=K.firstChild,H=K.nextSibling,Y=H.firstChild,x=Y.nextSibling,D=x.nextSibling,T=R.nextSibling;return v(z,(()=>{var c=le(()=>!l());return()=>c()&&(()=>{var h=No();return F(()=>t(h,e({fontSize:"16px",fontWeight:"600",color:"#262626"}))),h})()})(),null),v(b,()=>r.map(c=>U(go,{get href(){return c.path},get class(){return e({width:"100%",padding:l()?"12px 20px":"12px 16px",border:"none",backgroundColor:d(c.path)?"#e6f7ff":"transparent",color:d(c.path)?"#1890ff":"#595959",fontSize:"14px",textDecoration:"none",cursor:"pointer",transition:"all 0.2s",display:"flex",alignItems:"center",gap:"12px",borderLeft:d(c.path)?"3px solid #1890ff":"3px solid transparent",_hover:{backgroundColor:"#f5f5f5",color:"#1890ff"}})},get children(){return[(()=>{var h=Gt();return v(h,()=>c.icon),F(()=>t(h,e({fontSize:"16px"}))),h})(),le(()=>le(()=>!l())()&&(()=>{var h=Gt();return v(h,()=>c.label),F(()=>t(h,e({fontWeight:d(c.path)?"500":"400"}))),h})())]}}))),I.$$click=()=>g(!l()),v(I,()=>l()?"→":"←"),v(V,u),v(T,()=>i.children),F(c=>{var h=e({display:"flex",minHeight:"100vh",backgroundColor:"#f5f5f5"}),B=e({width:l()?"64px":"240px",backgroundColor:"white",borderRight:"1px solid #e8e8e8",transition:"width 0.3s ease",display:"flex",flexDirection:"column",position:"fixed",height:"100vh",zIndex:1e3}),y=e({padding:"16px",borderBottom:"1px solid #e8e8e8",display:"flex",alignItems:"center",gap:"12px"}),P=e({width:"32px",height:"32px",backgroundColor:"#1890ff",borderRadius:"6px",display:"flex",alignItems:"center",justifyContent:"center",color:"white",fontSize:"16px",fontWeight:"bold"}),a=e({flex:1,padding:"8px 0",overflowY:"auto"}),k=e({padding:"16px",borderTop:"1px solid #e8e8e8"}),w=e({width:"100%",padding:"8px",border:"1px solid #d9d9d9",backgroundColor:"white",borderRadius:"4px",cursor:"pointer",fontSize:"12px",color:"#595959",_hover:{borderColor:"#1890ff",color:"#1890ff"}}),f=e({flex:1,marginLeft:l()?"64px":"240px",transition:"margin-left 0.3s ease",display:"flex",flexDirection:"column"}),S=e({backgroundColor:"white",borderBottom:"1px solid #e8e8e8",padding:"0 24px",height:"64px",display:"flex",alignItems:"center",justifyContent:"space-between"}),W=e({display:"flex",alignItems:"center",gap:"16px"}),E=e({fontSize:"18px",fontWeight:"500",color:"#262626",margin:0}),L=e({display:"flex",alignItems:"center",gap:"16px"}),N=e({padding:"6px 12px",border:"1px solid #d9d9d9",backgroundColor:"white",borderRadius:"4px",fontSize:"14px",cursor:"pointer",_hover:{borderColor:"#1890ff",color:"#1890ff"}}),n=e({padding:"6px 12px",border:"1px solid #d9d9d9",backgroundColor:"white",borderRadius:"4px",fontSize:"14px",cursor:"pointer",_hover:{borderColor:"#1890ff",color:"#1890ff"}}),$=e({width:"32px",height:"32px",backgroundColor:"#1890ff",borderRadius:"50%",display:"flex",alignItems:"center",justifyContent:"center",color:"white",fontSize:"14px",fontWeight:"500"}),M=e({flex:1,backgroundColor:"#f5f5f5",overflow:"auto"});return h!==c.e&&t(p,c.e=h),B!==c.t&&t(m,c.t=B),y!==c.a&&t(z,c.a=y),P!==c.o&&t(j,c.o=P),a!==c.i&&t(b,c.i=a),k!==c.n&&t(_,c.n=k),w!==c.s&&t(I,c.s=w),f!==c.h&&t(A,c.h=f),S!==c.r&&t(R,c.r=S),W!==c.d&&t(K,c.d=W),E!==c.l&&t(V,c.l=E),L!==c.u&&t(H,c.u=L),N!==c.c&&t(Y,c.c=N),n!==c.w&&t(x,c.w=n),$!==c.m&&t(D,c.m=$),M!==c.f&&t(T,c.f=M),c},{e:void 0,t:void 0,a:void 0,o:void 0,i:void 0,n:void 0,s:void 0,h:void 0,r:void 0,d:void 0,l:void 0,u:void 0,c:void 0,w:void 0,m:void 0,f:void 0}),p})()}st(["click"]);var Ko=X("<div><div></div><div><div><div><h3>今日行情</h3><span>实时更新</span></div><div></div></div><div><div><h3>最新资讯</h3><button type=button>查看更多</button></div><div></div></div></div><div>当前时间: "),Yo=X("<div><div></div><div><div></div><div><span></span></div></div><div><div></div><div></div></div><div><span>📊"),qo=X("<div><div><div></div><span></span></div><div><div></div><div><span></span><span>(<!>)"),Vo=X("<div><div></div><div><span></span><span>");function Ut(){const[i,l]=re(new Date().toLocaleString("zh-CN"));let g;lt(()=>{g=setInterval(()=>{l(new Date().toLocaleString("zh-CN"))},1e3)}),ct(()=>{g&&clearInterval(g)});const s=[{title:"总资产",value:"¥1,234,567.89",change:"+12.34%",trend:"up",icon:"💰",description:"较昨日增长"},{title:"今日收益",value:"¥8,765.43",change:"+2.15%",trend:"up",icon:"📈",description:"实时盈亏"},{title:"持仓市值",value:"¥987,654.32",change:"-0.87%",trend:"down",icon:"📊",description:"当前持仓"},{title:"可用资金",value:"¥246,913.57",change:"+5.67%",trend:"up",icon:"💳",description:"可投资金额"}],r=[{name:"A股指数",value:"3,245.67",change:"+23.45",percent:"+0.73%",trend:"up"},{name:"创业板",value:"2,156.89",change:"-12.34",percent:"-0.57%",trend:"down"},{name:"科创50",value:"1,234.56",change:"+45.67",percent:"+3.84%",trend:"up"},{name:"沪深300",value:"4,567.89",change:"+78.90",percent:"+1.76%",trend:"up"}],d=[{title:"A股市场今日表现强劲，科技股领涨",time:"10分钟前",type:"market"},{title:"央行宣布降准0.25个百分点",time:"30分钟前",type:"policy"},{title:"新能源板块持续活跃，多只个股涨停",time:"1小时前",type:"sector"},{title:"外资持续流入A股市场",time:"2小时前",type:"capital"}];return(()=>{var u=Ko(),p=u.firstChild,m=p.nextSibling,z=m.firstChild,j=z.firstChild,b=j.firstChild,_=b.nextSibling,I=j.nextSibling,A=z.nextSibling,R=A.firstChild,K=R.firstChild,V=K.nextSibling,H=R.nextSibling,Y=m.nextSibling;return Y.firstChild,v(p,U(at,{each:s,children:x=>(()=>{var D=Yo(),T=D.firstChild,c=T.nextSibling,h=c.firstChild,B=h.nextSibling,y=B.firstChild,P=c.nextSibling,a=P.firstChild,k=a.nextSibling,w=P.nextSibling;return w.firstChild,v(h,()=>x.icon),v(y,()=>x.trend==="up"?"↗":"↘"),v(B,()=>x.change,null),v(a,()=>x.value),v(k,()=>x.title),v(w,()=>x.description,null),F(f=>{var S=e({bg:"white",borderRadius:"16px",p:"24px",boxShadow:"0 4px 20px rgba(0,0,0,0.08)",border:"1px solid #f0f0f0",position:"relative",overflow:"hidden",transition:"all 0.3s cubic-bezier(0.4, 0, 0.2, 1)",_hover:{boxShadow:"0 8px 30px rgba(0,0,0,0.12)",transform:"translateY(-4px)"}}),W=e({position:"absolute",top:"-20px",right:"-20px",width:"80px",height:"80px",borderRadius:"50%",background:x.trend==="up"?"linear-gradient(135deg, rgba(82, 196, 26, 0.1), rgba(82, 196, 26, 0.05))":"linear-gradient(135deg, rgba(245, 34, 45, 0.1), rgba(245, 34, 45, 0.05))",opacity:.6}),E=e({display:"flex",alignItems:"flex-start",justifyContent:"space-between",mb:"16px"}),L=e({fontSize:"32px",lineHeight:1}),N=e({display:"flex",alignItems:"center",gap:"4px",padding:"4px 8px",borderRadius:"12px",fontSize:"12px",fontWeight:"600",backgroundColor:x.trend==="up"?"#f6ffed":"#fff2f0",color:x.trend==="up"?"#52c41a":"#f5222d"}),n=e({mb:"8px"}),$=e({fontSize:"28px",fontWeight:"700",color:"#262626",lineHeight:1.2,mb:"4px"}),M=e({fontSize:"14px",color:"#8c8c8c",fontWeight:"500"}),G=e({fontSize:"12px",color:"#8c8c8c",display:"flex",alignItems:"center",gap:"4px"});return S!==f.e&&t(D,f.e=S),W!==f.t&&t(T,f.t=W),E!==f.a&&t(c,f.a=E),L!==f.o&&t(h,f.o=L),N!==f.i&&t(B,f.i=N),n!==f.n&&t(P,f.n=n),$!==f.s&&t(a,f.s=$),M!==f.h&&t(k,f.h=M),G!==f.r&&t(w,f.r=G),f},{e:void 0,t:void 0,a:void 0,o:void 0,i:void 0,n:void 0,s:void 0,h:void 0,r:void 0}),D})()})),v(I,U(at,{each:r,children:x=>(()=>{var D=qo(),T=D.firstChild,c=T.firstChild,h=c.nextSibling,B=T.nextSibling,y=B.firstChild,P=y.nextSibling,a=P.firstChild,k=a.nextSibling,w=k.firstChild,f=w.nextSibling;return f.nextSibling,v(h,()=>x.name),v(y,()=>x.value),v(a,()=>x.change),v(k,()=>x.percent,f),F(S=>{var W=e({display:"flex",alignItems:"center",justifyContent:"space-between",padding:"12px",borderRadius:"8px",bg:"#fafafa",transition:"all 0.2s ease",cursor:"pointer",_hover:{bg:"#f0f0f0"}}),E=e({display:"flex",alignItems:"center",gap:"12px"}),L=e({width:"8px",height:"8px",borderRadius:"50%",backgroundColor:x.trend==="up"?"#52c41a":"#f5222d"}),N=e({fontSize:"14px",fontWeight:"500",color:"#262626"}),n=e({textAlign:"right"}),$=e({fontSize:"14px",fontWeight:"600",color:"#262626",mb:"2px"}),M=e({fontSize:"12px",color:x.trend==="up"?"#52c41a":"#f5222d",display:"flex",alignItems:"center",gap:"4px",justifyContent:"flex-end"});return W!==S.e&&t(D,S.e=W),E!==S.t&&t(T,S.t=E),L!==S.a&&t(c,S.a=L),N!==S.o&&t(h,S.o=N),n!==S.i&&t(B,S.i=n),$!==S.n&&t(y,S.n=$),M!==S.s&&t(P,S.s=M),S},{e:void 0,t:void 0,a:void 0,o:void 0,i:void 0,n:void 0,s:void 0}),D})()})),v(H,U(at,{each:d,children:x=>(()=>{var D=Vo(),T=D.firstChild,c=T.nextSibling,h=c.firstChild,B=h.nextSibling;return v(T,()=>x.title),v(h,()=>x.time),v(B,()=>x.type),F(y=>{var P=e({p:"12px",borderRadius:"8px",bg:"#fafafa",transition:"all 0.2s ease",cursor:"pointer",_hover:{bg:"#f0f0f0"}}),a=e({fontSize:"14px",fontWeight:"500",color:"#262626",mb:"8px",lineHeight:"1.4"}),k=e({display:"flex",alignItems:"center",justifyContent:"space-between"}),w=e({fontSize:"12px",color:"#8c8c8c"}),f=e({fontSize:"10px",color:"#1890ff",bg:"#e6f7ff",px:"6px",py:"2px",borderRadius:"4px"});return P!==y.e&&t(D,y.e=P),a!==y.t&&t(T,y.t=a),k!==y.a&&t(c,y.a=k),w!==y.o&&t(h,y.o=w),f!==y.i&&t(B,y.i=f),y},{e:void 0,t:void 0,a:void 0,o:void 0,i:void 0}),D})()})),v(Y,i,null),F(x=>{var D=e({display:"flex",flexDirection:"column",gap:"24px",width:"100%",padding:"24px",backgroundColor:"#f5f5f5",minHeight:"100%"}),T=e({display:"grid",gridTemplateColumns:"repeat(auto-fit, minmax(280px, 1fr))",gap:"20px"}),c=e({display:"grid",gridTemplateColumns:"1fr 1fr",gap:"24px"}),h=e({bg:"white",borderRadius:"16px",p:"24px",boxShadow:"0 4px 20px rgba(0,0,0,0.08)",border:"1px solid #f0f0f0"}),B=e({display:"flex",alignItems:"center",justifyContent:"space-between",mb:"20px"}),y=e({fontSize:"18px",fontWeight:"600",color:"#262626",margin:0}),P=e({fontSize:"12px",color:"#8c8c8c",bg:"#f5f5f5",px:"8px",py:"4px",borderRadius:"4px"}),a=e({display:"flex",flexDirection:"column",gap:"12px"}),k=e({bg:"white",borderRadius:"16px",p:"24px",boxShadow:"0 4px 20px rgba(0,0,0,0.08)",border:"1px solid #f0f0f0"}),w=e({display:"flex",alignItems:"center",justifyContent:"space-between",mb:"20px"}),f=e({fontSize:"18px",fontWeight:"600",color:"#262626",margin:0}),S=e({fontSize:"12px",color:"#1890ff",bg:"transparent",border:"none",cursor:"pointer",_hover:{textDecoration:"underline"}}),W=e({display:"flex",flexDirection:"column",gap:"16px"}),E=e({textAlign:"center",fontSize:"12px",color:"#8c8c8c",mt:"16px"});return D!==x.e&&t(u,x.e=D),T!==x.t&&t(p,x.t=T),c!==x.a&&t(m,x.a=c),h!==x.o&&t(z,x.o=h),B!==x.i&&t(j,x.i=B),y!==x.n&&t(b,x.n=y),P!==x.s&&t(_,x.s=P),a!==x.h&&t(I,x.h=a),k!==x.r&&t(A,x.r=k),w!==x.d&&t(R,x.d=w),f!==x.l&&t(K,x.l=f),S!==x.u&&t(V,x.u=S),W!==x.c&&t(H,x.c=W),E!==x.w&&t(Y,x.w=E),x},{e:void 0,t:void 0,a:void 0,o:void 0,i:void 0,n:void 0,s:void 0,h:void 0,r:void 0,d:void 0,l:void 0,u:void 0,c:void 0,w:void 0}),u})()}var Go=X("<div><div><h1>🔧 API 连接测试</h1><p>测试前端与后端API的连接状态</p></div><div><button>开始测试</button></div><div><div><h2>测试结果</h2></div><div></div></div><div><div><h2>配置信息</h2></div><div><div><div><h3>API Base URL</h3><p></p></div><div><h3>环境模式</h3><p></p></div><div><h3>代理配置</h3><p>/api → localhost:8000"),Uo=X('<p>点击"开始测试"按钮运行API连接测试'),Zo=X("<div>"),Qo=X("<div><div><div></div><div><h3></h3><p>"),Jo=X("<div>ms");const nt={get:async(i,l)=>{const g=l?"?"+new URLSearchParams(l).toString():"",r=await fetch("https://api.yourdomain.com"+i+g);if(!r.ok)throw new Error(`HTTP ${r.status}`);return r.json().catch(()=>({}))}},rt={SYSTEM:{HEALTH:"/v1/health"},MARKET:{OVERVIEW:"/v1/market/overview",SEARCH:"/v1/market/search"},AUTH:{PROFILE:"/v1/auth/profile"}};function ei(){const[i,l]=re([]),g=(r,d,u,p)=>{l(m=>[...m,{name:r,status:d,message:u,duration:p}])},s=async()=>{l([]);try{const r=Date.now();await nt.get(rt.SYSTEM.HEALTH);const d=Date.now()-r;g("系统健康检查","success","连接成功",d)}catch(r){g("系统健康检查","error",r.message||"连接失败")}try{const r=Date.now();await nt.get(rt.MARKET.OVERVIEW);const d=Date.now()-r;g("市场概览","success","数据获取成功",d)}catch(r){g("市场概览","error",r.message||"数据获取失败")}try{const r=Date.now();await nt.get(rt.MARKET.SEARCH,{q:"AAPL"});const d=Date.now()-r;g("股票搜索","success","搜索成功",d)}catch(r){g("股票搜索","error",r.message||"搜索失败")}try{const r=Date.now();await nt.get(rt.AUTH.PROFILE);const d=Date.now()-r;g("用户信息","success","获取成功",d)}catch(r){g("用户信息","error",r.message||"获取失败（预期，因为未登录）")}};return lt(()=>{console.log("ApiTest mounted")}),(()=>{var r=Go(),d=r.firstChild,u=d.firstChild,p=u.nextSibling,m=d.nextSibling,z=m.firstChild,j=m.nextSibling,b=j.firstChild,_=b.firstChild,I=b.nextSibling,A=j.nextSibling,R=A.firstChild,K=R.firstChild,V=R.nextSibling,H=V.firstChild,Y=H.firstChild,x=Y.firstChild,D=x.nextSibling,T=Y.nextSibling,c=T.firstChild,h=c.nextSibling,B=T.nextSibling,y=B.firstChild,P=y.nextSibling;return z.$$click=s,v(I,(()=>{var a=le(()=>i().length===0);return()=>a()?(()=>{var k=Uo();return F(()=>t(k,e({color:"gray.500",textAlign:"center",padding:"40px 0"}))),k})():(()=>{var k=Zo();return v(k,()=>i().map(w=>(()=>{var f=Qo(),S=f.firstChild,W=S.firstChild,E=W.nextSibling,L=E.firstChild,N=L.nextSibling;return v(W,()=>w.status==="success"?"✅":"❌"),v(L,()=>w.name),v(N,()=>w.message),v(f,(()=>{var n=le(()=>!!w.duration);return()=>n()&&(()=>{var $=Jo(),M=$.firstChild;return v($,()=>w.duration,M),F(()=>t($,e({fontSize:"12px",color:"gray.500",backgroundColor:"gray.100",padding:"4px 8px",borderRadius:"4px"}))),$})()})(),null),F(n=>{var $=e({display:"flex",alignItems:"center",justifyContent:"space-between",padding:"16px",borderRadius:"8px",border:"1px solid",borderColor:w.status==="success"?"green.200":"red.200",backgroundColor:w.status==="success"?"green.50":"red.50"}),M=e({display:"flex",alignItems:"center",gap:"12px"}),G=e({fontSize:"20px"}),Z=e({fontSize:"16px",fontWeight:"600",color:"gray.900",marginBottom:"4px"}),Q=e({fontSize:"14px",color:"gray.600"});return $!==n.e&&t(f,n.e=$),M!==n.t&&t(S,n.t=M),G!==n.a&&t(W,n.a=G),Z!==n.o&&t(L,n.o=Z),Q!==n.i&&t(N,n.i=Q),n},{e:void 0,t:void 0,a:void 0,o:void 0,i:void 0}),f})())),F(()=>t(k,e({display:"flex",flexDirection:"column",gap:"16px"}))),k})()})()),v(D,()=>"https://api.yourdomain.com"),v(h,()=>"production"),F(a=>{var k=e({padding:"24px",maxWidth:"1200px",margin:"0 auto"}),w=e({marginBottom:"32px"}),f=e({fontSize:"32px",fontWeight:"bold",color:"gray.900",marginBottom:"8px"}),S=e({fontSize:"16px",color:"gray.600"}),W=e({marginBottom:"32px"}),E=e({padding:"12px 24px",backgroundColor:"blue.600",color:"white",border:"none",borderRadius:"8px",fontSize:"16px",fontWeight:"500",cursor:"pointer",transition:"all 0.2s",_hover:{backgroundColor:"blue.700"}}),L=e({backgroundColor:"white",borderRadius:"12px",boxShadow:"0 1px 3px rgba(0, 0, 0, 0.1)",border:"1px solid #e5e7eb",overflow:"hidden"}),N=e({padding:"24px",borderBottom:"1px solid #e5e7eb"}),n=e({fontSize:"20px",fontWeight:"bold",color:"gray.900"}),$=e({padding:"24px"}),M=e({marginTop:"32px",backgroundColor:"white",borderRadius:"12px",boxShadow:"0 1px 3px rgba(0, 0, 0, 0.1)",border:"1px solid #e5e7eb",overflow:"hidden"}),G=e({padding:"24px",borderBottom:"1px solid #e5e7eb"}),Z=e({fontSize:"20px",fontWeight:"bold",color:"gray.900"}),Q=e({padding:"24px"}),ee=e({display:"grid",gridTemplateColumns:"repeat(auto-fit, minmax(300px, 1fr))",gap:"16px"}),be=e({fontSize:"14px",fontWeight:"600",color:"gray.700",marginBottom:"8px"}),ae=e({fontSize:"14px",color:"gray.600",fontFamily:"monospace",backgroundColor:"gray.100",padding:"8px",borderRadius:"4px"}),se=e({fontSize:"14px",fontWeight:"600",color:"gray.700",marginBottom:"8px"}),ce=e({fontSize:"14px",color:"gray.600",fontFamily:"monospace",backgroundColor:"gray.100",padding:"8px",borderRadius:"4px"}),ge=e({fontSize:"14px",fontWeight:"600",color:"gray.700",marginBottom:"8px"}),he=e({fontSize:"14px",color:"gray.600",fontFamily:"monospace",backgroundColor:"gray.100",padding:"8px",borderRadius:"4px"});return k!==a.e&&t(r,a.e=k),w!==a.t&&t(d,a.t=w),f!==a.a&&t(u,a.a=f),S!==a.o&&t(p,a.o=S),W!==a.i&&t(m,a.i=W),E!==a.n&&t(z,a.n=E),L!==a.s&&t(j,a.s=L),N!==a.h&&t(b,a.h=N),n!==a.r&&t(_,a.r=n),$!==a.d&&t(I,a.d=$),M!==a.l&&t(A,a.l=M),G!==a.u&&t(R,a.u=G),Z!==a.c&&t(K,a.c=Z),Q!==a.w&&t(V,a.w=Q),ee!==a.m&&t(H,a.m=ee),be!==a.f&&t(x,a.f=be),ae!==a.y&&t(D,a.y=ae),se!==a.g&&t(c,a.g=se),ce!==a.p&&t(h,a.p=ce),ge!==a.b&&t(y,a.b=ge),he!==a.T&&t(P,a.T=he),a},{e:void 0,t:void 0,a:void 0,o:void 0,i:void 0,n:void 0,s:void 0,h:void 0,r:void 0,d:void 0,l:void 0,u:void 0,c:void 0,w:void 0,m:void 0,f:void 0,y:void 0,g:void 0,p:void 0,b:void 0,T:void 0}),r})()}st(["click"]);class ti{constructor(){this.socket=null,this.reconnectAttempts=0,this.maxReconnectAttempts=5,this.reconnectDelay=1e3,this.connectionStatusSignal=re("disconnected"),this.marketDataSignal=re(new Map),this.connectionStatus=this.connectionStatusSignal[0],this.setConnectionStatus=this.connectionStatusSignal[1],this.marketData=this.marketDataSignal[0],this.setMarketData=this.marketDataSignal[1],this.connect()}connect(){try{this.setConnectionStatus("connecting"),setTimeout(()=>{this.setConnectionStatus("error"),console.log("WebSocket连接模拟：无法连接到服务器，将使用模拟数据")},1e3)}catch(l){console.error("WebSocket连接失败:",l),this.setConnectionStatus("error")}}subscribeToMarketData(l){console.log("订阅市场数据:",l)}unsubscribeFromMarketData(l){console.log("取消订阅市场数据:",l)}reconnect(){console.log("尝试重新连接..."),this.setConnectionStatus("connecting"),setTimeout(()=>{this.setConnectionStatus("error"),console.log("重连失败，继续使用模拟数据")},1500)}disconnect(){this.socket&&(this.socket.disconnect(),this.socket=null),this.setConnectionStatus("disconnected")}}const ne=new ti;function oi(){return{connectionStatus:ne.connectionStatus,marketData:ne.marketData,subscribeToMarketData:ne.subscribeToMarketData.bind(ne),unsubscribeFromMarketData:ne.unsubscribeFromMarketData.bind(ne),disconnect:ne.disconnect.bind(ne),reconnect:ne.reconnect.bind(ne)}}var ii=X("<div><div><div><h1>行情分析</h1><div><div><div></div></div></div></div><div><div><span>沪深A股</span><span>数据更新时间: 15:30</span></div></div></div><div><h2>市场概览</h2><div><div><div>上证指数</div><div>3,247.89</div><div>-12.34 (-0.38%)</div></div><div><div>深证成指</div><div>10,567.23</div><div>+45.67 (+0.43%)</div></div><div><div>创业板指</div><div>2,234.56</div><div>-8.90 (-0.40%)</div></div><div><div>科创50</div><div>1,123.45</div><div>+15.23 (+1.37%)</div></div></div></div><div><div><input type=text placeholder=请输入股票代码或名称><button type=button>搜索</button></div><div><button type=button>全部</button><button type=button>沪A</button><button type=button>深A</button><button type=button>创业板</button></div></div><div><div><h2>股票列表</h2></div><div><table><thead><tr><th>代码</th><th>名称</th><th>现价</th><th>涨跌额</th><th>涨跌幅</th><th>成交量</th><th>最高价</th><th>最低价</th><th>操作</th></tr></thead><tbody></tbody></table></div><div><div>共 <!> 条数据</div><div><button type=button>上一页</button><span>1</span><button type=button>下一页"),ni=X("<button>重新连接"),ri=X("<tr><td></td><td></td><td></td><td></td><td>%</td><td></td><td></td><td></td><td><div><button type=button>卖</button><button type=button>买"),di=X("<div><div><h3> 详细信息</h3></div><div><div>📊</div><p>K线图表和技术指标</p><p>这里将显示选中股票的详细分析图表");function li(){const i=[{symbol:"AAPL",name:"苹果公司",price:150.25,change:2.15,changePercent:1.45,volume:12e5,high:152.3,low:148.9,open:149.5,marketCap:25e11},{symbol:"TSLA",name:"特斯拉",price:245.8,change:-3.25,changePercent:-1.3,volume:28e5,high:250.1,low:244.5,open:248.9,marketCap:78e10},{symbol:"MSFT",name:"微软",price:310.45,change:2.85,changePercent:.93,volume:15e5,high:312,low:308.2,open:309.1,marketCap:23e11},{symbol:"GOOGL",name:"谷歌",price:2650.3,change:38.45,changePercent:1.47,volume:8e5,high:2665,low:2635.5,open:2640,marketCap:17e11},{symbol:"AMZN",name:"亚马逊",price:3245.67,change:-15.23,changePercent:-.47,volume:95e4,high:3260,low:3230.5,open:3255,marketCap:165e10}],[l,g]=re(i),s=oi(),[r,d]=re("AAPL"),[u,p]=re("");xo(()=>{const b=s.marketData();b.size>0&&g(_=>_.map(I=>{const A=b.get(I.symbol);return A?{...I,price:A.price,change:A.change,changePercent:A.changePercent,volume:A.volume}:I}))}),lt(()=>{console.log("Market page mounted");const b=i.map(A=>A.symbol);s.subscribeToMarketData(b);let _;setTimeout(()=>{_=setInterval(()=>{s.connectionStatus()!=="connected"&&g(A=>A.map(R=>({...R,price:Math.max(.01,R.price+(Math.random()-.5)*2),change:R.change+(Math.random()-.5)*.5,changePercent:R.changePercent+(Math.random()-.5)*.2,volume:Math.max(0,R.volume+Math.floor((Math.random()-.5)*1e5))})))},3e3)},2e3),ct(()=>{_&&clearInterval(_),s.unsubscribeFromMarketData(b)})});const m=()=>{const b=u().toLowerCase();return l().filter(_=>_.symbol.toLowerCase().includes(b)||_.name.toLowerCase().includes(b))},z=(b,_=2)=>new Intl.NumberFormat("zh-CN",{minimumFractionDigits:_,maximumFractionDigits:_}).format(b),j=b=>b>=1e6?`${(b/1e6).toFixed(1)}M`:b>=1e3?`${(b/1e3).toFixed(1)}K`:b.toString();return(()=>{var b=ii(),_=b.firstChild,I=_.firstChild,A=I.firstChild,R=A.nextSibling,K=R.firstChild,V=K.firstChild,H=I.nextSibling,Y=H.firstChild,x=Y.firstChild,D=x.nextSibling,T=_.nextSibling,c=T.firstChild,h=c.nextSibling,B=h.firstChild,y=B.firstChild,P=y.nextSibling,a=P.nextSibling,k=B.nextSibling,w=k.firstChild,f=w.nextSibling,S=f.nextSibling,W=k.nextSibling,E=W.firstChild,L=E.nextSibling,N=L.nextSibling,n=W.nextSibling,$=n.firstChild,M=$.nextSibling,G=M.nextSibling,Z=T.nextSibling,Q=Z.firstChild,ee=Q.firstChild,be=ee.nextSibling,ae=Q.nextSibling,se=ae.firstChild,ce=se.nextSibling,ge=ce.nextSibling,he=ge.nextSibling,_e=Z.nextSibling,ye=_e.firstChild,et=ye.firstChild,ke=ye.nextSibling,ze=ke.firstChild,Re=ze.firstChild,Ie=Re.firstChild,Ae=Ie.firstChild,Te=Ae.nextSibling,Be=Te.nextSibling,$e=Be.nextSibling,We=$e.nextSibling,De=We.nextSibling,Pe=De.nextSibling,Ee=Pe.nextSibling,tt=Ee.nextSibling,ot=Re.nextSibling,Me=ke.nextSibling,me=Me.firstChild,it=me.firstChild,Le=it.nextSibling;Le.nextSibling;var je=me.nextSibling,Oe=je.firstChild,xt=Oe.nextSibling,so=xt.nextSibling;return v(K,(()=>{var o=le(()=>s.connectionStatus()==="connected");return()=>o()?"实时连接":le(()=>s.connectionStatus()==="connecting")()?"连接中...":s.connectionStatus()==="error"?"连接失败":"模拟数据"})(),null),v(R,(()=>{var o=le(()=>s.connectionStatus()!=="connected");return()=>o()&&(()=>{var q=ni();return q.$$click=()=>s.reconnect(),F(()=>t(q,e({padding:"4px 8px",backgroundColor:"#1890ff",color:"white",border:"none",borderRadius:"4px",fontSize:"12px",cursor:"pointer",_hover:{backgroundColor:"#40a9ff"}}))),q})()})(),null),ee.$$input=o=>p(o.currentTarget.value),v(ot,()=>m().map(o=>(()=>{var q=ri(),te=q.firstChild,oe=te.nextSibling,xe=oe.nextSibling,ie=xe.nextSibling,J=ie.nextSibling,Se=J.firstChild,ue=J.nextSibling,O=ue.nextSibling,de=O.nextSibling,ve=de.nextSibling,fe=ve.firstChild,pe=fe.firstChild,Ce=pe.nextSibling;return q.$$click=()=>d(o.symbol),v(te,()=>o.symbol),v(oe,()=>o.name),v(xe,()=>z(o.price)),v(ie,()=>o.change>=0?"+":"",null),v(ie,()=>z(o.change),null),v(J,()=>o.changePercent>=0?"+":"",Se),v(J,()=>z(o.changePercent),Se),v(ue,()=>j(o.volume)),v(O,()=>z(o.high)),v(de,()=>z(o.low)),F(C=>{var Fe=e({borderBottom:"1px solid #f0f0f0",cursor:"pointer",transition:"background-color 0.2s",backgroundColor:r()===o.symbol?"#e6f7ff":"transparent",_hover:{backgroundColor:"#fafafa"}}),He=e({padding:"12px 16px",fontSize:"14px",fontWeight:"600",color:"#262626"}),Ne=e({padding:"12px 16px",fontSize:"14px",color:"#262626"}),Xe=e({padding:"12px 16px",textAlign:"right",fontSize:"14px",fontWeight:"600",color:"#262626"}),Ke=e({padding:"12px 16px",textAlign:"right",fontSize:"14px",fontWeight:"500",color:o.change>=0?"#52c41a":"#f5222d"}),Ye=e({padding:"12px 16px",textAlign:"right",fontSize:"14px",fontWeight:"500",color:o.changePercent>=0?"#52c41a":"#f5222d"}),qe=e({padding:"12px 16px",textAlign:"right",fontSize:"14px",color:"#8c8c8c"}),Ve=e({padding:"12px 16px",textAlign:"right",fontSize:"14px",color:"#8c8c8c"}),Ge=e({padding:"12px 16px",textAlign:"right",fontSize:"14px",color:"#8c8c8c"}),Ue=e({padding:"12px 16px",textAlign:"right"}),Ze=e({display:"flex",gap:"4px",justifyContent:"flex-end"}),Qe=e({padding:"4px 8px",backgroundColor:"#f5222d",color:"white",border:"none",borderRadius:"2px",fontSize:"12px",cursor:"pointer"}),Je=e({padding:"4px 8px",backgroundColor:"#52c41a",color:"white",border:"none",borderRadius:"2px",fontSize:"12px",cursor:"pointer"});return Fe!==C.e&&t(q,C.e=Fe),He!==C.t&&t(te,C.t=He),Ne!==C.a&&t(oe,C.a=Ne),Xe!==C.o&&t(xe,C.o=Xe),Ke!==C.i&&t(ie,C.i=Ke),Ye!==C.n&&t(J,C.n=Ye),qe!==C.s&&t(ue,C.s=qe),Ve!==C.h&&t(O,C.h=Ve),Ge!==C.r&&t(de,C.r=Ge),Ue!==C.d&&t(ve,C.d=Ue),Ze!==C.l&&t(fe,C.l=Ze),Qe!==C.u&&t(pe,C.u=Qe),Je!==C.c&&t(Ce,C.c=Je),C},{e:void 0,t:void 0,a:void 0,o:void 0,i:void 0,n:void 0,s:void 0,h:void 0,r:void 0,d:void 0,l:void 0,u:void 0,c:void 0}),q})())),v(me,()=>m().length,Le),v(b,(()=>{var o=le(()=>!!r());return()=>o()&&(()=>{var q=di(),te=q.firstChild,oe=te.firstChild,xe=oe.firstChild,ie=te.nextSibling,J=ie.firstChild,Se=J.nextSibling,ue=Se.nextSibling;return v(oe,r,xe),F(O=>{var de=e({marginTop:"24px",backgroundColor:"white",borderRadius:"8px",border:"1px solid #e8e8e8",overflow:"hidden"}),ve=e({padding:"20px",borderBottom:"1px solid #e8e8e8"}),fe=e({fontSize:"16px",fontWeight:"600",color:"#262626",margin:"0"}),pe=e({padding:"20px",textAlign:"center",color:"#8c8c8c"}),Ce=e({fontSize:"48px",marginBottom:"16px"}),C=e({fontSize:"12px"});return de!==O.e&&t(q,O.e=de),ve!==O.t&&t(te,O.t=ve),fe!==O.a&&t(oe,O.a=fe),pe!==O.o&&t(ie,O.o=pe),Ce!==O.i&&t(J,O.i=Ce),C!==O.n&&t(ue,O.n=C),O},{e:void 0,t:void 0,a:void 0,o:void 0,i:void 0,n:void 0}),q})()})(),null),F(o=>{var q=e({padding:"24px",backgroundColor:"#f5f5f5",minHeight:"100%"}),te=e({marginBottom:"24px"}),oe=e({display:"flex",alignItems:"center",justifyContent:"space-between",marginBottom:"8px"}),xe=e({fontSize:"24px",fontWeight:"600",color:"#262626",margin:0}),ie=e({display:"flex",alignItems:"center",gap:"8px"}),J=e({display:"flex",alignItems:"center",gap:"6px",padding:"4px 8px",borderRadius:"4px",fontSize:"12px",fontWeight:"500",backgroundColor:s.connectionStatus()==="connected"?"#f6ffed":s.connectionStatus()==="connecting"?"#fff7e6":s.connectionStatus()==="error"?"#fff2f0":"#f5f5f5",color:s.connectionStatus()==="connected"?"#52c41a":s.connectionStatus()==="connecting"?"#faad14":s.connectionStatus()==="error"?"#f5222d":"#8c8c8c"}),Se=e({width:"6px",height:"6px",borderRadius:"50%",backgroundColor:"currentColor"}),ue=e({display:"flex",alignItems:"center",gap:"16px"}),O=e({display:"flex",alignItems:"center",gap:"8px"}),de=e({padding:"4px 8px",backgroundColor:"#f6ffed",color:"#52c41a",borderRadius:"4px",fontSize:"12px",fontWeight:"500"}),ve=e({fontSize:"14px",color:"#8c8c8c"}),fe=e({marginBottom:"24px",backgroundColor:"white",borderRadius:"8px",border:"1px solid #e8e8e8",padding:"20px"}),pe=e({fontSize:"16px",fontWeight:"600",color:"#262626",marginBottom:"16px"}),Ce=e({display:"grid",gridTemplateColumns:"repeat(4, 1fr)",gap:"24px"}),C=e({textAlign:"center"}),Fe=e({fontSize:"12px",color:"#8c8c8c",marginBottom:"4px"}),He=e({fontSize:"18px",fontWeight:"600",color:"#f5222d",marginBottom:"2px"}),Ne=e({fontSize:"12px",color:"#f5222d"}),Xe=e({textAlign:"center"}),Ke=e({fontSize:"12px",color:"#8c8c8c",marginBottom:"4px"}),Ye=e({fontSize:"18px",fontWeight:"600",color:"#52c41a",marginBottom:"2px"}),qe=e({fontSize:"12px",color:"#52c41a"}),Ve=e({textAlign:"center"}),Ge=e({fontSize:"12px",color:"#8c8c8c",marginBottom:"4px"}),Ue=e({fontSize:"18px",fontWeight:"600",color:"#f5222d",marginBottom:"2px"}),Ze=e({fontSize:"12px",color:"#f5222d"}),Qe=e({textAlign:"center"}),Je=e({fontSize:"12px",color:"#8c8c8c",marginBottom:"4px"}),ut=e({fontSize:"18px",fontWeight:"600",color:"#52c41a",marginBottom:"2px"}),vt=e({fontSize:"12px",color:"#52c41a"}),ft=e({marginBottom:"16px",backgroundColor:"white",borderRadius:"8px",border:"1px solid #e8e8e8",padding:"16px",display:"flex",justifyContent:"space-between",alignItems:"center"}),pt=e({display:"flex",alignItems:"center",gap:"12px"}),bt=e({padding:"8px 12px",border:"1px solid #d9d9d9",borderRadius:"4px",fontSize:"14px",width:"200px",_focus:{outline:"none",borderColor:"#1890ff"}}),ht=e({padding:"8px 16px",backgroundColor:"#1890ff",color:"white",border:"none",borderRadius:"4px",fontSize:"14px",cursor:"pointer"}),mt=e({display:"flex",alignItems:"center",gap:"8px"}),St=e({padding:"6px 12px",backgroundColor:"#1890ff",color:"white",border:"none",borderRadius:"4px",fontSize:"12px",cursor:"pointer"}),Ct=e({padding:"6px 12px",backgroundColor:"white",color:"#262626",border:"1px solid #d9d9d9",borderRadius:"4px",fontSize:"12px",cursor:"pointer"}),yt=e({padding:"6px 12px",backgroundColor:"white",color:"#262626",border:"1px solid #d9d9d9",borderRadius:"4px",fontSize:"12px",cursor:"pointer"}),kt=e({padding:"6px 12px",backgroundColor:"white",color:"#262626",border:"1px solid #d9d9d9",borderRadius:"4px",fontSize:"12px",cursor:"pointer"}),wt=e({backgroundColor:"white",borderRadius:"8px",border:"1px solid #e8e8e8",overflow:"hidden"}),_t=e({padding:"16px 20px",borderBottom:"1px solid #e8e8e8",backgroundColor:"#fafafa"}),zt=e({fontSize:"16px",fontWeight:"600",color:"#262626",margin:"0"}),Rt=e({overflowX:"auto"}),It=e({width:"100%",borderCollapse:"collapse"}),At=e({backgroundColor:"#fafafa"}),Tt=e({padding:"12px 16px",textAlign:"left",fontSize:"12px",fontWeight:"600",color:"#8c8c8c"}),Bt=e({padding:"12px 16px",textAlign:"left",fontSize:"12px",fontWeight:"600",color:"#8c8c8c"}),$t=e({padding:"12px 16px",textAlign:"right",fontSize:"12px",fontWeight:"600",color:"#8c8c8c"}),Wt=e({padding:"12px 16px",textAlign:"right",fontSize:"12px",fontWeight:"600",color:"#8c8c8c"}),Dt=e({padding:"12px 16px",textAlign:"right",fontSize:"12px",fontWeight:"600",color:"#8c8c8c"}),Pt=e({padding:"12px 16px",textAlign:"right",fontSize:"12px",fontWeight:"600",color:"#8c8c8c"}),Et=e({padding:"12px 16px",textAlign:"right",fontSize:"12px",fontWeight:"600",color:"#8c8c8c"}),Mt=e({padding:"12px 16px",textAlign:"right",fontSize:"12px",fontWeight:"600",color:"#8c8c8c"}),Lt=e({padding:"12px 16px",textAlign:"right",fontSize:"12px",fontWeight:"600",color:"#8c8c8c"}),jt=e({padding:"16px 20px",borderTop:"1px solid #f0f0f0",display:"flex",justifyContent:"space-between",alignItems:"center"}),Ot=e({fontSize:"14px",color:"#8c8c8c"}),Ft=e({display:"flex",gap:"8px",alignItems:"center"}),Ht=e({padding:"4px 8px",backgroundColor:"white",color:"#262626",border:"1px solid #d9d9d9",borderRadius:"4px",fontSize:"12px",cursor:"pointer"}),Nt=e({padding:"4px 8px",backgroundColor:"#1890ff",color:"white",borderRadius:"4px",fontSize:"12px"}),Xt=e({padding:"4px 8px",backgroundColor:"white",color:"#262626",border:"1px solid #d9d9d9",borderRadius:"4px",fontSize:"12px",cursor:"pointer"});return q!==o.e&&t(b,o.e=q),te!==o.t&&t(_,o.t=te),oe!==o.a&&t(I,o.a=oe),xe!==o.o&&t(A,o.o=xe),ie!==o.i&&t(R,o.i=ie),J!==o.n&&t(K,o.n=J),Se!==o.s&&t(V,o.s=Se),ue!==o.h&&t(H,o.h=ue),O!==o.r&&t(Y,o.r=O),de!==o.d&&t(x,o.d=de),ve!==o.l&&t(D,o.l=ve),fe!==o.u&&t(T,o.u=fe),pe!==o.c&&t(c,o.c=pe),Ce!==o.w&&t(h,o.w=Ce),C!==o.m&&t(B,o.m=C),Fe!==o.f&&t(y,o.f=Fe),He!==o.y&&t(P,o.y=He),Ne!==o.g&&t(a,o.g=Ne),Xe!==o.p&&t(k,o.p=Xe),Ke!==o.b&&t(w,o.b=Ke),Ye!==o.T&&t(f,o.T=Ye),qe!==o.A&&t(S,o.A=qe),Ve!==o.O&&t(W,o.O=Ve),Ge!==o.I&&t(E,o.I=Ge),Ue!==o.S&&t(L,o.S=Ue),Ze!==o.W&&t(N,o.W=Ze),Qe!==o.C&&t(n,o.C=Qe),Je!==o.B&&t($,o.B=Je),ut!==o.v&&t(M,o.v=ut),vt!==o.k&&t(G,o.k=vt),ft!==o.x&&t(Z,o.x=ft),pt!==o.j&&t(Q,o.j=pt),bt!==o.q&&t(ee,o.q=bt),ht!==o.z&&t(be,o.z=ht),mt!==o.P&&t(ae,o.P=mt),St!==o.H&&t(se,o.H=St),Ct!==o.F&&t(ce,o.F=Ct),yt!==o.M&&t(ge,o.M=yt),kt!==o.D&&t(he,o.D=kt),wt!==o.R&&t(_e,o.R=wt),_t!==o.E&&t(ye,o.E=_t),zt!==o.L&&t(et,o.L=zt),Rt!==o.N&&t(ke,o.N=Rt),It!==o.G&&t(ze,o.G=It),At!==o.U&&t(Ie,o.U=At),Tt!==o.K&&t(Ae,o.K=Tt),Bt!==o.V&&t(Te,o.V=Bt),$t!==o.Y&&t(Be,o.Y=$t),Wt!==o.J&&t($e,o.J=Wt),Dt!==o.Q&&t(We,o.Q=Dt),Pt!==o.Z&&t(De,o.Z=Pt),Et!==o.X&&t(Pe,o.X=Et),Mt!==o._&&t(Ee,o._=Mt),Lt!==o.$&&t(tt,o.$=Lt),jt!==o.te&&t(Me,o.te=jt),Ot!==o.tt&&t(me,o.tt=Ot),Ft!==o.ta&&t(je,o.ta=Ft),Ht!==o.to&&t(Oe,o.to=Ht),Nt!==o.ti&&t(xt,o.ti=Nt),Xt!==o.tn&&t(so,o.tn=Xt),o},{e:void 0,t:void 0,a:void 0,o:void 0,i:void 0,n:void 0,s:void 0,h:void 0,r:void 0,d:void 0,l:void 0,u:void 0,c:void 0,w:void 0,m:void 0,f:void 0,y:void 0,g:void 0,p:void 0,b:void 0,T:void 0,A:void 0,O:void 0,I:void 0,S:void 0,W:void 0,C:void 0,B:void 0,v:void 0,k:void 0,x:void 0,j:void 0,q:void 0,z:void 0,P:void 0,H:void 0,F:void 0,M:void 0,D:void 0,R:void 0,E:void 0,L:void 0,N:void 0,G:void 0,U:void 0,K:void 0,V:void 0,Y:void 0,J:void 0,Q:void 0,Z:void 0,X:void 0,_:void 0,$:void 0,te:void 0,tt:void 0,ta:void 0,to:void 0,ti:void 0,tn:void 0}),F(()=>ee.value=u()),b})()}st(["input","click"]);var ai=X("<div><div>");function si(i){const[l,g]=re();let s;return lt(async()=>{const r=l();if(r)try{Kt.config({paths:{vs:"https://cdn.jsdelivr.net/npm/monaco-editor@0.45.0/min/vs"}});const d=await Kt.init();i.language==="python"&&d.languages.registerCompletionItemProvider("python",{provideCompletionItems:(u,p)=>({suggestions:[{label:"def",kind:d.languages.CompletionItemKind.Keyword,insertText:"def ${1:function_name}(${2:parameters}):\n    ${3:pass}",insertTextRules:d.languages.CompletionItemInsertTextRule.InsertAsSnippet,documentation:"Define a function"},{label:"initialize",kind:d.languages.CompletionItemKind.Function,insertText:"def initialize(context):\n    ${1:pass}",insertTextRules:d.languages.CompletionItemInsertTextRule.InsertAsSnippet,documentation:"策略初始化函数"},{label:"handle_data",kind:d.languages.CompletionItemKind.Function,insertText:"def handle_data(context, data):\n    ${1:pass}",insertTextRules:d.languages.CompletionItemInsertTextRule.InsertAsSnippet,documentation:"主要的交易逻辑函数"},{label:"order_target_percent",kind:d.languages.CompletionItemKind.Function,insertText:"order_target_percent(${1:security}, ${2:percent})",insertTextRules:d.languages.CompletionItemInsertTextRule.InsertAsSnippet,documentation:"下单到目标百分比"},{label:"attribute_history",kind:d.languages.CompletionItemKind.Function,insertText:"attribute_history(${1:security}, ${2:count}, ${3:unit}, ${4:fields})",insertTextRules:d.languages.CompletionItemInsertTextRule.InsertAsSnippet,documentation:"获取历史数据"},{label:"log.info",kind:d.languages.CompletionItemKind.Function,insertText:"log.info(${1:message})",insertTextRules:d.languages.CompletionItemInsertTextRule.InsertAsSnippet,documentation:"输出日志信息"}]})}),s=d.editor.create(r,{value:i.value||"",language:i.language||"python",theme:i.theme||"vs",fontSize:13,lineNumbers:"on",roundedSelection:!1,scrollBeyondLastLine:!1,minimap:{enabled:!0},automaticLayout:!0,tabSize:4,insertSpaces:!0,wordWrap:"on",folding:!0,renderLineHighlight:"all",selectOnLineNumbers:!0,matchBrackets:"always",...i.options}),s.onDidChangeModelContent(()=>{i.onChange&&s&&i.onChange(s.getValue())}),s.addCommand(d.KeyMod.CtrlCmd|d.KeyCode.KeyS,()=>{console.log("保存策略快捷键触发")}),s.addCommand(d.KeyMod.CtrlCmd|d.KeyCode.Enter,()=>{console.log("运行策略快捷键触发")})}catch(d){console.error("Monaco Editor 初始化失败:",d)}}),ct(()=>{s&&s.dispose()}),(()=>{var r=ai(),d=r.firstChild;return uo(g,d),F(u=>{var p=e({width:"100%",height:`${i.height||400}px`,border:"1px solid #e5e7eb",borderRadius:"6px",overflow:"hidden"}),m=e({width:"100%",height:"100%"});return p!==u.e&&t(r,u.e=p),m!==u.t&&t(d,u.t=m),u},{e:void 0,t:void 0}),r})()}var ci=X('<div><div><h1>🧠 策略编辑器</h1><p>创建和编辑量化交易策略</p></div><div><div><div><h2>策略代码</h2><div><button>运行回测</button><button>保存策略</button></div></div><div></div></div><div><div><h2>回测结果</h2></div><div><div><div>📊</div><p>等待回测结果</p><p>点击"运行回测"开始策略测试</p></div></div></div></div><div><h3>策略模板</h3><div><button><div>双均线策略</div><div>基于移动平均线的经典策略</div></button><button><div>RSI策略</div><div>基于相对强弱指标的策略</div></button><button><div>布林带策略</div><div>利用布林带进行交易决策</div></button><button><div>机器学习策略</div><div>基于AI模型的量化策略');function gi(){const[i,l]=re(`# 量化策略示例
# 这是一个简单的移动平均线策略

def initialize(context):
    # 设置基准和股票
    g.benchmark = '000300.XSHG'
    g.stock = '000001.XSHE'
    
def handle_data(context, data):
    # 获取历史价格
    hist = attribute_history(g.stock, 20, '1d', ['close'])
    ma5 = hist['close'][-5:].mean()
    ma20 = hist['close'][-20:].mean()
    current_price = data[g.stock].close
    
    # 交易逻辑
    if ma5 > ma20 and current_price > ma5:
        # 金叉买入信号
        order_target_percent(g.stock, 0.8)
        log.info(f"买入信号，价格: {current_price}")
    elif ma5 < ma20:
        # 死叉卖出信号
        order_target_percent(g.stock, 0)
        log.info(f"卖出信号，价格: {current_price}")
`);return(()=>{var g=ci(),s=g.firstChild,r=s.firstChild,d=r.nextSibling,u=s.nextSibling,p=u.firstChild,m=p.firstChild,z=m.firstChild,j=z.nextSibling,b=j.firstChild,_=b.nextSibling,I=m.nextSibling,A=p.nextSibling,R=A.firstChild,K=R.firstChild,V=R.nextSibling,H=V.firstChild,Y=H.firstChild,x=Y.nextSibling,D=x.nextSibling,T=u.nextSibling,c=T.firstChild,h=c.nextSibling,B=h.firstChild,y=B.firstChild,P=y.nextSibling,a=B.nextSibling,k=a.firstChild,w=k.nextSibling,f=a.nextSibling,S=f.firstChild,W=S.nextSibling,E=f.nextSibling,L=E.firstChild,N=L.nextSibling;return v(I,U(si,{get value(){return i()},language:"python",theme:"vs",height:500,onChange:l,options:{minimap:{enabled:!0},fontSize:14,wordWrap:"on",automaticLayout:!0}})),F(n=>{var $=e({padding:"24px",maxWidth:"1400px",margin:"0 auto",height:"100%"}),M=e({marginBottom:"32px"}),G=e({fontSize:"32px",fontWeight:"bold",color:"gray.900",marginBottom:"8px"}),Z=e({fontSize:"16px",color:"gray.600"}),Q=e({display:"grid",gridTemplateColumns:"1fr 1fr",gap:"24px",height:"calc(100vh - 200px)"}),ee=e({backgroundColor:"white",borderRadius:"12px",boxShadow:"0 1px 3px rgba(0, 0, 0, 0.1)",border:"1px solid #e5e7eb",overflow:"hidden",display:"flex",flexDirection:"column"}),be=e({padding:"16px 20px",borderBottom:"1px solid #e5e7eb",backgroundColor:"#f9fafb",display:"flex",justifyContent:"space-between",alignItems:"center"}),ae=e({fontSize:"16px",fontWeight:"600",color:"gray.900"}),se=e({display:"flex",gap:"8px"}),ce=e({padding:"6px 12px",backgroundColor:"blue.600",color:"white",border:"none",borderRadius:"6px",fontSize:"12px",fontWeight:"500",cursor:"pointer",_hover:{backgroundColor:"blue.700"}}),ge=e({padding:"6px 12px",backgroundColor:"green.600",color:"white",border:"none",borderRadius:"6px",fontSize:"12px",fontWeight:"500",cursor:"pointer",_hover:{backgroundColor:"green.700"}}),he=e({flex:1,padding:"8px"}),_e=e({backgroundColor:"white",borderRadius:"12px",boxShadow:"0 1px 3px rgba(0, 0, 0, 0.1)",border:"1px solid #e5e7eb",overflow:"hidden",display:"flex",flexDirection:"column"}),ye=e({padding:"16px 20px",borderBottom:"1px solid #e5e7eb",backgroundColor:"#f9fafb"}),et=e({fontSize:"16px",fontWeight:"600",color:"gray.900"}),ke=e({flex:1,padding:"20px",display:"flex",alignItems:"center",justifyContent:"center"}),ze=e({textAlign:"center",color:"gray.500"}),Re=e({fontSize:"48px",marginBottom:"16px"}),Ie=e({fontSize:"16px",marginBottom:"8px"}),Ae=e({fontSize:"14px"}),Te=e({marginTop:"24px",backgroundColor:"white",borderRadius:"12px",boxShadow:"0 1px 3px rgba(0, 0, 0, 0.1)",border:"1px solid #e5e7eb",padding:"20px"}),Be=e({fontSize:"18px",fontWeight:"600",color:"gray.900",marginBottom:"16px"}),$e=e({display:"grid",gridTemplateColumns:"repeat(auto-fit, minmax(200px, 1fr))",gap:"12px"}),We=e({padding:"12px 16px",border:"1px solid #e5e7eb",borderRadius:"8px",backgroundColor:"white",textAlign:"left",cursor:"pointer",transition:"all 0.2s",_hover:{borderColor:"blue.500",backgroundColor:"blue.50"}}),De=e({fontSize:"14px",fontWeight:"500",color:"gray.900",marginBottom:"4px"}),Pe=e({fontSize:"12px",color:"gray.600"}),Ee=e({padding:"12px 16px",border:"1px solid #e5e7eb",borderRadius:"8px",backgroundColor:"white",textAlign:"left",cursor:"pointer",transition:"all 0.2s",_hover:{borderColor:"blue.500",backgroundColor:"blue.50"}}),tt=e({fontSize:"14px",fontWeight:"500",color:"gray.900",marginBottom:"4px"}),ot=e({fontSize:"12px",color:"gray.600"}),Me=e({padding:"12px 16px",border:"1px solid #e5e7eb",borderRadius:"8px",backgroundColor:"white",textAlign:"left",cursor:"pointer",transition:"all 0.2s",_hover:{borderColor:"blue.500",backgroundColor:"blue.50"}}),me=e({fontSize:"14px",fontWeight:"500",color:"gray.900",marginBottom:"4px"}),it=e({fontSize:"12px",color:"gray.600"}),Le=e({padding:"12px 16px",border:"1px solid #e5e7eb",borderRadius:"8px",backgroundColor:"white",textAlign:"left",cursor:"pointer",transition:"all 0.2s",_hover:{borderColor:"blue.500",backgroundColor:"blue.50"}}),je=e({fontSize:"14px",fontWeight:"500",color:"gray.900",marginBottom:"4px"}),Oe=e({fontSize:"12px",color:"gray.600"});return $!==n.e&&t(g,n.e=$),M!==n.t&&t(s,n.t=M),G!==n.a&&t(r,n.a=G),Z!==n.o&&t(d,n.o=Z),Q!==n.i&&t(u,n.i=Q),ee!==n.n&&t(p,n.n=ee),be!==n.s&&t(m,n.s=be),ae!==n.h&&t(z,n.h=ae),se!==n.r&&t(j,n.r=se),ce!==n.d&&t(b,n.d=ce),ge!==n.l&&t(_,n.l=ge),he!==n.u&&t(I,n.u=he),_e!==n.c&&t(A,n.c=_e),ye!==n.w&&t(R,n.w=ye),et!==n.m&&t(K,n.m=et),ke!==n.f&&t(V,n.f=ke),ze!==n.y&&t(H,n.y=ze),Re!==n.g&&t(Y,n.g=Re),Ie!==n.p&&t(x,n.p=Ie),Ae!==n.b&&t(D,n.b=Ae),Te!==n.T&&t(T,n.T=Te),Be!==n.A&&t(c,n.A=Be),$e!==n.O&&t(h,n.O=$e),We!==n.I&&t(B,n.I=We),De!==n.S&&t(y,n.S=De),Pe!==n.W&&t(P,n.W=Pe),Ee!==n.C&&t(a,n.C=Ee),tt!==n.B&&t(k,n.B=tt),ot!==n.v&&t(w,n.v=ot),Me!==n.k&&t(f,n.k=Me),me!==n.x&&t(S,n.x=me),it!==n.j&&t(W,n.j=it),Le!==n.q&&t(E,n.q=Le),je!==n.z&&t(L,n.z=je),Oe!==n.P&&t(N,n.P=Oe),n},{e:void 0,t:void 0,a:void 0,o:void 0,i:void 0,n:void 0,s:void 0,h:void 0,r:void 0,d:void 0,l:void 0,u:void 0,c:void 0,w:void 0,m:void 0,f:void 0,y:void 0,g:void 0,p:void 0,b:void 0,T:void 0,A:void 0,O:void 0,I:void 0,S:void 0,W:void 0,C:void 0,B:void 0,v:void 0,k:void 0,x:void 0,j:void 0,q:void 0,z:void 0,P:void 0}),g})()}function xi(){return U(vo,{get children(){return U(we,{path:"/",component:Xo,get children(){return[U(we,{path:"/",component:Ut}),U(we,{path:"/dashboard",component:Ut}),U(we,{path:"/market",component:li}),U(we,{path:"/strategy-editor",component:gi}),U(we,{path:"/api-test",component:ei})]}})}})}const Zt=document.getElementById("root");Zt&&fo(()=>U(xi,{}),Zt);
