/*!-----------------------------------------------------------
 * Copyright (c) Microsoft Corporation. All rights reserved.
 * Version: 0.45.0(5e5af013f8d295555a7210df0d5f2cea0bf5dd56)
 * Released under the MIT license
 * https://github.com/microsoft/vscode/blob/main/LICENSE.txt
 *-----------------------------------------------------------*/define("vs/editor/editor.main.nls",{"vs/base/browser/ui/actionbar/actionViewItems":["{0} ({1})"],"vs/base/browser/ui/findinput/findInput":["input"],"vs/base/browser/ui/findinput/findInputToggles":["Match Case","Match Whole Word","Use Regular Expression"],"vs/base/browser/ui/findinput/replaceInput":["input","Preserve Case"],"vs/base/browser/ui/hover/hoverWidget":["Inspect this in the accessible view with {0}.","Inspect this in the accessible view via the command Open Accessible View which is currently not triggerable via keybinding."],"vs/base/browser/ui/iconLabel/iconLabelHover":["Loading..."],"vs/base/browser/ui/inputbox/inputBox":["Error: {0}","Warning: {0}","Info: {0}"," or {0} for history"," ({0} for history)","Cleared Input"],"vs/base/browser/ui/keybindingLabel/keybindingLabel":["Unbound"],"vs/base/browser/ui/selectBox/selectBoxCustom":["Select Box"],"vs/base/browser/ui/toolbar/toolbar":["More Actions..."],"vs/base/browser/ui/tree/abstractTree":["Filter","Fuzzy Match","Type to filter","Type to search","Type to search","Close","No elements found."],"vs/base/common/actions":["(empty)"],"vs/base/common/errorMessage":["{0}: {1}","A system error occurred ({0})","An unknown error occurred. Please consult the log for more details.","An unknown error occurred. Please consult the log for more details.","{0} ({1} errors in total)","An unknown error occurred. Please consult the log for more details."],"vs/base/common/keybindingLabels":["Ctrl","Shift","Alt","Windows","Ctrl","Shift","Alt","Super","Control","Shift","Option","Command","Control","Shift","Alt","Windows","Control","Shift","Alt","Super"],"vs/base/common/platform":["_"],"vs/editor/browser/controller/textAreaHandler":["editor","The editor is not accessible at this time.","{0} To enable screen reader optimized mode, use {1}","{0} To enable screen reader optimized mode, open the quick pick with {1} and run the command Toggle Screen Reader Accessibility Mode, which is currently not triggerable via keyboard.","{0} Please assign a keybinding for the command Toggle Screen Reader Accessibility Mode by accessing the keybindings editor with {1} and run it."],"vs/editor/browser/coreCommands":["Stick to the end even when going to longer lines","Stick to the end even when going to longer lines","Removed secondary cursors"],"vs/editor/browser/editorExtensions":["&&Undo","Undo","&&Redo","Redo","&&Select All","Select All"],"vs/editor/browser/widget/codeEditorWidget":["The number of cursors has been limited to {0}. Consider using [find and replace](https://code.visualstudio.com/docs/editor/codebasics#_find-and-replace) for larger changes or increase the editor multi cursor limit setting.","Increase Multi Cursor Limit"],"vs/editor/browser/widget/diffEditor/accessibleDiffViewer":["Icon for 'Insert' in accessible diff viewer.","Icon for 'Remove' in accessible diff viewer.","Icon for 'Close' in accessible diff viewer.","Close","Accessible Diff Viewer. Use arrow up and down to navigate.","no lines changed","1 line changed","{0} lines changed","Difference {0} of {1}: original line {2}, {3}, modified line {4}, {5}","blank","{0} unchanged line {1}","{0} original line {1} modified line {2}","+ {0} modified line {1}","- {0} original line {1}"],"vs/editor/browser/widget/diffEditor/colors":["The border color for text that got moved in the diff editor.","The active border color for text that got moved in the diff editor.","The color of the shadow around unchanged region widgets."],"vs/editor/browser/widget/diffEditor/decorations":["Line decoration for inserts in the diff editor.","Line decoration for removals in the diff editor."],"vs/editor/browser/widget/diffEditor/diffEditor.contribution":["Toggle Collapse Unchanged Regions","Toggle Show Moved Code Blocks","Toggle Use Inline View When Space Is Limited","Use Inline View When Space Is Limited","Show Moved Code Blocks","Diff Editor","Switch Side","Exit Compare Move","Collapse All Unchanged Regions","Show All Unchanged Regions","Accessible Diff Viewer","Go to Next Difference","Open Accessible Diff Viewer","Go to Previous Difference"],"vs/editor/browser/widget/diffEditor/diffEditorDecorations":["Revert Selected Changes","Revert Change"],"vs/editor/browser/widget/diffEditor/diffEditorEditors":[" use {0} to open the accessibility help."],"vs/editor/browser/widget/diffEditor/hideUnchangedRegionsFeature":["Fold Unchanged Region","Click or drag to show more above","Show Unchanged Region","Click or drag to show more below","{0} hidden lines","Double click to unfold"],"vs/editor/browser/widget/diffEditor/inlineDiffDeletedCodeMargin":["Copy deleted lines","Copy deleted line","Copy changed lines","Copy changed line","Copy deleted line ({0})","Copy changed line ({0})","Revert this change"],"vs/editor/browser/widget/diffEditor/movedBlocksLines":["Code moved with changes to line {0}-{1}","Code moved with changes from line {0}-{1}","Code moved to line {0}-{1}","Code moved from line {0}-{1}"],"vs/editor/browser/widget/multiDiffEditorWidget/colors":["The background color of the diff editor's header"],"vs/editor/common/config/editorConfigurationSchema":["Editor","The number of spaces a tab is equal to. This setting is overridden based on the file contents when {0} is on.",'The number of spaces used for indentation or `"tabSize"` to use the value from `#editor.tabSize#`. This setting is overridden based on the file contents when `#editor.detectIndentation#` is on.',"Insert spaces when pressing `Tab`. This setting is overridden based on the file contents when {0} is on.","Controls whether {0} and {1} will be automatically detected when a file is opened based on the file contents.","Remove trailing auto inserted whitespace.","Special handling for large files to disable certain memory intensive features.","Turn off Word Based Suggestions.","Only suggest words from the active document.","Suggest words from all open documents of the same language.","Suggest words from all open documents.","Controls whether completions should be computed based on words in the document and from which documents they are computed.","Semantic highlighting enabled for all color themes.","Semantic highlighting disabled for all color themes.","Semantic highlighting is configured by the current color theme's `semanticHighlighting` setting.","Controls whether the semanticHighlighting is shown for the languages that support it.","Keep peek editors open even when double-clicking their content or when hitting `Escape`.","Lines above this length will not be tokenized for performance reasons","Controls whether the tokenization should happen asynchronously on a web worker.","Controls whether async tokenization should be logged. For debugging only.","Controls whether async tokenization should be verified against legacy background tokenization. Might slow down tokenization. For debugging only.","Defines the bracket symbols that increase or decrease the indentation.","The opening bracket character or string sequence.","The closing bracket character or string sequence.","Defines the bracket pairs that are colorized by their nesting level if bracket pair colorization is enabled.","The opening bracket character or string sequence.","The closing bracket character or string sequence.","Timeout in milliseconds after which diff computation is cancelled. Use 0 for no timeout.","Maximum file size in MB for which to compute diffs. Use 0 for no limit.","Controls whether the diff editor shows the diff side by side or inline.","If the diff editor width is smaller than this value, the inline view is used.","If enabled and the editor width is too small, the inline view is used.","When enabled, the diff editor shows arrows in its glyph margin to revert changes.","When enabled, the diff editor ignores changes in leading or trailing whitespace.","Controls whether the diff editor shows +/- indicators for added/removed changes.","Controls whether the editor shows CodeLens.","Lines will never wrap.","Lines will wrap at the viewport width.","Lines will wrap according to the {0} setting.","Uses the legacy diffing algorithm.","Uses the advanced diffing algorithm.","Controls whether the diff editor shows unchanged regions.","Controls how many lines are used for unchanged regions.","Controls how many lines are used as a minimum for unchanged regions.","Controls how many lines are used as context when comparing unchanged regions.","Controls whether the diff editor should show detected code moves.","Controls whether the diff editor shows empty decorations to see where characters got inserted or deleted."],"vs/editor/common/config/editorOptions":["Use platform APIs to detect when a Screen Reader is attached.","Optimize for usage with a Screen Reader.","Assume a screen reader is not attached.","Controls if the UI should run in a mode where it is optimized for screen readers.","Controls whether a space character is inserted when commenting.","Controls if empty lines should be ignored with toggle, add or remove actions for line comments.","Controls whether copying without a selection copies the current line.","Controls whether the cursor should jump to find matches while typing.","Never seed search string from the editor selection.","Always seed search string from the editor selection, including word at cursor position.","Only seed search string from the editor selection.","Controls whether the search string in the Find Widget is seeded from the editor selection.","Never turn on Find in Selection automatically (default).","Always turn on Find in Selection automatically.","Turn on Find in Selection automatically when multiple lines of content are selected.","Controls the condition for turning on Find in Selection automatically.","Controls whether the Find Widget should read or modify the shared find clipboard on macOS.","Controls whether the Find Widget should add extra lines on top of the editor. When true, you can scroll beyond the first line when the Find Widget is visible.","Controls whether the search automatically restarts from the beginning (or the end) when no further matches can be found.","Enables/Disables font ligatures ('calt' and 'liga' font features). Change this to a string for fine-grained control of the 'font-feature-settings' CSS property.","Explicit 'font-feature-settings' CSS property. A boolean can be passed instead if one only needs to turn on/off ligatures.","Configures font ligatures or font features. Can be either a boolean to enable/disable ligatures or a string for the value of the CSS 'font-feature-settings' property.","Enables/Disables the translation from font-weight to font-variation-settings. Change this to a string for fine-grained control of the 'font-variation-settings' CSS property.","Explicit 'font-variation-settings' CSS property. A boolean can be passed instead if one only needs to translate font-weight to font-variation-settings.","Configures font variations. Can be either a boolean to enable/disable the translation from font-weight to font-variation-settings or a string for the value of the CSS 'font-variation-settings' property.","Controls the font size in pixels.",'Only "normal" and "bold" keywords or numbers between 1 and 1000 are allowed.','Controls the font weight. Accepts "normal" and "bold" keywords or numbers between 1 and 1000.',"Show Peek view of the results (default)","Go to the primary result and show a Peek view","Go to the primary result and enable Peek-less navigation to others","This setting is deprecated, please use separate settings like 'editor.editor.gotoLocation.multipleDefinitions' or 'editor.editor.gotoLocation.multipleImplementations' instead.","Controls the behavior the 'Go to Definition'-command when multiple target locations exist.","Controls the behavior the 'Go to Type Definition'-command when multiple target locations exist.","Controls the behavior the 'Go to Declaration'-command when multiple target locations exist.","Controls the behavior the 'Go to Implementations'-command when multiple target locations exist.","Controls the behavior the 'Go to References'-command when multiple target locations exist.","Alternative command id that is being executed when the result of 'Go to Definition' is the current location.","Alternative command id that is being executed when the result of 'Go to Type Definition' is the current location.","Alternative command id that is being executed when the result of 'Go to Declaration' is the current location.","Alternative command id that is being executed when the result of 'Go to Implementation' is the current location.","Alternative command id that is being executed when the result of 'Go to Reference' is the current location.","Controls whether the hover is shown.","Controls the delay in milliseconds after which the hover is shown.","Controls whether the hover should remain visible when mouse is moved over it.","Controls the delay in milliseconds after which the hover is hidden. Requires `editor.hover.sticky` to be enabled.","Prefer showing hovers above the line, if there's space.","Assumes that all characters are of the same width. This is a fast algorithm that works correctly for monospace fonts and certain scripts (like Latin characters) where glyphs are of equal width.","Delegates wrapping points computation to the browser. This is a slow algorithm, that might cause freezes for large files, but it works correctly in all cases.","Controls the algorithm that computes wrapping points. Note that when in accessibility mode, advanced will be used for the best experience.","Enables the Code Action lightbulb in the editor.","Don not show the AI icon.","Show an AI icon when the code action menu contains an AI action, but only on code.","Show an AI icon when the code action menu contains an AI action, on code and empty lines.","Show an AI icon along with the lightbulb when the code action menu contains an AI action.","Shows the nested current scopes during the scroll at the top of the editor.","Defines the maximum number of sticky lines to show.","Defines the model to use for determining which lines to stick. If the outline model does not exist, it will fall back on the folding provider model which falls back on the indentation model. This order is respected in all three cases.","Enable scrolling of Sticky Scroll with the editor's horizontal scrollbar.","Enables the inlay hints in the editor.","Inlay hints are enabled","Inlay hints are showing by default and hide when holding {0}","Inlay hints are hidden by default and show when holding {0}","Inlay hints are disabled","Controls font size of inlay hints in the editor. As default the {0} is used when the configured value is less than {1} or greater than the editor font size.","Controls font family of inlay hints in the editor. When set to empty, the {0} is used.","Enables the padding around the inlay hints in the editor.",`Controls the line height. 
 - Use 0 to automatically compute the line height from the font size.
 - Values between 0 and 8 will be used as a multiplier with the font size.
 - Values greater than or equal to 8 will be used as effective values.`,"Controls whether the minimap is shown.","Controls whether the minimap is hidden automatically.","The minimap has the same size as the editor contents (and might scroll).","The minimap will stretch or shrink as necessary to fill the height of the editor (no scrolling).","The minimap will shrink as necessary to never be larger than the editor (no scrolling).","Controls the size of the minimap.","Controls the side where to render the minimap.","Controls when the minimap slider is shown.","Scale of content drawn in the minimap: 1, 2 or 3.","Render the actual characters on a line as opposed to color blocks.","Limit the width of the minimap to render at most a certain number of columns.","Controls the amount of space between the top edge of the editor and the first line.","Controls the amount of space between the bottom edge of the editor and the last line.","Enables a pop-up that shows parameter documentation and type information as you type.","Controls whether the parameter hints menu cycles or closes when reaching the end of the list.","Quick suggestions show inside the suggest widget","Quick suggestions show as ghost text","Quick suggestions are disabled","Enable quick suggestions inside strings.","Enable quick suggestions inside comments.","Enable quick suggestions outside of strings and comments.","Controls whether suggestions should automatically show up while typing. This can be controlled for typing in comments, strings, and other code. Quick suggestion can be configured to show as ghost text or with the suggest widget. Also be aware of the '{0}'-setting which controls if suggestions are triggered by special characters.","Line numbers are not rendered.","Line numbers are rendered as absolute number.","Line numbers are rendered as distance in lines to cursor position.","Line numbers are rendered every 10 lines.","Controls the display of line numbers.","Number of monospace characters at which this editor ruler will render.","Color of this editor ruler.","Render vertical rulers after a certain number of monospace characters. Use multiple values for multiple rulers. No rulers are drawn if array is empty.","The vertical scrollbar will be visible only when necessary.","The vertical scrollbar will always be visible.","The vertical scrollbar will always be hidden.","Controls the visibility of the vertical scrollbar.","The horizontal scrollbar will be visible only when necessary.","The horizontal scrollbar will always be visible.","The horizontal scrollbar will always be hidden.","Controls the visibility of the horizontal scrollbar.","The width of the vertical scrollbar.","The height of the horizontal scrollbar.","Controls whether clicks scroll by page or jump to click position.","When set, the horizontal scrollbar will not increase the size of the editor's content.","Controls whether all non-basic ASCII characters are highlighted. Only characters between U+0020 and U+007E, tab, line-feed and carriage-return are considered basic ASCII.","Controls whether characters that just reserve space or have no width at all are highlighted.","Controls whether characters are highlighted that can be confused with basic ASCII characters, except those that are common in the current user locale.","Controls whether characters in comments should also be subject to Unicode highlighting.","Controls whether characters in strings should also be subject to Unicode highlighting.","Defines allowed characters that are not being highlighted.","Unicode characters that are common in allowed locales are not being highlighted.","Controls whether to automatically show inline suggestions in the editor.","Show the inline suggestion toolbar whenever an inline suggestion is shown.","Show the inline suggestion toolbar when hovering over an inline suggestion.","Never show the inline suggestion toolbar.","Controls when to show the inline suggestion toolbar.","Controls how inline suggestions interact with the suggest widget. If enabled, the suggest widget is not shown automatically when inline suggestions are available.","Controls whether bracket pair colorization is enabled or not. Use {0} to override the bracket highlight colors.","Controls whether each bracket type has its own independent color pool.","Enables bracket pair guides.","Enables bracket pair guides only for the active bracket pair.","Disables bracket pair guides.","Controls whether bracket pair guides are enabled or not.","Enables horizontal guides as addition to vertical bracket pair guides.","Enables horizontal guides only for the active bracket pair.","Disables horizontal bracket pair guides.","Controls whether horizontal bracket pair guides are enabled or not.","Controls whether the editor should highlight the active bracket pair.","Controls whether the editor should render indent guides.","Highlights the active indent guide.","Highlights the active indent guide even if bracket guides are highlighted.","Do not highlight the active indent guide.","Controls whether the editor should highlight the active indent guide.","Insert suggestion without overwriting text right of the cursor.","Insert suggestion and overwrite text right of the cursor.","Controls whether words are overwritten when accepting completions. Note that this depends on extensions opting into this feature.","Controls whether filtering and sorting suggestions accounts for small typos.","Controls whether sorting favors words that appear close to the cursor.","Controls whether remembered suggestion selections are shared between multiple workspaces and windows (needs `#editor.suggestSelection#`).","Always select a suggestion when automatically triggering IntelliSense.","Never select a suggestion when automatically triggering IntelliSense.","Select a suggestion only when triggering IntelliSense from a trigger character.","Select a suggestion only when triggering IntelliSense as you type.","Controls whether a suggestion is selected when the widget shows. Note that this only applies to automatically triggered suggestions (`#editor.quickSuggestions#` and `#editor.suggestOnTriggerCharacters#`) and that a suggestion is always selected when explicitly invoked, e.g via `Ctrl+Space`.","Controls whether an active snippet prevents quick suggestions.","Controls whether to show or hide icons in suggestions.","Controls the visibility of the status bar at the bottom of the suggest widget.","Controls whether to preview the suggestion outcome in the editor.","Controls whether suggest details show inline with the label or only in the details widget.","This setting is deprecated. The suggest widget can now be resized.","This setting is deprecated, please use separate settings like 'editor.suggest.showKeywords' or 'editor.suggest.showSnippets' instead.","When enabled IntelliSense shows `method`-suggestions.","When enabled IntelliSense shows `function`-suggestions.","When enabled IntelliSense shows `constructor`-suggestions.","When enabled IntelliSense shows `deprecated`-suggestions.","When enabled IntelliSense filtering requires that the first character matches on a word start. For example, `c` on `Console` or `WebContext` but _not_ on `description`. When disabled IntelliSense will show more results but still sorts them by match quality.","When enabled IntelliSense shows `field`-suggestions.","When enabled IntelliSense shows `variable`-suggestions.","When enabled IntelliSense shows `class`-suggestions.","When enabled IntelliSense shows `struct`-suggestions.","When enabled IntelliSense shows `interface`-suggestions.","When enabled IntelliSense shows `module`-suggestions.","When enabled IntelliSense shows `property`-suggestions.","When enabled IntelliSense shows `event`-suggestions.","When enabled IntelliSense shows `operator`-suggestions.","When enabled IntelliSense shows `unit`-suggestions.","When enabled IntelliSense shows `value`-suggestions.","When enabled IntelliSense shows `constant`-suggestions.","When enabled IntelliSense shows `enum`-suggestions.","When enabled IntelliSense shows `enumMember`-suggestions.","When enabled IntelliSense shows `keyword`-suggestions.","When enabled IntelliSense shows `text`-suggestions.","When enabled IntelliSense shows `color`-suggestions.","When enabled IntelliSense shows `file`-suggestions.","When enabled IntelliSense shows `reference`-suggestions.","When enabled IntelliSense shows `customcolor`-suggestions.","When enabled IntelliSense shows `folder`-suggestions.","When enabled IntelliSense shows `typeParameter`-suggestions.","When enabled IntelliSense shows `snippet`-suggestions.","When enabled IntelliSense shows `user`-suggestions.","When enabled IntelliSense shows `issues`-suggestions.","Whether leading and trailing whitespace should always be selected.","Whether subwords (like 'foo' in 'fooBar' or 'foo_bar') should be selected.","No indentation. Wrapped lines begin at column 1.","Wrapped lines get the same indentation as the parent.","Wrapped lines get +1 indentation toward the parent.","Wrapped lines get +2 indentation toward the parent.","Controls the indentation of wrapped lines.","Controls whether you can drag and drop a file into a text editor by holding down `Shift`-key (instead of opening the file in an editor).","Controls if a widget is shown when dropping files into the editor. This widget lets you control how the file is dropped.","Show the drop selector widget after a file is dropped into the editor.","Never show the drop selector widget. Instead the default drop provider is always used.","Controls whether you can paste content in different ways.","Controls if a widget is shown when pasting content in to the editor. This widget lets you control how the file is pasted.","Show the paste selector widget after content is pasted into the editor.","Never show the paste selector widget. Instead the default pasting behavior is always used.","Controls whether suggestions should be accepted on commit characters. For example, in JavaScript, the semi-colon (`;`) can be a commit character that accepts a suggestion and types that character.","Only accept a suggestion with `Enter` when it makes a textual change.","Controls whether suggestions should be accepted on `Enter`, in addition to `Tab`. Helps to avoid ambiguity between inserting new lines or accepting suggestions.","Controls the number of lines in the editor that can be read out by a screen reader at once. When we detect a screen reader we automatically set the default to be 500. Warning: this has a performance implication for numbers larger than the default.","Editor content","Control whether inline suggestions are announced by a screen reader.","Use language configurations to determine when to autoclose brackets.","Autoclose brackets only when the cursor is to the left of whitespace.","Controls whether the editor should automatically close brackets after the user adds an opening bracket.","Use language configurations to determine when to autoclose comments.","Autoclose comments only when the cursor is to the left of whitespace.","Controls whether the editor should automatically close comments after the user adds an opening comment.","Remove adjacent closing quotes or brackets only if they were automatically inserted.","Controls whether the editor should remove adjacent closing quotes or brackets when deleting.","Type over closing quotes or brackets only if they were automatically inserted.","Controls whether the editor should type over closing quotes or brackets.","Use language configurations to determine when to autoclose quotes.","Autoclose quotes only when the cursor is to the left of whitespace.","Controls whether the editor should automatically close quotes after the user adds an opening quote.","The editor will not insert indentation automatically.","The editor will keep the current line's indentation.","The editor will keep the current line's indentation and honor language defined brackets.","The editor will keep the current line's indentation, honor language defined brackets and invoke special onEnterRules defined by languages.","The editor will keep the current line's indentation, honor language defined brackets, invoke special onEnterRules defined by languages, and honor indentationRules defined by languages.","Controls whether the editor should automatically adjust the indentation when users type, paste, move or indent lines.","Use language configurations to determine when to automatically surround selections.","Surround with quotes but not brackets.","Surround with brackets but not quotes.","Controls whether the editor should automatically surround selections when typing quotes or brackets.","Emulate selection behavior of tab characters when using spaces for indentation. Selection will stick to tab stops.","Controls whether the editor shows CodeLens.","Controls the font family for CodeLens.","Controls the font size in pixels for CodeLens. When set to 0, 90% of `#editor.fontSize#` is used.","Controls whether the editor should render the inline color decorators and color picker.","Make the color picker appear both on click and hover of the color decorator","Make the color picker appear on hover of the color decorator","Make the color picker appear on click of the color decorator","Controls the condition to make a color picker appear from a color decorator","Controls the max number of color decorators that can be rendered in an editor at once.","Enable that the selection with the mouse and keys is doing column selection.","Controls whether syntax highlighting should be copied into the clipboard.","Control the cursor animation style.","Smooth caret animation is disabled.","Smooth caret animation is enabled only when the user moves the cursor with an explicit gesture.","Smooth caret animation is always enabled.","Controls whether the smooth caret animation should be enabled.","Controls the cursor style.","Controls the minimal number of visible leading lines (minimum 0) and trailing lines (minimum 1) surrounding the cursor. Known as 'scrollOff' or 'scrollOffset' in some other editors.","`cursorSurroundingLines` is enforced only when triggered via the keyboard or API.","`cursorSurroundingLines` is enforced always.","Controls when `#cursorSurroundingLines#` should be enforced.","Controls the width of the cursor when `#editor.cursorStyle#` is set to `line`.","Controls whether the editor should allow moving selections via drag and drop.","Use a new rendering method with svgs.","Use a new rendering method with font characters.","Use the stable rendering method.","Controls whether whitespace is rendered with a new, experimental method.","Scrolling speed multiplier when pressing `Alt`.","Controls whether the editor has code folding enabled.","Use a language-specific folding strategy if available, else the indentation-based one.","Use the indentation-based folding strategy.","Controls the strategy for computing folding ranges.","Controls whether the editor should highlight folded ranges.","Controls whether the editor automatically collapses import ranges.","The maximum number of foldable regions. Increasing this value may result in the editor becoming less responsive when the current source has a large number of foldable regions.","Controls whether clicking on the empty content after a folded line will unfold the line.","Controls the font family.","Controls whether the editor should automatically format the pasted content. A formatter must be available and the formatter should be able to format a range in a document.","Controls whether the editor should automatically format the line after typing.","Controls whether the editor should render the vertical glyph margin. Glyph margin is mostly used for debugging.","Controls whether the cursor should be hidden in the overview ruler.","Controls the letter spacing in pixels.","Controls whether the editor has linked editing enabled. Depending on the language, related symbols such as HTML tags, are updated while editing.","Controls whether the editor should detect links and make them clickable.","Highlight matching brackets.","A multiplier to be used on the `deltaX` and `deltaY` of mouse wheel scroll events.","Zoom the font of the editor when using mouse wheel and holding `Ctrl`.","Merge multiple cursors when they are overlapping.","Maps to `Control` on Windows and Linux and to `Command` on macOS.","Maps to `Alt` on Windows and Linux and to `Option` on macOS.","The modifier to be used to add multiple cursors with the mouse. The Go to Definition and Open Link mouse gestures will adapt such that they do not conflict with the [multicursor modifier](https://code.visualstudio.com/docs/editor/codebasics#_multicursor-modifier).","Each cursor pastes a single line of the text.","Each cursor pastes the full text.","Controls pasting when the line count of the pasted text matches the cursor count.","Controls the max number of cursors that can be in an active editor at once.","Does not highlight occurrences.","Highlights occurrences only in the current file.","Experimental: Highlights occurrences across all valid open files.","Controls whether occurrences should be highlighted across open files.","Controls whether a border should be drawn around the overview ruler.","Focus the tree when opening peek","Focus the editor when opening peek","Controls whether to focus the inline editor or the tree in the peek widget.","Controls whether the Go to Definition mouse gesture always opens the peek widget.","Controls the delay in milliseconds after which quick suggestions will show up.","Controls whether the editor auto renames on type.","Deprecated, use `editor.linkedEditing` instead.","Controls whether the editor should render control characters.","Render last line number when the file ends with a newline.","Highlights both the gutter and the current line.","Controls how the editor should render the current line highlight.","Controls if the editor should render the current line highlight only when the editor is focused.","Render whitespace characters except for single spaces between words.","Render whitespace characters only on selected text.","Render only trailing whitespace characters.","Controls how the editor should render whitespace characters.","Controls whether selections should have rounded corners.","Controls the number of extra characters beyond which the editor will scroll horizontally.","Controls whether the editor will scroll beyond the last line.","Scroll only along the predominant axis when scrolling both vertically and horizontally at the same time. Prevents horizontal drift when scrolling vertically on a trackpad.","Controls whether the Linux primary clipboard should be supported.","Controls whether the editor should highlight matches similar to the selection.","Always show the folding controls.","Never show the folding controls and reduce the gutter size.","Only show the folding controls when the mouse is over the gutter.","Controls when the folding controls on the gutter are shown.","Controls fading out of unused code.","Controls strikethrough deprecated variables.","Show snippet suggestions on top of other suggestions.","Show snippet suggestions below other suggestions.","Show snippets suggestions with other suggestions.","Do not show snippet suggestions.","Controls whether snippets are shown with other suggestions and how they are sorted.","Controls whether the editor will scroll using an animation.","Controls whether the accessibility hint should be provided to screen reader users when an inline completion is shown.","Font size for the suggest widget. When set to {0}, the value of {1} is used.","Line height for the suggest widget. When set to {0}, the value of {1} is used. The minimum value is 8.","Controls whether suggestions should automatically show up when typing trigger characters.","Always select the first suggestion.","Select recent suggestions unless further typing selects one, e.g. `console.| -> console.log` because `log` has been completed recently.","Select suggestions based on previous prefixes that have completed those suggestions, e.g. `co -> console` and `con -> const`.","Controls how suggestions are pre-selected when showing the suggest list.","Tab complete will insert the best matching suggestion when pressing tab.","Disable tab completions.","Tab complete snippets when their prefix match. Works best when 'quickSuggestions' aren't enabled.","Enables tab completions.","Unusual line terminators are automatically removed.","Unusual line terminators are ignored.","Unusual line terminators prompt to be removed.","Remove unusual line terminators that might cause problems.","Inserting and deleting whitespace follows tab stops.","Use the default line break rule.","Word breaks should not be used for Chinese/Japanese/Korean (CJK) text. Non-CJK text behavior is the same as for normal.","Controls the word break rules used for Chinese/Japanese/Korean (CJK) text.","Characters that will be used as word separators when doing word related navigations or operations.","Lines will never wrap.","Lines will wrap at the viewport width.","Lines will wrap at `#editor.wordWrapColumn#`.","Lines will wrap at the minimum of viewport and `#editor.wordWrapColumn#`.","Controls how lines should wrap.","Controls the wrapping column of the editor when `#editor.wordWrap#` is `wordWrapColumn` or `bounded`.","Controls whether inline color decorations should be shown using the default document color provider","Controls whether the editor receives tabs or defers them to the workbench for navigation."],"vs/editor/common/core/editorColorRegistry":["Background color for the highlight of line at the cursor position.","Background color for the border around the line at the cursor position.","Background color of highlighted ranges, like by quick open and find features. The color must not be opaque so as not to hide underlying decorations.","Background color of the border around highlighted ranges.","Background color of highlighted symbol, like for go to definition or go next/previous symbol. The color must not be opaque so as not to hide underlying decorations.","Background color of the border around highlighted symbols.","Color of the editor cursor.","The background color of the editor cursor. Allows customizing the color of a character overlapped by a block cursor.","Color of whitespace characters in the editor.","Color of editor line numbers.","Color of the editor indentation guides.","'editorIndentGuide.background' is deprecated. Use 'editorIndentGuide.background1' instead.","Color of the active editor indentation guides.","'editorIndentGuide.activeBackground' is deprecated. Use 'editorIndentGuide.activeBackground1' instead.","Color of the editor indentation guides (1).","Color of the editor indentation guides (2).","Color of the editor indentation guides (3).","Color of the editor indentation guides (4).","Color of the editor indentation guides (5).","Color of the editor indentation guides (6).","Color of the active editor indentation guides (1).","Color of the active editor indentation guides (2).","Color of the active editor indentation guides (3).","Color of the active editor indentation guides (4).","Color of the active editor indentation guides (5).","Color of the active editor indentation guides (6).","Color of editor active line number","Id is deprecated. Use 'editorLineNumber.activeForeground' instead.","Color of editor active line number","Color of the final editor line when editor.renderFinalNewline is set to dimmed.","Color of the editor rulers.","Foreground color of editor CodeLens","Background color behind matching brackets","Color for matching brackets boxes","Color of the overview ruler border.","Background color of the editor overview ruler.","Background color of the editor gutter. The gutter contains the glyph margins and the line numbers.","Border color of unnecessary (unused) source code in the editor.",`Opacity of unnecessary (unused) source code in the editor. For example, "#000000c0" will render the code with 75% opacity. For high contrast themes, use the  'editorUnnecessaryCode.border' theme color to underline unnecessary code instead of fading it out.`,"Border color of ghost text in the editor.","Foreground color of the ghost text in the editor.","Background color of the ghost text in the editor.","Overview ruler marker color for range highlights. The color must not be opaque so as not to hide underlying decorations.","Overview ruler marker color for errors.","Overview ruler marker color for warnings.","Overview ruler marker color for infos.","Foreground color of brackets (1). Requires enabling bracket pair colorization.","Foreground color of brackets (2). Requires enabling bracket pair colorization.","Foreground color of brackets (3). Requires enabling bracket pair colorization.","Foreground color of brackets (4). Requires enabling bracket pair colorization.","Foreground color of brackets (5). Requires enabling bracket pair colorization.","Foreground color of brackets (6). Requires enabling bracket pair colorization.","Foreground color of unexpected brackets.","Background color of inactive bracket pair guides (1). Requires enabling bracket pair guides.","Background color of inactive bracket pair guides (2). Requires enabling bracket pair guides.","Background color of inactive bracket pair guides (3). Requires enabling bracket pair guides.","Background color of inactive bracket pair guides (4). Requires enabling bracket pair guides.","Background color of inactive bracket pair guides (5). Requires enabling bracket pair guides.","Background color of inactive bracket pair guides (6). Requires enabling bracket pair guides.","Background color of active bracket pair guides (1). Requires enabling bracket pair guides.","Background color of active bracket pair guides (2). Requires enabling bracket pair guides.","Background color of active bracket pair guides (3). Requires enabling bracket pair guides.","Background color of active bracket pair guides (4). Requires enabling bracket pair guides.","Background color of active bracket pair guides (5). Requires enabling bracket pair guides.","Background color of active bracket pair guides (6). Requires enabling bracket pair guides.","Border color used to highlight unicode characters.","Background color used to highlight unicode characters."],"vs/editor/common/editorContextKeys":["Whether the editor text has focus (cursor is blinking)","Whether the editor or an editor widget has focus (e.g. focus is in the find widget)","Whether an editor or a rich text input has focus (cursor is blinking)","Whether the editor is read-only","Whether the context is a diff editor","Whether the context is an embedded diff editor","Whether the context is a multi diff editor","Whether all files in multi diff editor are collapsed","Whether the diff editor has changes","Whether a moved code block is selected for comparison","Whether the accessible diff viewer is visible","Whether the diff editor render side by side inline breakpoint is reached","Whether `editor.columnSelection` is enabled","Whether the editor has text selected","Whether the editor has multiple selections","Whether `Tab` will move focus out of the editor","Whether the editor hover is visible","Whether the editor hover is focused","Whether the sticky scroll is focused","Whether the sticky scroll is visible","Whether the standalone color picker is visible","Whether the standalone color picker is focused","Whether the editor is part of a larger editor (e.g. notebooks)","The language identifier of the editor","Whether the editor has a completion item provider","Whether the editor has a code actions provider","Whether the editor has a code lens provider","Whether the editor has a definition provider","Whether the editor has a declaration provider","Whether the editor has an implementation provider","Whether the editor has a type definition provider","Whether the editor has a hover provider","Whether the editor has a document highlight provider","Whether the editor has a document symbol provider","Whether the editor has a reference provider","Whether the editor has a rename provider","Whether the editor has a signature help provider","Whether the editor has an inline hints provider","Whether the editor has a document formatting provider","Whether the editor has a document selection formatting provider","Whether the editor has multiple document formatting providers","Whether the editor has multiple document selection formatting providers"],"vs/editor/common/languages":["array","boolean","class","constant","constructor","enumeration","enumeration member","event","field","file","function","interface","key","method","module","namespace","null","number","object","operator","package","property","string","struct","type parameter","variable","{0} ({1})"],"vs/editor/common/languages/modesRegistry":["Plain Text"],"vs/editor/common/model/editStack":["Typing"],"vs/editor/common/standaloneStrings":["Developer: Inspect Tokens","Go to Line/Column...","Show all Quick Access Providers","Command Palette","Show And Run Commands","Go to Symbol...","Go to Symbol by Category...","Editor content","Press Alt+F1 for Accessibility Options.","Toggle High Contrast Theme","Made {0} edits in {1} files"],"vs/editor/common/viewLayout/viewLineRenderer":["Show more ({0})","{0} chars"],"vs/editor/contrib/anchorSelect/browser/anchorSelect":["Selection Anchor","Anchor set at {0}:{1}","Set Selection Anchor","Go to Selection Anchor","Select from Anchor to Cursor","Cancel Selection Anchor"],"vs/editor/contrib/bracketMatching/browser/bracketMatching":["Overview ruler marker color for matching brackets.","Go to Bracket","Select to Bracket","Remove Brackets","Go to &&Bracket","Select the text inside and including the brackets or curly braces"],"vs/editor/contrib/caretOperations/browser/caretOperations":["Move Selected Text Left","Move Selected Text Right"],"vs/editor/contrib/caretOperations/browser/transpose":["Transpose Letters"],"vs/editor/contrib/clipboard/browser/clipboard":["Cu&&t","Cut","Cut","Cut","&&Copy","Copy","Copy","Copy","Copy As","Copy As","Share","Share","Share","&&Paste","Paste","Paste","Paste","Copy With Syntax Highlighting"],"vs/editor/contrib/codeAction/browser/codeAction":["An unknown error occurred while applying the code action"],"vs/editor/contrib/codeAction/browser/codeActionCommands":["Kind of the code action to run.","Controls when the returned actions are applied.","Always apply the first returned code action.","Apply the first returned code action if it is the only one.","Do not apply the returned code actions.","Controls if only preferred code actions should be returned.","Quick Fix...","No code actions available","No preferred code actions for '{0}' available","No code actions for '{0}' available","No preferred code actions available","No code actions available","Refactor...","No preferred refactorings for '{0}' available","No refactorings for '{0}' available","No preferred refactorings available","No refactorings available","Source Action...","No preferred source actions for '{0}' available","No source actions for '{0}' available","No preferred source actions available","No source actions available","Organize Imports","No organize imports action available","Fix All","No fix all action available","Auto Fix...","No auto fixes available"],"vs/editor/contrib/codeAction/browser/codeActionContributions":["Enable/disable showing group headers in the Code Action menu.","Enable/disable showing nearest Quick Fix within a line when not currently on a diagnostic."],"vs/editor/contrib/codeAction/browser/codeActionController":["Context: {0} at line {1} and column {2}.","Hide Disabled","Show Disabled"],"vs/editor/contrib/codeAction/browser/codeActionMenu":["More Actions...","Quick Fix","Extract","Inline","Rewrite","Move","Surround With","Source Action"],"vs/editor/contrib/codeAction/browser/lightBulbWidget":["Show Code Actions. Preferred Quick Fix Available ({0})","Show Code Actions ({0})","Show Code Actions","Start Inline Chat ({0})","Start Inline Chat","Trigger AI Action"],"vs/editor/contrib/codelens/browser/codelensController":["Show CodeLens Commands For Current Line","Select a command"],"vs/editor/contrib/colorPicker/browser/colorPickerWidget":["Click to toggle color options (rgb/hsl/hex)","Icon to close the color picker"],"vs/editor/contrib/colorPicker/browser/standaloneColorPickerActions":["Show or Focus Standalone Color Picker","&&Show or Focus Standalone Color Picker","Hide the Color Picker","Insert Color with Standalone Color Picker"],"vs/editor/contrib/comment/browser/comment":["Toggle Line Comment","&&Toggle Line Comment","Add Line Comment","Remove Line Comment","Toggle Block Comment","Toggle &&Block Comment"],"vs/editor/contrib/contextmenu/browser/contextmenu":["Minimap","Render Characters","Vertical size","Proportional","Fill","Fit","Slider","Mouse Over","Always","Show Editor Context Menu"],"vs/editor/contrib/cursorUndo/browser/cursorUndo":["Cursor Undo","Cursor Redo"],"vs/editor/contrib/dropOrPasteInto/browser/copyPasteContribution":["Paste As...","The id of the paste edit to try applying. If not provided, the editor will show a picker."],"vs/editor/contrib/dropOrPasteInto/browser/copyPasteController":["Whether the paste widget is showing","Show paste options...","Running paste handlers. Click to cancel","Select Paste Action","Running paste handlers"],"vs/editor/contrib/dropOrPasteInto/browser/defaultProviders":["Built-in","Insert Plain Text","Insert Uris","Insert Uri","Insert Paths","Insert Path","Insert Relative Paths","Insert Relative Path"],"vs/editor/contrib/dropOrPasteInto/browser/dropIntoEditorContribution":["Configures the default drop provider to use for content of a given mime type."],"vs/editor/contrib/dropOrPasteInto/browser/dropIntoEditorController":["Whether the drop widget is showing","Show drop options...","Running drop handlers. Click to cancel"],"vs/editor/contrib/editorState/browser/keybindingCancellation":["Whether the editor runs a cancellable operation, e.g. like 'Peek References'"],"vs/editor/contrib/find/browser/findController":["The file is too large to perform a replace all operation.","Find","&&Find",`Overrides "Use Regular Expression" flag.
The flag will not be saved for the future.
0: Do Nothing
1: True
2: False`,`Overrides "Match Whole Word" flag.
The flag will not be saved for the future.
0: Do Nothing
1: True
2: False`,`Overrides "Math Case" flag.
The flag will not be saved for the future.
0: Do Nothing
1: True
2: False`,`Overrides "Preserve Case" flag.
The flag will not be saved for the future.
0: Do Nothing
1: True
2: False`,"Find With Arguments","Find With Selection","Find Next","Find Previous","Go to Match...","No matches. Try searching for something else.","Type a number to go to a specific match (between 1 and {0})","Please type a number between 1 and {0}","Please type a number between 1 and {0}","Find Next Selection","Find Previous Selection","Replace","&&Replace"],"vs/editor/contrib/find/browser/findWidget":["Icon for 'Find in Selection' in the editor find widget.","Icon to indicate that the editor find widget is collapsed.","Icon to indicate that the editor find widget is expanded.","Icon for 'Replace' in the editor find widget.","Icon for 'Replace All' in the editor find widget.","Icon for 'Find Previous' in the editor find widget.","Icon for 'Find Next' in the editor find widget.","Find / Replace","Find","Find","Previous Match","Next Match","Find in Selection","Close","Replace","Replace","Replace","Replace All","Toggle Replace","Only the first {0} results are highlighted, but all find operations work on the entire text.","{0} of {1}","No results","{0} found","{0} found for '{1}'","{0} found for '{1}', at {2}","{0} found for '{1}'","Ctrl+Enter now inserts line break instead of replacing all. You can modify the keybinding for editor.action.replaceAll to override this behavior."],"vs/editor/contrib/folding/browser/folding":["Unfold","Unfold Recursively","Fold","Toggle Fold","Fold Recursively","Fold All Block Comments","Fold All Regions","Unfold All Regions","Fold All Except Selected","Unfold All Except Selected","Fold All","Unfold All","Go to Parent Fold","Go to Previous Folding Range","Go to Next Folding Range","Create Folding Range from Selection","Remove Manual Folding Ranges","Fold Level {0}"],"vs/editor/contrib/folding/browser/foldingDecorations":["Background color behind folded ranges. The color must not be opaque so as not to hide underlying decorations.","Color of the folding control in the editor gutter.","Icon for expanded ranges in the editor glyph margin.","Icon for collapsed ranges in the editor glyph margin.","Icon for manually collapsed ranges in the editor glyph margin.","Icon for manually expanded ranges in the editor glyph margin."],"vs/editor/contrib/fontZoom/browser/fontZoom":["Editor Font Zoom In","Editor Font Zoom Out","Editor Font Zoom Reset"],"vs/editor/contrib/format/browser/formatActions":["Format Document","Format Selection"],"vs/editor/contrib/gotoError/browser/gotoError":["Go to Next Problem (Error, Warning, Info)","Icon for goto next marker.","Go to Previous Problem (Error, Warning, Info)","Icon for goto previous marker.","Go to Next Problem in Files (Error, Warning, Info)","Next &&Problem","Go to Previous Problem in Files (Error, Warning, Info)","Previous &&Problem"],"vs/editor/contrib/gotoError/browser/gotoErrorWidget":["Error","Warning","Info","Hint","{0} at {1}. ","{0} of {1} problems","{0} of {1} problem","Editor marker navigation widget error color.","Editor marker navigation widget error heading background.","Editor marker navigation widget warning color.","Editor marker navigation widget warning heading background.","Editor marker navigation widget info color.","Editor marker navigation widget info heading background.","Editor marker navigation widget background."],"vs/editor/contrib/gotoSymbol/browser/goToCommands":["Peek","Definitions","No definition found for '{0}'","No definition found","Go to Definition","Go to &&Definition","Open Definition to the Side","Peek Definition","Declarations","No declaration found for '{0}'","No declaration found","Go to Declaration","Go to &&Declaration","No declaration found for '{0}'","No declaration found","Peek Declaration","Type Definitions","No type definition found for '{0}'","No type definition found","Go to Type Definition","Go to &&Type Definition","Peek Type Definition","Implementations","No implementation found for '{0}'","No implementation found","Go to Implementations","Go to &&Implementations","Peek Implementations","No references found for '{0}'","No references found","Go to References","Go to &&References","References","Peek References","References","Go to Any Symbol","Locations","No results for '{0}'","References"],"vs/editor/contrib/gotoSymbol/browser/link/goToDefinitionAtPosition":["Click to show {0} definitions."],"vs/editor/contrib/gotoSymbol/browser/peek/referencesController":["Whether reference peek is visible, like 'Peek References' or 'Peek Definition'","Loading...","{0} ({1})"],"vs/editor/contrib/gotoSymbol/browser/peek/referencesTree":["{0} references","{0} reference","References"],"vs/editor/contrib/gotoSymbol/browser/peek/referencesWidget":["no preview available","No results","References"],"vs/editor/contrib/gotoSymbol/browser/referencesModel":["in {0} on line {1} at column {2}","{0} in {1} on line {2} at column {3}","1 symbol in {0}, full path {1}","{0} symbols in {1}, full path {2}","No results found","Found 1 symbol in {0}","Found {0} symbols in {1}","Found {0} symbols in {1} files"],"vs/editor/contrib/gotoSymbol/browser/symbolNavigation":["Whether there are symbol locations that can be navigated via keyboard-only.","Symbol {0} of {1}, {2} for next","Symbol {0} of {1}"],"vs/editor/contrib/hover/browser/hover":["Show or Focus Hover","The hover will not automatically take focus.","The hover will take focus only if it is already visible.","The hover will automatically take focus when it appears.","Show Definition Preview Hover","Scroll Up Hover","Scroll Down Hover","Scroll Left Hover","Scroll Right Hover","Page Up Hover","Page Down Hover","Go To Top Hover","Go To Bottom Hover"],"vs/editor/contrib/hover/browser/markdownHoverParticipant":["Loading...","Rendering paused for long line for performance reasons. This can be configured via `editor.stopRenderingLineAfter`.","Tokenization is skipped for long lines for performance reasons. This can be configured via `editor.maxTokenizationLineLength`."],"vs/editor/contrib/hover/browser/markerHoverParticipant":["View Problem","No quick fixes available","Checking for quick fixes...","No quick fixes available","Quick Fix..."],"vs/editor/contrib/inPlaceReplace/browser/inPlaceReplace":["Replace with Previous Value","Replace with Next Value"],"vs/editor/contrib/indentation/browser/indentation":["Convert Indentation to Spaces","Convert Indentation to Tabs","Configured Tab Size","Default Tab Size","Current Tab Size","Select Tab Size for Current File","Indent Using Tabs","Indent Using Spaces","Change Tab Display Size","Detect Indentation from Content","Reindent Lines","Reindent Selected Lines"],"vs/editor/contrib/inlayHints/browser/inlayHintsHover":["Double-click to insert","cmd + click","ctrl + click","option + click","alt + click","Go to Definition ({0}), right click for more","Go to Definition ({0})","Execute Command"],"vs/editor/contrib/inlineCompletions/browser/commands":["Show Next Inline Suggestion","Show Previous Inline Suggestion","Trigger Inline Suggestion","Accept Next Word Of Inline Suggestion","Accept Word","Accept Next Line Of Inline Suggestion","Accept Line","Accept Inline Suggestion","Accept","Hide Inline Suggestion","Always Show Toolbar"],"vs/editor/contrib/inlineCompletions/browser/hoverParticipant":["Suggestion:"],"vs/editor/contrib/inlineCompletions/browser/inlineCompletionContextKeys":["Whether an inline suggestion is visible","Whether the inline suggestion starts with whitespace","Whether the inline suggestion starts with whitespace that is less than what would be inserted by tab","Whether suggestions should be suppressed for the current suggestion"],"vs/editor/contrib/inlineCompletions/browser/inlineCompletionsController":["Inspect this in the accessible view ({0})"],"vs/editor/contrib/inlineCompletions/browser/inlineCompletionsHintsWidget":["Icon for show next parameter hint.","Icon for show previous parameter hint.","{0} ({1})","Previous","Next"],"vs/editor/contrib/lineSelection/browser/lineSelection":["Expand Line Selection"],"vs/editor/contrib/linesOperations/browser/linesOperations":["Copy Line Up","&&Copy Line Up","Copy Line Down","Co&&py Line Down","Duplicate Selection","&&Duplicate Selection","Move Line Up","Mo&&ve Line Up","Move Line Down","Move &&Line Down","Sort Lines Ascending","Sort Lines Descending","Delete Duplicate Lines","Trim Trailing Whitespace","Delete Line","Indent Line","Outdent Line","Insert Line Above","Insert Line Below","Delete All Left","Delete All Right","Join Lines","Transpose Characters around the Cursor","Transform to Uppercase","Transform to Lowercase","Transform to Title Case","Transform to Snake Case","Transform to Camel Case","Transform to Kebab Case"],"vs/editor/contrib/linkedEditing/browser/linkedEditing":["Start Linked Editing","Background color when the editor auto renames on type."],"vs/editor/contrib/links/browser/links":["Failed to open this link because it is not well-formed: {0}","Failed to open this link because its target is missing.","Execute command","Follow link","cmd + click","ctrl + click","option + click","alt + click","Execute command {0}","Open Link"],"vs/editor/contrib/message/browser/messageController":["Whether the editor is currently showing an inline message"],"vs/editor/contrib/multicursor/browser/multicursor":["Cursor added: {0}","Cursors added: {0}","Add Cursor Above","&&Add Cursor Above","Add Cursor Below","A&&dd Cursor Below","Add Cursors to Line Ends","Add C&&ursors to Line Ends","Add Cursors To Bottom","Add Cursors To Top","Add Selection To Next Find Match","Add &&Next Occurrence","Add Selection To Previous Find Match","Add P&&revious Occurrence","Move Last Selection To Next Find Match","Move Last Selection To Previous Find Match","Select All Occurrences of Find Match","Select All &&Occurrences","Change All Occurrences","Focus Next Cursor","Focuses the next cursor","Focus Previous Cursor","Focuses the previous cursor"],"vs/editor/contrib/parameterHints/browser/parameterHints":["Trigger Parameter Hints"],"vs/editor/contrib/parameterHints/browser/parameterHintsWidget":["Icon for show next parameter hint.","Icon for show previous parameter hint.","{0}, hint","Foreground color of the active item in the parameter hint."],"vs/editor/contrib/peekView/browser/peekView":["Whether the current code editor is embedded inside peek","Close","Background color of the peek view title area.","Color of the peek view title.","Color of the peek view title info.","Color of the peek view borders and arrow.","Background color of the peek view result list.","Foreground color for line nodes in the peek view result list.","Foreground color for file nodes in the peek view result list.","Background color of the selected entry in the peek view result list.","Foreground color of the selected entry in the peek view result list.","Background color of the peek view editor.","Background color of the gutter in the peek view editor.","Background color of sticky scroll in the peek view editor.","Match highlight color in the peek view result list.","Match highlight color in the peek view editor.","Match highlight border in the peek view editor."],"vs/editor/contrib/quickAccess/browser/gotoLineQuickAccess":["Open a text editor first to go to a line.","Go to line {0} and character {1}.","Go to line {0}.","Current Line: {0}, Character: {1}. Type a line number between 1 and {2} to navigate to.","Current Line: {0}, Character: {1}. Type a line number to navigate to."],"vs/editor/contrib/quickAccess/browser/gotoSymbolQuickAccess":["To go to a symbol, first open a text editor with symbol information.","The active text editor does not provide symbol information.","No matching editor symbols","No editor symbols","Open to the Side","Open to the Bottom","symbols ({0})","properties ({0})","methods ({0})","functions ({0})","constructors ({0})","variables ({0})","classes ({0})","structs ({0})","events ({0})","operators ({0})","interfaces ({0})","namespaces ({0})","packages ({0})","type parameters ({0})","modules ({0})","properties ({0})","enumerations ({0})","enumeration members ({0})","strings ({0})","files ({0})","arrays ({0})","numbers ({0})","booleans ({0})","objects ({0})","keys ({0})","fields ({0})","constants ({0})"],"vs/editor/contrib/readOnlyMessage/browser/contribution":["Cannot edit in read-only input","Cannot edit in read-only editor"],"vs/editor/contrib/rename/browser/rename":["No result.","An unknown error occurred while resolving rename location","Renaming '{0}' to '{1}'","Renaming {0} to {1}","Successfully renamed '{0}' to '{1}'. Summary: {2}","Rename failed to apply edits","Rename failed to compute edits","Rename Symbol","Enable/disable the ability to preview changes before renaming"],"vs/editor/contrib/rename/browser/renameInputField":["Whether the rename input widget is visible","Rename input. Type new name and press Enter to commit.","{0} to Rename, {1} to Preview"],"vs/editor/contrib/smartSelect/browser/smartSelect":["Expand Selection","&&Expand Selection","Shrink Selection","&&Shrink Selection"],"vs/editor/contrib/snippet/browser/snippetController2":["Whether the editor in current in snippet mode","Whether there is a next tab stop when in snippet mode","Whether there is a previous tab stop when in snippet mode","Go to next placeholder..."],"vs/editor/contrib/snippet/browser/snippetVariables":["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday","Sun","Mon","Tue","Wed","Thu","Fri","Sat","January","February","March","April","May","June","July","August","September","October","November","December","Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],"vs/editor/contrib/stickyScroll/browser/stickyScrollActions":["Toggle Sticky Scroll","&&Toggle Sticky Scroll","Sticky Scroll","&&Sticky Scroll","Focus Sticky Scroll","&&Focus Sticky Scroll","Select next sticky scroll line","Select previous sticky scroll line","Go to focused sticky scroll line","Select Editor"],"vs/editor/contrib/suggest/browser/suggest":["Whether any suggestion is focused","Whether suggestion details are visible","Whether there are multiple suggestions to pick from","Whether inserting the current suggestion yields in a change or has everything already been typed","Whether suggestions are inserted when pressing Enter","Whether the current suggestion has insert and replace behaviour","Whether the default behaviour is to insert or replace","Whether the current suggestion supports to resolve further details"],"vs/editor/contrib/suggest/browser/suggestController":["Accepting '{0}' made {1} additional edits","Trigger Suggest","Insert","Insert","Replace","Replace","Insert","show less","show more","Reset Suggest Widget Size"],"vs/editor/contrib/suggest/browser/suggestWidget":["Background color of the suggest widget.","Border color of the suggest widget.","Foreground color of the suggest widget.","Foreground color of the selected entry in the suggest widget.","Icon foreground color of the selected entry in the suggest widget.","Background color of the selected entry in the suggest widget.","Color of the match highlights in the suggest widget.","Color of the match highlights in the suggest widget when an item is focused.","Foreground color of the suggest widget status.","Loading...","No suggestions.","Suggest","{0} {1}, {2}","{0} {1}","{0}, {1}","{0}, docs: {1}"],"vs/editor/contrib/suggest/browser/suggestWidgetDetails":["Close","Loading..."],"vs/editor/contrib/suggest/browser/suggestWidgetRenderer":["Icon for more information in the suggest widget.","Read More"],"vs/editor/contrib/suggest/browser/suggestWidgetStatus":["{0} ({1})"],"vs/editor/contrib/symbolIcons/browser/symbolIcons":["The foreground color for array symbols. These symbols appear in the outline, breadcrumb, and suggest widget.","The foreground color for boolean symbols. These symbols appear in the outline, breadcrumb, and suggest widget.","The foreground color for class symbols. These symbols appear in the outline, breadcrumb, and suggest widget.","The foreground color for color symbols. These symbols appear in the outline, breadcrumb, and suggest widget.","The foreground color for constant symbols. These symbols appear in the outline, breadcrumb, and suggest widget.","The foreground color for constructor symbols. These symbols appear in the outline, breadcrumb, and suggest widget.","The foreground color for enumerator symbols. These symbols appear in the outline, breadcrumb, and suggest widget.","The foreground color for enumerator member symbols. These symbols appear in the outline, breadcrumb, and suggest widget.","The foreground color for event symbols. These symbols appear in the outline, breadcrumb, and suggest widget.","The foreground color for field symbols. These symbols appear in the outline, breadcrumb, and suggest widget.","The foreground color for file symbols. These symbols appear in the outline, breadcrumb, and suggest widget.","The foreground color for folder symbols. These symbols appear in the outline, breadcrumb, and suggest widget.","The foreground color for function symbols. These symbols appear in the outline, breadcrumb, and suggest widget.","The foreground color for interface symbols. These symbols appear in the outline, breadcrumb, and suggest widget.","The foreground color for key symbols. These symbols appear in the outline, breadcrumb, and suggest widget.","The foreground color for keyword symbols. These symbols appear in the outline, breadcrumb, and suggest widget.","The foreground color for method symbols. These symbols appear in the outline, breadcrumb, and suggest widget.","The foreground color for module symbols. These symbols appear in the outline, breadcrumb, and suggest widget.","The foreground color for namespace symbols. These symbols appear in the outline, breadcrumb, and suggest widget.","The foreground color for null symbols. These symbols appear in the outline, breadcrumb, and suggest widget.","The foreground color for number symbols. These symbols appear in the outline, breadcrumb, and suggest widget.","The foreground color for object symbols. These symbols appear in the outline, breadcrumb, and suggest widget.","The foreground color for operator symbols. These symbols appear in the outline, breadcrumb, and suggest widget.","The foreground color for package symbols. These symbols appear in the outline, breadcrumb, and suggest widget.","The foreground color for property symbols. These symbols appear in the outline, breadcrumb, and suggest widget.","The foreground color for reference symbols. These symbols appear in the outline, breadcrumb, and suggest widget.","The foreground color for snippet symbols. These symbols appear in the outline, breadcrumb, and suggest widget.","The foreground color for string symbols. These symbols appear in the outline, breadcrumb, and suggest widget.","The foreground color for struct symbols. These symbols appear in the outline, breadcrumb, and suggest widget.","The foreground color for text symbols. These symbols appear in the outline, breadcrumb, and suggest widget.","The foreground color for type parameter symbols. These symbols appear in the outline, breadcrumb, and suggest widget.","The foreground color for unit symbols. These symbols appear in the outline, breadcrumb, and suggest widget.","The foreground color for variable symbols. These symbols appear in the outline, breadcrumb, and suggest widget."],"vs/editor/contrib/toggleTabFocusMode/browser/toggleTabFocusMode":["Toggle Tab Key Moves Focus","Pressing Tab will now move focus to the next focusable element","Pressing Tab will now insert the tab character"],"vs/editor/contrib/tokenization/browser/tokenization":["Developer: Force Retokenize"],"vs/editor/contrib/unicodeHighlighter/browser/unicodeHighlighter":["Icon shown with a warning message in the extensions editor.","This document contains many non-basic ASCII unicode characters","This document contains many ambiguous unicode characters","This document contains many invisible unicode characters","The character {0} could be confused with the ASCII character {1}, which is more common in source code.","The character {0} could be confused with the character {1}, which is more common in source code.","The character {0} is invisible.","The character {0} is not a basic ASCII character.","Adjust settings","Disable Highlight In Comments","Disable highlighting of characters in comments","Disable Highlight In Strings","Disable highlighting of characters in strings","Disable Ambiguous Highlight","Disable highlighting of ambiguous characters","Disable Invisible Highlight","Disable highlighting of invisible characters","Disable Non ASCII Highlight","Disable highlighting of non basic ASCII characters","Show Exclude Options","Exclude {0} (invisible character) from being highlighted","Exclude {0} from being highlighted",'Allow unicode characters that are more common in the language "{0}".',"Configure Unicode Highlight Options"],"vs/editor/contrib/unusualLineTerminators/browser/unusualLineTerminators":["Unusual Line Terminators","Detected unusual line terminators","The file '{0}' contains one or more unusual line terminator characters, like Line Separator (LS) or Paragraph Separator (PS).\n\nIt is recommended to remove them from the file. This can be configured via `editor.unusualLineTerminators`.","&&Remove Unusual Line Terminators","Ignore"],"vs/editor/contrib/wordHighlighter/browser/highlightDecorations":["Background color of a symbol during read-access, like reading a variable. The color must not be opaque so as not to hide underlying decorations.","Background color of a symbol during write-access, like writing to a variable. The color must not be opaque so as not to hide underlying decorations.","Background color of a textual occurrence for a symbol. The color must not be opaque so as not to hide underlying decorations.","Border color of a symbol during read-access, like reading a variable.","Border color of a symbol during write-access, like writing to a variable.","Border color of a textual occurrence for a symbol.","Overview ruler marker color for symbol highlights. The color must not be opaque so as not to hide underlying decorations.","Overview ruler marker color for write-access symbol highlights. The color must not be opaque so as not to hide underlying decorations.","Overview ruler marker color of a textual occurrence for a symbol. The color must not be opaque so as not to hide underlying decorations."],"vs/editor/contrib/wordHighlighter/browser/wordHighlighter":["Go to Next Symbol Highlight","Go to Previous Symbol Highlight","Trigger Symbol Highlight"],"vs/editor/contrib/wordOperations/browser/wordOperations":["Delete Word"],"vs/platform/action/common/actionCommonCategories":["View","Help","Test","File","Preferences","Developer"],"vs/platform/actionWidget/browser/actionList":["{0} to apply, {1} to preview","{0} to apply","{0}, Disabled Reason: {1}","Action Widget"],"vs/platform/actionWidget/browser/actionWidget":["Background color for toggled action items in action bar.","Whether the action widget list is visible","Hide action widget","Select previous action","Select next action","Accept selected action","Preview selected action"],"vs/platform/actions/browser/menuEntryActionViewItem":["{0} ({1})","{0} ({1})",`{0}
[{1}] {2}`],"vs/platform/actions/browser/toolbar":["Hide","Reset Menu"],"vs/platform/actions/common/menuService":["Hide '{0}'"],"vs/platform/audioCues/browser/audioCueService":["Error on Line","Warning on Line","Folded Area on Line","Breakpoint on Line","Inline Suggestion on Line","Terminal Quick Fix","Debugger Stopped on Breakpoint","No Inlay Hints on Line","Task Completed","Task Failed","Terminal Command Failed","Terminal Bell","Notebook Cell Completed","Notebook Cell Failed","Diff Line Inserted","Diff Line Deleted","Diff Line Modified","Chat Request Sent","Chat Response Received","Chat Response Pending","Clear","Save","Format"],"vs/platform/configuration/common/configurationRegistry":["Default Language Configuration Overrides","Configure settings to be overridden for the {0} language.","Configure editor settings to be overridden for a language.","This setting does not support per-language configuration.","Configure editor settings to be overridden for a language.","This setting does not support per-language configuration.","Cannot register an empty property","Cannot register '{0}'. This matches property pattern '\\\\[.*\\\\]$' for describing language specific editor settings. Use 'configurationDefaults' contribution.","Cannot register '{0}'. This property is already registered.","Cannot register '{0}'. The associated policy {1} is already registered with {2}."],"vs/platform/contextkey/browser/contextKeyService":["A command that returns information about context keys"],"vs/platform/contextkey/common/contextkey":["Empty context key expression","Did you forget to write an expression? You can also put 'false' or 'true' to always evaluate to false or true, respectively.","'in' after 'not'.","closing parenthesis ')'","Unexpected token","Did you forget to put && or || before the token?","Unexpected end of expression","Did you forget to put a context key?",`Expected: {0}
Received: '{1}'.`],"vs/platform/contextkey/common/contextkeys":["Whether the operating system is macOS","Whether the operating system is Linux","Whether the operating system is Windows","Whether the platform is a web browser","Whether the operating system is macOS on a non-browser platform","Whether the operating system is iOS","Whether the platform is a mobile web browser","Quality type of VS Code","Whether keyboard focus is inside an input box"],"vs/platform/contextkey/common/scanner":["Did you mean {0}?","Did you mean {0} or {1}?","Did you mean {0}, {1} or {2}?","Did you forget to open or close the quote?","Did you forget to escape the '/' (slash) character? Put two backslashes before it to escape, e.g., '\\\\/'."],"vs/platform/history/browser/contextScopedHistoryWidget":["Whether suggestion are visible"],"vs/platform/keybinding/common/abstractKeybindingService":["({0}) was pressed. Waiting for second key of chord...","({0}) was pressed. Waiting for next key of chord...","The key combination ({0}, {1}) is not a command.","The key combination ({0}, {1}) is not a command."],"vs/platform/list/browser/listService":["Workbench","Maps to `Control` on Windows and Linux and to `Command` on macOS.","Maps to `Alt` on Windows and Linux and to `Option` on macOS.","The modifier to be used to add an item in trees and lists to a multi-selection with the mouse (for example in the explorer, open editors and scm view). The 'Open to Side' mouse gestures - if supported - will adapt such that they do not conflict with the multiselect modifier.","Controls how to open items in trees and lists using the mouse (if supported). Note that some trees and lists might choose to ignore this setting if it is not applicable.","Controls whether lists and trees support horizontal scrolling in the workbench. Warning: turning on this setting has a performance implication.","Controls whether clicks in the scrollbar scroll page by page.","Controls tree indentation in pixels.","Controls whether the tree should render indent guides.","Controls whether lists and trees have smooth scrolling.","A multiplier to be used on the `deltaX` and `deltaY` of mouse wheel scroll events.","Scrolling speed multiplier when pressing `Alt`.","Highlight elements when searching. Further up and down navigation will traverse only the highlighted elements.","Filter elements when searching.","Controls the default find mode for lists and trees in the workbench.","Simple keyboard navigation focuses elements which match the keyboard input. Matching is done only on prefixes.","Highlight keyboard navigation highlights elements which match the keyboard input. Further up and down navigation will traverse only the highlighted elements.","Filter keyboard navigation will filter out and hide all the elements which do not match the keyboard input.","Controls the keyboard navigation style for lists and trees in the workbench. Can be simple, highlight and filter.","Please use 'workbench.list.defaultFindMode' and	'workbench.list.typeNavigationMode' instead.","Use fuzzy matching when searching.","Use contiguous matching when searching.","Controls the type of matching used when searching lists and trees in the workbench.","Controls how tree folders are expanded when clicking the folder names. Note that some trees and lists might choose to ignore this setting if it is not applicable.","Controls whether sticky scrolling is enabled in trees.","Controls the number of sticky elements displayed in the tree when `#workbench.tree.enableStickyScroll#` is enabled.","Controls how type navigation works in lists and trees in the workbench. When set to `trigger`, type navigation begins once the `list.triggerTypeNavigation` command is run."],"vs/platform/markers/common/markers":["Error","Warning","Info"],"vs/platform/quickinput/browser/commandsQuickAccess":["recently used","similar commands","commonly used","other commands","similar commands","{0}, {1}","Command '{0}' resulted in an error"],"vs/platform/quickinput/browser/helpQuickAccess":["{0}, {1}"],"vs/platform/quickinput/browser/quickInput":["Back","Press 'Enter' to confirm your input or 'Escape' to cancel","{0}/{1}","Type to narrow down results."],"vs/platform/quickinput/browser/quickInputController":["Toggle all checkboxes","{0} Results","{0} Selected","OK","Custom","Back ({0})","Back"],"vs/platform/quickinput/browser/quickInputList":["Quick Input"],"vs/platform/quickinput/browser/quickInputUtils":["Click to execute command '{0}'"],"vs/platform/theme/common/colorRegistry":["Overall foreground color. This color is only used if not overridden by a component.","Overall foreground for disabled elements. This color is only used if not overridden by a component.","Overall foreground color for error messages. This color is only used if not overridden by a component.","Foreground color for description text providing additional information, for example for a label.","The default color for icons in the workbench.","Overall border color for focused elements. This color is only used if not overridden by a component.","An extra border around elements to separate them from others for greater contrast.","An extra border around active elements to separate them from others for greater contrast.","The background color of text selections in the workbench (e.g. for input fields or text areas). Note that this does not apply to selections within the editor.","Color for text separators.","Foreground color for links in text.","Foreground color for links in text when clicked on and on mouse hover.","Foreground color for preformatted text segments.","Background color for preformatted text segments.","Background color for block quotes in text.","Border color for block quotes in text.","Background color for code blocks in text.","Shadow color of widgets such as find/replace inside the editor.","Border color of widgets such as find/replace inside the editor.","Input box background.","Input box foreground.","Input box border.","Border color of activated options in input fields.","Background color of activated options in input fields.","Background hover color of options in input fields.","Foreground color of activated options in input fields.","Input box foreground color for placeholder text.","Input validation background color for information severity.","Input validation foreground color for information severity.","Input validation border color for information severity.","Input validation background color for warning severity.","Input validation foreground color for warning severity.","Input validation border color for warning severity.","Input validation background color for error severity.","Input validation foreground color for error severity.","Input validation border color for error severity.","Dropdown background.","Dropdown list background.","Dropdown foreground.","Dropdown border.","Button foreground color.","Button separator color.","Button background color.","Button background color when hovering.","Button border color.","Secondary button foreground color.","Secondary button background color.","Secondary button background color when hovering.","Badge background color. Badges are small information labels, e.g. for search results count.","Badge foreground color. Badges are small information labels, e.g. for search results count.","Scrollbar shadow to indicate that the view is scrolled.","Scrollbar slider background color.","Scrollbar slider background color when hovering.","Scrollbar slider background color when clicked on.","Background color of the progress bar that can show for long running operations.","Background color of error text in the editor. The color must not be opaque so as not to hide underlying decorations.","Foreground color of error squigglies in the editor.","If set, color of double underlines for errors in the editor.","Background color of warning text in the editor. The color must not be opaque so as not to hide underlying decorations.","Foreground color of warning squigglies in the editor.","If set, color of double underlines for warnings in the editor.","Background color of info text in the editor. The color must not be opaque so as not to hide underlying decorations.","Foreground color of info squigglies in the editor.","If set, color of double underlines for infos in the editor.","Foreground color of hint squigglies in the editor.","If set, color of double underlines for hints in the editor.","Border color of active sashes.","Editor background color.","Editor default foreground color.","Sticky scroll background color for the editor","Sticky scroll on hover background color for the editor","Background color of editor widgets, such as find/replace.","Foreground color of editor widgets, such as find/replace.","Border color of editor widgets. The color is only used if the widget chooses to have a border and if the color is not overridden by a widget.","Border color of the resize bar of editor widgets. The color is only used if the widget chooses to have a resize border and if the color is not overridden by a widget.","Quick picker background color. The quick picker widget is the container for pickers like the command palette.","Quick picker foreground color. The quick picker widget is the container for pickers like the command palette.","Quick picker title background color. The quick picker widget is the container for pickers like the command palette.","Quick picker color for grouping labels.","Quick picker color for grouping borders.","Keybinding label background color. The keybinding label is used to represent a keyboard shortcut.","Keybinding label foreground color. The keybinding label is used to represent a keyboard shortcut.","Keybinding label border color. The keybinding label is used to represent a keyboard shortcut.","Keybinding label border bottom color. The keybinding label is used to represent a keyboard shortcut.","Color of the editor selection.","Color of the selected text for high contrast.","Color of the selection in an inactive editor. The color must not be opaque so as not to hide underlying decorations.","Color for regions with the same content as the selection. The color must not be opaque so as not to hide underlying decorations.","Border color for regions with the same content as the selection.","Color of the current search match.","Color of the other search matches. The color must not be opaque so as not to hide underlying decorations.","Color of the range limiting the search. The color must not be opaque so as not to hide underlying decorations.","Border color of the current search match.","Border color of the other search matches.","Border color of the range limiting the search. The color must not be opaque so as not to hide underlying decorations.","Color of the Search Editor query matches.","Border color of the Search Editor query matches.","Color of the text in the search viewlet's completion message.","Highlight below the word for which a hover is shown. The color must not be opaque so as not to hide underlying decorations.","Background color of the editor hover.","Foreground color of the editor hover.","Border color of the editor hover.","Background color of the editor hover status bar.","Color of active links.","Foreground color of inline hints","Background color of inline hints","Foreground color of inline hints for types","Background color of inline hints for types","Foreground color of inline hints for parameters","Background color of inline hints for parameters","The color used for the lightbulb actions icon.","The color used for the lightbulb auto fix actions icon.","The color used for the lightbulb AI icon.","Background color for text that got inserted. The color must not be opaque so as not to hide underlying decorations.","Background color for text that got removed. The color must not be opaque so as not to hide underlying decorations.","Background color for lines that got inserted. The color must not be opaque so as not to hide underlying decorations.","Background color for lines that got removed. The color must not be opaque so as not to hide underlying decorations.","Background color for the margin where lines got inserted.","Background color for the margin where lines got removed.","Diff overview ruler foreground for inserted content.","Diff overview ruler foreground for removed content.","Outline color for the text that got inserted.","Outline color for text that got removed.","Border color between the two text editors.","Color of the diff editor's diagonal fill. The diagonal fill is used in side-by-side diff views.","The background color of unchanged blocks in the diff editor.","The foreground color of unchanged blocks in the diff editor.","The background color of unchanged code in the diff editor.","List/Tree background color for the focused item when the list/tree is active. An active list/tree has keyboard focus, an inactive does not.","List/Tree foreground color for the focused item when the list/tree is active. An active list/tree has keyboard focus, an inactive does not.","List/Tree outline color for the focused item when the list/tree is active. An active list/tree has keyboard focus, an inactive does not.","List/Tree outline color for the focused item when the list/tree is active and selected. An active list/tree has keyboard focus, an inactive does not.","List/Tree background color for the selected item when the list/tree is active. An active list/tree has keyboard focus, an inactive does not.","List/Tree foreground color for the selected item when the list/tree is active. An active list/tree has keyboard focus, an inactive does not.","List/Tree icon foreground color for the selected item when the list/tree is active. An active list/tree has keyboard focus, an inactive does not.","List/Tree background color for the selected item when the list/tree is inactive. An active list/tree has keyboard focus, an inactive does not.","List/Tree foreground color for the selected item when the list/tree is inactive. An active list/tree has keyboard focus, an inactive does not.","List/Tree icon foreground color for the selected item when the list/tree is inactive. An active list/tree has keyboard focus, an inactive does not.","List/Tree background color for the focused item when the list/tree is inactive. An active list/tree has keyboard focus, an inactive does not.","List/Tree outline color for the focused item when the list/tree is inactive. An active list/tree has keyboard focus, an inactive does not.","List/Tree background when hovering over items using the mouse.","List/Tree foreground when hovering over items using the mouse.","List/Tree drag and drop background when moving items around using the mouse.","List/Tree foreground color of the match highlights when searching inside the list/tree.","List/Tree foreground color of the match highlights on actively focused items when searching inside the list/tree.","List/Tree foreground color for invalid items, for example an unresolved root in explorer.","Foreground color of list items containing errors.","Foreground color of list items containing warnings.","Background color of the type filter widget in lists and trees.","Outline color of the type filter widget in lists and trees.","Outline color of the type filter widget in lists and trees, when there are no matches.","Shadow color of the type filter widget in lists and trees.","Background color of the filtered match.","Border color of the filtered match.","Tree stroke color for the indentation guides.","Tree stroke color for the indentation guides that are not active.","Table border color between columns.","Background color for odd table rows.","List/Tree foreground color for items that are deemphasized. ","Background color of checkbox widget.","Background color of checkbox widget when the element it's in is selected.","Foreground color of checkbox widget.","Border color of checkbox widget.","Border color of checkbox widget when the element it's in is selected.","Please use quickInputList.focusBackground instead","Quick picker foreground color for the focused item.","Quick picker icon foreground color for the focused item.","Quick picker background color for the focused item.","Border color of menus.","Foreground color of menu items.","Background color of menu items.","Foreground color of the selected menu item in menus.","Background color of the selected menu item in menus.","Border color of the selected menu item in menus.","Color of a separator menu item in menus.","Toolbar background when hovering over actions using the mouse","Toolbar outline when hovering over actions using the mouse","Toolbar background when holding the mouse over actions","Highlight background color of a snippet tabstop.","Highlight border color of a snippet tabstop.","Highlight background color of the final tabstop of a snippet.","Highlight border color of the final tabstop of a snippet.","Color of focused breadcrumb items.","Background color of breadcrumb items.","Color of focused breadcrumb items.","Color of selected breadcrumb items.","Background color of breadcrumb item picker.","Current header background in inline merge-conflicts. The color must not be opaque so as not to hide underlying decorations.","Current content background in inline merge-conflicts. The color must not be opaque so as not to hide underlying decorations.","Incoming header background in inline merge-conflicts. The color must not be opaque so as not to hide underlying decorations.","Incoming content background in inline merge-conflicts. The color must not be opaque so as not to hide underlying decorations.","Common ancestor header background in inline merge-conflicts. The color must not be opaque so as not to hide underlying decorations.","Common ancestor content background in inline merge-conflicts. The color must not be opaque so as not to hide underlying decorations.","Border color on headers and the splitter in inline merge-conflicts.","Current overview ruler foreground for inline merge-conflicts.","Incoming overview ruler foreground for inline merge-conflicts.","Common ancestor overview ruler foreground for inline merge-conflicts.","Overview ruler marker color for find matches. The color must not be opaque so as not to hide underlying decorations.","Overview ruler marker color for selection highlights. The color must not be opaque so as not to hide underlying decorations.","Minimap marker color for find matches.","Minimap marker color for repeating editor selections.","Minimap marker color for the editor selection.","Minimap marker color for infos.","Minimap marker color for warnings.","Minimap marker color for errors.","Minimap background color.",'Opacity of foreground elements rendered in the minimap. For example, "#000000c0" will render the elements with 75% opacity.',"Minimap slider background color.","Minimap slider background color when hovering.","Minimap slider background color when clicked on.","The color used for the problems error icon.","The color used for the problems warning icon.","The color used for the problems info icon.","The foreground color used in charts.","The color used for horizontal lines in charts.","The red color used in chart visualizations.","The blue color used in chart visualizations.","The yellow color used in chart visualizations.","The orange color used in chart visualizations.","The green color used in chart visualizations.","The purple color used in chart visualizations."],"vs/platform/theme/common/iconRegistry":["The id of the font to use. If not set, the font that is defined first is used.","The font character associated with the icon definition.","Icon for the close action in widgets.","Icon for goto previous editor location.","Icon for goto next editor location."],"vs/platform/undoRedo/common/undoRedoService":["The following files have been closed and modified on disk: {0}.","The following files have been modified in an incompatible way: {0}.","Could not undo '{0}' across all files. {1}","Could not undo '{0}' across all files. {1}","Could not undo '{0}' across all files because changes were made to {1}","Could not undo '{0}' across all files because there is already an undo or redo operation running on {1}","Could not undo '{0}' across all files because an undo or redo operation occurred in the meantime","Would you like to undo '{0}' across all files?","&&Undo in {0} Files","Undo this &&File","Could not undo '{0}' because there is already an undo or redo operation running.","Would you like to undo '{0}'?","&&Yes","No","Could not redo '{0}' across all files. {1}","Could not redo '{0}' across all files. {1}","Could not redo '{0}' across all files because changes were made to {1}","Could not redo '{0}' across all files because there is already an undo or redo operation running on {1}","Could not redo '{0}' across all files because an undo or redo operation occurred in the meantime","Could not redo '{0}' because there is already an undo or redo operation running."],"vs/platform/workspace/common/workspace":["Code Workspace"]});

//# sourceMappingURL=../../../min-maps/vs/editor/editor.main.nls.js.map