import { JSX, For, Show, createMemo, splitProps } from 'solid-js'
import {
  createSolidTable,
  flexRender,
  getCoreRowModel,
  getSortedRowModel,
  getFilteredRowModel,
  getPaginationRowModel,
  ColumnDef,
  SortingState,
  ColumnFiltersState,
  PaginationState,
} from '@tanstack/solid-table'
import { css } from '../../../styled-system/css'
import { Button, Tag } from './index'
import clsx from 'clsx'

export interface TableProps<T> {
  data: T[]
  columns: ColumnDef<T, any>[]
  loading?: boolean
  pagination?: boolean
  pageSize?: number
  sortable?: boolean
  filterable?: boolean
  selectable?: boolean
  onRowSelect?: (rows: T[]) => void
  onRowClick?: (row: T) => void
  class?: string
  height?: string
  sticky?: boolean
}

export function Table<T>(props: TableProps<T>) {
  const [local, others] = splitProps(props, [
    'data',
    'columns',
    'loading',
    'pagination',
    'pageSize',
    'sortable',
    'filterable',
    'selectable',
    'onRowSelect',
    'onRowClick',
    'class',
    'height',
    'sticky',
  ])

  const table = createSolidTable({
    get data() {
      return local.data || []
    },
    get columns() {
      return local.columns
    },
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: local.sortable ? getSortedRowModel() : undefined,
    getFilteredRowModel: local.filterable ? getFilteredRowModel() : undefined,
    getPaginationRowModel: local.pagination ? getPaginationRowModel() : undefined,
    initialState: {
      pagination: {
        pageSize: local.pageSize || 10,
      },
    },
  })

  const getTableStyles = () => ({
    width: '100%',
    backgroundColor: 'white',
    borderRadius: '4px',
    border: '1px solid',
    borderColor: 'border.base',
    overflow: 'hidden',
    boxShadow: 'base',
  })

  const getTableContainerStyles = () => ({
    width: '100%',
    height: local.height || 'auto',
    overflow: 'auto',
    position: 'relative',
  })

  const getHeaderStyles = () => ({
    backgroundColor: 'bg.page',
    borderBottom: '1px solid',
    borderColor: 'border.base',
    position: local.sticky ? 'sticky' : 'static',
    top: 0,
    zIndex: 10,
  })

  const getHeaderCellStyles = () => ({
    padding: '12px 16px',
    textAlign: 'left',
    fontSize: '14px',
    fontWeight: '500',
    color: 'text.primary',
    borderRight: '1px solid',
    borderColor: 'border.lighter',
    cursor: 'pointer',
    userSelect: 'none',
    _hover: {
      backgroundColor: 'border.extra',
    },
    _last: {
      borderRight: 'none',
    },
  })

  const getRowStyles = (isEven: boolean) => ({
    backgroundColor: isEven ? 'white' : 'bg.page',
    borderBottom: '1px solid',
    borderColor: 'border.lighter',
    transition: 'all 0.2s',
    cursor: local.onRowClick ? 'pointer' : 'default',
    _hover: {
      backgroundColor: 'primary.50',
    },
    _last: {
      borderBottom: 'none',
    },
  })

  const getCellStyles = () => ({
    padding: '12px 16px',
    fontSize: '14px',
    color: 'text.regular',
    borderRight: '1px solid',
    borderColor: 'border.lighter',
    _last: {
      borderRight: 'none',
    },
  })

  const getLoadingStyles = () => ({
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(255, 255, 255, 0.8)',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    zIndex: 100,
  })

  const getPaginationStyles = () => ({
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: '16px',
    borderTop: '1px solid',
    borderColor: 'border.base',
    backgroundColor: 'white',
  })

  const getSortIcon = (isSorted: string | false) => {
    if (isSorted === 'asc') return '↑'
    if (isSorted === 'desc') return '↓'
    return '↕'
  }

  return (
    <div class={clsx(css(getTableStyles()), local.class)} {...others}>
      <div class={css(getTableContainerStyles())}>
        <table class={css({ width: '100%', borderCollapse: 'collapse' })}>
          <thead class={css(getHeaderStyles())}>
            <For each={table.getHeaderGroups()}>
              {(headerGroup) => (
                <tr>
                  <For each={headerGroup.headers}>
                    {(header) => (
                      <th
                        class={css(getHeaderCellStyles())}
                        onClick={header.column.getToggleSortingHandler()}
                      >
                        <div class={css({ display: 'flex', alignItems: 'center', gap: '8px' })}>
                          {flexRender(header.column.columnDef.header, header.getContext())}
                          <Show when={local.sortable && header.column.getCanSort()}>
                            <span class={css({ fontSize: '12px', color: 'text.secondary' })}>
                              {getSortIcon(header.column.getIsSorted())}
                            </span>
                          </Show>
                        </div>
                      </th>
                    )}
                  </For>
                </tr>
              )}
            </For>
          </thead>
          <tbody>
            <For each={table.getRowModel().rows}>
              {(row, index) => (
                <tr
                  class={css(getRowStyles(index() % 2 === 0))}
                  onClick={() => local.onRowClick?.(row.original)}
                >
                  <For each={row.getVisibleCells()}>
                    {(cell) => (
                      <td class={css(getCellStyles())}>
                        {flexRender(cell.column.columnDef.cell, cell.getContext())}
                      </td>
                    )}
                  </For>
                </tr>
              )}
            </For>
          </tbody>
        </table>

        <Show when={local.loading}>
          <div class={css(getLoadingStyles())}>
            <div class={css({ display: 'flex', alignItems: 'center', gap: '8px' })}>
              <span class={css({ animation: 'spin 1s linear infinite', fontSize: '20px' })}>⟳</span>
              <span class={css({ color: 'text.secondary' })}>加载中...</span>
            </div>
          </div>
        </Show>
      </div>

      <Show when={local.pagination}>
        <div class={css(getPaginationStyles())}>
          <div class={css({ display: 'flex', alignItems: 'center', gap: '8px' })}>
            <span class={css({ fontSize: '14px', color: 'text.secondary' })}>
              共 {table.getFilteredRowModel().rows.length} 条记录
            </span>
          </div>
          <div class={css({ display: 'flex', alignItems: 'center', gap: '8px' })}>
            <Button
              size="small"
              variant="default"
              disabled={!table.getCanPreviousPage()}
              onClick={() => table.previousPage()}
            >
              上一页
            </Button>
            <span class={css({ fontSize: '14px', color: 'text.regular' })}>
              第 {table.getState().pagination.pageIndex + 1} 页，共{' '}
              {table.getPageCount()} 页
            </span>
            <Button
              size="small"
              variant="default"
              disabled={!table.getCanNextPage()}
              onClick={() => table.nextPage()}
            >
              下一页
            </Button>
          </div>
        </div>
      </Show>
    </div>
  )
}
