import { JSX, onMount, onCleanup, createEffect, splitProps } from 'solid-js'
import { css } from '../../../styled-system/css'
import * as echarts from 'echarts'

export interface EChartsWrapperProps {
  option: echarts.EChartsOption
  width?: string | number
  height?: string | number
  theme?: string
  loading?: boolean
  onChartReady?: (chart: echarts.ECharts) => void
  onEvents?: Record<string, (params: any) => void>
  class?: string
}

export function EChartsWrapper(props: EChartsWrapperProps) {
  let chartRef: HTMLDivElement | undefined
  let chartInstance: echarts.ECharts | undefined

  const [local, others] = splitProps(props, [
    'option',
    'width',
    'height',
    'theme',
    'loading',
    'onChartReady',
    'onEvents',
    'class',
  ])

  const getContainerStyles = () => ({
    width: typeof local.width === 'number' ? `${local.width}px` : local.width || '100%',
    height: typeof local.height === 'number' ? `${local.height}px` : local.height || '400px',
    position: 'relative',
  })

  const initChart = () => {
    if (!chartRef) return

    // 销毁现有实例
    if (chartInstance) {
      chartInstance.dispose()
    }

    // 创建新实例
    chartInstance = echarts.init(chartRef, local.theme || 'light')

    // 设置配置
    chartInstance.setOption(local.option, true)

    // 绑定事件
    if (local.onEvents) {
      Object.entries(local.onEvents).forEach(([eventName, handler]) => {
        chartInstance!.on(eventName, handler)
      })
    }

    // 回调
    local.onChartReady?.(chartInstance)

    // 响应式调整
    const resizeObserver = new ResizeObserver(() => {
      chartInstance?.resize()
    })
    resizeObserver.observe(chartRef)

    // 清理函数
    onCleanup(() => {
      resizeObserver.disconnect()
      if (chartInstance) {
        chartInstance.dispose()
        chartInstance = undefined
      }
    })
  }

  onMount(() => {
    initChart()
  })

  // 响应配置变化
  createEffect(() => {
    if (chartInstance && local.option) {
      chartInstance.setOption(local.option, true)
    }
  })

  // 响应加载状态
  createEffect(() => {
    if (chartInstance) {
      if (local.loading) {
        chartInstance.showLoading('default', {
          text: '加载中...',
          color: '#409eff',
          textColor: '#000',
          maskColor: 'rgba(255, 255, 255, 0.8)',
          zlevel: 0,
        })
      } else {
        chartInstance.hideLoading()
      }
    }
  })

  return (
    <div
      ref={chartRef}
      class={css(getContainerStyles())}
      {...others}
    />
  )
}
