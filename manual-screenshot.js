import puppeteer from 'puppeteer';

async function takeManualScreenshot() {
  const browser = await puppeteer.launch({
    headless: false,
    defaultViewport: {
      width: 1920,
      height: 1080
    }
  });
  
  const page = await browser.newPage();
  
  try {
    // 访问本地开发服务器
    await page.goto('http://localhost:3001', {
      waitUntil: 'networkidle2',
      timeout: 30000 
    });
    
    // 等待页面完全加载
    await new Promise(resolve => setTimeout(resolve, 3000));
    
    // 截图仪表板页面
    await page.screenshot({ 
      path: 'dashboard-final.png',
      fullPage: true
    });
    console.log('Dashboard screenshot saved as dashboard-final.png');
    
    // 手动点击行情分析菜单
    console.log('Please manually click on the Market Analysis menu item...');
    console.log('Waiting 10 seconds for manual navigation...');
    await new Promise(resolve => setTimeout(resolve, 10000));
    
    // 截图行情分析页面
    await page.screenshot({ 
      path: 'market-final.png',
      fullPage: true
    });
    console.log('Market screenshot saved as market-final.png');
    
  } catch (error) {
    console.error('Error taking screenshot:', error);
  } finally {
    await browser.close();
  }
}

takeManualScreenshot();
