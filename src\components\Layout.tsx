import { createSignal } from 'solid-js';
import { A, useLocation, RouteSectionProps } from '@solidjs/router';
import { css } from '../../styled-system/css';

export default function Layout(props: RouteSectionProps) {
  const [sidebarCollapsed, setSidebarCollapsed] = createSignal(false);
  const location = useLocation();

  const menuItems = [
    {
      id: 'dashboard',
      label: '仪表板',
      icon: '📊',
      path: '/dashboard'
    },
    {
      id: 'market',
      label: '行情分析',
      icon: '📈',
      path: '/market'
    },
    {
      id: 'strategy-editor',
      label: '策略编辑器',
      icon: '🧠',
      path: '/strategy-editor'
    },
    {
      id: 'api-test',
      label: 'API测试',
      icon: '🔧',
      path: '/api-test'
    }
  ];

  const isActive = (path: string) => {
    return location.pathname === path || (path === '/dashboard' && location.pathname === '/');
  };

  const getCurrentPageLabel = () => {
    const currentItem = menuItems.find(item => isActive(item.path));
    return currentItem?.label || '仪表板';
  };

  return (
    <div class={css({
      display: 'flex',
      minHeight: '100vh',
      backgroundColor: '#f5f5f5'
    })}>
      {/* 左侧边栏 */}
      <aside class={css({
        width: sidebarCollapsed() ? '64px' : '240px',
        backgroundColor: 'white',
        borderRight: '1px solid #e8e8e8',
        transition: 'width 0.3s ease',
        display: 'flex',
        flexDirection: 'column',
        position: 'fixed',
        height: '100vh',
        zIndex: 1000
      })}>
        {/* Logo区域 */}
        <div class={css({
          padding: '12px 16px',
          borderBottom: '1px solid #e8e8e8',
          display: 'flex',
          alignItems: 'center',
          gap: '8px'
        })}>
          <div class={css({
            width: '28px',
            height: '28px',
            backgroundColor: '#1890ff',
            borderRadius: '4px',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            color: 'white',
            fontSize: '14px',
            fontWeight: 'bold'
          })}>
            量
          </div>
          {!sidebarCollapsed() && (
            <div>
              <div class={css({
                fontSize: '14px',
                fontWeight: '600',
                color: '#262626',
                lineHeight: 1.2
              })}>
                量化平台
              </div>
              <div class={css({
                fontSize: '11px',
                color: '#8c8c8c',
                lineHeight: 1
              })}>
                专业版 v2.0
              </div>
            </div>
          )}
        </div>

        {/* 导航菜单 */}
        <nav class={css({
          flex: 1,
          padding: '8px 0',
          overflowY: 'auto'
        })}>
          {menuItems.map((item) => (
            <A
              href={item.path}
              class={css({
                width: '100%',
                padding: sidebarCollapsed() ? '12px 20px' : '12px 16px',
                border: 'none',
                backgroundColor: isActive(item.path) ? '#e6f7ff' : 'transparent',
                color: isActive(item.path) ? '#1890ff' : '#595959',
                fontSize: '14px',
                textDecoration: 'none',
                cursor: 'pointer',
                transition: 'all 0.2s',
                display: 'flex',
                alignItems: 'center',
                gap: '12px',
                borderLeft: isActive(item.path) ? '3px solid #1890ff' : '3px solid transparent',
                _hover: {
                  backgroundColor: '#f5f5f5',
                  color: '#1890ff'
                }
              })}
            >
              <span class={css({ fontSize: '16px' })}>{item.icon}</span>
              {!sidebarCollapsed() && (
                <span class={css({ fontWeight: isActive(item.path) ? '500' : '400' })}>
                  {item.label}
                </span>
              )}
            </A>
          ))}
        </nav>

        {/* 折叠按钮 */}
        <div class={css({
          padding: '16px',
          borderTop: '1px solid #e8e8e8'
        })}>
          <button
            type="button"
            onClick={() => setSidebarCollapsed(!sidebarCollapsed())}
            class={css({
              width: '100%',
              padding: '8px',
              border: '1px solid #d9d9d9',
              backgroundColor: 'white',
              borderRadius: '4px',
              cursor: 'pointer',
              fontSize: '12px',
              color: '#595959',
              _hover: {
                borderColor: '#1890ff',
                color: '#1890ff'
              }
            })}
          >
            {sidebarCollapsed() ? '→' : '←'}
          </button>
        </div>
      </aside>

      {/* 主内容区域 */}
      <main class={css({
        flex: 1,
        marginLeft: sidebarCollapsed() ? '64px' : '240px',
        transition: 'margin-left 0.3s ease',
        display: 'flex',
        flexDirection: 'column'
      })}>
        {/* 顶部导航栏 */}
        <header class={css({
          backgroundColor: 'white',
          borderBottom: '1px solid #e8e8e8',
          padding: '0 24px',
          height: '64px',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between'
        })}>
          <div class={css({
            display: 'flex',
            alignItems: 'center',
            gap: '16px'
          })}>
            <h1 class={css({
              fontSize: '18px',
              fontWeight: '500',
              color: '#262626',
              margin: 0
            })}>
              {getCurrentPageLabel()}
            </h1>
          </div>

          <div class={css({
            display: 'flex',
            alignItems: 'center',
            gap: '16px'
          })}>
            <button type="button" class={css({
              padding: '6px 12px',
              border: '1px solid #d9d9d9',
              backgroundColor: 'white',
              borderRadius: '4px',
              fontSize: '14px',
              cursor: 'pointer',
              _hover: {
                borderColor: '#1890ff',
                color: '#1890ff'
              }
            })}>
              帮助
            </button>
            <button type="button" class={css({
              padding: '6px 12px',
              border: '1px solid #d9d9d9',
              backgroundColor: 'white',
              borderRadius: '4px',
              fontSize: '14px',
              cursor: 'pointer',
              _hover: {
                borderColor: '#1890ff',
                color: '#1890ff'
              }
            })}>
              设置
            </button>
            <div class={css({
              width: '32px',
              height: '32px',
              backgroundColor: '#1890ff',
              borderRadius: '50%',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              color: 'white',
              fontSize: '14px',
              fontWeight: '500'
            })}>
              用
            </div>
          </div>
        </header>

        {/* 页面内容 */}
        <div class={css({
          flex: 1,
          backgroundColor: '#f5f5f5',
          overflow: 'auto'
        })}>
          {props.children}
        </div>
      </main>
    </div>
  );
}