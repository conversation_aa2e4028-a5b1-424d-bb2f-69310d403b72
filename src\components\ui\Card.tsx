import { JSX, splitProps, Show } from 'solid-js'
import { css } from '../../../styled-system/css'
import clsx from 'clsx'

export interface CardProps extends JSX.HTMLAttributes<HTMLDivElement> {
  header?: JSX.Element | string
  shadow?: 'always' | 'hover' | 'never'
  bodyStyle?: JSX.CSSProperties
  headerStyle?: JSX.CSSProperties
}

export function Card(props: CardProps) {
  const [local, others] = splitProps(props, [
    'header',
    'shadow',
    'bodyStyle',
    'headerStyle',
    'children',
    'class',
  ])

  const getCardStyles = () => {
    const base = {
      backgroundColor: 'white',
      borderRadius: '4px',
      border: '1px solid',
      borderColor: 'border.lighter',
      overflow: 'hidden',
      transition: 'all 0.3s',
    }

    const shadowStyles = {
      always: {
        boxShadow: 'base',
      },
      hover: {
        _hover: {
          boxShadow: 'base',
        },
      },
      never: {},
    }

    return {
      ...base,
      ...shadowStyles[local.shadow || 'always'],
    }
  }

  const getHeaderStyles = () => ({
    padding: '18px 20px',
    borderBottom: '1px solid',
    borderColor: 'border.lighter',
    backgroundColor: 'bg.page',
    fontSize: '16px',
    fontWeight: '500',
    color: 'text.primary',
    ...local.headerStyle,
  })

  const getBodyStyles = () => ({
    padding: '20px',
    ...local.bodyStyle,
  })

  return (
    <div class={clsx(css(getCardStyles()), local.class)} {...others}>
      <Show when={local.header}>
        <div class={css(getHeaderStyles())}>
          {typeof local.header === 'string' ? local.header : local.header}
        </div>
      </Show>
      <div class={css(getBodyStyles())}>{local.children}</div>
    </div>
  )
}
