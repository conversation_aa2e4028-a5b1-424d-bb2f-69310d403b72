import { JSX, splitProps, Show } from 'solid-js'
import { css } from '../../../styled-system/css'
import clsx from 'clsx'

export interface TagProps extends JSX.HTMLAttributes<HTMLSpanElement> {
  type?: 'primary' | 'success' | 'warning' | 'danger' | 'info'
  size?: 'large' | 'default' | 'small'
  effect?: 'dark' | 'light' | 'plain'
  closable?: boolean
  round?: boolean
  onClose?: () => void
}

export function Tag(props: TagProps) {
  const [local, others] = splitProps(props, [
    'type',
    'size',
    'effect',
    'closable',
    'round',
    'onClose',
    'children',
    'class',
  ])

  const getTagStyles = () => {
    const base = {
      display: 'inline-flex',
      alignItems: 'center',
      gap: '4px',
      borderRadius: local.round ? '16px' : '4px',
      border: '1px solid',
      fontSize: '12px',
      fontWeight: '400',
      lineHeight: 1,
      whiteSpace: 'nowrap',
      verticalAlign: 'middle',
    }

    const sizeStyles = {
      large: {
        height: '32px',
        padding: '0 12px',
        fontSize: '14px',
      },
      default: {
        height: '24px',
        padding: '0 8px',
        fontSize: '12px',
      },
      small: {
        height: '20px',
        padding: '0 6px',
        fontSize: '11px',
      },
    }

    const typeColors = {
      primary: 'primary',
      success: 'success',
      warning: 'warning',
      danger: 'danger',
      info: 'info',
    }

    const colorKey = typeColors[local.type || 'primary']

    const effectStyles = {
      dark: {
        color: 'white',
        backgroundColor: `${colorKey}.500`,
        borderColor: `${colorKey}.500`,
      },
      light: {
        color: `${colorKey}.600`,
        backgroundColor: `${colorKey}.50`,
        borderColor: `${colorKey}.200`,
      },
      plain: {
        color: `${colorKey}.500`,
        backgroundColor: 'transparent',
        borderColor: `${colorKey}.500`,
      },
    }

    return {
      ...base,
      ...sizeStyles[local.size || 'default'],
      ...effectStyles[local.effect || 'light'],
    }
  }

  const getCloseIconStyles = () => ({
    cursor: 'pointer',
    fontSize: '10px',
    marginLeft: '4px',
    borderRadius: '50%',
    width: '14px',
    height: '14px',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    _hover: {
      backgroundColor: 'rgba(0, 0, 0, 0.1)',
    },
  })

  const handleClose = (e: MouseEvent) => {
    e.stopPropagation()
    local.onClose?.()
  }

  return (
    <span class={clsx(css(getTagStyles()), local.class)} {...others}>
      {local.children}
      <Show when={local.closable}>
        <span class={css(getCloseIconStyles())} onClick={handleClose}>
          ✕
        </span>
      </Show>
    </span>
  )
}
