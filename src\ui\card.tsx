import { JSX } from 'solid-js';
import { css } from '../../styled-system/css';

export interface CardProps {
  title?: JSX.Element | string;
  extra?: JSX.Element;
  children?: JSX.Element;
}

export default function Card(props: CardProps) {
  return (
    <div class={css({ backgroundColor: 'white', borderRadius: '8px', border: '1px solid #e8e8e8' })}>
      {(props.title || props.extra) && (
        <div class={css({ padding: '12px 16px', borderBottom: '1px solid #f0f0f0', display: 'flex', justifyContent: 'space-between', alignItems: 'center' })}>
          <div class={css({ fontSize: '14px', fontWeight: '600', color: '#262626' })}>{props.title}</div>
          <div>{props.extra}</div>
        </div>
      )}
      <div class={css({ padding: '16px' })}>{props.children}</div>
    </div>
  );
}

