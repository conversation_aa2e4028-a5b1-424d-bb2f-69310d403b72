{"name": "quant-frontend", "version": "1.0.0", "description": "轻量级量化交易前端平台", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "lint": "echo '<PERSON><PERSON> skipped'", "format": "echo 'Formatting skipped'", "type-check": "tsc --noEmit", "panda": "panda", "panda:codegen": "panda codegen", "panda:watch": "panda --watch"}, "dependencies": {"@kobalte/core": "^0.13.11", "@monaco-editor/loader": "^1.4.0", "@solidjs/router": "^0.13.0", "@tanstack/solid-table": "^8.21.3", "@tanstack/solid-virtual": "^3.13.12", "big.js": "^6.2.1", "clsx": "^2.1.1", "date-fns": "^3.0.0", "decimal.js": "^10.4.3", "echarts": "^6.0.0", "jotai": "^2.13.0", "lightweight-charts": "^4.1.0", "monaco-editor": "^0.45.0", "numeral": "^2.0.6", "socket.io-client": "^4.7.0", "solid-js": "^1.8.0", "uuid": "^9.0.1"}, "devDependencies": {"@pandacss/dev": "^0.39.2", "@park-ui/panda-preset": "^0.32.0", "@types/big.js": "^6.2.2", "@types/node": "^20.0.0", "@types/numeral": "^2.0.5", "@types/uuid": "^9.0.7", "@xenova/transformers": "^2.17.1", "autoprefixer": "^10.4.0", "glob": "^10.4.5", "postcss": "^8.4.0", "puppeteer": "^24.16.0", "typescript": "^5.0.0", "vite": "^5.0.0", "vite-plugin-solid": "^2.8.0"}, "engines": {"node": ">=18.0.0"}, "keywords": ["quantitative-trading", "solidjs", "lightweight", "financial", "charts", "ai-assisted"], "author": "Quant Frontend Team", "license": "MIT"}