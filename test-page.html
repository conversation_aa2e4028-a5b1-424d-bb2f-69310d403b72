<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <title>测试页面</title>
</head>
<body>
    <h1>静态测试页面</h1>
    <p>如果你能看到这个页面，说明服务器正常工作。</p>
    <button onclick="testAPI()">测试API</button>
    <div id="result"></div>
    
    <script>
        function testAPI() {
            fetch('http://localhost:3000/')
                .then(res => res.text())
                .then(data => {
                    document.getElementById('result').innerText = '首页HTML长度: ' + data.length + ' 字符';
                })
                .catch(err => {
                    document.getElementById('result').innerText = '错误: ' + err;
                });
        }
        
        // 自动测试
        window.onload = function() {
            console.log('页面加载成功');
            testAPI();
        }
    </script>
</body>
</html>