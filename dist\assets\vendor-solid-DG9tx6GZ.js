const tt=(e,t)=>e===t,z=Symbol("solid-proxy"),Te=typeof Proxy=="function",nt=Symbol("solid-track"),J={equals:tt};let $e=Fe;const I=1,Y=2,ke={owned:null,cleanups:null,context:null,owner:null};var w=null;let re=null,rt=null,A=null,L=null,T=null,ee=0;function V(e,t){const n=A,r=w,s=e.length===0,i=t===void 0?r:t,l=s?ke:{owned:null,cleanups:null,context:i?i.context:null,owner:i},o=s?e:()=>e(()=>R(()=>K(l)));w=l,A=null;try{return N(o,!0)}finally{A=n,w=r}}function F(e,t){t=t?Object.assign({},J,t):J;const n={value:e,observers:null,observerSlots:null,comparator:t.equals||void 0},r=s=>(typeof s=="function"&&(s=s(n.value)),Ue(n,s));return[je.bind(n),r]}function $(e,t,n){const r=we(e,t,!1,I);q(r)}function st(e,t,n){$e=ct;const r=we(e,t,!1,I);r.user=!0,T?T.push(r):q(r)}function E(e,t,n){n=n?Object.assign({},J,n):J;const r=we(e,t,!0,0);return r.observers=null,r.observerSlots=null,r.comparator=n.equals||void 0,q(r),je.bind(r)}function ot(e){return N(e,!1)}function R(e){if(A===null)return e();const t=A;A=null;try{return e()}finally{A=t}}function he(e,t,n){const r=Array.isArray(e);let s,i=n&&n.defer;return l=>{let o;if(r){o=Array(e.length);for(let c=0;c<e.length;c++)o[c]=e[c]()}else o=e();if(i)return i=!1,l;const a=R(()=>t(o,s,l));return s=o,a}}function yn(e){st(()=>R(e))}function ge(e){return w===null||(w.cleanups===null?w.cleanups=[e]:w.cleanups.push(e)),e}function Ie(){return w}function Ne(e,t){const n=w,r=A;w=e,A=null;try{return N(t,!0)}catch(s){be(s)}finally{w=n,A=r}}function it(e){const t=A,n=w;return Promise.resolve().then(()=>{A=t,w=n;let r;return N(e,!1),A=w=null,r?r.done:void 0})}const[wn,bn]=F(!1);function De(e,t){const n=Symbol("context");return{id:n,Provider:ft(n),defaultValue:e}}function me(e){let t;return w&&w.context&&(t=w.context[e.id])!==void 0?t:e.defaultValue}function ye(e){const t=E(e),n=E(()=>le(t()));return n.toArray=()=>{const r=n();return Array.isArray(r)?r:r!=null?[r]:[]},n}function je(){if(this.sources&&this.state)if(this.state===I)q(this);else{const e=L;L=null,N(()=>Z(this),!1),L=e}if(A){const e=this.observers?this.observers.length:0;A.sources?(A.sources.push(this),A.sourceSlots.push(e)):(A.sources=[this],A.sourceSlots=[e]),this.observers?(this.observers.push(A),this.observerSlots.push(A.sources.length-1)):(this.observers=[A],this.observerSlots=[A.sources.length-1])}return this.value}function Ue(e,t,n){let r=e.value;return(!e.comparator||!e.comparator(r,t))&&(e.value=t,e.observers&&e.observers.length&&N(()=>{for(let s=0;s<e.observers.length;s+=1){const i=e.observers[s],l=re&&re.running;l&&re.disposed.has(i),(l?!i.tState:!i.state)&&(i.pure?L.push(i):T.push(i),i.observers&&Me(i)),l||(i.state=I)}if(L.length>1e6)throw L=[],new Error},!1)),t}function q(e){if(!e.fn)return;K(e);const t=ee;lt(e,e.value,t)}function lt(e,t,n){let r;const s=w,i=A;A=w=e;try{r=e.fn(t)}catch(l){return e.pure&&(e.state=I,e.owned&&e.owned.forEach(K),e.owned=null),e.updatedAt=n+1,be(l)}finally{A=i,w=s}(!e.updatedAt||e.updatedAt<=n)&&(e.updatedAt!=null&&"observers"in e?Ue(e,r):e.value=r,e.updatedAt=n)}function we(e,t,n,r=I,s){const i={fn:e,state:r,updatedAt:null,owned:null,sources:null,sourceSlots:null,cleanups:null,value:t,owner:w,context:w?w.context:null,pure:n};return w===null||w!==ke&&(w.owned?w.owned.push(i):w.owned=[i]),i}function Q(e){if(e.state===0)return;if(e.state===Y)return Z(e);if(e.suspense&&R(e.suspense.inFallback))return e.suspense.effects.push(e);const t=[e];for(;(e=e.owner)&&(!e.updatedAt||e.updatedAt<ee);)e.state&&t.push(e);for(let n=t.length-1;n>=0;n--)if(e=t[n],e.state===I)q(e);else if(e.state===Y){const r=L;L=null,N(()=>Z(e,t[0]),!1),L=r}}function N(e,t){if(L)return e();let n=!1;t||(L=[]),T?n=!0:T=[],ee++;try{const r=e();return at(n),r}catch(r){n||(T=null),L=null,be(r)}}function at(e){if(L&&(Fe(L),L=null),e)return;const t=T;T=null,t.length&&N(()=>$e(t),!1)}function Fe(e){for(let t=0;t<e.length;t++)Q(e[t])}function ct(e){let t,n=0;for(t=0;t<e.length;t++){const r=e[t];r.user?e[n++]=r:Q(r)}for(t=0;t<n;t++)Q(e[t])}function Z(e,t){e.state=0;for(let n=0;n<e.sources.length;n+=1){const r=e.sources[n];if(r.sources){const s=r.state;s===I?r!==t&&(!r.updatedAt||r.updatedAt<ee)&&Q(r):s===Y&&Z(r,t)}}}function Me(e){for(let t=0;t<e.observers.length;t+=1){const n=e.observers[t];n.state||(n.state=Y,n.pure?L.push(n):T.push(n),n.observers&&Me(n))}}function K(e){let t;if(e.sources)for(;e.sources.length;){const n=e.sources.pop(),r=e.sourceSlots.pop(),s=n.observers;if(s&&s.length){const i=s.pop(),l=n.observerSlots.pop();r<s.length&&(i.sourceSlots[l]=r,s[r]=i,n.observerSlots[r]=l)}}if(e.tOwned){for(t=e.tOwned.length-1;t>=0;t--)K(e.tOwned[t]);delete e.tOwned}if(e.owned){for(t=e.owned.length-1;t>=0;t--)K(e.owned[t]);e.owned=null}if(e.cleanups){for(t=e.cleanups.length-1;t>=0;t--)e.cleanups[t]();e.cleanups=null}e.state=0}function ut(e){return e instanceof Error?e:new Error(typeof e=="string"?e:"Unknown error",{cause:e})}function be(e,t=w){throw ut(e)}function le(e){if(typeof e=="function"&&!e.length)return le(e());if(Array.isArray(e)){const t=[];for(let n=0;n<e.length;n++){const r=le(e[n]);Array.isArray(r)?t.push.apply(t,r):t.push(r)}return t}return e}function ft(e,t){return function(r){let s;return $(()=>s=R(()=>(w.context={...w.context,[e]:r.value},ye(()=>r.children))),void 0),s}}const dt=Symbol("fallback");function ve(e){for(let t=0;t<e.length;t++)e[t]()}function ht(e,t,n={}){let r=[],s=[],i=[],l=0,o=t.length>1?[]:null;return ge(()=>ve(i)),()=>{let a=e()||[],c=a.length,f,u;return a[nt],R(()=>{let g,S,d,m,b,y,C,v,O;if(c===0)l!==0&&(ve(i),i=[],r=[],s=[],l=0,o&&(o=[])),n.fallback&&(r=[dt],s[0]=V(D=>(i[0]=D,n.fallback())),l=1);else if(l===0){for(s=new Array(c),u=0;u<c;u++)r[u]=a[u],s[u]=V(h);l=c}else{for(d=new Array(c),m=new Array(c),o&&(b=new Array(c)),y=0,C=Math.min(l,c);y<C&&r[y]===a[y];y++);for(C=l-1,v=c-1;C>=y&&v>=y&&r[C]===a[v];C--,v--)d[v]=s[C],m[v]=i[C],o&&(b[v]=o[C]);for(g=new Map,S=new Array(v+1),u=v;u>=y;u--)O=a[u],f=g.get(O),S[u]=f===void 0?-1:f,g.set(O,u);for(f=y;f<=C;f++)O=r[f],u=g.get(O),u!==void 0&&u!==-1?(d[u]=s[f],m[u]=i[f],o&&(b[u]=o[f]),u=S[u],g.set(O,u)):i[f]();for(u=y;u<c;u++)u in d?(s[u]=d[u],i[u]=m[u],o&&(o[u]=b[u],o[u](u))):s[u]=V(h);s=s.slice(0,l=c),r=a.slice(0)}return s});function h(g){if(i[u]=g,o){const[S,d]=F(u);return o[u]=d,t(a[u],S)}return t(a[u])}}}function k(e,t){return R(()=>e(t||{}))}function G(){return!0}const ae={get(e,t,n){return t===z?n:e.get(t)},has(e,t){return t===z?!0:e.has(t)},set:G,deleteProperty:G,getOwnPropertyDescriptor(e,t){return{configurable:!0,enumerable:!0,get(){return e.get(t)},set:G,deleteProperty:G}},ownKeys(e){return e.keys()}};function se(e){return(e=typeof e=="function"?e():e)?e:{}}function gt(){for(let e=0,t=this.length;e<t;++e){const n=this[e]();if(n!==void 0)return n}}function ce(...e){let t=!1;for(let l=0;l<e.length;l++){const o=e[l];t=t||!!o&&z in o,e[l]=typeof o=="function"?(t=!0,E(o)):o}if(Te&&t)return new Proxy({get(l){for(let o=e.length-1;o>=0;o--){const a=se(e[o])[l];if(a!==void 0)return a}},has(l){for(let o=e.length-1;o>=0;o--)if(l in se(e[o]))return!0;return!1},keys(){const l=[];for(let o=0;o<e.length;o++)l.push(...Object.keys(se(e[o])));return[...new Set(l)]}},ae);const n={},r=Object.create(null);for(let l=e.length-1;l>=0;l--){const o=e[l];if(!o)continue;const a=Object.getOwnPropertyNames(o);for(let c=a.length-1;c>=0;c--){const f=a[c];if(f==="__proto__"||f==="constructor")continue;const u=Object.getOwnPropertyDescriptor(o,f);if(!r[f])r[f]=u.get?{enumerable:!0,configurable:!0,get:gt.bind(n[f]=[u.get.bind(o)])}:u.value!==void 0?u:void 0;else{const h=n[f];h&&(u.get?h.push(u.get.bind(o)):u.value!==void 0&&h.push(()=>u.value))}}}const s={},i=Object.keys(r);for(let l=i.length-1;l>=0;l--){const o=i[l],a=r[o];a&&a.get?Object.defineProperty(s,o,a):s[o]=a?a.value:void 0}return s}function mt(e,...t){if(Te&&z in e){const s=new Set(t.length>1?t.flat():t[0]),i=t.map(l=>new Proxy({get(o){return l.includes(o)?e[o]:void 0},has(o){return l.includes(o)&&o in e},keys(){return l.filter(o=>o in e)}},ae));return i.push(new Proxy({get(l){return s.has(l)?void 0:e[l]},has(l){return s.has(l)?!1:l in e},keys(){return Object.keys(e).filter(l=>!s.has(l))}},ae)),i}const n={},r=t.map(()=>({}));for(const s of Object.getOwnPropertyNames(e)){const i=Object.getOwnPropertyDescriptor(e,s),l=!i.get&&!i.set&&i.enumerable&&i.writable&&i.configurable;let o=!1,a=0;for(const c of t)c.includes(s)&&(o=!0,l?r[a][s]=i.value:Object.defineProperty(r[a],s,i)),++a;o||(l?n[s]=i.value:Object.defineProperty(n,s,i))}return[...r,n]}const yt=e=>`Stale read from <${e}>.`;function pn(e){const t="fallback"in e&&{fallback:()=>e.fallback};return E(ht(()=>e.each,e.children,t||void 0))}function _e(e){const t=e.keyed,n=E(()=>e.when,void 0,void 0),r=t?n:E(n,void 0,{equals:(s,i)=>!s==!i});return E(()=>{const s=r();if(s){const i=e.children;return typeof i=="function"&&i.length>0?R(()=>i(t?s:()=>{if(!R(r))throw yt("Show");return n()})):i}return e.fallback},void 0,void 0)}const wt=["allowfullscreen","async","alpha","autofocus","autoplay","checked","controls","default","disabled","formnovalidate","hidden","indeterminate","inert","ismap","loop","multiple","muted","nomodule","novalidate","open","playsinline","readonly","required","reversed","seamless","selected","adauctionheaders","browsingtopics","credentialless","defaultchecked","defaultmuted","defaultselected","defer","disablepictureinpicture","disableremoteplayback","preservespitch","shadowrootclonable","shadowrootcustomelementregistry","shadowrootdelegatesfocus","shadowrootserializable","sharedstoragewritable"],bt=new Set(["className","value","readOnly","noValidate","formNoValidate","isMap","noModule","playsInline","adAuctionHeaders","allowFullscreen","browsingTopics","defaultChecked","defaultMuted","defaultSelected","disablePictureInPicture","disableRemotePlayback","preservesPitch","shadowRootClonable","shadowRootCustomElementRegistry","shadowRootDelegatesFocus","shadowRootSerializable","sharedStorageWritable",...wt]),pt=new Set(["innerHTML","textContent","innerText","children"]),At=Object.assign(Object.create(null),{className:"class",htmlFor:"for"}),Pt=Object.assign(Object.create(null),{class:"className",novalidate:{$:"noValidate",FORM:1},formnovalidate:{$:"formNoValidate",BUTTON:1,INPUT:1},ismap:{$:"isMap",IMG:1},nomodule:{$:"noModule",SCRIPT:1},playsinline:{$:"playsInline",VIDEO:1},readonly:{$:"readOnly",INPUT:1,TEXTAREA:1},adauctionheaders:{$:"adAuctionHeaders",IFRAME:1},allowfullscreen:{$:"allowFullscreen",IFRAME:1},browsingtopics:{$:"browsingTopics",IMG:1},defaultchecked:{$:"defaultChecked",INPUT:1},defaultmuted:{$:"defaultMuted",AUDIO:1,VIDEO:1},defaultselected:{$:"defaultSelected",OPTION:1},disablepictureinpicture:{$:"disablePictureInPicture",VIDEO:1},disableremoteplayback:{$:"disableRemotePlayback",AUDIO:1,VIDEO:1},preservespitch:{$:"preservesPitch",AUDIO:1,VIDEO:1},shadowrootclonable:{$:"shadowRootClonable",TEMPLATE:1},shadowrootdelegatesfocus:{$:"shadowRootDelegatesFocus",TEMPLATE:1},shadowrootserializable:{$:"shadowRootSerializable",TEMPLATE:1},sharedstoragewritable:{$:"sharedStorageWritable",IFRAME:1,IMG:1}});function Et(e,t){const n=Pt[e];return typeof n=="object"?n[t]?n.$:void 0:n}const St=new Set(["beforeinput","click","dblclick","contextmenu","focusin","focusout","input","keydown","keyup","mousedown","mousemove","mouseout","mouseover","mouseup","pointerdown","pointermove","pointerout","pointerover","pointerup","touchend","touchmove","touchstart"]),vt=e=>E(()=>e());function Ct(e,t,n){let r=n.length,s=t.length,i=r,l=0,o=0,a=t[s-1].nextSibling,c=null;for(;l<s||o<i;){if(t[l]===n[o]){l++,o++;continue}for(;t[s-1]===n[i-1];)s--,i--;if(s===l){const f=i<r?o?n[o-1].nextSibling:n[i-o]:a;for(;o<i;)e.insertBefore(n[o++],f)}else if(i===o)for(;l<s;)(!c||!c.has(t[l]))&&t[l].remove(),l++;else if(t[l]===n[i-1]&&n[o]===t[s-1]){const f=t[--s].nextSibling;e.insertBefore(n[o++],t[l++].nextSibling),e.insertBefore(n[--i],f),t[s]=n[i]}else{if(!c){c=new Map;let u=o;for(;u<i;)c.set(n[u],u++)}const f=c.get(t[l]);if(f!=null)if(o<f&&f<i){let u=l,h=1,g;for(;++u<s&&u<i&&!((g=c.get(t[u]))==null||g!==f+h);)h++;if(h>f-o){const S=t[l];for(;o<f;)e.insertBefore(n[o++],S)}else e.replaceChild(n[o++],t[l++])}else l++;else t[l++].remove()}}}const Ce="_$DX_DELEGATE";function An(e,t,n,r={}){let s;return V(i=>{s=i,t===document?e():Nt(t,e(),t.firstChild?null:void 0,n)},r.owner),()=>{s(),t.textContent=""}}function Lt(e,t,n,r){let s;const i=()=>{const o=document.createElement("template");return o.innerHTML=e,o.content.firstChild},l=()=>(s||(s=i())).cloneNode(!0);return l.cloneNode=l,l}function Be(e,t=window.document){const n=t[Ce]||(t[Ce]=new Set);for(let r=0,s=e.length;r<s;r++){const i=e[r];n.has(i)||(n.add(i),t.addEventListener(i,Ut))}}function ue(e,t,n){n==null?e.removeAttribute(t):e.setAttribute(t,n)}function Ot(e,t,n){n?e.setAttribute(t,""):e.removeAttribute(t)}function Rt(e,t){t==null?e.removeAttribute("class"):e.className=t}function xt(e,t,n,r){if(r)Array.isArray(n)?(e[`$$${t}`]=n[0],e[`$$${t}Data`]=n[1]):e[`$$${t}`]=n;else if(Array.isArray(n)){const s=n[0];e.addEventListener(t,n[0]=i=>s.call(e,n[1],i))}else e.addEventListener(t,n,typeof n!="function"&&n)}function Tt(e,t,n={}){const r=Object.keys(t||{}),s=Object.keys(n);let i,l;for(i=0,l=s.length;i<l;i++){const o=s[i];!o||o==="undefined"||t[o]||(Le(e,o,!1),delete n[o])}for(i=0,l=r.length;i<l;i++){const o=r[i],a=!!t[o];!o||o==="undefined"||n[o]===a||!a||(Le(e,o,!0),n[o]=a)}return n}function $t(e,t,n){if(!t)return n?ue(e,"style"):t;const r=e.style;if(typeof t=="string")return r.cssText=t;typeof n=="string"&&(r.cssText=n=void 0),n||(n={}),t||(t={});let s,i;for(i in n)t[i]==null&&r.removeProperty(i),delete n[i];for(i in t)s=t[i],s!==n[i]&&(r.setProperty(i,s),n[i]=s);return n}function kt(e,t={},n,r){const s={};return $(()=>s.children=W(e,t.children,s.children)),$(()=>typeof t.ref=="function"&&It(t.ref,e)),$(()=>Dt(e,t,n,!0,s,!0)),s}function It(e,t,n){return R(()=>e(t,n))}function Nt(e,t,n,r){if(n!==void 0&&!r&&(r=[]),typeof t!="function")return W(e,t,r,n);$(s=>W(e,t(),s,n),r)}function Dt(e,t,n,r,s={},i=!1){t||(t={});for(const l in s)if(!(l in t)){if(l==="children")continue;s[l]=Oe(e,l,null,s[l],n,i,t)}for(const l in t){if(l==="children")continue;const o=t[l];s[l]=Oe(e,l,o,s[l],n,i,t)}}function jt(e){return e.toLowerCase().replace(/-([a-z])/g,(t,n)=>n.toUpperCase())}function Le(e,t,n){const r=t.trim().split(/\s+/);for(let s=0,i=r.length;s<i;s++)e.classList.toggle(r[s],n)}function Oe(e,t,n,r,s,i,l){let o,a,c,f,u;if(t==="style")return $t(e,n,r);if(t==="classList")return Tt(e,n,r);if(n===r)return r;if(t==="ref")i||n(e);else if(t.slice(0,3)==="on:"){const h=t.slice(3);r&&e.removeEventListener(h,r,typeof r!="function"&&r),n&&e.addEventListener(h,n,typeof n!="function"&&n)}else if(t.slice(0,10)==="oncapture:"){const h=t.slice(10);r&&e.removeEventListener(h,r,!0),n&&e.addEventListener(h,n,!0)}else if(t.slice(0,2)==="on"){const h=t.slice(2).toLowerCase(),g=St.has(h);if(!g&&r){const S=Array.isArray(r)?r[0]:r;e.removeEventListener(h,S)}(g||n)&&(xt(e,h,n,g),g&&Be([h]))}else t.slice(0,5)==="attr:"?ue(e,t.slice(5),n):t.slice(0,5)==="bool:"?Ot(e,t.slice(5),n):(u=t.slice(0,5)==="prop:")||(c=pt.has(t))||(f=Et(t,e.tagName))||(a=bt.has(t))||(o=e.nodeName.includes("-")||"is"in l)?(u&&(t=t.slice(5),a=!0),t==="class"||t==="className"?Rt(e,n):o&&!a&&!c?e[jt(t)]=n:e[f||t]=n):ue(e,At[t]||t,n);return n}function Ut(e){let t=e.target;const n=`$$${e.type}`,r=e.target,s=e.currentTarget,i=a=>Object.defineProperty(e,"target",{configurable:!0,value:a}),l=()=>{const a=t[n];if(a&&!t.disabled){const c=t[`${n}Data`];if(c!==void 0?a.call(t,c,e):a.call(t,e),e.cancelBubble)return}return t.host&&typeof t.host!="string"&&!t.host._$host&&t.contains(e.target)&&i(t.host),!0},o=()=>{for(;l()&&(t=t._$host||t.parentNode||t.host););};if(Object.defineProperty(e,"currentTarget",{configurable:!0,get(){return t||document}}),e.composedPath){const a=e.composedPath();i(a[0]);for(let c=0;c<a.length-2&&(t=a[c],!!l());c++){if(t._$host){t=t._$host,o();break}if(t.parentNode===s)break}}else o();i(r)}function W(e,t,n,r,s){for(;typeof n=="function";)n=n();if(t===n)return n;const i=typeof t,l=r!==void 0;if(e=l&&n[0]&&n[0].parentNode||e,i==="string"||i==="number"){if(i==="number"&&(t=t.toString(),t===n))return n;if(l){let o=n[0];o&&o.nodeType===3?o.data!==t&&(o.data=t):o=document.createTextNode(t),n=B(e,n,r,o)}else n!==""&&typeof n=="string"?n=e.firstChild.data=t:n=e.textContent=t}else if(t==null||i==="boolean")n=B(e,n,r);else{if(i==="function")return $(()=>{let o=t();for(;typeof o=="function";)o=o();n=W(e,o,n,r)}),()=>n;if(Array.isArray(t)){const o=[],a=n&&Array.isArray(n);if(fe(o,t,n,s))return $(()=>n=W(e,o,n,r,!0)),()=>n;if(o.length===0){if(n=B(e,n,r),l)return n}else a?n.length===0?Re(e,o,r):Ct(e,n,o):(n&&B(e),Re(e,o));n=o}else if(t.nodeType){if(Array.isArray(n)){if(l)return n=B(e,n,r,t);B(e,n,null,t)}else n==null||n===""||!e.firstChild?e.appendChild(t):e.replaceChild(t,e.firstChild);n=t}}return n}function fe(e,t,n,r){let s=!1;for(let i=0,l=t.length;i<l;i++){let o=t[i],a=n&&n[e.length],c;if(!(o==null||o===!0||o===!1))if((c=typeof o)=="object"&&o.nodeType)e.push(o);else if(Array.isArray(o))s=fe(e,o,a)||s;else if(c==="function")if(r){for(;typeof o=="function";)o=o();s=fe(e,Array.isArray(o)?o:[o],Array.isArray(a)?a:[a])||s}else e.push(o),s=!0;else{const f=String(o);a&&a.nodeType===3&&a.data===f?e.push(a):e.push(document.createTextNode(f))}}return s}function Re(e,t,n=null){for(let r=0,s=t.length;r<s;r++)e.insertBefore(t[r],n)}function B(e,t,n,r){if(n===void 0)return e.textContent="";const s=r||document.createTextNode("");if(t.length){let i=!1;for(let l=t.length-1;l>=0;l--){const o=t[l];if(s!==o){const a=o.parentNode===e;!i&&!l?a?e.replaceChild(s,o):e.insertBefore(s,n):a&&o.remove()}else i=!0}}else e.insertBefore(s,n);return[s]}const Ft=!1;function Ve(){let e=new Set;function t(s){return e.add(s),()=>e.delete(s)}let n=!1;function r(s,i){if(n)return!(n=!1);const l={to:s,options:i,defaultPrevented:!1,preventDefault:()=>l.defaultPrevented=!0};for(const o of e)o.listener({...l,from:o.location,retry:a=>{a&&(n=!0),o.navigate(s,{...i,resolve:!1})}});return!l.defaultPrevented}return{subscribe:t,confirm:r}}let de;function pe(){(!window.history.state||window.history.state._depth==null)&&window.history.replaceState({...window.history.state,_depth:window.history.length-1},""),de=window.history.state._depth}pe();function Mt(e){return{...e,_depth:window.history.state&&window.history.state._depth}}function _t(e,t){let n=!1;return()=>{const r=de;pe();const s=r==null?null:de-r;if(n){n=!1;return}s&&t(s)?(n=!0,window.history.go(-s)):e()}}const Bt=/^(?:[a-z0-9]+:)?\/\//i,Vt=/^\/+|(\/)\/+$/g,Ke="http://sr";function M(e,t=!1){const n=e.replace(Vt,"$1");return n?t||/^[?#]/.test(n)?n:"/"+n:""}function X(e,t,n){if(Bt.test(t))return;const r=M(e),s=n&&M(n);let i="";return!s||t.startsWith("/")?i=r:s.toLowerCase().indexOf(r.toLowerCase())!==0?i=r+s:i=s,(i||"/")+M(t,!i)}function Kt(e,t){if(e==null)throw new Error(t);return e}function Wt(e,t){return M(e).replace(/\/*(\*.*)?$/g,"")+M(t)}function We(e){const t={};return e.searchParams.forEach((n,r)=>{t[r]=n}),t}function qt(e,t,n){const[r,s]=e.split("/*",2),i=r.split("/").filter(Boolean),l=i.length;return o=>{const a=o.split("/").filter(Boolean),c=a.length-l;if(c<0||c>0&&s===void 0&&!t)return null;const f={path:l?"":"/",params:{}},u=h=>n===void 0?void 0:n[h];for(let h=0;h<l;h++){const g=i[h],S=a[h],d=g[0]===":",m=d?g.slice(1):g;if(d&&oe(S,u(m)))f.params[m]=S;else if(d||!oe(S,g))return null;f.path+=`/${S}`}if(s){const h=c?a.slice(-c).join("/"):"";if(oe(h,u(s)))f.params[s]=h;else return null}return f}}function oe(e,t){const n=r=>r.localeCompare(e,void 0,{sensitivity:"base"})===0;return t===void 0?!0:typeof t=="string"?n(t):typeof t=="function"?t(e):Array.isArray(t)?t.some(n):t instanceof RegExp?t.test(e):!1}function Ht(e){const[t,n]=e.pattern.split("/*",2),r=t.split("/").filter(Boolean);return r.reduce((s,i)=>s+(i.startsWith(":")?2:3),r.length-(n===void 0?0:1))}function qe(e){const t=new Map,n=Ie();return new Proxy({},{get(r,s){return t.has(s)||Ne(n,()=>t.set(s,E(()=>e()[s]))),t.get(s)()},getOwnPropertyDescriptor(){return{enumerable:!0,configurable:!0}},ownKeys(){return Reflect.ownKeys(e())}})}function He(e){let t=/(\/?\:[^\/]+)\?/.exec(e);if(!t)return[e];let n=e.slice(0,t.index),r=e.slice(t.index+t[0].length);const s=[n,n+=t[1]];for(;t=/^(\/\:[^\/]+)\?/.exec(r);)s.push(n+=t[1]),r=r.slice(t[0].length);return He(r).reduce((i,l)=>[...i,...s.map(o=>o+l)],[])}const Gt=100,Ge=De(),Ae=De(),Pe=()=>Kt(me(Ge),"<A> and 'use' router primitives can be only used inside a Route."),Xt=()=>me(Ae)||Pe().base,zt=e=>{const t=Xt();return E(()=>t.resolvePath(e()))},Jt=e=>{const t=Pe();return E(()=>{const n=e();return n!==void 0?t.renderPath(n):n})},Yt=()=>Pe().location;function Qt(e,t=""){const{component:n,load:r,children:s,info:i}=e,l=!s||Array.isArray(s)&&!s.length,o={key:e,component:n,load:r,info:i};return Xe(e.path).reduce((a,c)=>{for(const f of He(c)){const u=Wt(t,f);let h=l?u:u.split("/*",1)[0];h=h.split("/").map(g=>g.startsWith(":")||g.startsWith("*")?g:encodeURIComponent(g)).join("/"),a.push({...o,originalPath:c,pattern:h,matcher:qt(h,!l,e.matchFilters)})}return a},[])}function Zt(e,t=0){return{routes:e,score:Ht(e[e.length-1])*1e4-t,matcher(n){const r=[];for(let s=e.length-1;s>=0;s--){const i=e[s],l=i.matcher(n);if(!l)return null;r.unshift({...l,route:i})}return r}}}function Xe(e){return Array.isArray(e)?e:[e]}function ze(e,t="",n=[],r=[]){const s=Xe(e);for(let i=0,l=s.length;i<l;i++){const o=s[i];if(o&&typeof o=="object"){o.hasOwnProperty("path")||(o.path="");const a=Qt(o,t);for(const c of a){n.push(c);const f=Array.isArray(o.children)&&o.children.length===0;if(o.children&&!f)ze(o.children,c.pattern,n,r);else{const u=Zt([...n],r.length);r.push(u)}n.pop()}}}return n.length?r:r.sort((i,l)=>l.score-i.score)}function ie(e,t){for(let n=0,r=e.length;n<r;n++){const s=e[n].matcher(t);if(s)return s}return[]}function en(e,t){const n=new URL(Ke),r=E(a=>{const c=e();try{return new URL(c,n)}catch{return console.error(`Invalid path ${c}`),a}},n,{equals:(a,c)=>a.href===c.href}),s=E(()=>r().pathname),i=E(()=>r().search,!0),l=E(()=>r().hash),o=()=>"";return{get pathname(){return s()},get search(){return i()},get hash(){return l()},get state(){return t()},get key(){return o()},query:qe(he(i,()=>We(r())))}}let U;function tn(){return U}function nn(e,t,n,r={}){const{signal:[s,i],utils:l={}}=e,o=l.parsePath||(p=>p),a=l.renderPath||(p=>p),c=l.beforeLeave||Ve(),f=X("",r.base||"");if(f===void 0)throw new Error(`${f} is not a valid base path`);f&&!s().value&&i({value:f,replace:!0,scroll:!1});const[u,h]=F(!1);let g;const S=(p,P)=>{P.value===d()&&P.state===b()||(g===void 0&&h(!0),U=p,g=P,it(()=>{g===P&&(m(g.value),y(g.state),O[1]([]))}).finally(()=>{g===P&&ot(()=>{U=void 0,p==="navigate"&&Ze(g),h(!1),g=void 0})}))},[d,m]=F(s().value),[b,y]=F(s().state),C=en(d,b),v=[],O=F([]),D=E(()=>typeof r.transformUrl=="function"?ie(t(),r.transformUrl(C.pathname)):ie(t(),C.pathname)),Je=qe(()=>{const p=D(),P={};for(let x=0;x<p.length;x++)Object.assign(P,p[x].params);return P}),Ee={pattern:f,path:()=>f,outlet:()=>null,resolvePath(p){return X(f,p)}};return $(he(s,p=>S("native",p),{defer:!0})),{base:Ee,location:C,params:Je,isRouting:u,renderPath:a,parsePath:o,navigatorFactory:Qe,matches:D,beforeLeave:c,preloadRoute:et,singleFlight:r.singleFlight===void 0?!0:r.singleFlight,submissions:O};function Ye(p,P,x){R(()=>{if(typeof P=="number"){P&&(l.go?l.go(P):console.warn("Router integration does not support relative routing"));return}const{replace:te,resolve:ne,scroll:_,state:H}={replace:!1,resolve:!0,scroll:!0,...x},j=ne?p.resolvePath(P):X("",P);if(j===void 0)throw new Error(`Path '${P}' is not a routable path`);if(v.length>=Gt)throw new Error("Too many redirects");const Se=d();(j!==Se||H!==b())&&(Ft||c.confirm(j,x)&&(v.push({value:Se,replace:te,scroll:_,state:b()}),S("navigate",{value:j,state:H})))})}function Qe(p){return p=p||me(Ae)||Ee,(P,x)=>Ye(p,P,x)}function Ze(p){const P=v[0];P&&(i({...p,replace:P.replace,scroll:P.scroll}),v.length=0)}function et(p,P={}){const x=ie(t(),p.pathname),te=U;U="preload";for(let ne in x){const{route:_,params:H}=x[ne];_.component&&_.component.preload&&_.component.preload();const{load:j}=_;P.preloadData&&j&&Ne(n(),()=>j({params:H,location:{pathname:p.pathname,search:p.search,hash:p.hash,query:We(p),state:null,key:""},intent:"preload"}))}U=te}}function rn(e,t,n,r){const{base:s,location:i,params:l}=e,{pattern:o,component:a,load:c}=r().route,f=E(()=>r().path);a&&a.preload&&a.preload();const u=c?c({params:l,location:i,intent:U||"initial"}):void 0;return{parent:t,pattern:o,path:f,outlet:()=>a?k(a,{params:l,location:i,data:u,get children(){return n()}}):n(),resolvePath(g){return X(s.path(),g,f())}}}const sn=e=>t=>{const{base:n}=t,r=ye(()=>t.children),s=E(()=>ze(r(),t.base||""));let i;const l=nn(e,s,()=>i,{base:n,singleFlight:t.singleFlight,transformUrl:t.transformUrl});return e.create&&e.create(l),k(Ge.Provider,{value:l,get children(){return k(on,{routerState:l,get root(){return t.root},get load(){return t.rootLoad},get children(){return[vt(()=>(i=Ie())&&null),k(ln,{routerState:l,get branches(){return s()}})]}})}})};function on(e){const t=e.routerState.location,n=e.routerState.params,r=E(()=>e.load&&R(()=>{e.load({params:n,location:t,intent:tn()||"initial"})}));return k(_e,{get when(){return e.root},keyed:!0,get fallback(){return e.children},children:s=>k(s,{params:n,location:t,get data(){return r()},get children(){return e.children}})})}function ln(e){const t=[];let n;const r=E(he(e.routerState.matches,(s,i,l)=>{let o=i&&s.length===i.length;const a=[];for(let c=0,f=s.length;c<f;c++){const u=i&&i[c],h=s[c];l&&u&&h.route.key===u.route.key?a[c]=l[c]:(o=!1,t[c]&&t[c](),V(g=>{t[c]=g,a[c]=rn(e.routerState,a[c-1]||e.routerState.base,xe(()=>r()[c+1]),()=>e.routerState.matches()[c])}))}return t.splice(s.length).forEach(c=>c()),l&&o?l:(n=a[0],a)}));return xe(()=>r()&&n)()}const xe=e=>()=>k(_e,{get when(){return e()},keyed:!0,children:t=>k(Ae.Provider,{value:t,get children(){return t.outlet()}})}),Pn=e=>{const t=ye(()=>e.children);return ce(e,{get children(){return t()}})};function an([e,t],n,r){return[e,r?s=>t(r(s)):t]}function cn(e){if(e==="#")return null;try{return document.querySelector(e)}catch{return null}}function un(e){let t=!1;const n=s=>typeof s=="string"?{value:s}:s,r=an(F(n(e.get()),{equals:(s,i)=>s.value===i.value&&s.state===i.state}),void 0,s=>(!t&&e.set(s),s));return e.init&&ge(e.init((s=e.get())=>{t=!0,r[1](n(s)),t=!1})),sn({signal:r,create:e.create,utils:e.utils})}function fn(e,t,n){return e.addEventListener(t,n),()=>e.removeEventListener(t,n)}function dn(e,t){const n=cn(`#${e}`);n?n.scrollIntoView():t&&window.scrollTo(0,0)}const hn=new Map;function gn(e=!0,t=!1,n="/_server",r){return s=>{const i=s.base.path(),l=s.navigatorFactory(s.base);let o={};function a(d){return d.namespaceURI==="http://www.w3.org/2000/svg"}function c(d){if(d.defaultPrevented||d.button!==0||d.metaKey||d.altKey||d.ctrlKey||d.shiftKey)return;const m=d.composedPath().find(D=>D instanceof Node&&D.nodeName.toUpperCase()==="A");if(!m||t&&!m.hasAttribute("link"))return;const b=a(m),y=b?m.href.baseVal:m.href;if((b?m.target.baseVal:m.target)||!y&&!m.hasAttribute("state"))return;const v=(m.getAttribute("rel")||"").split(/\s+/);if(m.hasAttribute("download")||v&&v.includes("external"))return;const O=b?new URL(y,document.baseURI):new URL(y);if(!(O.origin!==window.location.origin||i&&O.pathname&&!O.pathname.toLowerCase().startsWith(i.toLowerCase())))return[m,O]}function f(d){const m=c(d);if(!m)return;const[b,y]=m,C=s.parsePath(y.pathname+y.search+y.hash),v=b.getAttribute("state");d.preventDefault(),l(C,{resolve:!1,replace:b.hasAttribute("replace"),scroll:!b.hasAttribute("noscroll"),state:v&&JSON.parse(v)})}function u(d){const m=c(d);if(!m)return;const[b,y]=m;typeof r=="function"&&(y.pathname=r(y.pathname)),o[y.pathname]||s.preloadRoute(y,{preloadData:b.getAttribute("preload")!=="false"})}function h(d){const m=c(d);if(!m)return;const[b,y]=m;typeof r=="function"&&(y.pathname=r(y.pathname)),!o[y.pathname]&&(o[y.pathname]=setTimeout(()=>{s.preloadRoute(y,{preloadData:b.getAttribute("preload")!=="false"}),delete o[y.pathname]},200))}function g(d){const m=c(d);if(!m)return;const[,b]=m;typeof r=="function"&&(b.pathname=r(b.pathname)),o[b.pathname]&&(clearTimeout(o[b.pathname]),delete o[b.pathname])}function S(d){if(d.defaultPrevented)return;let m=d.submitter&&d.submitter.hasAttribute("formaction")?d.submitter.getAttribute("formaction"):d.target.getAttribute("action");if(!m)return;if(!m.startsWith("https://action/")){const y=new URL(m,Ke);if(m=s.parsePath(y.pathname+y.search),!m.startsWith(n))return}if(d.target.method.toUpperCase()!=="POST")throw new Error("Only POST forms are supported for Actions");const b=hn.get(m);if(b){d.preventDefault();const y=new FormData(d.target);d.submitter&&d.submitter.name&&y.append(d.submitter.name,d.submitter.value),b.call({r:s,f:d.target},y)}}Be(["click","submit"]),document.addEventListener("click",f),e&&(document.addEventListener("mouseover",h),document.addEventListener("mouseout",g),document.addEventListener("focusin",u),document.addEventListener("touchstart",u)),document.addEventListener("submit",S),ge(()=>{document.removeEventListener("click",f),e&&(document.removeEventListener("mouseover",h),document.removeEventListener("mouseout",g),document.removeEventListener("focusin",u),document.removeEventListener("touchstart",u)),document.removeEventListener("submit",S)})}}function En(e){const t=()=>{const r=window.location.pathname+window.location.search;return{value:e.transformUrl?e.transformUrl(r)+window.location.hash:r+window.location.hash,state:window.history.state}},n=Ve();return un({get:t,set({value:r,replace:s,scroll:i,state:l}){s?window.history.replaceState(Mt(l),"",r):window.history.pushState(l,"",r),dn(decodeURIComponent(window.location.hash.slice(1)),i),pe()},init:r=>fn(window,"popstate",_t(r,s=>{if(s&&s<0)return!n.confirm(s);{const i=t();return!n.confirm(i.value,{state:i.state})}})),create:gn(e.preload,e.explicitLinks,e.actionBase,e.transformUrl),utils:{go:r=>window.history.go(r),beforeLeave:n}})(e)}var mn=Lt("<a>");function Sn(e){e=ce({inactiveClass:"inactive",activeClass:"active"},e);const[,t]=mt(e,["href","state","class","activeClass","inactiveClass","end"]),n=zt(()=>e.href),r=Jt(n),s=Yt(),i=E(()=>{const l=n();if(l===void 0)return[!1,!1];const o=M(l.split(/[?#]/,1)[0]).toLowerCase(),a=M(s.pathname).toLowerCase();return[e.end?o===a:a.startsWith(o+"/")||a===o,o===a]});return(()=>{var l=mn();return kt(l,ce(t,{get href(){return r()||e.href},get state(){return JSON.stringify(e.state)},get classList(){return{...e.class&&{[e.class]:!0},[e.inactiveClass]:!i()[0],[e.activeClass]:i()[0],...t.classList}},link:"",get"aria-current"(){return i()[1]?"page":void 0}}),!1),l})()}export{Sn as A,pn as F,Pn as R,$ as a,Rt as b,F as c,Be as d,k as e,ge as f,st as g,It as h,Nt as i,En as j,vt as m,yn as o,An as r,Lt as t,Yt as u};
