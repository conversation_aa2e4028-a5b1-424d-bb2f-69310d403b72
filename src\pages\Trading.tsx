import { createSignal, onMount, For } from 'solid-js';
import { css } from '../../styled-system/css';
import { Card, Button, Input } from '../components/ui';
import Modal from '../components/Modal';

interface Order {
  id: string;
  symbol: string;
  type: 'buy' | 'sell';
  quantity: number;
  price: number;
  status: 'pending' | 'filled' | 'cancelled';
  timestamp: Date;
}

interface Position {
  symbol: string;
  quantity: number;
  avgPrice: number;
  currentPrice: number;
  unrealizedPnL: number;
  unrealizedPnLPercent: number;
}

// 临时占位：简化主题与国际化，避免未定义引用
const useTheme = () => ({ theme: () => 'light' as 'light' | 'dark' });
const useI18n = () => ({ t: (key: string) => key });

export default function Trading() {
  const { theme } = useTheme();
  const { t } = useI18n();
  
  const [orders, setOrders] = createSignal<Order[]>([
    {
      id: '1',
      symbol: 'AAPL',
      type: 'buy',
      quantity: 100,
      price: 150.25,
      status: 'filled',
      timestamp: new Date('2024-01-15T10:30:00')
    },
    {
      id: '2',
      symbol: 'TSLA',
      type: 'sell',
      quantity: 50,
      price: 245.80,
      status: 'pending',
      timestamp: new Date('2024-01-15T11:15:00')
    },
    {
      id: '3',
      symbol: 'MSFT',
      type: 'buy',
      quantity: 75,
      price: 310.45,
      status: 'filled',
      timestamp: new Date('2024-01-15T09:45:00')
    }
  ]);

  const [positions, setPositions] = createSignal<Position[]>([
    {
      symbol: 'AAPL',
      quantity: 100,
      avgPrice: 150.25,
      currentPrice: 152.30,
      unrealizedPnL: 205.00,
      unrealizedPnLPercent: 1.36
    },
    {
      symbol: 'MSFT',
      quantity: 75,
      avgPrice: 310.45,
      currentPrice: 308.90,
      unrealizedPnL: -116.25,
      unrealizedPnLPercent: -0.50
    }
  ]);

  const [showOrderModal, setShowOrderModal] = createSignal(false);
  const [orderForm, setOrderForm] = createSignal({
    symbol: '',
    type: 'buy' as 'buy' | 'sell',
    quantity: '',
    price: '',
    orderType: 'limit' as 'market' | 'limit'
  });

  const [portfolioSummary] = createSignal({
    totalValue: 45678.90,
    totalPnL: 88.75,
    totalPnLPercent: 0.19,
    buyingPower: 12345.67,
    marginUsed: 5432.10
  });

  onMount(() => {
    console.log('Trading page mounted');
    // 模拟实时价格更新
    const interval = setInterval(() => {
      setPositions(prev => prev.map(pos => {
        const priceChange = (Math.random() - 0.5) * 2;
        const newPrice = pos.currentPrice + priceChange;
        const unrealizedPnL = (newPrice - pos.avgPrice) * pos.quantity;
        const unrealizedPnLPercent = (unrealizedPnL / (pos.avgPrice * pos.quantity)) * 100;
        
        return {
          ...pos,
          currentPrice: newPrice,
          unrealizedPnL,
          unrealizedPnLPercent
        };
      }));
    }, 3000);
    
    return () => clearInterval(interval);
  });

  const handleSubmitOrder = () => {
    const form = orderForm();
    if (!form.symbol || !form.quantity || (!form.price && form.orderType === 'limit')) {
      alert('请填写完整的订单信息');
      return;
    }

    const newOrder: Order = {
      id: Date.now().toString(),
      symbol: form.symbol.toUpperCase(),
      type: form.type,
      quantity: parseInt(form.quantity),
      price: form.orderType === 'market' ? 0 : parseFloat(form.price),
      status: 'pending',
      timestamp: new Date()
    };

    setOrders(prev => [newOrder, ...prev]);
    setShowOrderModal(false);
    setOrderForm({
      symbol: '',
      type: 'buy',
      quantity: '',
      price: '',
      orderType: 'limit'
    });
  };

  const summary = portfolioSummary();

  return (
    <div class={css({
      padding: '24px',
      maxWidth: '1400px',
      margin: '0 auto',
      backgroundColor: theme() === 'dark' ? 'gray.900' : 'gray.50',
      minHeight: '100vh'
    })}>
      {/* 页面标题 */}
      <div class={css({
        marginBottom: '32px',
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center'
      })}>
        <div>
          <h1 class={css({
            fontSize: '32px',
            fontWeight: 'bold',
            color: theme() === 'dark' ? 'gray.100' : 'gray.900',
            marginBottom: '8px'
          })}>
            💼 {t('nav.trading')}
          </h1>
          <p class={css({
            fontSize: '16px',
            color: theme() === 'dark' ? 'gray.400' : 'gray.600'
          })}>
            管理您的交易订单和持仓
          </p>
        </div>
        
        <Button 
          variant="primary" 
          size="lg"
          icon={<span>➕</span>}
          onClick={() => setShowOrderModal(true)}
        >
          新建订单
        </Button>
      </div>

      {/* 账户概览 */}
      <div class={css({
        display: 'grid',
        gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',
        gap: '24px',
        marginBottom: '32px'
      })}>
        <Card padding="md" shadow="md">
          <div class={css({ textAlign: 'center' })}>
            <h3 class={css({
              fontSize: '14px',
              fontWeight: '500',
              color: theme() === 'dark' ? 'gray.400' : 'gray.600',
              marginBottom: '8px'
            })}>
              账户总值
            </h3>
            <p class={css({
              fontSize: '24px',
              fontWeight: 'bold',
              color: theme() === 'dark' ? 'gray.100' : 'gray.900'
            })}>
              ¥{summary.totalValue.toLocaleString()}
            </p>
          </div>
        </Card>

        <Card padding="md" shadow="md">
          <div class={css({ textAlign: 'center' })}>
            <h3 class={css({
              fontSize: '14px',
              fontWeight: '500',
              color: theme() === 'dark' ? 'gray.400' : 'gray.600',
              marginBottom: '8px'
            })}>
              总盈亏
            </h3>
            <p class={css({
              fontSize: '24px',
              fontWeight: 'bold',
              color: summary.totalPnL >= 0 ? 'green.600' : 'red.600'
            })}>
              {summary.totalPnL >= 0 ? '+' : ''}¥{Math.abs(summary.totalPnL).toFixed(2)}
            </p>
            <p class={css({
              fontSize: '12px',
              color: summary.totalPnL >= 0 ? 'green.600' : 'red.600'
            })}>
              {summary.totalPnL >= 0 ? '+' : ''}{summary.totalPnLPercent.toFixed(2)}%
            </p>
          </div>
        </Card>

        <Card padding="md" shadow="md">
          <div class={css({ textAlign: 'center' })}>
            <h3 class={css({
              fontSize: '14px',
              fontWeight: '500',
              color: theme() === 'dark' ? 'gray.400' : 'gray.600',
              marginBottom: '8px'
            })}>
              可用资金
            </h3>
            <p class={css({
              fontSize: '24px',
              fontWeight: 'bold',
              color: theme() === 'dark' ? 'gray.100' : 'gray.900'
            })}>
              ¥{summary.buyingPower.toLocaleString()}
            </p>
          </div>
        </Card>

        <Card padding="md" shadow="md">
          <div class={css({ textAlign: 'center' })}>
            <h3 class={css({
              fontSize: '14px',
              fontWeight: '500',
              color: theme() === 'dark' ? 'gray.400' : 'gray.600',
              marginBottom: '8px'
            })}>
              已用保证金
            </h3>
            <p class={css({
              fontSize: '24px',
              fontWeight: 'bold',
              color: 'orange.600'
            })}>
              ¥{summary.marginUsed.toLocaleString()}
            </p>
          </div>
        </Card>
      </div>

      {/* 持仓和订单 */}
      <div class={css({
        display: 'grid',
        gridTemplateColumns: '1fr 1fr',
        gap: '24px',
        '@media (max-width: 1024px)': {
          gridTemplateColumns: '1fr'
        }
      })}>
        {/* 当前持仓 */}
        <Card title="当前持仓" padding="none" shadow="md">
          <div class={css({ overflowX: 'auto' })}>
            <table class={css({
              width: '100%',
              borderCollapse: 'collapse'
            })}>
              <thead>
                <tr class={css({
                  backgroundColor: theme() === 'dark' ? 'gray.800' : 'gray.50'
                })}>
                  <th class={css({
                    padding: '12px 16px',
                    textAlign: 'left',
                    fontSize: '12px',
                    fontWeight: '600',
                    color: theme() === 'dark' ? 'gray.300' : 'gray.600'
                  })}>
                    代码
                  </th>
                  <th class={css({
                    padding: '12px 16px',
                    textAlign: 'right',
                    fontSize: '12px',
                    fontWeight: '600',
                    color: theme() === 'dark' ? 'gray.300' : 'gray.600'
                  })}>
                    数量
                  </th>
                  <th class={css({
                    padding: '12px 16px',
                    textAlign: 'right',
                    fontSize: '12px',
                    fontWeight: '600',
                    color: theme() === 'dark' ? 'gray.300' : 'gray.600'
                  })}>
                    盈亏
                  </th>
                </tr>
              </thead>
              <tbody>
                <For each={positions()}>
                  {(position) => (
                    <tr class={css({
                      borderBottom: '1px solid',
                      borderColor: theme() === 'dark' ? 'gray.700' : 'gray.200'
                    })}>
                      <td class={css({
                        padding: '12px 16px',
                        fontSize: '14px',
                        fontWeight: '500',
                        color: theme() === 'dark' ? 'gray.100' : 'gray.900'
                      })}>
                        {position.symbol}
                      </td>
                      <td class={css({
                        padding: '12px 16px',
                        textAlign: 'right',
                        fontSize: '14px',
                        color: theme() === 'dark' ? 'gray.200' : 'gray.700'
                      })}>
                        {position.quantity}
                      </td>
                      <td class={css({
                        padding: '12px 16px',
                        textAlign: 'right',
                        fontSize: '14px',
                        fontWeight: '500',
                        color: position.unrealizedPnL >= 0 ? 'green.600' : 'red.600'
                      })}>
                        {position.unrealizedPnL >= 0 ? '+' : ''}¥{Math.abs(position.unrealizedPnL).toFixed(2)}
                        <br />
                        <span class={css({ fontSize: '12px' })}>
                          {position.unrealizedPnL >= 0 ? '+' : ''}{position.unrealizedPnLPercent.toFixed(2)}%
                        </span>
                      </td>
                    </tr>
                  )}
                </For>
              </tbody>
            </table>
          </div>
        </Card>

        {/* 订单历史 */}
        <Card title="订单历史" padding="none" shadow="md">
          <div class={css({ overflowX: 'auto' })}>
            <table class={css({
              width: '100%',
              borderCollapse: 'collapse'
            })}>
              <thead>
                <tr class={css({
                  backgroundColor: theme() === 'dark' ? 'gray.800' : 'gray.50'
                })}>
                  <th class={css({
                    padding: '12px 16px',
                    textAlign: 'left',
                    fontSize: '12px',
                    fontWeight: '600',
                    color: theme() === 'dark' ? 'gray.300' : 'gray.600'
                  })}>
                    代码
                  </th>
                  <th class={css({
                    padding: '12px 16px',
                    textAlign: 'center',
                    fontSize: '12px',
                    fontWeight: '600',
                    color: theme() === 'dark' ? 'gray.300' : 'gray.600'
                  })}>
                    类型
                  </th>
                  <th class={css({
                    padding: '12px 16px',
                    textAlign: 'right',
                    fontSize: '12px',
                    fontWeight: '600',
                    color: theme() === 'dark' ? 'gray.300' : 'gray.600'
                  })}>
                    状态
                  </th>
                </tr>
              </thead>
              <tbody>
                <For each={orders()}>
                  {(order) => (
                    <tr class={css({
                      borderBottom: '1px solid',
                      borderColor: theme() === 'dark' ? 'gray.700' : 'gray.200'
                    })}>
                      <td class={css({
                        padding: '12px 16px',
                        fontSize: '14px',
                        fontWeight: '500',
                        color: theme() === 'dark' ? 'gray.100' : 'gray.900'
                      })}>
                        {order.symbol}
                        <br />
                        <span class={css({
                          fontSize: '12px',
                          color: theme() === 'dark' ? 'gray.400' : 'gray.600'
                        })}>
                          {order.quantity}股 @ ¥{order.price}
                        </span>
                      </td>
                      <td class={css({
                        padding: '12px 16px',
                        textAlign: 'center',
                        fontSize: '14px'
                      })}>
                        <span class={css({
                          padding: '4px 8px',
                          borderRadius: '4px',
                          fontSize: '12px',
                          fontWeight: '500',
                          backgroundColor: order.type === 'buy' ? 'green.100' : 'red.100',
                          color: order.type === 'buy' ? 'green.800' : 'red.800'
                        })}>
                          {order.type === 'buy' ? '买入' : '卖出'}
                        </span>
                      </td>
                      <td class={css({
                        padding: '12px 16px',
                        textAlign: 'right',
                        fontSize: '14px'
                      })}>
                        <span class={css({
                          padding: '4px 8px',
                          borderRadius: '4px',
                          fontSize: '12px',
                          fontWeight: '500',
                          backgroundColor: 
                            order.status === 'filled' ? 'green.100' :
                            order.status === 'pending' ? 'yellow.100' : 'red.100',
                          color: 
                            order.status === 'filled' ? 'green.800' :
                            order.status === 'pending' ? 'yellow.800' : 'red.800'
                        })}>
                          {order.status === 'filled' ? '已成交' :
                           order.status === 'pending' ? '待成交' : '已取消'}
                        </span>
                      </td>
                    </tr>
                  )}
                </For>
              </tbody>
            </table>
          </div>
        </Card>
      </div>

      {/* 新建订单模态框 */}
      <Modal
        isOpen={showOrderModal()}
        onClose={() => setShowOrderModal(false)}
        title="新建订单"
        size="md"
      >
        <div class={css({
          display: 'flex',
          flexDirection: 'column',
          gap: '16px'
        })}>
          <Input
            label="股票代码"
            placeholder="例如: AAPL"
            value={orderForm().symbol}
            onInput={(e) => setOrderForm(prev => ({ ...prev, symbol: e.currentTarget.value }))}
          />
          
          <div class={css({
            display: 'grid',
            gridTemplateColumns: '1fr 1fr',
            gap: '16px'
          })}>
            <div>
              <label class={css({
                display: 'block',
                fontSize: '14px',
                fontWeight: '500',
                color: theme() === 'dark' ? 'gray.300' : 'gray.700',
                marginBottom: '8px'
              })}>
                交易类型
              </label>
              <select
                aria-label="交易类型"
                value={orderForm().type}
                onChange={(e) => setOrderForm(prev => ({ ...prev, type: e.currentTarget.value as 'buy' | 'sell' }))}
                class={css({
                  width: '100%',
                  padding: '10px 14px',
                  border: '1px solid',
                  borderColor: theme() === 'dark' ? 'gray.600' : 'gray.300',
                  borderRadius: '8px',
                  backgroundColor: theme() === 'dark' ? 'gray.800' : 'white',
                  color: theme() === 'dark' ? 'gray.100' : 'gray.900'
                })}
              >
                <option value="buy">买入</option>
                <option value="sell">卖出</option>
              </select>
            </div>
            
            <div>
              <label class={css({
                display: 'block',
                fontSize: '14px',
                fontWeight: '500',
                color: theme() === 'dark' ? 'gray.300' : 'gray.700',
                marginBottom: '8px'
              })}>
                订单类型
              </label>
              <select
                aria-label="订单类型"
                value={orderForm().orderType}
                onChange={(e) => setOrderForm(prev => ({ ...prev, orderType: e.currentTarget.value as 'market' | 'limit' }))}
                class={css({
                  width: '100%',
                  padding: '10px 14px',
                  border: '1px solid',
                  borderColor: theme() === 'dark' ? 'gray.600' : 'gray.300',
                  borderRadius: '8px',
                  backgroundColor: theme() === 'dark' ? 'gray.800' : 'white',
                  color: theme() === 'dark' ? 'gray.100' : 'gray.900'
                })}
              >
                <option value="market">市价单</option>
                <option value="limit">限价单</option>
              </select>
            </div>
          </div>
          
          <div class={css({
            display: 'grid',
            gridTemplateColumns: '1fr 1fr',
            gap: '16px'
          })}>
            <Input
              label="数量"
              type="number"
              placeholder="100"
              value={orderForm().quantity}
              onInput={(e) => setOrderForm(prev => ({ ...prev, quantity: e.currentTarget.value }))}
            />
            
            {orderForm().orderType === 'limit' && (
              <Input
                label="价格"
                type="number"
                step="0.01"
                placeholder="150.25"
                value={orderForm().price}
                onInput={(e) => setOrderForm(prev => ({ ...prev, price: e.currentTarget.value }))}
              />
            )}
          </div>
        </div>
        
        <div slot="footer">
          <Button variant="ghost" onClick={() => setShowOrderModal(false)}>
            取消
          </Button>
          <Button variant="primary" onClick={handleSubmitOrder}>
            提交订单
          </Button>
        </div>
      </Modal>
    </div>
  );
}
