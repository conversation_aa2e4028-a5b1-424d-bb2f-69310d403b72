import { createSignal, onMount, For } from 'solid-js'
import { css } from '../../styled-system/css'
import { Card, Button, Input, Tag, Dropdown, Message } from '../components/ui'
import { CandlestickChart, KlineData } from '../components/charts'

export default function MarketHistorical() {
  const [selectedSymbol, setSelectedSymbol] = createSignal('000001')
  const [selectedPeriod, setSelectedPeriod] = createSignal('1D')
  const [loading, setLoading] = createSignal(false)
  const [klineData, setKlineData] = createSignal<KlineData[]>([])

  // 股票选项
  const stockOptions = [
    { key: '000001', label: '平安银行 (000001)', onClick: () => setSelectedSymbol('000001') },
    { key: '000002', label: '万科A (000002)', onClick: () => setSelectedSymbol('000002') },
    { key: '600036', label: '招商银行 (600036)', onClick: () => setSelectedSymbol('600036') },
    { key: '600519', label: '贵州茅台 (600519)', onClick: () => setSelectedSymbol('600519') },
    { key: '000858', label: '五粮液 (000858)', onClick: () => setSelectedSymbol('000858') },
  ]

  // 技术指标选项
  const indicators = [
    { key: 'ma', label: 'MA', active: true },
    { key: 'boll', label: 'BOLL', active: false },
    { key: 'macd', label: 'MACD', active: false },
    { key: 'rsi', label: 'RSI', active: false },
    { key: 'kdj', label: 'KDJ', active: false },
  ]

  // 生成模拟K线数据
  const generateMockKlineData = (symbol: string, days: number = 100): KlineData[] => {
    const data: KlineData[] = []
    let basePrice = 10 + Math.random() * 100 // 基础价格
    
    for (let i = 0; i < days; i++) {
      const date = new Date()
      date.setDate(date.getDate() - (days - i))
      
      // 模拟价格波动
      const volatility = 0.02 + Math.random() * 0.03
      const change = (Math.random() - 0.5) * volatility
      
      const open = basePrice
      const close = open * (1 + change)
      const high = Math.max(open, close) * (1 + Math.random() * 0.02)
      const low = Math.min(open, close) * (1 - Math.random() * 0.02)
      const volume = Math.floor((50000 + Math.random() * 200000) * 100)
      
      data.push({
        date: date.toISOString().split('T')[0],
        open: Number(open.toFixed(2)),
        close: Number(close.toFixed(2)),
        high: Number(high.toFixed(2)),
        low: Number(low.toFixed(2)),
        volume,
      })
      
      basePrice = close
    }
    
    return data
  }

  const loadKlineData = async (symbol: string, period: string) => {
    setLoading(true)
    Message.info(`正在加载 ${symbol} ${period} 数据...`)
    
    try {
      // 模拟API请求延迟
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      // 生成模拟数据
      const data = generateMockKlineData(symbol, 200)
      setKlineData(data)
      
      Message.success('数据加载完成')
    } catch (error) {
      Message.error('数据加载失败')
    } finally {
      setLoading(false)
    }
  }

  const handleSymbolChange = (symbol: string) => {
    setSelectedSymbol(symbol)
    loadKlineData(symbol, selectedPeriod())
  }

  const handlePeriodChange = (period: string) => {
    setSelectedPeriod(period)
    loadKlineData(selectedSymbol(), period)
  }

  const handleRefresh = () => {
    loadKlineData(selectedSymbol(), selectedPeriod())
  }

  const handleExport = () => {
    Message.info('导出功能开发中...')
  }

  onMount(() => {
    loadKlineData(selectedSymbol(), selectedPeriod())
  })

  const getCurrentStock = () => {
    return stockOptions.find(stock => stock.key === selectedSymbol())
  }

  return (
    <div class={css({ padding: '20px', minHeight: '100vh', backgroundColor: 'bg.page' })}>
      {/* 页面标题和工具栏 */}
      <div class={css({ marginBottom: '20px' })}>
        <div class={css({
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          marginBottom: '16px',
        })}>
          <h1 class={css({
            fontSize: '24px',
            fontWeight: '600',
            color: 'text.primary',
            margin: 0,
          })}>
            历史行情
          </h1>
          <div class={css({ display: 'flex', alignItems: 'center', gap: '12px' })}>
            <Input
              placeholder="搜索股票代码..."
              class={css({ width: '200px' })}
            />
            <Button variant="primary" onClick={handleRefresh} loading={loading()}>
              刷新
            </Button>
            <Button variant="default" onClick={handleExport}>
              导出
            </Button>
          </div>
        </div>

        {/* 股票选择和指标工具栏 */}
        <div class={css({
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          padding: '12px 16px',
          backgroundColor: 'white',
          borderRadius: '4px',
          border: '1px solid',
          borderColor: 'border.base',
        })}>
          <div class={css({ display: 'flex', alignItems: 'center', gap: '16px' })}>
            <Dropdown
              items={stockOptions}
              trigger={
                <Button variant="default">
                  {getCurrentStock()?.label || '选择股票'} ▼
                </Button>
              }
              onSelect={handleSymbolChange}
            />
            
            <div class={css({ display: 'flex', alignItems: 'center', gap: '8px' })}>
              <span class={css({ fontSize: '14px', color: 'text.secondary' })}>技术指标:</span>
              <For each={indicators}>
                {(indicator) => (
                  <Tag
                    type={indicator.active ? 'primary' : 'info'}
                    effect="light"
                    size="small"
                    class={css({ cursor: 'pointer' })}
                  >
                    {indicator.label}
                  </Tag>
                )}
              </For>
            </div>
          </div>

          <div class={css({ display: 'flex', alignItems: 'center', gap: '8px' })}>
            <span class={css({ fontSize: '14px', color: 'text.secondary' })}>
              数据更新时间: {new Date().toLocaleTimeString()}
            </span>
          </div>
        </div>
      </div>

      {/* K线图 */}
      <CandlestickChart
        data={klineData()}
        symbol={getCurrentStock()?.label || selectedSymbol()}
        loading={loading()}
        height="600px"
        onPeriodChange={handlePeriodChange}
      />

      {/* 数据统计 */}
      <div class={css({ marginTop: '20px' })}>
        <Card header="数据统计" shadow="always">
          <div class={css({
            display: 'grid',
            gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',
            gap: '16px',
          })}>
            <div class={css({ textAlign: 'center' })}>
              <div class={css({ fontSize: '12px', color: 'text.secondary', marginBottom: '4px' })}>
                数据范围
              </div>
              <div class={css({ fontSize: '14px', fontWeight: '500', color: 'text.primary' })}>
                {klineData().length > 0 ? 
                  `${klineData()[0]?.date} ~ ${klineData()[klineData().length - 1]?.date}` : 
                  '暂无数据'
                }
              </div>
            </div>
            
            <div class={css({ textAlign: 'center' })}>
              <div class={css({ fontSize: '12px', color: 'text.secondary', marginBottom: '4px' })}>
                数据点数
              </div>
              <div class={css({ fontSize: '14px', fontWeight: '500', color: 'text.primary' })}>
                {klineData().length} 个交易日
              </div>
            </div>
            
            <div class={css({ textAlign: 'center' })}>
              <div class={css({ fontSize: '12px', color: 'text.secondary', marginBottom: '4px' })}>
                最新价格
              </div>
              <div class={css({ fontSize: '14px', fontWeight: '500', color: 'text.primary' })}>
                {klineData().length > 0 ? 
                  `¥${klineData()[klineData().length - 1]?.close.toFixed(2)}` : 
                  '--'
                }
              </div>
            </div>
            
            <div class={css({ textAlign: 'center' })}>
              <div class={css({ fontSize: '12px', color: 'text.secondary', marginBottom: '4px' })}>
                期间涨跌幅
              </div>
              <div class={css({ fontSize: '14px', fontWeight: '500' })}>
                {klineData().length > 1 ? (() => {
                  const first = klineData()[0].close
                  const last = klineData()[klineData().length - 1].close
                  const change = ((last - first) / first) * 100
                  return (
                    <span class={css({ color: change >= 0 ? 'success.500' : 'danger.500' })}>
                      {change >= 0 ? '+' : ''}{change.toFixed(2)}%
                    </span>
                  )
                })() : '--'}
              </div>
            </div>
          </div>
        </Card>
      </div>
    </div>
  )
}
