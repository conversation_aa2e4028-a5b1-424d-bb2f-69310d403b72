import{m as q,s as re,t as V,a as At,i as w,b as ee,c as O,d as g,e as Wt,f as W,g as R,S as ce,h as ts,j as Ii,F as Me,k as Pi,P as Mo,l as ns,o as Q,n as os,D as rs,p as Y,u as qe,q as Xe,r as be,v as gt,w as is,x as ls,y as ki,z as _n,A as Dt,B as ss,C as Lr,E as as,R as et,G as cs,H as ds}from"./vendor-solid-CRKygbqg.js";import{l as zr}from"./vendor-editor-l7stcynF.js";(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const r of document.querySelectorAll('link[rel="modulepreload"]'))o(r);new MutationObserver(r=>{for(const i of r)if(i.type==="childList")for(const l of i.addedNodes)l.tagName==="LINK"&&l.rel==="modulepreload"&&o(l)}).observe(document,{childList:!0,subtree:!0});function n(r){const i={};return r.integrity&&(i.integrity=r.integrity),r.referrerPolicy&&(i.referrerPolicy=r.referrerPolicy),r.crossOrigin==="use-credentials"?i.credentials="include":r.crossOrigin==="anonymous"?i.credentials="omit":i.credentials="same-origin",i}function o(r){if(r.ep)return;r.ep=!0;const i=n(r);fetch(r.href,i)}})();function On(e){return typeof e=="object"&&e!=null&&!Array.isArray(e)}function us(e){return Object.fromEntries(Object.entries(e??{}).filter(([t,n])=>n!==void 0))}var gs=e=>e==="base";function fs(e){return e.slice().filter(t=>!gs(t))}function Or(e){return String.fromCharCode(e+(e>25?39:97))}function ps(e){let t="",n;for(n=Math.abs(e);n>52;n=n/52|0)t=Or(n%52)+t;return Or(n%52)+t}function hs(e,t){let n=t.length;for(;n;)e=e*33^t.charCodeAt(--n);return e}function ms(e){return ps(hs(5381,e)>>>0)}var $i=/\s*!(important)?/i;function vs(e){return typeof e=="string"?$i.test(e):!1}function xs(e){return typeof e=="string"?e.replace($i,"").trim():e}function Mi(e){return typeof e=="string"?e.replaceAll(" ","_"):e}var Fo=e=>{const t=new Map;return(...o)=>{const r=JSON.stringify(o);if(t.has(r))return t.get(r);const i=e(...o);return t.set(r,i),i}};function Fi(...e){return e.filter(Boolean).reduce((n,o)=>(Object.keys(o).forEach(r=>{const i=n[r],l=o[r];On(i)&&On(l)?n[r]=Fi(i,l):n[r]=l}),n),{})}var bs=e=>e!=null;function Ei(e,t,n={}){const{stop:o,getKey:r}=n;function i(l,s=[]){if(On(l)||Array.isArray(l)){const a={};for(const[d,m]of Object.entries(l)){const f=r?.(d,m)??d,p=[...s,f];if(o?.(l,p))return t(l,s);const u=i(m,p);bs(u)&&(a[f]=u)}return a}return t(l,s)}return i(e)}function ys(e,t){return e.reduce((n,o,r)=>{const i=t[r];return o!=null&&(n[i]=o),n},{})}function Ai(e,t,n=!0){const{utility:o,conditions:r}=t,{hasShorthand:i,resolveShorthand:l}=o;return Ei(e,s=>Array.isArray(s)?ys(s,r.breakpoints.keys):s,{stop:s=>Array.isArray(s),getKey:n?s=>i?l(s):s:void 0})}var Ss={shift:e=>e,finalize:e=>e,breakpoints:{keys:[]}},ws=e=>typeof e=="string"?e.replaceAll(/[\n\s]+/g," "):e;function Cs(e){const{utility:t,hash:n,conditions:o=Ss}=e,r=l=>[t.prefix,l].filter(Boolean).join("-"),i=(l,s)=>{let a;if(n){const d=[...o.finalize(l),s];a=r(t.toHash(d,ms))}else a=[...o.finalize(l),r(s)].join(":");return a};return Fo(({base:l,...s}={})=>{const a=Object.assign(s,l),d=Ai(a,e),m=new Set;return Ei(d,(f,p)=>{const u=vs(f);if(f==null)return;const[h,...y]=o.shift(p),S=fs(y),x=t.transform(h,xs(ws(f)));let b=i(S,x.className);u&&(b=`${b}!`),m.add(b)}),Array.from(m).join(" ")})}function _s(...e){return e.flat().filter(t=>On(t)&&Object.keys(us(t)).length>0)}function Rs(e){function t(r){const i=_s(...r);return i.length===1?i:i.map(l=>Ai(l,e))}function n(...r){return Fi(...t(r))}function o(...r){return Object.assign({},...t(r))}return{mergeCss:Fo(n),assignCss:o}}var Is=/([A-Z])/g,Ps=/^ms-/,ks=Fo(e=>e.startsWith("--")?e:e.replace(Is,"-$1").replace(Ps,"-ms-").toLowerCase()),$s="cm,mm,Q,in,pc,pt,px,em,ex,ch,rem,lh,rlh,vw,vh,vmin,vmax,vb,vi,svw,svh,lvw,lvh,dvw,dvh,cqw,cqh,cqi,cqb,cqmin,cqmax,%";`${$s.split(",").join("|")}`;const Ms="_dark,_light,_hover,_focus,_focusWithin,_focusVisible,_disabled,_active,_visited,_target,_readOnly,_readWrite,_empty,_checked,_enabled,_expanded,_highlighted,_before,_after,_firstLetter,_firstLine,_marker,_selection,_file,_backdrop,_first,_last,_only,_even,_odd,_firstOfType,_lastOfType,_onlyOfType,_peerFocus,_peerHover,_peerActive,_peerFocusWithin,_peerFocusVisible,_peerDisabled,_peerChecked,_peerInvalid,_peerExpanded,_peerPlaceholderShown,_groupFocus,_groupHover,_groupActive,_groupFocusWithin,_groupFocusVisible,_groupDisabled,_groupChecked,_groupExpanded,_groupInvalid,_indeterminate,_required,_valid,_invalid,_autofill,_inRange,_outOfRange,_placeholder,_placeholderShown,_pressed,_selected,_default,_optional,_open,_closed,_fullscreen,_loading,_currentPage,_currentStep,_motionReduce,_motionSafe,_print,_landscape,_portrait,_osDark,_osLight,_highContrast,_lessContrast,_moreContrast,_ltr,_rtl,_scrollbar,_scrollbarThumb,_scrollbarTrack,_horizontal,_vertical,_starting,sm,smOnly,smDown,md,mdOnly,mdDown,lg,lgOnly,lgDown,xl,xlOnly,xlDown,2xl,2xlOnly,2xlDown,smToMd,smToLg,smToXl,smTo2xl,mdToLg,mdToXl,mdTo2xl,lgToXl,lgTo2xl,xlTo2xl,@/xs,@/sm,@/md,@/lg,@/xl,@/2xl,@/3xl,@/4xl,@/5xl,@/6xl,@/7xl,@/8xl,base",Di=new Set(Ms.split(","));function Vr(e){return Di.has(e)||/^@|&|&$/.test(e)}const Fs=/^_/,Es=/&|@/;function As(e){return e.map(t=>Di.has(t)?t.replace(Fs,""):Es.test(t)?`[${Mi(t.trim())}]`:t)}function Ds(e){return e.sort((t,n)=>{const o=Vr(t),r=Vr(n);return o&&!r?1:!o&&r?-1:0})}const Ts="aspectRatio:aspect,boxDecorationBreak:decoration,zIndex:z,boxSizing:box,objectPosition:obj-pos,objectFit:obj-fit,overscrollBehavior:overscroll,overscrollBehaviorX:overscroll-x,overscrollBehaviorY:overscroll-y,position:pos/1,top:top,left:left,insetInline:inset-x/insetX,insetBlock:inset-y/insetY,inset:inset,insetBlockEnd:inset-b,insetBlockStart:inset-t,insetInlineEnd:end/insetEnd/1,insetInlineStart:start/insetStart/1,right:right,bottom:bottom,float:float,visibility:vis,display:d,hideFrom:hide,hideBelow:show,flexBasis:basis,flex:flex,flexDirection:flex/flexDir,flexGrow:grow,flexShrink:shrink,gridTemplateColumns:grid-cols,gridTemplateRows:grid-rows,gridColumn:col-span,gridRow:row-span,gridColumnStart:col-start,gridColumnEnd:col-end,gridAutoFlow:grid-flow,gridAutoColumns:auto-cols,gridAutoRows:auto-rows,gap:gap,gridGap:gap,gridRowGap:gap-x,gridColumnGap:gap-y,rowGap:gap-x,columnGap:gap-y,justifyContent:justify,alignContent:content,alignItems:items,alignSelf:self,padding:p/1,paddingLeft:pl/1,paddingRight:pr/1,paddingTop:pt/1,paddingBottom:pb/1,paddingBlock:py/1/paddingY,paddingBlockEnd:pb,paddingBlockStart:pt,paddingInline:px/paddingX/1,paddingInlineEnd:pe/1/paddingEnd,paddingInlineStart:ps/1/paddingStart,marginLeft:ml/1,marginRight:mr/1,marginTop:mt/1,marginBottom:mb/1,margin:m/1,marginBlock:my/1/marginY,marginBlockEnd:mb,marginBlockStart:mt,marginInline:mx/1/marginX,marginInlineEnd:me/1/marginEnd,marginInlineStart:ms/1/marginStart,spaceX:space-x,spaceY:space-y,outlineWidth:ring-width/ringWidth,outlineColor:ring-color/ringColor,outline:ring/1,outlineOffset:ring-offset/ringOffset,divideX:divide-x,divideY:divide-y,divideColor:divide-color,divideStyle:divide-style,width:w/1,inlineSize:w,minWidth:min-w/minW,minInlineSize:min-w,maxWidth:max-w/maxW,maxInlineSize:max-w,height:h/1,blockSize:h,minHeight:min-h/minH,minBlockSize:min-h,maxHeight:max-h/maxH,maxBlockSize:max-b,color:text,fontFamily:font,fontSize:fs,fontWeight:fw,fontSmoothing:smoothing,fontVariantNumeric:numeric,letterSpacing:tracking,lineHeight:leading,textAlign:text-align,textDecoration:text-decor,textDecorationColor:text-decor-color,textEmphasisColor:text-emphasis-color,textDecorationStyle:decoration-style,textDecorationThickness:decoration-thickness,textUnderlineOffset:underline-offset,textTransform:text-transform,textIndent:indent,textShadow:text-shadow,textShadowColor:text-shadow/textShadowColor,textOverflow:text-overflow,verticalAlign:v-align,wordBreak:break,textWrap:text-wrap,truncate:truncate,lineClamp:clamp,listStyleType:list-type,listStylePosition:list-pos,listStyleImage:list-img,backgroundPosition:bg-pos/bgPosition,backgroundPositionX:bg-pos-x/bgPositionX,backgroundPositionY:bg-pos-y/bgPositionY,backgroundAttachment:bg-attach/bgAttachment,backgroundClip:bg-clip/bgClip,background:bg/1,backgroundColor:bg/bgColor,backgroundOrigin:bg-origin/bgOrigin,backgroundImage:bg-img/bgImage,backgroundRepeat:bg-repeat/bgRepeat,backgroundBlendMode:bg-blend/bgBlendMode,backgroundSize:bg-size/bgSize,backgroundGradient:bg-gradient/bgGradient,textGradient:text-gradient,gradientFromPosition:gradient-from-pos,gradientToPosition:gradient-to-pos,gradientFrom:gradient-from,gradientTo:gradient-to,gradientVia:gradient-via,gradientViaPosition:gradient-via-pos,borderRadius:rounded/1,borderTopLeftRadius:rounded-tl/roundedTopLeft,borderTopRightRadius:rounded-tr/roundedTopRight,borderBottomRightRadius:rounded-br/roundedBottomRight,borderBottomLeftRadius:rounded-bl/roundedBottomLeft,borderTopRadius:rounded-t/roundedTop,borderRightRadius:rounded-r/roundedRight,borderBottomRadius:rounded-b/roundedBottom,borderLeftRadius:rounded-l/roundedLeft,borderStartStartRadius:rounded-ss/roundedStartStart,borderStartEndRadius:rounded-se/roundedStartEnd,borderStartRadius:rounded-s/roundedStart,borderEndStartRadius:rounded-es/roundedEndStart,borderEndEndRadius:rounded-ee/roundedEndEnd,borderEndRadius:rounded-e/roundedEnd,border:border,borderWidth:border-w,borderTopWidth:border-tw,borderLeftWidth:border-lw,borderRightWidth:border-rw,borderBottomWidth:border-bw,borderColor:border,borderInline:border-x/borderX,borderInlineWidth:border-x/borderXWidth,borderInlineColor:border-x/borderXColor,borderBlock:border-y/borderY,borderBlockWidth:border-y/borderYWidth,borderBlockColor:border-y/borderYColor,borderLeft:border-l,borderLeftColor:border-l,borderInlineStart:border-s/borderStart,borderInlineStartWidth:border-s/borderStartWidth,borderInlineStartColor:border-s/borderStartColor,borderRight:border-r,borderRightColor:border-r,borderInlineEnd:border-e/borderEnd,borderInlineEndWidth:border-e/borderEndWidth,borderInlineEndColor:border-e/borderEndColor,borderTop:border-t,borderTopColor:border-t,borderBottom:border-b,borderBottomColor:border-b,borderBlockEnd:border-be,borderBlockEndColor:border-be,borderBlockStart:border-bs,borderBlockStartColor:border-bs,boxShadow:shadow/1,boxShadowColor:shadow-color/shadowColor,mixBlendMode:mix-blend,filter:filter,brightness:brightness,contrast:contrast,grayscale:grayscale,hueRotate:hue-rotate,invert:invert,saturate:saturate,sepia:sepia,dropShadow:drop-shadow,blur:blur,backdropFilter:backdrop,backdropBlur:backdrop-blur,backdropBrightness:backdrop-brightness,backdropContrast:backdrop-contrast,backdropGrayscale:backdrop-grayscale,backdropHueRotate:backdrop-hue-rotate,backdropInvert:backdrop-invert,backdropOpacity:backdrop-opacity,backdropSaturate:backdrop-saturate,backdropSepia:backdrop-sepia,borderCollapse:border,borderSpacing:border-spacing,borderSpacingX:border-spacing-x,borderSpacingY:border-spacing-y,tableLayout:table,transitionTimingFunction:ease,transitionDelay:delay,transitionDuration:duration,transitionProperty:transition-prop,transition:transition,animation:animation,animationName:animation-name,animationTimingFunction:animation-ease,animationDuration:animation-duration,animationDelay:animation-delay,transformOrigin:origin,rotate:rotate,rotateX:rotate-x,rotateY:rotate-y,rotateZ:rotate-z,scale:scale,scaleX:scale-x,scaleY:scale-y,translate:translate,translateX:translate-x/x,translateY:translate-y/y,translateZ:translate-z/z,accentColor:accent,caretColor:caret,scrollBehavior:scroll,scrollbar:scrollbar,scrollMargin:scroll-m,scrollMarginLeft:scroll-ml,scrollMarginRight:scroll-mr,scrollMarginTop:scroll-mt,scrollMarginBottom:scroll-mb,scrollMarginBlock:scroll-my/scrollMarginY,scrollMarginBlockEnd:scroll-mb,scrollMarginBlockStart:scroll-mt,scrollMarginInline:scroll-mx/scrollMarginX,scrollMarginInlineEnd:scroll-me,scrollMarginInlineStart:scroll-ms,scrollPadding:scroll-p,scrollPaddingBlock:scroll-pb/scrollPaddingY,scrollPaddingBlockStart:scroll-pt,scrollPaddingBlockEnd:scroll-pb,scrollPaddingInline:scroll-px/scrollPaddingX,scrollPaddingInlineEnd:scroll-pe,scrollPaddingInlineStart:scroll-ps,scrollPaddingLeft:scroll-pl,scrollPaddingRight:scroll-pr,scrollPaddingTop:scroll-pt,scrollPaddingBottom:scroll-pb,scrollSnapAlign:snap-align,scrollSnapStop:snap-stop,scrollSnapType:snap-type,scrollSnapStrictness:snap-strictness,scrollSnapMargin:snap-m,scrollSnapMarginTop:snap-mt,scrollSnapMarginBottom:snap-mb,scrollSnapMarginLeft:snap-ml,scrollSnapMarginRight:snap-mr,touchAction:touch,userSelect:select,fill:fill,stroke:stroke,strokeWidth:stroke-w,srOnly:sr,debug:debug,appearance:appearance,backfaceVisibility:backface,clipPath:clip-path,hyphens:hyphens,mask:mask,maskImage:mask-image,maskSize:mask-size,textSizeAdjust:text-adjust,container:cq,containerName:cq-name,containerType:cq-type,textStyle:textStyle",Ti=new Map,Li=new Map;Ts.split(",").forEach(e=>{const[t,n]=e.split(":"),[o,...r]=n.split("/");Ti.set(t,o),r.length&&r.forEach(i=>{Li.set(i==="1"?o:i,t)})});const Br=e=>Li.get(e)||e,zi={conditions:{shift:Ds,finalize:As,breakpoints:{keys:["base","sm","md","lg","xl","2xl"]}},utility:{transform:(e,t)=>{const n=Br(e);return{className:`${Ti.get(n)||ks(n)}_${Mi(t)}`}},hasShorthand:!0,toHash:(e,t)=>t(e.join(":")),resolveShorthand:Br}},Ls=Cs(zi),c=(...e)=>Ls(Oi(...e));c.raw=(...e)=>Oi(...e);const{mergeCss:Oi}=Rs(zi);function Kt(){let e="",t=0,n;for(;t<arguments.length;)(n=arguments[t++])&&typeof n=="string"&&(e&&(e+=" "),e+=n);return e}function Vi(e){var t,n,o="";if(typeof e=="string"||typeof e=="number")o+=e;else if(typeof e=="object")if(Array.isArray(e)){var r=e.length;for(t=0;t<r;t++)e[t]&&(n=Vi(e[t]))&&(o&&(o+=" "),o+=n)}else for(n in e)e[n]&&(o&&(o+=" "),o+=n);return o}function Zt(){for(var e,t,n=0,o="",r=arguments.length;n<r;n++)(e=arguments[n])&&(t=Vi(e))&&(o&&(o+=" "),o+=t);return o}var zs=V("<button>"),Os=V("<span>⟳");function oe(e){const t=q({variant:"default",size:"default",type:"button"},e),[n,o]=re(t,["variant","size","loading","disabled","icon","round","circle","plain","children","class"]),r=()=>{const i={display:"inline-flex",alignItems:"center",justifyContent:"center",gap:"8px",fontWeight:"500",borderRadius:n.round?"20px":n.circle?"50%":"4px",border:"1px solid",cursor:"pointer",transition:"all 0.3s",outline:"none",userSelect:"none",verticalAlign:"middle",whiteSpace:"nowrap",textDecoration:"none",_focus:{outline:"2px solid",outlineOffset:"2px"},_disabled:{cursor:"not-allowed",opacity:"0.5"}},l={large:{height:"40px",padding:n.circle?"0":"12px 20px",fontSize:"14px",minWidth:n.circle?"40px":"auto"},default:{height:"32px",padding:n.circle?"0":"8px 16px",fontSize:"14px",minWidth:n.circle?"32px":"auto"},small:{height:"24px",padding:n.circle?"0":"4px 12px",fontSize:"12px",minWidth:n.circle?"24px":"auto"}},s={primary:n.plain?{color:"primary.500",backgroundColor:"primary.50",borderColor:"primary.200",_hover:{backgroundColor:"primary.500",color:"white",borderColor:"primary.500"},_focus:{outlineColor:"primary.500"}}:{color:"white",backgroundColor:"primary.500",borderColor:"primary.500",_hover:{backgroundColor:"primary.600",borderColor:"primary.600"},_focus:{outlineColor:"primary.500"}},success:n.plain?{color:"success.500",backgroundColor:"success.50",borderColor:"success.200",_hover:{backgroundColor:"success.500",color:"white",borderColor:"success.500"},_focus:{outlineColor:"success.500"}}:{color:"white",backgroundColor:"success.500",borderColor:"success.500",_hover:{backgroundColor:"success.600",borderColor:"success.600"},_focus:{outlineColor:"success.500"}},warning:n.plain?{color:"warning.500",backgroundColor:"warning.50",borderColor:"warning.200",_hover:{backgroundColor:"warning.500",color:"white",borderColor:"warning.500"},_focus:{outlineColor:"warning.500"}}:{color:"white",backgroundColor:"warning.500",borderColor:"warning.500",_hover:{backgroundColor:"warning.600",borderColor:"warning.600"},_focus:{outlineColor:"warning.500"}},danger:n.plain?{color:"danger.500",backgroundColor:"danger.50",borderColor:"danger.200",_hover:{backgroundColor:"danger.500",color:"white",borderColor:"danger.500"},_focus:{outlineColor:"danger.500"}}:{color:"white",backgroundColor:"danger.500",borderColor:"danger.500",_hover:{backgroundColor:"danger.600",borderColor:"danger.600"},_focus:{outlineColor:"danger.500"}},info:n.plain?{color:"info.500",backgroundColor:"info.50",borderColor:"info.200",_hover:{backgroundColor:"info.500",color:"white",borderColor:"info.500"},_focus:{outlineColor:"info.500"}}:{color:"white",backgroundColor:"info.500",borderColor:"info.500",_hover:{backgroundColor:"info.600",borderColor:"info.600"},_focus:{outlineColor:"info.500"}},text:{color:"primary.500",backgroundColor:"transparent",borderColor:"transparent",_hover:{color:"primary.600",backgroundColor:"primary.50"},_focus:{outlineColor:"primary.500"}},default:{color:"text.regular",backgroundColor:"white",borderColor:"border.base",_hover:{color:"primary.500",borderColor:"primary.300"},_focus:{outlineColor:"primary.500"}}};return{...i,...l[n.size],...s[n.variant]}};return(()=>{var i=zs();return At(i,q({get class(){return Zt(c(r()),n.class)},get disabled(){return n.disabled||n.loading}},o),!1,!0),w(i,(()=>{var l=ee(()=>!!n.loading);return()=>l()&&(()=>{var s=Os();return O(()=>g(s,c({animation:"spin 1s linear infinite"}))),s})()})(),null),w(i,(()=>{var l=ee(()=>!!(n.icon&&!n.loading));return()=>l()&&n.icon})(),null),w(i,(()=>{var l=ee(()=>!!(n.children&&!n.circle));return()=>l()&&n.children})(),null),i})()}var Jn=V("<span>"),Vs=V("<span>✕"),Bs=V("<div><input>");function Eo(e){const t=q({size:"default",type:"text"},e),[n,o]=re(t,["size","disabled","clearable","prefixIcon","suffixIcon","showPassword","error","onClear","class","value"]),[r,i]=W(!1),[l,s]=W(!1),a=()=>{const h={position:"relative",display:"inline-flex",alignItems:"center",width:"100%",borderRadius:"4px",border:"1px solid",backgroundColor:"white",transition:"all 0.3s",_focusWithin:{borderColor:"primary.500",boxShadow:"0 0 0 2px rgba(64, 158, 255, 0.2)"}},y={large:{height:"40px",fontSize:"14px"},default:{height:"32px",fontSize:"14px"},small:{height:"24px",fontSize:"12px"}},S={borderColor:n.error?"danger.500":l()?"primary.500":"border.base",_hover:n.disabled?{}:{borderColor:n.error?"danger.600":"border.light"},_disabled:{backgroundColor:"bg.page",borderColor:"border.lighter",cursor:"not-allowed"}};return{...h,...y[n.size],...S}},d=()=>{const h=n.prefixIcon?"32px":"12px",y=n.clearable||n.suffixIcon||n.showPassword?"32px":"12px";return{width:"100%",height:"100%",border:"none",outline:"none",backgroundColor:"transparent",color:"text.primary",fontSize:"inherit",paddingLeft:h,paddingRight:y,_placeholder:{color:"text.placeholder"},_disabled:{cursor:"not-allowed",color:"text.placeholder"}}},m=h=>({position:"absolute",top:"50%",transform:"translateY(-50%)",[h==="prefix"?"left":"right"]:"8px",color:"text.secondary",fontSize:"14px",cursor:h==="suffix"&&n.clearable?"pointer":"default",zIndex:1}),f=()=>{n.onClear?.()},p=()=>{i(!r())},u=()=>n.showPassword?r()?"text":"password":o.type||"text";return(()=>{var h=Bs(),y=h.firstChild;return w(h,R(ce,{get when(){return n.prefixIcon},get children(){var S=Jn();return w(S,()=>n.prefixIcon),O(()=>g(S,c(m("prefix")))),S}}),y),At(y,q(o,{get type(){return u()},get class(){return c(d())},get disabled(){return n.disabled},get value(){return n.value},onFocus:()=>s(!0),onBlur:()=>s(!1)}),!1,!1),w(h,R(ce,{get when(){return ee(()=>!!n.clearable)()&&n.value},get children(){var S=Vs();return S.$$click=f,O(()=>g(S,c({...m("suffix"),cursor:"pointer",_hover:{color:"text.primary"}}))),S}}),null),w(h,R(ce,{get when(){return n.showPassword},get children(){var S=Jn();return S.$$click=p,w(S,()=>r()?"👁️":"👁️‍🗨️"),O(()=>g(S,c({...m("suffix"),cursor:"pointer",_hover:{color:"text.primary"}}))),S}}),null),w(h,R(ce,{get when(){return ee(()=>!!(n.suffixIcon&&!n.clearable))()&&!n.showPassword},get children(){var S=Jn();return w(S,()=>n.suffixIcon),O(()=>g(S,c(m("suffix")))),S}}),null),O(()=>g(h,Zt(c(a()),n.class))),h})()}Wt(["click"]);var Ks=V("<div>"),Hs=V("<div><div>");function Vn(e){const[t,n]=re(e,["header","shadow","bodyStyle","headerStyle","children","class"]),o=()=>({...{backgroundColor:"white",borderRadius:"4px",border:"1px solid",borderColor:"border.lighter",overflow:"hidden",transition:"all 0.3s"},...{always:{boxShadow:"base"},hover:{_hover:{boxShadow:"base"}},never:{}}[t.shadow||"always"]}),r=()=>({padding:"18px 20px",borderBottom:"1px solid",borderColor:"border.lighter",backgroundColor:"bg.page",fontSize:"16px",fontWeight:"500",color:"text.primary",...t.headerStyle}),i=()=>({padding:"20px",...t.bodyStyle});return(()=>{var l=Hs(),s=l.firstChild;return At(l,q({get class(){return Zt(c(o()),t.class)}},n),!1,!0),w(l,R(ce,{get when(){return t.header},get children(){var a=Ks();return w(a,(()=>{var d=ee(()=>typeof t.header=="string");return()=>(d(),t.header)})()),O(()=>g(a,c(r()))),a}}),s),w(s,()=>t.children),O(()=>g(s,c(i()))),l})()}var Ws=V("<span>✕"),Ns=V("<span>");function zn(e){const[t,n]=re(e,["type","size","effect","closable","round","onClose","children","class"]),o=()=>{const l={display:"inline-flex",alignItems:"center",gap:"4px",borderRadius:t.round?"16px":"4px",border:"1px solid",fontSize:"12px",fontWeight:"400",lineHeight:1,whiteSpace:"nowrap",verticalAlign:"middle"},s={large:{height:"32px",padding:"0 12px",fontSize:"14px"},default:{height:"24px",padding:"0 8px",fontSize:"12px"},small:{height:"20px",padding:"0 6px",fontSize:"11px"}},d={primary:"primary",success:"success",warning:"warning",danger:"danger",info:"info"}[t.type||"primary"],m={dark:{color:"white",backgroundColor:`${d}.500`,borderColor:`${d}.500`},light:{color:`${d}.600`,backgroundColor:`${d}.50`,borderColor:`${d}.200`},plain:{color:`${d}.500`,backgroundColor:"transparent",borderColor:`${d}.500`}};return{...l,...s[t.size||"default"],...m[t.effect||"light"]}},r=()=>({cursor:"pointer",fontSize:"10px",marginLeft:"4px",borderRadius:"50%",width:"14px",height:"14px",display:"flex",alignItems:"center",justifyContent:"center",_hover:{backgroundColor:"rgba(0, 0, 0, 0.1)"}}),i=l=>{l.stopPropagation(),t.onClose?.()};return(()=>{var l=Ns();return At(l,q({get class(){return Zt(c(o()),t.class)}},n),!1,!0),w(l,()=>t.children,null),w(l,R(ce,{get when(){return t.closable},get children(){var s=Ws();return s.$$click=i,O(()=>g(s,c(r()))),s}}),null),l})()}Wt(["click"]);/**
   * table-core
   *
   * Copyright (c) TanStack
   *
   * This source code is licensed under the MIT license found in the
   * LICENSE.md file in the root directory of this source tree.
   *
   * @license MIT
   */function Pt(e,t){return typeof e=="function"?e(t):e}function Ve(e,t){return n=>{t.setState(o=>({...o,[e]:Pt(n,o[e])}))}}function Nn(e){return e instanceof Function}function Gs(e){return Array.isArray(e)&&e.every(t=>typeof t=="number")}function js(e,t){const n=[],o=r=>{r.forEach(i=>{n.push(i);const l=t(i);l!=null&&l.length&&o(l)})};return o(e),n}function N(e,t,n){let o=[],r;return i=>{let l;n.key&&n.debug&&(l=Date.now());const s=e(i);if(!(s.length!==o.length||s.some((m,f)=>o[f]!==m)))return r;o=s;let d;if(n.key&&n.debug&&(d=Date.now()),r=t(...s),n==null||n.onChange==null||n.onChange(r),n.key&&n.debug&&n!=null&&n.debug()){const m=Math.round((Date.now()-l)*100)/100,f=Math.round((Date.now()-d)*100)/100,p=f/16,u=(h,y)=>{for(h=String(h);h.length<y;)h=" "+h;return h};console.info(`%c⏱ ${u(f,5)} /${u(m,5)} ms`,`
            font-size: .6rem;
            font-weight: bold;
            color: hsl(${Math.max(0,Math.min(120-120*p,120))}deg 100% 31%);`,n?.key)}return r}}function G(e,t,n,o){return{debug:()=>{var r;return(r=e?.debugAll)!=null?r:e[t]},key:n,onChange:o}}function Us(e,t,n,o){const r=()=>{var l;return(l=i.getValue())!=null?l:e.options.renderFallbackValue},i={id:`${t.id}_${n.id}`,row:t,column:n,getValue:()=>t.getValue(o),renderValue:r,getContext:N(()=>[e,n,t,i],(l,s,a,d)=>({table:l,column:s,row:a,cell:d,getValue:d.getValue,renderValue:d.renderValue}),G(e.options,"debugCells","cell.getContext"))};return e._features.forEach(l=>{l.createCell==null||l.createCell(i,n,t,e)},{}),i}function qs(e,t,n,o){var r,i;const s={...e._getDefaultColumnDef(),...t},a=s.accessorKey;let d=(r=(i=s.id)!=null?i:a?typeof String.prototype.replaceAll=="function"?a.replaceAll(".","_"):a.replace(/\./g,"_"):void 0)!=null?r:typeof s.header=="string"?s.header:void 0,m;if(s.accessorFn?m=s.accessorFn:a&&(a.includes(".")?m=p=>{let u=p;for(const y of a.split(".")){var h;u=(h=u)==null?void 0:h[y],u===void 0&&console.warn(`"${y}" in deeply nested key "${a}" returned undefined.`)}return u}:m=p=>p[s.accessorKey]),!d)throw new Error(s.accessorFn?"Columns require an id when using an accessorFn":"Columns require an id when using a non-string header");let f={id:`${String(d)}`,accessorFn:m,parent:o,depth:n,columnDef:s,columns:[],getFlatColumns:N(()=>[!0],()=>{var p;return[f,...(p=f.columns)==null?void 0:p.flatMap(u=>u.getFlatColumns())]},G(e.options,"debugColumns","column.getFlatColumns")),getLeafColumns:N(()=>[e._getOrderColumnsFn()],p=>{var u;if((u=f.columns)!=null&&u.length){let h=f.columns.flatMap(y=>y.getLeafColumns());return p(h)}return[f]},G(e.options,"debugColumns","column.getLeafColumns"))};for(const p of e._features)p.createColumn==null||p.createColumn(f,e);return f}const Re="debugHeaders";function Kr(e,t,n){var o;let i={id:(o=n.id)!=null?o:t.id,column:t,index:n.index,isPlaceholder:!!n.isPlaceholder,placeholderId:n.placeholderId,depth:n.depth,subHeaders:[],colSpan:0,rowSpan:0,headerGroup:null,getLeafHeaders:()=>{const l=[],s=a=>{a.subHeaders&&a.subHeaders.length&&a.subHeaders.map(s),l.push(a)};return s(i),l},getContext:()=>({table:e,header:i,column:t})};return e._features.forEach(l=>{l.createHeader==null||l.createHeader(i,e)}),i}const Xs={createTable:e=>{e.getHeaderGroups=N(()=>[e.getAllColumns(),e.getVisibleLeafColumns(),e.getState().columnPinning.left,e.getState().columnPinning.right],(t,n,o,r)=>{var i,l;const s=(i=o?.map(f=>n.find(p=>p.id===f)).filter(Boolean))!=null?i:[],a=(l=r?.map(f=>n.find(p=>p.id===f)).filter(Boolean))!=null?l:[],d=n.filter(f=>!(o!=null&&o.includes(f.id))&&!(r!=null&&r.includes(f.id)));return In(t,[...s,...d,...a],e)},G(e.options,Re,"getHeaderGroups")),e.getCenterHeaderGroups=N(()=>[e.getAllColumns(),e.getVisibleLeafColumns(),e.getState().columnPinning.left,e.getState().columnPinning.right],(t,n,o,r)=>(n=n.filter(i=>!(o!=null&&o.includes(i.id))&&!(r!=null&&r.includes(i.id))),In(t,n,e,"center")),G(e.options,Re,"getCenterHeaderGroups")),e.getLeftHeaderGroups=N(()=>[e.getAllColumns(),e.getVisibleLeafColumns(),e.getState().columnPinning.left],(t,n,o)=>{var r;const i=(r=o?.map(l=>n.find(s=>s.id===l)).filter(Boolean))!=null?r:[];return In(t,i,e,"left")},G(e.options,Re,"getLeftHeaderGroups")),e.getRightHeaderGroups=N(()=>[e.getAllColumns(),e.getVisibleLeafColumns(),e.getState().columnPinning.right],(t,n,o)=>{var r;const i=(r=o?.map(l=>n.find(s=>s.id===l)).filter(Boolean))!=null?r:[];return In(t,i,e,"right")},G(e.options,Re,"getRightHeaderGroups")),e.getFooterGroups=N(()=>[e.getHeaderGroups()],t=>[...t].reverse(),G(e.options,Re,"getFooterGroups")),e.getLeftFooterGroups=N(()=>[e.getLeftHeaderGroups()],t=>[...t].reverse(),G(e.options,Re,"getLeftFooterGroups")),e.getCenterFooterGroups=N(()=>[e.getCenterHeaderGroups()],t=>[...t].reverse(),G(e.options,Re,"getCenterFooterGroups")),e.getRightFooterGroups=N(()=>[e.getRightHeaderGroups()],t=>[...t].reverse(),G(e.options,Re,"getRightFooterGroups")),e.getFlatHeaders=N(()=>[e.getHeaderGroups()],t=>t.map(n=>n.headers).flat(),G(e.options,Re,"getFlatHeaders")),e.getLeftFlatHeaders=N(()=>[e.getLeftHeaderGroups()],t=>t.map(n=>n.headers).flat(),G(e.options,Re,"getLeftFlatHeaders")),e.getCenterFlatHeaders=N(()=>[e.getCenterHeaderGroups()],t=>t.map(n=>n.headers).flat(),G(e.options,Re,"getCenterFlatHeaders")),e.getRightFlatHeaders=N(()=>[e.getRightHeaderGroups()],t=>t.map(n=>n.headers).flat(),G(e.options,Re,"getRightFlatHeaders")),e.getCenterLeafHeaders=N(()=>[e.getCenterFlatHeaders()],t=>t.filter(n=>{var o;return!((o=n.subHeaders)!=null&&o.length)}),G(e.options,Re,"getCenterLeafHeaders")),e.getLeftLeafHeaders=N(()=>[e.getLeftFlatHeaders()],t=>t.filter(n=>{var o;return!((o=n.subHeaders)!=null&&o.length)}),G(e.options,Re,"getLeftLeafHeaders")),e.getRightLeafHeaders=N(()=>[e.getRightFlatHeaders()],t=>t.filter(n=>{var o;return!((o=n.subHeaders)!=null&&o.length)}),G(e.options,Re,"getRightLeafHeaders")),e.getLeafHeaders=N(()=>[e.getLeftHeaderGroups(),e.getCenterHeaderGroups(),e.getRightHeaderGroups()],(t,n,o)=>{var r,i,l,s,a,d;return[...(r=(i=t[0])==null?void 0:i.headers)!=null?r:[],...(l=(s=n[0])==null?void 0:s.headers)!=null?l:[],...(a=(d=o[0])==null?void 0:d.headers)!=null?a:[]].map(m=>m.getLeafHeaders()).flat()},G(e.options,Re,"getLeafHeaders"))}};function In(e,t,n,o){var r,i;let l=0;const s=function(p,u){u===void 0&&(u=1),l=Math.max(l,u),p.filter(h=>h.getIsVisible()).forEach(h=>{var y;(y=h.columns)!=null&&y.length&&s(h.columns,u+1)},0)};s(e);let a=[];const d=(p,u)=>{const h={depth:u,id:[o,`${u}`].filter(Boolean).join("_"),headers:[]},y=[];p.forEach(S=>{const x=[...y].reverse()[0],b=S.column.depth===h.depth;let v,_=!1;if(b&&S.column.parent?v=S.column.parent:(v=S.column,_=!0),x&&x?.column===v)x.subHeaders.push(S);else{const C=Kr(n,v,{id:[o,u,v.id,S?.id].filter(Boolean).join("_"),isPlaceholder:_,placeholderId:_?`${y.filter(M=>M.column===v).length}`:void 0,depth:u,index:y.length});C.subHeaders.push(S),y.push(C)}h.headers.push(S),S.headerGroup=h}),a.push(h),u>0&&d(y,u-1)},m=t.map((p,u)=>Kr(n,p,{depth:l,index:u}));d(m,l-1),a.reverse();const f=p=>p.filter(h=>h.column.getIsVisible()).map(h=>{let y=0,S=0,x=[0];h.subHeaders&&h.subHeaders.length?(x=[],f(h.subHeaders).forEach(v=>{let{colSpan:_,rowSpan:C}=v;y+=_,x.push(C)})):y=1;const b=Math.min(...x);return S=S+b,h.colSpan=y,h.rowSpan=S,{colSpan:y,rowSpan:S}});return f((r=(i=a[0])==null?void 0:i.headers)!=null?r:[]),a}const Ao=(e,t,n,o,r,i,l)=>{let s={id:t,index:o,original:n,depth:r,parentId:l,_valuesCache:{},_uniqueValuesCache:{},getValue:a=>{if(s._valuesCache.hasOwnProperty(a))return s._valuesCache[a];const d=e.getColumn(a);if(d!=null&&d.accessorFn)return s._valuesCache[a]=d.accessorFn(s.original,o),s._valuesCache[a]},getUniqueValues:a=>{if(s._uniqueValuesCache.hasOwnProperty(a))return s._uniqueValuesCache[a];const d=e.getColumn(a);if(d!=null&&d.accessorFn)return d.columnDef.getUniqueValues?(s._uniqueValuesCache[a]=d.columnDef.getUniqueValues(s.original,o),s._uniqueValuesCache[a]):(s._uniqueValuesCache[a]=[s.getValue(a)],s._uniqueValuesCache[a])},renderValue:a=>{var d;return(d=s.getValue(a))!=null?d:e.options.renderFallbackValue},subRows:[],getLeafRows:()=>js(s.subRows,a=>a.subRows),getParentRow:()=>s.parentId?e.getRow(s.parentId,!0):void 0,getParentRows:()=>{let a=[],d=s;for(;;){const m=d.getParentRow();if(!m)break;a.push(m),d=m}return a.reverse()},getAllCells:N(()=>[e.getAllLeafColumns()],a=>a.map(d=>Us(e,s,d,d.id)),G(e.options,"debugRows","getAllCells")),_getAllCellsByColumnId:N(()=>[s.getAllCells()],a=>a.reduce((d,m)=>(d[m.column.id]=m,d),{}),G(e.options,"debugRows","getAllCellsByColumnId"))};for(let a=0;a<e._features.length;a++){const d=e._features[a];d==null||d.createRow==null||d.createRow(s,e)}return s},Ys={createColumn:(e,t)=>{e._getFacetedRowModel=t.options.getFacetedRowModel&&t.options.getFacetedRowModel(t,e.id),e.getFacetedRowModel=()=>e._getFacetedRowModel?e._getFacetedRowModel():t.getPreFilteredRowModel(),e._getFacetedUniqueValues=t.options.getFacetedUniqueValues&&t.options.getFacetedUniqueValues(t,e.id),e.getFacetedUniqueValues=()=>e._getFacetedUniqueValues?e._getFacetedUniqueValues():new Map,e._getFacetedMinMaxValues=t.options.getFacetedMinMaxValues&&t.options.getFacetedMinMaxValues(t,e.id),e.getFacetedMinMaxValues=()=>{if(e._getFacetedMinMaxValues)return e._getFacetedMinMaxValues()}}},Bi=(e,t,n)=>{var o,r;const i=n==null||(o=n.toString())==null?void 0:o.toLowerCase();return!!(!((r=e.getValue(t))==null||(r=r.toString())==null||(r=r.toLowerCase())==null)&&r.includes(i))};Bi.autoRemove=e=>Ue(e);const Ki=(e,t,n)=>{var o;return!!(!((o=e.getValue(t))==null||(o=o.toString())==null)&&o.includes(n))};Ki.autoRemove=e=>Ue(e);const Hi=(e,t,n)=>{var o;return((o=e.getValue(t))==null||(o=o.toString())==null?void 0:o.toLowerCase())===n?.toLowerCase()};Hi.autoRemove=e=>Ue(e);const Wi=(e,t,n)=>{var o;return(o=e.getValue(t))==null?void 0:o.includes(n)};Wi.autoRemove=e=>Ue(e);const Ni=(e,t,n)=>!n.some(o=>{var r;return!((r=e.getValue(t))!=null&&r.includes(o))});Ni.autoRemove=e=>Ue(e)||!(e!=null&&e.length);const Gi=(e,t,n)=>n.some(o=>{var r;return(r=e.getValue(t))==null?void 0:r.includes(o)});Gi.autoRemove=e=>Ue(e)||!(e!=null&&e.length);const ji=(e,t,n)=>e.getValue(t)===n;ji.autoRemove=e=>Ue(e);const Ui=(e,t,n)=>e.getValue(t)==n;Ui.autoRemove=e=>Ue(e);const Do=(e,t,n)=>{let[o,r]=n;const i=e.getValue(t);return i>=o&&i<=r};Do.resolveFilterValue=e=>{let[t,n]=e,o=typeof t!="number"?parseFloat(t):t,r=typeof n!="number"?parseFloat(n):n,i=t===null||Number.isNaN(o)?-1/0:o,l=n===null||Number.isNaN(r)?1/0:r;if(i>l){const s=i;i=l,l=s}return[i,l]};Do.autoRemove=e=>Ue(e)||Ue(e[0])&&Ue(e[1]);const at={includesString:Bi,includesStringSensitive:Ki,equalsString:Hi,arrIncludes:Wi,arrIncludesAll:Ni,arrIncludesSome:Gi,equals:ji,weakEquals:Ui,inNumberRange:Do};function Ue(e){return e==null||e===""}const Zs={getDefaultColumnDef:()=>({filterFn:"auto"}),getInitialState:e=>({columnFilters:[],...e}),getDefaultOptions:e=>({onColumnFiltersChange:Ve("columnFilters",e),filterFromLeafRows:!1,maxLeafRowFilterDepth:100}),createColumn:(e,t)=>{e.getAutoFilterFn=()=>{const n=t.getCoreRowModel().flatRows[0],o=n?.getValue(e.id);return typeof o=="string"?at.includesString:typeof o=="number"?at.inNumberRange:typeof o=="boolean"||o!==null&&typeof o=="object"?at.equals:Array.isArray(o)?at.arrIncludes:at.weakEquals},e.getFilterFn=()=>{var n,o;return Nn(e.columnDef.filterFn)?e.columnDef.filterFn:e.columnDef.filterFn==="auto"?e.getAutoFilterFn():(n=(o=t.options.filterFns)==null?void 0:o[e.columnDef.filterFn])!=null?n:at[e.columnDef.filterFn]},e.getCanFilter=()=>{var n,o,r;return((n=e.columnDef.enableColumnFilter)!=null?n:!0)&&((o=t.options.enableColumnFilters)!=null?o:!0)&&((r=t.options.enableFilters)!=null?r:!0)&&!!e.accessorFn},e.getIsFiltered=()=>e.getFilterIndex()>-1,e.getFilterValue=()=>{var n;return(n=t.getState().columnFilters)==null||(n=n.find(o=>o.id===e.id))==null?void 0:n.value},e.getFilterIndex=()=>{var n,o;return(n=(o=t.getState().columnFilters)==null?void 0:o.findIndex(r=>r.id===e.id))!=null?n:-1},e.setFilterValue=n=>{t.setColumnFilters(o=>{const r=e.getFilterFn(),i=o?.find(m=>m.id===e.id),l=Pt(n,i?i.value:void 0);if(Hr(r,l,e)){var s;return(s=o?.filter(m=>m.id!==e.id))!=null?s:[]}const a={id:e.id,value:l};if(i){var d;return(d=o?.map(m=>m.id===e.id?a:m))!=null?d:[]}return o!=null&&o.length?[...o,a]:[a]})}},createRow:(e,t)=>{e.columnFilters={},e.columnFiltersMeta={}},createTable:e=>{e.setColumnFilters=t=>{const n=e.getAllLeafColumns(),o=r=>{var i;return(i=Pt(t,r))==null?void 0:i.filter(l=>{const s=n.find(a=>a.id===l.id);if(s){const a=s.getFilterFn();if(Hr(a,l.value,s))return!1}return!0})};e.options.onColumnFiltersChange==null||e.options.onColumnFiltersChange(o)},e.resetColumnFilters=t=>{var n,o;e.setColumnFilters(t?[]:(n=(o=e.initialState)==null?void 0:o.columnFilters)!=null?n:[])},e.getPreFilteredRowModel=()=>e.getCoreRowModel(),e.getFilteredRowModel=()=>(!e._getFilteredRowModel&&e.options.getFilteredRowModel&&(e._getFilteredRowModel=e.options.getFilteredRowModel(e)),e.options.manualFiltering||!e._getFilteredRowModel?e.getPreFilteredRowModel():e._getFilteredRowModel())}};function Hr(e,t,n){return(e&&e.autoRemove?e.autoRemove(t,n):!1)||typeof t>"u"||typeof t=="string"&&!t}const Js=(e,t,n)=>n.reduce((o,r)=>{const i=r.getValue(e);return o+(typeof i=="number"?i:0)},0),Qs=(e,t,n)=>{let o;return n.forEach(r=>{const i=r.getValue(e);i!=null&&(o>i||o===void 0&&i>=i)&&(o=i)}),o},ea=(e,t,n)=>{let o;return n.forEach(r=>{const i=r.getValue(e);i!=null&&(o<i||o===void 0&&i>=i)&&(o=i)}),o},ta=(e,t,n)=>{let o,r;return n.forEach(i=>{const l=i.getValue(e);l!=null&&(o===void 0?l>=l&&(o=r=l):(o>l&&(o=l),r<l&&(r=l)))}),[o,r]},na=(e,t)=>{let n=0,o=0;if(t.forEach(r=>{let i=r.getValue(e);i!=null&&(i=+i)>=i&&(++n,o+=i)}),n)return o/n},oa=(e,t)=>{if(!t.length)return;const n=t.map(i=>i.getValue(e));if(!Gs(n))return;if(n.length===1)return n[0];const o=Math.floor(n.length/2),r=n.sort((i,l)=>i-l);return n.length%2!==0?r[o]:(r[o-1]+r[o])/2},ra=(e,t)=>Array.from(new Set(t.map(n=>n.getValue(e))).values()),ia=(e,t)=>new Set(t.map(n=>n.getValue(e))).size,la=(e,t)=>t.length,Qn={sum:Js,min:Qs,max:ea,extent:ta,mean:na,median:oa,unique:ra,uniqueCount:ia,count:la},sa={getDefaultColumnDef:()=>({aggregatedCell:e=>{var t,n;return(t=(n=e.getValue())==null||n.toString==null?void 0:n.toString())!=null?t:null},aggregationFn:"auto"}),getInitialState:e=>({grouping:[],...e}),getDefaultOptions:e=>({onGroupingChange:Ve("grouping",e),groupedColumnMode:"reorder"}),createColumn:(e,t)=>{e.toggleGrouping=()=>{t.setGrouping(n=>n!=null&&n.includes(e.id)?n.filter(o=>o!==e.id):[...n??[],e.id])},e.getCanGroup=()=>{var n,o;return((n=e.columnDef.enableGrouping)!=null?n:!0)&&((o=t.options.enableGrouping)!=null?o:!0)&&(!!e.accessorFn||!!e.columnDef.getGroupingValue)},e.getIsGrouped=()=>{var n;return(n=t.getState().grouping)==null?void 0:n.includes(e.id)},e.getGroupedIndex=()=>{var n;return(n=t.getState().grouping)==null?void 0:n.indexOf(e.id)},e.getToggleGroupingHandler=()=>{const n=e.getCanGroup();return()=>{n&&e.toggleGrouping()}},e.getAutoAggregationFn=()=>{const n=t.getCoreRowModel().flatRows[0],o=n?.getValue(e.id);if(typeof o=="number")return Qn.sum;if(Object.prototype.toString.call(o)==="[object Date]")return Qn.extent},e.getAggregationFn=()=>{var n,o;if(!e)throw new Error;return Nn(e.columnDef.aggregationFn)?e.columnDef.aggregationFn:e.columnDef.aggregationFn==="auto"?e.getAutoAggregationFn():(n=(o=t.options.aggregationFns)==null?void 0:o[e.columnDef.aggregationFn])!=null?n:Qn[e.columnDef.aggregationFn]}},createTable:e=>{e.setGrouping=t=>e.options.onGroupingChange==null?void 0:e.options.onGroupingChange(t),e.resetGrouping=t=>{var n,o;e.setGrouping(t?[]:(n=(o=e.initialState)==null?void 0:o.grouping)!=null?n:[])},e.getPreGroupedRowModel=()=>e.getFilteredRowModel(),e.getGroupedRowModel=()=>(!e._getGroupedRowModel&&e.options.getGroupedRowModel&&(e._getGroupedRowModel=e.options.getGroupedRowModel(e)),e.options.manualGrouping||!e._getGroupedRowModel?e.getPreGroupedRowModel():e._getGroupedRowModel())},createRow:(e,t)=>{e.getIsGrouped=()=>!!e.groupingColumnId,e.getGroupingValue=n=>{if(e._groupingValuesCache.hasOwnProperty(n))return e._groupingValuesCache[n];const o=t.getColumn(n);return o!=null&&o.columnDef.getGroupingValue?(e._groupingValuesCache[n]=o.columnDef.getGroupingValue(e.original),e._groupingValuesCache[n]):e.getValue(n)},e._groupingValuesCache={}},createCell:(e,t,n,o)=>{e.getIsGrouped=()=>t.getIsGrouped()&&t.id===n.groupingColumnId,e.getIsPlaceholder=()=>!e.getIsGrouped()&&t.getIsGrouped(),e.getIsAggregated=()=>{var r;return!e.getIsGrouped()&&!e.getIsPlaceholder()&&!!((r=n.subRows)!=null&&r.length)}}};function aa(e,t,n){if(!(t!=null&&t.length)||!n)return e;const o=e.filter(i=>!t.includes(i.id));return n==="remove"?o:[...t.map(i=>e.find(l=>l.id===i)).filter(Boolean),...o]}const ca={getInitialState:e=>({columnOrder:[],...e}),getDefaultOptions:e=>({onColumnOrderChange:Ve("columnOrder",e)}),createColumn:(e,t)=>{e.getIndex=N(n=>[xn(t,n)],n=>n.findIndex(o=>o.id===e.id),G(t.options,"debugColumns","getIndex")),e.getIsFirstColumn=n=>{var o;return((o=xn(t,n)[0])==null?void 0:o.id)===e.id},e.getIsLastColumn=n=>{var o;const r=xn(t,n);return((o=r[r.length-1])==null?void 0:o.id)===e.id}},createTable:e=>{e.setColumnOrder=t=>e.options.onColumnOrderChange==null?void 0:e.options.onColumnOrderChange(t),e.resetColumnOrder=t=>{var n;e.setColumnOrder(t?[]:(n=e.initialState.columnOrder)!=null?n:[])},e._getOrderColumnsFn=N(()=>[e.getState().columnOrder,e.getState().grouping,e.options.groupedColumnMode],(t,n,o)=>r=>{let i=[];if(!(t!=null&&t.length))i=r;else{const l=[...t],s=[...r];for(;s.length&&l.length;){const a=l.shift(),d=s.findIndex(m=>m.id===a);d>-1&&i.push(s.splice(d,1)[0])}i=[...i,...s]}return aa(i,n,o)},G(e.options,"debugTable","_getOrderColumnsFn"))}},eo=()=>({left:[],right:[]}),da={getInitialState:e=>({columnPinning:eo(),...e}),getDefaultOptions:e=>({onColumnPinningChange:Ve("columnPinning",e)}),createColumn:(e,t)=>{e.pin=n=>{const o=e.getLeafColumns().map(r=>r.id).filter(Boolean);t.setColumnPinning(r=>{var i,l;if(n==="right"){var s,a;return{left:((s=r?.left)!=null?s:[]).filter(f=>!(o!=null&&o.includes(f))),right:[...((a=r?.right)!=null?a:[]).filter(f=>!(o!=null&&o.includes(f))),...o]}}if(n==="left"){var d,m;return{left:[...((d=r?.left)!=null?d:[]).filter(f=>!(o!=null&&o.includes(f))),...o],right:((m=r?.right)!=null?m:[]).filter(f=>!(o!=null&&o.includes(f)))}}return{left:((i=r?.left)!=null?i:[]).filter(f=>!(o!=null&&o.includes(f))),right:((l=r?.right)!=null?l:[]).filter(f=>!(o!=null&&o.includes(f)))}})},e.getCanPin=()=>e.getLeafColumns().some(o=>{var r,i,l;return((r=o.columnDef.enablePinning)!=null?r:!0)&&((i=(l=t.options.enableColumnPinning)!=null?l:t.options.enablePinning)!=null?i:!0)}),e.getIsPinned=()=>{const n=e.getLeafColumns().map(s=>s.id),{left:o,right:r}=t.getState().columnPinning,i=n.some(s=>o?.includes(s)),l=n.some(s=>r?.includes(s));return i?"left":l?"right":!1},e.getPinnedIndex=()=>{var n,o;const r=e.getIsPinned();return r?(n=(o=t.getState().columnPinning)==null||(o=o[r])==null?void 0:o.indexOf(e.id))!=null?n:-1:0}},createRow:(e,t)=>{e.getCenterVisibleCells=N(()=>[e._getAllVisibleCells(),t.getState().columnPinning.left,t.getState().columnPinning.right],(n,o,r)=>{const i=[...o??[],...r??[]];return n.filter(l=>!i.includes(l.column.id))},G(t.options,"debugRows","getCenterVisibleCells")),e.getLeftVisibleCells=N(()=>[e._getAllVisibleCells(),t.getState().columnPinning.left],(n,o)=>(o??[]).map(i=>n.find(l=>l.column.id===i)).filter(Boolean).map(i=>({...i,position:"left"})),G(t.options,"debugRows","getLeftVisibleCells")),e.getRightVisibleCells=N(()=>[e._getAllVisibleCells(),t.getState().columnPinning.right],(n,o)=>(o??[]).map(i=>n.find(l=>l.column.id===i)).filter(Boolean).map(i=>({...i,position:"right"})),G(t.options,"debugRows","getRightVisibleCells"))},createTable:e=>{e.setColumnPinning=t=>e.options.onColumnPinningChange==null?void 0:e.options.onColumnPinningChange(t),e.resetColumnPinning=t=>{var n,o;return e.setColumnPinning(t?eo():(n=(o=e.initialState)==null?void 0:o.columnPinning)!=null?n:eo())},e.getIsSomeColumnsPinned=t=>{var n;const o=e.getState().columnPinning;if(!t){var r,i;return!!((r=o.left)!=null&&r.length||(i=o.right)!=null&&i.length)}return!!((n=o[t])!=null&&n.length)},e.getLeftLeafColumns=N(()=>[e.getAllLeafColumns(),e.getState().columnPinning.left],(t,n)=>(n??[]).map(o=>t.find(r=>r.id===o)).filter(Boolean),G(e.options,"debugColumns","getLeftLeafColumns")),e.getRightLeafColumns=N(()=>[e.getAllLeafColumns(),e.getState().columnPinning.right],(t,n)=>(n??[]).map(o=>t.find(r=>r.id===o)).filter(Boolean),G(e.options,"debugColumns","getRightLeafColumns")),e.getCenterLeafColumns=N(()=>[e.getAllLeafColumns(),e.getState().columnPinning.left,e.getState().columnPinning.right],(t,n,o)=>{const r=[...n??[],...o??[]];return t.filter(i=>!r.includes(i.id))},G(e.options,"debugColumns","getCenterLeafColumns"))}};function ua(e){return e||(typeof document<"u"?document:null)}const Pn={size:150,minSize:20,maxSize:Number.MAX_SAFE_INTEGER},to=()=>({startOffset:null,startSize:null,deltaOffset:null,deltaPercentage:null,isResizingColumn:!1,columnSizingStart:[]}),ga={getDefaultColumnDef:()=>Pn,getInitialState:e=>({columnSizing:{},columnSizingInfo:to(),...e}),getDefaultOptions:e=>({columnResizeMode:"onEnd",columnResizeDirection:"ltr",onColumnSizingChange:Ve("columnSizing",e),onColumnSizingInfoChange:Ve("columnSizingInfo",e)}),createColumn:(e,t)=>{e.getSize=()=>{var n,o,r;const i=t.getState().columnSizing[e.id];return Math.min(Math.max((n=e.columnDef.minSize)!=null?n:Pn.minSize,(o=i??e.columnDef.size)!=null?o:Pn.size),(r=e.columnDef.maxSize)!=null?r:Pn.maxSize)},e.getStart=N(n=>[n,xn(t,n),t.getState().columnSizing],(n,o)=>o.slice(0,e.getIndex(n)).reduce((r,i)=>r+i.getSize(),0),G(t.options,"debugColumns","getStart")),e.getAfter=N(n=>[n,xn(t,n),t.getState().columnSizing],(n,o)=>o.slice(e.getIndex(n)+1).reduce((r,i)=>r+i.getSize(),0),G(t.options,"debugColumns","getAfter")),e.resetSize=()=>{t.setColumnSizing(n=>{let{[e.id]:o,...r}=n;return r})},e.getCanResize=()=>{var n,o;return((n=e.columnDef.enableResizing)!=null?n:!0)&&((o=t.options.enableColumnResizing)!=null?o:!0)},e.getIsResizing=()=>t.getState().columnSizingInfo.isResizingColumn===e.id},createHeader:(e,t)=>{e.getSize=()=>{let n=0;const o=r=>{if(r.subHeaders.length)r.subHeaders.forEach(o);else{var i;n+=(i=r.column.getSize())!=null?i:0}};return o(e),n},e.getStart=()=>{if(e.index>0){const n=e.headerGroup.headers[e.index-1];return n.getStart()+n.getSize()}return 0},e.getResizeHandler=n=>{const o=t.getColumn(e.column.id),r=o?.getCanResize();return i=>{if(!o||!r||(i.persist==null||i.persist(),no(i)&&i.touches&&i.touches.length>1))return;const l=e.getSize(),s=e?e.getLeafHeaders().map(x=>[x.column.id,x.column.getSize()]):[[o.id,o.getSize()]],a=no(i)?Math.round(i.touches[0].clientX):i.clientX,d={},m=(x,b)=>{typeof b=="number"&&(t.setColumnSizingInfo(v=>{var _,C;const M=t.options.columnResizeDirection==="rtl"?-1:1,A=(b-((_=v?.startOffset)!=null?_:0))*M,P=Math.max(A/((C=v?.startSize)!=null?C:0),-.999999);return v.columnSizingStart.forEach(F=>{let[D,T]=F;d[D]=Math.round(Math.max(T+T*P,0)*100)/100}),{...v,deltaOffset:A,deltaPercentage:P}}),(t.options.columnResizeMode==="onChange"||x==="end")&&t.setColumnSizing(v=>({...v,...d})))},f=x=>m("move",x),p=x=>{m("end",x),t.setColumnSizingInfo(b=>({...b,isResizingColumn:!1,startOffset:null,startSize:null,deltaOffset:null,deltaPercentage:null,columnSizingStart:[]}))},u=ua(n),h={moveHandler:x=>f(x.clientX),upHandler:x=>{u?.removeEventListener("mousemove",h.moveHandler),u?.removeEventListener("mouseup",h.upHandler),p(x.clientX)}},y={moveHandler:x=>(x.cancelable&&(x.preventDefault(),x.stopPropagation()),f(x.touches[0].clientX),!1),upHandler:x=>{var b;u?.removeEventListener("touchmove",y.moveHandler),u?.removeEventListener("touchend",y.upHandler),x.cancelable&&(x.preventDefault(),x.stopPropagation()),p((b=x.touches[0])==null?void 0:b.clientX)}},S=fa()?{passive:!1}:!1;no(i)?(u?.addEventListener("touchmove",y.moveHandler,S),u?.addEventListener("touchend",y.upHandler,S)):(u?.addEventListener("mousemove",h.moveHandler,S),u?.addEventListener("mouseup",h.upHandler,S)),t.setColumnSizingInfo(x=>({...x,startOffset:a,startSize:l,deltaOffset:0,deltaPercentage:0,columnSizingStart:s,isResizingColumn:o.id}))}}},createTable:e=>{e.setColumnSizing=t=>e.options.onColumnSizingChange==null?void 0:e.options.onColumnSizingChange(t),e.setColumnSizingInfo=t=>e.options.onColumnSizingInfoChange==null?void 0:e.options.onColumnSizingInfoChange(t),e.resetColumnSizing=t=>{var n;e.setColumnSizing(t?{}:(n=e.initialState.columnSizing)!=null?n:{})},e.resetHeaderSizeInfo=t=>{var n;e.setColumnSizingInfo(t?to():(n=e.initialState.columnSizingInfo)!=null?n:to())},e.getTotalSize=()=>{var t,n;return(t=(n=e.getHeaderGroups()[0])==null?void 0:n.headers.reduce((o,r)=>o+r.getSize(),0))!=null?t:0},e.getLeftTotalSize=()=>{var t,n;return(t=(n=e.getLeftHeaderGroups()[0])==null?void 0:n.headers.reduce((o,r)=>o+r.getSize(),0))!=null?t:0},e.getCenterTotalSize=()=>{var t,n;return(t=(n=e.getCenterHeaderGroups()[0])==null?void 0:n.headers.reduce((o,r)=>o+r.getSize(),0))!=null?t:0},e.getRightTotalSize=()=>{var t,n;return(t=(n=e.getRightHeaderGroups()[0])==null?void 0:n.headers.reduce((o,r)=>o+r.getSize(),0))!=null?t:0}}};let kn=null;function fa(){if(typeof kn=="boolean")return kn;let e=!1;try{const t={get passive(){return e=!0,!1}},n=()=>{};window.addEventListener("test",n,t),window.removeEventListener("test",n)}catch{e=!1}return kn=e,kn}function no(e){return e.type==="touchstart"}const pa={getInitialState:e=>({columnVisibility:{},...e}),getDefaultOptions:e=>({onColumnVisibilityChange:Ve("columnVisibility",e)}),createColumn:(e,t)=>{e.toggleVisibility=n=>{e.getCanHide()&&t.setColumnVisibility(o=>({...o,[e.id]:n??!e.getIsVisible()}))},e.getIsVisible=()=>{var n,o;const r=e.columns;return(n=r.length?r.some(i=>i.getIsVisible()):(o=t.getState().columnVisibility)==null?void 0:o[e.id])!=null?n:!0},e.getCanHide=()=>{var n,o;return((n=e.columnDef.enableHiding)!=null?n:!0)&&((o=t.options.enableHiding)!=null?o:!0)},e.getToggleVisibilityHandler=()=>n=>{e.toggleVisibility==null||e.toggleVisibility(n.target.checked)}},createRow:(e,t)=>{e._getAllVisibleCells=N(()=>[e.getAllCells(),t.getState().columnVisibility],n=>n.filter(o=>o.column.getIsVisible()),G(t.options,"debugRows","_getAllVisibleCells")),e.getVisibleCells=N(()=>[e.getLeftVisibleCells(),e.getCenterVisibleCells(),e.getRightVisibleCells()],(n,o,r)=>[...n,...o,...r],G(t.options,"debugRows","getVisibleCells"))},createTable:e=>{const t=(n,o)=>N(()=>[o(),o().filter(r=>r.getIsVisible()).map(r=>r.id).join("_")],r=>r.filter(i=>i.getIsVisible==null?void 0:i.getIsVisible()),G(e.options,"debugColumns",n));e.getVisibleFlatColumns=t("getVisibleFlatColumns",()=>e.getAllFlatColumns()),e.getVisibleLeafColumns=t("getVisibleLeafColumns",()=>e.getAllLeafColumns()),e.getLeftVisibleLeafColumns=t("getLeftVisibleLeafColumns",()=>e.getLeftLeafColumns()),e.getRightVisibleLeafColumns=t("getRightVisibleLeafColumns",()=>e.getRightLeafColumns()),e.getCenterVisibleLeafColumns=t("getCenterVisibleLeafColumns",()=>e.getCenterLeafColumns()),e.setColumnVisibility=n=>e.options.onColumnVisibilityChange==null?void 0:e.options.onColumnVisibilityChange(n),e.resetColumnVisibility=n=>{var o;e.setColumnVisibility(n?{}:(o=e.initialState.columnVisibility)!=null?o:{})},e.toggleAllColumnsVisible=n=>{var o;n=(o=n)!=null?o:!e.getIsAllColumnsVisible(),e.setColumnVisibility(e.getAllLeafColumns().reduce((r,i)=>({...r,[i.id]:n||!(i.getCanHide!=null&&i.getCanHide())}),{}))},e.getIsAllColumnsVisible=()=>!e.getAllLeafColumns().some(n=>!(n.getIsVisible!=null&&n.getIsVisible())),e.getIsSomeColumnsVisible=()=>e.getAllLeafColumns().some(n=>n.getIsVisible==null?void 0:n.getIsVisible()),e.getToggleAllColumnsVisibilityHandler=()=>n=>{var o;e.toggleAllColumnsVisible((o=n.target)==null?void 0:o.checked)}}};function xn(e,t){return t?t==="center"?e.getCenterVisibleLeafColumns():t==="left"?e.getLeftVisibleLeafColumns():e.getRightVisibleLeafColumns():e.getVisibleLeafColumns()}const ha={createTable:e=>{e._getGlobalFacetedRowModel=e.options.getFacetedRowModel&&e.options.getFacetedRowModel(e,"__global__"),e.getGlobalFacetedRowModel=()=>e.options.manualFiltering||!e._getGlobalFacetedRowModel?e.getPreFilteredRowModel():e._getGlobalFacetedRowModel(),e._getGlobalFacetedUniqueValues=e.options.getFacetedUniqueValues&&e.options.getFacetedUniqueValues(e,"__global__"),e.getGlobalFacetedUniqueValues=()=>e._getGlobalFacetedUniqueValues?e._getGlobalFacetedUniqueValues():new Map,e._getGlobalFacetedMinMaxValues=e.options.getFacetedMinMaxValues&&e.options.getFacetedMinMaxValues(e,"__global__"),e.getGlobalFacetedMinMaxValues=()=>{if(e._getGlobalFacetedMinMaxValues)return e._getGlobalFacetedMinMaxValues()}}},ma={getInitialState:e=>({globalFilter:void 0,...e}),getDefaultOptions:e=>({onGlobalFilterChange:Ve("globalFilter",e),globalFilterFn:"auto",getColumnCanGlobalFilter:t=>{var n;const o=(n=e.getCoreRowModel().flatRows[0])==null||(n=n._getAllCellsByColumnId()[t.id])==null?void 0:n.getValue();return typeof o=="string"||typeof o=="number"}}),createColumn:(e,t)=>{e.getCanGlobalFilter=()=>{var n,o,r,i;return((n=e.columnDef.enableGlobalFilter)!=null?n:!0)&&((o=t.options.enableGlobalFilter)!=null?o:!0)&&((r=t.options.enableFilters)!=null?r:!0)&&((i=t.options.getColumnCanGlobalFilter==null?void 0:t.options.getColumnCanGlobalFilter(e))!=null?i:!0)&&!!e.accessorFn}},createTable:e=>{e.getGlobalAutoFilterFn=()=>at.includesString,e.getGlobalFilterFn=()=>{var t,n;const{globalFilterFn:o}=e.options;return Nn(o)?o:o==="auto"?e.getGlobalAutoFilterFn():(t=(n=e.options.filterFns)==null?void 0:n[o])!=null?t:at[o]},e.setGlobalFilter=t=>{e.options.onGlobalFilterChange==null||e.options.onGlobalFilterChange(t)},e.resetGlobalFilter=t=>{e.setGlobalFilter(t?void 0:e.initialState.globalFilter)}}},va={getInitialState:e=>({expanded:{},...e}),getDefaultOptions:e=>({onExpandedChange:Ve("expanded",e),paginateExpandedRows:!0}),createTable:e=>{let t=!1,n=!1;e._autoResetExpanded=()=>{var o,r;if(!t){e._queue(()=>{t=!0});return}if((o=(r=e.options.autoResetAll)!=null?r:e.options.autoResetExpanded)!=null?o:!e.options.manualExpanding){if(n)return;n=!0,e._queue(()=>{e.resetExpanded(),n=!1})}},e.setExpanded=o=>e.options.onExpandedChange==null?void 0:e.options.onExpandedChange(o),e.toggleAllRowsExpanded=o=>{o??!e.getIsAllRowsExpanded()?e.setExpanded(!0):e.setExpanded({})},e.resetExpanded=o=>{var r,i;e.setExpanded(o?{}:(r=(i=e.initialState)==null?void 0:i.expanded)!=null?r:{})},e.getCanSomeRowsExpand=()=>e.getPrePaginationRowModel().flatRows.some(o=>o.getCanExpand()),e.getToggleAllRowsExpandedHandler=()=>o=>{o.persist==null||o.persist(),e.toggleAllRowsExpanded()},e.getIsSomeRowsExpanded=()=>{const o=e.getState().expanded;return o===!0||Object.values(o).some(Boolean)},e.getIsAllRowsExpanded=()=>{const o=e.getState().expanded;return typeof o=="boolean"?o===!0:!(!Object.keys(o).length||e.getRowModel().flatRows.some(r=>!r.getIsExpanded()))},e.getExpandedDepth=()=>{let o=0;return(e.getState().expanded===!0?Object.keys(e.getRowModel().rowsById):Object.keys(e.getState().expanded)).forEach(i=>{const l=i.split(".");o=Math.max(o,l.length)}),o},e.getPreExpandedRowModel=()=>e.getSortedRowModel(),e.getExpandedRowModel=()=>(!e._getExpandedRowModel&&e.options.getExpandedRowModel&&(e._getExpandedRowModel=e.options.getExpandedRowModel(e)),e.options.manualExpanding||!e._getExpandedRowModel?e.getPreExpandedRowModel():e._getExpandedRowModel())},createRow:(e,t)=>{e.toggleExpanded=n=>{t.setExpanded(o=>{var r;const i=o===!0?!0:!!(o!=null&&o[e.id]);let l={};if(o===!0?Object.keys(t.getRowModel().rowsById).forEach(s=>{l[s]=!0}):l=o,n=(r=n)!=null?r:!i,!i&&n)return{...l,[e.id]:!0};if(i&&!n){const{[e.id]:s,...a}=l;return a}return o})},e.getIsExpanded=()=>{var n;const o=t.getState().expanded;return!!((n=t.options.getIsRowExpanded==null?void 0:t.options.getIsRowExpanded(e))!=null?n:o===!0||o?.[e.id])},e.getCanExpand=()=>{var n,o,r;return(n=t.options.getRowCanExpand==null?void 0:t.options.getRowCanExpand(e))!=null?n:((o=t.options.enableExpanding)!=null?o:!0)&&!!((r=e.subRows)!=null&&r.length)},e.getIsAllParentsExpanded=()=>{let n=!0,o=e;for(;n&&o.parentId;)o=t.getRow(o.parentId,!0),n=o.getIsExpanded();return n},e.getToggleExpandedHandler=()=>{const n=e.getCanExpand();return()=>{n&&e.toggleExpanded()}}}},ho=0,mo=10,oo=()=>({pageIndex:ho,pageSize:mo}),xa={getInitialState:e=>({...e,pagination:{...oo(),...e?.pagination}}),getDefaultOptions:e=>({onPaginationChange:Ve("pagination",e)}),createTable:e=>{let t=!1,n=!1;e._autoResetPageIndex=()=>{var o,r;if(!t){e._queue(()=>{t=!0});return}if((o=(r=e.options.autoResetAll)!=null?r:e.options.autoResetPageIndex)!=null?o:!e.options.manualPagination){if(n)return;n=!0,e._queue(()=>{e.resetPageIndex(),n=!1})}},e.setPagination=o=>{const r=i=>Pt(o,i);return e.options.onPaginationChange==null?void 0:e.options.onPaginationChange(r)},e.resetPagination=o=>{var r;e.setPagination(o?oo():(r=e.initialState.pagination)!=null?r:oo())},e.setPageIndex=o=>{e.setPagination(r=>{let i=Pt(o,r.pageIndex);const l=typeof e.options.pageCount>"u"||e.options.pageCount===-1?Number.MAX_SAFE_INTEGER:e.options.pageCount-1;return i=Math.max(0,Math.min(i,l)),{...r,pageIndex:i}})},e.resetPageIndex=o=>{var r,i;e.setPageIndex(o?ho:(r=(i=e.initialState)==null||(i=i.pagination)==null?void 0:i.pageIndex)!=null?r:ho)},e.resetPageSize=o=>{var r,i;e.setPageSize(o?mo:(r=(i=e.initialState)==null||(i=i.pagination)==null?void 0:i.pageSize)!=null?r:mo)},e.setPageSize=o=>{e.setPagination(r=>{const i=Math.max(1,Pt(o,r.pageSize)),l=r.pageSize*r.pageIndex,s=Math.floor(l/i);return{...r,pageIndex:s,pageSize:i}})},e.setPageCount=o=>e.setPagination(r=>{var i;let l=Pt(o,(i=e.options.pageCount)!=null?i:-1);return typeof l=="number"&&(l=Math.max(-1,l)),{...r,pageCount:l}}),e.getPageOptions=N(()=>[e.getPageCount()],o=>{let r=[];return o&&o>0&&(r=[...new Array(o)].fill(null).map((i,l)=>l)),r},G(e.options,"debugTable","getPageOptions")),e.getCanPreviousPage=()=>e.getState().pagination.pageIndex>0,e.getCanNextPage=()=>{const{pageIndex:o}=e.getState().pagination,r=e.getPageCount();return r===-1?!0:r===0?!1:o<r-1},e.previousPage=()=>e.setPageIndex(o=>o-1),e.nextPage=()=>e.setPageIndex(o=>o+1),e.firstPage=()=>e.setPageIndex(0),e.lastPage=()=>e.setPageIndex(e.getPageCount()-1),e.getPrePaginationRowModel=()=>e.getExpandedRowModel(),e.getPaginationRowModel=()=>(!e._getPaginationRowModel&&e.options.getPaginationRowModel&&(e._getPaginationRowModel=e.options.getPaginationRowModel(e)),e.options.manualPagination||!e._getPaginationRowModel?e.getPrePaginationRowModel():e._getPaginationRowModel()),e.getPageCount=()=>{var o;return(o=e.options.pageCount)!=null?o:Math.ceil(e.getRowCount()/e.getState().pagination.pageSize)},e.getRowCount=()=>{var o;return(o=e.options.rowCount)!=null?o:e.getPrePaginationRowModel().rows.length}}},ro=()=>({top:[],bottom:[]}),ba={getInitialState:e=>({rowPinning:ro(),...e}),getDefaultOptions:e=>({onRowPinningChange:Ve("rowPinning",e)}),createRow:(e,t)=>{e.pin=(n,o,r)=>{const i=o?e.getLeafRows().map(a=>{let{id:d}=a;return d}):[],l=r?e.getParentRows().map(a=>{let{id:d}=a;return d}):[],s=new Set([...l,e.id,...i]);t.setRowPinning(a=>{var d,m;if(n==="bottom"){var f,p;return{top:((f=a?.top)!=null?f:[]).filter(y=>!(s!=null&&s.has(y))),bottom:[...((p=a?.bottom)!=null?p:[]).filter(y=>!(s!=null&&s.has(y))),...Array.from(s)]}}if(n==="top"){var u,h;return{top:[...((u=a?.top)!=null?u:[]).filter(y=>!(s!=null&&s.has(y))),...Array.from(s)],bottom:((h=a?.bottom)!=null?h:[]).filter(y=>!(s!=null&&s.has(y)))}}return{top:((d=a?.top)!=null?d:[]).filter(y=>!(s!=null&&s.has(y))),bottom:((m=a?.bottom)!=null?m:[]).filter(y=>!(s!=null&&s.has(y)))}})},e.getCanPin=()=>{var n;const{enableRowPinning:o,enablePinning:r}=t.options;return typeof o=="function"?o(e):(n=o??r)!=null?n:!0},e.getIsPinned=()=>{const n=[e.id],{top:o,bottom:r}=t.getState().rowPinning,i=n.some(s=>o?.includes(s)),l=n.some(s=>r?.includes(s));return i?"top":l?"bottom":!1},e.getPinnedIndex=()=>{var n,o;const r=e.getIsPinned();if(!r)return-1;const i=(n=r==="top"?t.getTopRows():t.getBottomRows())==null?void 0:n.map(l=>{let{id:s}=l;return s});return(o=i?.indexOf(e.id))!=null?o:-1}},createTable:e=>{e.setRowPinning=t=>e.options.onRowPinningChange==null?void 0:e.options.onRowPinningChange(t),e.resetRowPinning=t=>{var n,o;return e.setRowPinning(t?ro():(n=(o=e.initialState)==null?void 0:o.rowPinning)!=null?n:ro())},e.getIsSomeRowsPinned=t=>{var n;const o=e.getState().rowPinning;if(!t){var r,i;return!!((r=o.top)!=null&&r.length||(i=o.bottom)!=null&&i.length)}return!!((n=o[t])!=null&&n.length)},e._getPinnedRows=(t,n,o)=>{var r;return((r=e.options.keepPinnedRows)==null||r?(n??[]).map(l=>{const s=e.getRow(l,!0);return s.getIsAllParentsExpanded()?s:null}):(n??[]).map(l=>t.find(s=>s.id===l))).filter(Boolean).map(l=>({...l,position:o}))},e.getTopRows=N(()=>[e.getRowModel().rows,e.getState().rowPinning.top],(t,n)=>e._getPinnedRows(t,n,"top"),G(e.options,"debugRows","getTopRows")),e.getBottomRows=N(()=>[e.getRowModel().rows,e.getState().rowPinning.bottom],(t,n)=>e._getPinnedRows(t,n,"bottom"),G(e.options,"debugRows","getBottomRows")),e.getCenterRows=N(()=>[e.getRowModel().rows,e.getState().rowPinning.top,e.getState().rowPinning.bottom],(t,n,o)=>{const r=new Set([...n??[],...o??[]]);return t.filter(i=>!r.has(i.id))},G(e.options,"debugRows","getCenterRows"))}},ya={getInitialState:e=>({rowSelection:{},...e}),getDefaultOptions:e=>({onRowSelectionChange:Ve("rowSelection",e),enableRowSelection:!0,enableMultiRowSelection:!0,enableSubRowSelection:!0}),createTable:e=>{e.setRowSelection=t=>e.options.onRowSelectionChange==null?void 0:e.options.onRowSelectionChange(t),e.resetRowSelection=t=>{var n;return e.setRowSelection(t?{}:(n=e.initialState.rowSelection)!=null?n:{})},e.toggleAllRowsSelected=t=>{e.setRowSelection(n=>{t=typeof t<"u"?t:!e.getIsAllRowsSelected();const o={...n},r=e.getPreGroupedRowModel().flatRows;return t?r.forEach(i=>{i.getCanSelect()&&(o[i.id]=!0)}):r.forEach(i=>{delete o[i.id]}),o})},e.toggleAllPageRowsSelected=t=>e.setRowSelection(n=>{const o=typeof t<"u"?t:!e.getIsAllPageRowsSelected(),r={...n};return e.getRowModel().rows.forEach(i=>{vo(r,i.id,o,!0,e)}),r}),e.getPreSelectedRowModel=()=>e.getCoreRowModel(),e.getSelectedRowModel=N(()=>[e.getState().rowSelection,e.getCoreRowModel()],(t,n)=>Object.keys(t).length?io(e,n):{rows:[],flatRows:[],rowsById:{}},G(e.options,"debugTable","getSelectedRowModel")),e.getFilteredSelectedRowModel=N(()=>[e.getState().rowSelection,e.getFilteredRowModel()],(t,n)=>Object.keys(t).length?io(e,n):{rows:[],flatRows:[],rowsById:{}},G(e.options,"debugTable","getFilteredSelectedRowModel")),e.getGroupedSelectedRowModel=N(()=>[e.getState().rowSelection,e.getSortedRowModel()],(t,n)=>Object.keys(t).length?io(e,n):{rows:[],flatRows:[],rowsById:{}},G(e.options,"debugTable","getGroupedSelectedRowModel")),e.getIsAllRowsSelected=()=>{const t=e.getFilteredRowModel().flatRows,{rowSelection:n}=e.getState();let o=!!(t.length&&Object.keys(n).length);return o&&t.some(r=>r.getCanSelect()&&!n[r.id])&&(o=!1),o},e.getIsAllPageRowsSelected=()=>{const t=e.getPaginationRowModel().flatRows.filter(r=>r.getCanSelect()),{rowSelection:n}=e.getState();let o=!!t.length;return o&&t.some(r=>!n[r.id])&&(o=!1),o},e.getIsSomeRowsSelected=()=>{var t;const n=Object.keys((t=e.getState().rowSelection)!=null?t:{}).length;return n>0&&n<e.getFilteredRowModel().flatRows.length},e.getIsSomePageRowsSelected=()=>{const t=e.getPaginationRowModel().flatRows;return e.getIsAllPageRowsSelected()?!1:t.filter(n=>n.getCanSelect()).some(n=>n.getIsSelected()||n.getIsSomeSelected())},e.getToggleAllRowsSelectedHandler=()=>t=>{e.toggleAllRowsSelected(t.target.checked)},e.getToggleAllPageRowsSelectedHandler=()=>t=>{e.toggleAllPageRowsSelected(t.target.checked)}},createRow:(e,t)=>{e.toggleSelected=(n,o)=>{const r=e.getIsSelected();t.setRowSelection(i=>{var l;if(n=typeof n<"u"?n:!r,e.getCanSelect()&&r===n)return i;const s={...i};return vo(s,e.id,n,(l=o?.selectChildren)!=null?l:!0,t),s})},e.getIsSelected=()=>{const{rowSelection:n}=t.getState();return To(e,n)},e.getIsSomeSelected=()=>{const{rowSelection:n}=t.getState();return xo(e,n)==="some"},e.getIsAllSubRowsSelected=()=>{const{rowSelection:n}=t.getState();return xo(e,n)==="all"},e.getCanSelect=()=>{var n;return typeof t.options.enableRowSelection=="function"?t.options.enableRowSelection(e):(n=t.options.enableRowSelection)!=null?n:!0},e.getCanSelectSubRows=()=>{var n;return typeof t.options.enableSubRowSelection=="function"?t.options.enableSubRowSelection(e):(n=t.options.enableSubRowSelection)!=null?n:!0},e.getCanMultiSelect=()=>{var n;return typeof t.options.enableMultiRowSelection=="function"?t.options.enableMultiRowSelection(e):(n=t.options.enableMultiRowSelection)!=null?n:!0},e.getToggleSelectedHandler=()=>{const n=e.getCanSelect();return o=>{var r;n&&e.toggleSelected((r=o.target)==null?void 0:r.checked)}}}},vo=(e,t,n,o,r)=>{var i;const l=r.getRow(t,!0);n?(l.getCanMultiSelect()||Object.keys(e).forEach(s=>delete e[s]),l.getCanSelect()&&(e[t]=!0)):delete e[t],o&&(i=l.subRows)!=null&&i.length&&l.getCanSelectSubRows()&&l.subRows.forEach(s=>vo(e,s.id,n,o,r))};function io(e,t){const n=e.getState().rowSelection,o=[],r={},i=function(l,s){return l.map(a=>{var d;const m=To(a,n);if(m&&(o.push(a),r[a.id]=a),(d=a.subRows)!=null&&d.length&&(a={...a,subRows:i(a.subRows)}),m)return a}).filter(Boolean)};return{rows:i(t.rows),flatRows:o,rowsById:r}}function To(e,t){var n;return(n=t[e.id])!=null?n:!1}function xo(e,t,n){var o;if(!((o=e.subRows)!=null&&o.length))return!1;let r=!0,i=!1;return e.subRows.forEach(l=>{if(!(i&&!r)&&(l.getCanSelect()&&(To(l,t)?i=!0:r=!1),l.subRows&&l.subRows.length)){const s=xo(l,t);s==="all"?i=!0:(s==="some"&&(i=!0),r=!1)}}),r?"all":i?"some":!1}const bo=/([0-9]+)/gm,Sa=(e,t,n)=>qi(kt(e.getValue(n)).toLowerCase(),kt(t.getValue(n)).toLowerCase()),wa=(e,t,n)=>qi(kt(e.getValue(n)),kt(t.getValue(n))),Ca=(e,t,n)=>Lo(kt(e.getValue(n)).toLowerCase(),kt(t.getValue(n)).toLowerCase()),_a=(e,t,n)=>Lo(kt(e.getValue(n)),kt(t.getValue(n))),Ra=(e,t,n)=>{const o=e.getValue(n),r=t.getValue(n);return o>r?1:o<r?-1:0},Ia=(e,t,n)=>Lo(e.getValue(n),t.getValue(n));function Lo(e,t){return e===t?0:e>t?1:-1}function kt(e){return typeof e=="number"?isNaN(e)||e===1/0||e===-1/0?"":String(e):typeof e=="string"?e:""}function qi(e,t){const n=e.split(bo).filter(Boolean),o=t.split(bo).filter(Boolean);for(;n.length&&o.length;){const r=n.shift(),i=o.shift(),l=parseInt(r,10),s=parseInt(i,10),a=[l,s].sort();if(isNaN(a[0])){if(r>i)return 1;if(i>r)return-1;continue}if(isNaN(a[1]))return isNaN(l)?-1:1;if(l>s)return 1;if(s>l)return-1}return n.length-o.length}const un={alphanumeric:Sa,alphanumericCaseSensitive:wa,text:Ca,textCaseSensitive:_a,datetime:Ra,basic:Ia},Pa={getInitialState:e=>({sorting:[],...e}),getDefaultColumnDef:()=>({sortingFn:"auto",sortUndefined:1}),getDefaultOptions:e=>({onSortingChange:Ve("sorting",e),isMultiSortEvent:t=>t.shiftKey}),createColumn:(e,t)=>{e.getAutoSortingFn=()=>{const n=t.getFilteredRowModel().flatRows.slice(10);let o=!1;for(const r of n){const i=r?.getValue(e.id);if(Object.prototype.toString.call(i)==="[object Date]")return un.datetime;if(typeof i=="string"&&(o=!0,i.split(bo).length>1))return un.alphanumeric}return o?un.text:un.basic},e.getAutoSortDir=()=>{const n=t.getFilteredRowModel().flatRows[0];return typeof n?.getValue(e.id)=="string"?"asc":"desc"},e.getSortingFn=()=>{var n,o;if(!e)throw new Error;return Nn(e.columnDef.sortingFn)?e.columnDef.sortingFn:e.columnDef.sortingFn==="auto"?e.getAutoSortingFn():(n=(o=t.options.sortingFns)==null?void 0:o[e.columnDef.sortingFn])!=null?n:un[e.columnDef.sortingFn]},e.toggleSorting=(n,o)=>{const r=e.getNextSortingOrder(),i=typeof n<"u"&&n!==null;t.setSorting(l=>{const s=l?.find(u=>u.id===e.id),a=l?.findIndex(u=>u.id===e.id);let d=[],m,f=i?n:r==="desc";if(l!=null&&l.length&&e.getCanMultiSort()&&o?s?m="toggle":m="add":l!=null&&l.length&&a!==l.length-1?m="replace":s?m="toggle":m="replace",m==="toggle"&&(i||r||(m="remove")),m==="add"){var p;d=[...l,{id:e.id,desc:f}],d.splice(0,d.length-((p=t.options.maxMultiSortColCount)!=null?p:Number.MAX_SAFE_INTEGER))}else m==="toggle"?d=l.map(u=>u.id===e.id?{...u,desc:f}:u):m==="remove"?d=l.filter(u=>u.id!==e.id):d=[{id:e.id,desc:f}];return d})},e.getFirstSortDir=()=>{var n,o;return((n=(o=e.columnDef.sortDescFirst)!=null?o:t.options.sortDescFirst)!=null?n:e.getAutoSortDir()==="desc")?"desc":"asc"},e.getNextSortingOrder=n=>{var o,r;const i=e.getFirstSortDir(),l=e.getIsSorted();return l?l!==i&&((o=t.options.enableSortingRemoval)==null||o)&&(!(n&&(r=t.options.enableMultiRemove)!=null)||r)?!1:l==="desc"?"asc":"desc":i},e.getCanSort=()=>{var n,o;return((n=e.columnDef.enableSorting)!=null?n:!0)&&((o=t.options.enableSorting)!=null?o:!0)&&!!e.accessorFn},e.getCanMultiSort=()=>{var n,o;return(n=(o=e.columnDef.enableMultiSort)!=null?o:t.options.enableMultiSort)!=null?n:!!e.accessorFn},e.getIsSorted=()=>{var n;const o=(n=t.getState().sorting)==null?void 0:n.find(r=>r.id===e.id);return o?o.desc?"desc":"asc":!1},e.getSortIndex=()=>{var n,o;return(n=(o=t.getState().sorting)==null?void 0:o.findIndex(r=>r.id===e.id))!=null?n:-1},e.clearSorting=()=>{t.setSorting(n=>n!=null&&n.length?n.filter(o=>o.id!==e.id):[])},e.getToggleSortingHandler=()=>{const n=e.getCanSort();return o=>{n&&(o.persist==null||o.persist(),e.toggleSorting==null||e.toggleSorting(void 0,e.getCanMultiSort()?t.options.isMultiSortEvent==null?void 0:t.options.isMultiSortEvent(o):!1))}}},createTable:e=>{e.setSorting=t=>e.options.onSortingChange==null?void 0:e.options.onSortingChange(t),e.resetSorting=t=>{var n,o;e.setSorting(t?[]:(n=(o=e.initialState)==null?void 0:o.sorting)!=null?n:[])},e.getPreSortedRowModel=()=>e.getGroupedRowModel(),e.getSortedRowModel=()=>(!e._getSortedRowModel&&e.options.getSortedRowModel&&(e._getSortedRowModel=e.options.getSortedRowModel(e)),e.options.manualSorting||!e._getSortedRowModel?e.getPreSortedRowModel():e._getSortedRowModel())}},ka=[Xs,pa,ca,da,Ys,Zs,ha,ma,Pa,sa,va,xa,ba,ya,ga];function $a(e){var t,n;(e.debugAll||e.debugTable)&&console.info("Creating Table Instance...");const o=[...ka,...(t=e._features)!=null?t:[]];let r={_features:o};const i=r._features.reduce((p,u)=>Object.assign(p,u.getDefaultOptions==null?void 0:u.getDefaultOptions(r)),{}),l=p=>r.options.mergeOptions?r.options.mergeOptions(i,p):{...i,...p};let a={...{},...(n=e.initialState)!=null?n:{}};r._features.forEach(p=>{var u;a=(u=p.getInitialState==null?void 0:p.getInitialState(a))!=null?u:a});const d=[];let m=!1;const f={_features:o,options:{...i,...e},initialState:a,_queue:p=>{d.push(p),m||(m=!0,Promise.resolve().then(()=>{for(;d.length;)d.shift()();m=!1}).catch(u=>setTimeout(()=>{throw u})))},reset:()=>{r.setState(r.initialState)},setOptions:p=>{const u=Pt(p,r.options);r.options=l(u)},getState:()=>r.options.state,setState:p=>{r.options.onStateChange==null||r.options.onStateChange(p)},_getRowId:(p,u,h)=>{var y;return(y=r.options.getRowId==null?void 0:r.options.getRowId(p,u,h))!=null?y:`${h?[h.id,u].join("."):u}`},getCoreRowModel:()=>(r._getCoreRowModel||(r._getCoreRowModel=r.options.getCoreRowModel(r)),r._getCoreRowModel()),getRowModel:()=>r.getPaginationRowModel(),getRow:(p,u)=>{let h=(u?r.getPrePaginationRowModel():r.getRowModel()).rowsById[p];if(!h&&(h=r.getCoreRowModel().rowsById[p],!h))throw new Error(`getRow could not find row with ID: ${p}`);return h},_getDefaultColumnDef:N(()=>[r.options.defaultColumn],p=>{var u;return p=(u=p)!=null?u:{},{header:h=>{const y=h.header.column.columnDef;return y.accessorKey?y.accessorKey:y.accessorFn?y.id:null},cell:h=>{var y,S;return(y=(S=h.renderValue())==null||S.toString==null?void 0:S.toString())!=null?y:null},...r._features.reduce((h,y)=>Object.assign(h,y.getDefaultColumnDef==null?void 0:y.getDefaultColumnDef()),{}),...p}},G(e,"debugColumns","_getDefaultColumnDef")),_getColumnDefs:()=>r.options.columns,getAllColumns:N(()=>[r._getColumnDefs()],p=>{const u=function(h,y,S){return S===void 0&&(S=0),h.map(x=>{const b=qs(r,x,S,y),v=x;return b.columns=v.columns?u(v.columns,b,S+1):[],b})};return u(p)},G(e,"debugColumns","getAllColumns")),getAllFlatColumns:N(()=>[r.getAllColumns()],p=>p.flatMap(u=>u.getFlatColumns()),G(e,"debugColumns","getAllFlatColumns")),_getAllFlatColumnsById:N(()=>[r.getAllFlatColumns()],p=>p.reduce((u,h)=>(u[h.id]=h,u),{}),G(e,"debugColumns","getAllFlatColumnsById")),getAllLeafColumns:N(()=>[r.getAllColumns(),r._getOrderColumnsFn()],(p,u)=>{let h=p.flatMap(y=>y.getLeafColumns());return u(h)},G(e,"debugColumns","getAllLeafColumns")),getColumn:p=>{const u=r._getAllFlatColumnsById()[p];return u||console.error(`[Table] Column with id '${p}' does not exist.`),u}};Object.assign(r,f);for(let p=0;p<r._features.length;p++){const u=r._features[p];u==null||u.createTable==null||u.createTable(r)}return r}function Ma(){return e=>N(()=>[e.options.data],t=>{const n={rows:[],flatRows:[],rowsById:{}},o=function(r,i,l){i===void 0&&(i=0);const s=[];for(let d=0;d<r.length;d++){const m=Ao(e,e._getRowId(r[d],d,l),r[d],d,i,void 0,l?.id);if(n.flatRows.push(m),n.rowsById[m.id]=m,s.push(m),e.options.getSubRows){var a;m.originalSubRows=e.options.getSubRows(r[d],d),(a=m.originalSubRows)!=null&&a.length&&(m.subRows=o(m.originalSubRows,i+1,m))}}return s};return n.rows=o(t),n},G(e.options,"debugTable","getRowModel",()=>e._autoResetPageIndex()))}function Fa(e){const t=[],n=o=>{var r;t.push(o),(r=o.subRows)!=null&&r.length&&o.getIsExpanded()&&o.subRows.forEach(n)};return e.rows.forEach(n),{rows:t,flatRows:e.flatRows,rowsById:e.rowsById}}function Ea(e,t,n){return n.options.filterFromLeafRows?Aa(e,t,n):Da(e,t,n)}function Aa(e,t,n){var o;const r=[],i={},l=(o=n.options.maxLeafRowFilterDepth)!=null?o:100,s=function(a,d){d===void 0&&(d=0);const m=[];for(let p=0;p<a.length;p++){var f;let u=a[p];const h=Ao(n,u.id,u.original,u.index,u.depth,void 0,u.parentId);if(h.columnFilters=u.columnFilters,(f=u.subRows)!=null&&f.length&&d<l){if(h.subRows=s(u.subRows,d+1),u=h,t(u)&&!h.subRows.length){m.push(u),i[u.id]=u,r.push(u);continue}if(t(u)||h.subRows.length){m.push(u),i[u.id]=u,r.push(u);continue}}else u=h,t(u)&&(m.push(u),i[u.id]=u,r.push(u))}return m};return{rows:s(e),flatRows:r,rowsById:i}}function Da(e,t,n){var o;const r=[],i={},l=(o=n.options.maxLeafRowFilterDepth)!=null?o:100,s=function(a,d){d===void 0&&(d=0);const m=[];for(let p=0;p<a.length;p++){let u=a[p];if(t(u)){var f;if((f=u.subRows)!=null&&f.length&&d<l){const y=Ao(n,u.id,u.original,u.index,u.depth,void 0,u.parentId);y.subRows=s(u.subRows,d+1),u=y}m.push(u),r.push(u),i[u.id]=u}}return m};return{rows:s(e),flatRows:r,rowsById:i}}function Ta(){return e=>N(()=>[e.getPreFilteredRowModel(),e.getState().columnFilters,e.getState().globalFilter],(t,n,o)=>{if(!t.rows.length||!(n!=null&&n.length)&&!o){for(let p=0;p<t.flatRows.length;p++)t.flatRows[p].columnFilters={},t.flatRows[p].columnFiltersMeta={};return t}const r=[],i=[];(n??[]).forEach(p=>{var u;const h=e.getColumn(p.id);if(!h)return;const y=h.getFilterFn();if(!y){console.warn(`Could not find a valid 'column.filterFn' for column with the ID: ${h.id}.`);return}r.push({id:p.id,filterFn:y,resolvedValue:(u=y.resolveFilterValue==null?void 0:y.resolveFilterValue(p.value))!=null?u:p.value})});const l=(n??[]).map(p=>p.id),s=e.getGlobalFilterFn(),a=e.getAllLeafColumns().filter(p=>p.getCanGlobalFilter());o&&s&&a.length&&(l.push("__global__"),a.forEach(p=>{var u;i.push({id:p.id,filterFn:s,resolvedValue:(u=s.resolveFilterValue==null?void 0:s.resolveFilterValue(o))!=null?u:o})}));let d,m;for(let p=0;p<t.flatRows.length;p++){const u=t.flatRows[p];if(u.columnFilters={},r.length)for(let h=0;h<r.length;h++){d=r[h];const y=d.id;u.columnFilters[y]=d.filterFn(u,y,d.resolvedValue,S=>{u.columnFiltersMeta[y]=S})}if(i.length){for(let h=0;h<i.length;h++){m=i[h];const y=m.id;if(m.filterFn(u,y,m.resolvedValue,S=>{u.columnFiltersMeta[y]=S})){u.columnFilters.__global__=!0;break}}u.columnFilters.__global__!==!0&&(u.columnFilters.__global__=!1)}}const f=p=>{for(let u=0;u<l.length;u++)if(p.columnFilters[l[u]]===!1)return!1;return!0};return Ea(t.rows,f,e)},G(e.options,"debugTable","getFilteredRowModel",()=>e._autoResetPageIndex()))}function La(e){return t=>N(()=>[t.getState().pagination,t.getPrePaginationRowModel(),t.options.paginateExpandedRows?void 0:t.getState().expanded],(n,o)=>{if(!o.rows.length)return o;const{pageSize:r,pageIndex:i}=n;let{rows:l,flatRows:s,rowsById:a}=o;const d=r*i,m=d+r;l=l.slice(d,m);let f;t.options.paginateExpandedRows?f={rows:l,flatRows:s,rowsById:a}:f=Fa({rows:l,flatRows:s,rowsById:a}),f.flatRows=[];const p=u=>{f.flatRows.push(u),u.subRows.length&&u.subRows.forEach(p)};return f.rows.forEach(p),f},G(t.options,"debugTable","getPaginationRowModel"))}function za(){return e=>N(()=>[e.getState().sorting,e.getPreSortedRowModel()],(t,n)=>{if(!n.rows.length||!(t!=null&&t.length))return n;const o=e.getState().sorting,r=[],i=o.filter(a=>{var d;return(d=e.getColumn(a.id))==null?void 0:d.getCanSort()}),l={};i.forEach(a=>{const d=e.getColumn(a.id);d&&(l[a.id]={sortUndefined:d.columnDef.sortUndefined,invertSorting:d.columnDef.invertSorting,sortingFn:d.getSortingFn()})});const s=a=>{const d=a.map(m=>({...m}));return d.sort((m,f)=>{for(let u=0;u<i.length;u+=1){var p;const h=i[u],y=l[h.id],S=y.sortUndefined,x=(p=h?.desc)!=null?p:!1;let b=0;if(S){const v=m.getValue(h.id),_=f.getValue(h.id),C=v===void 0,M=_===void 0;if(C||M){if(S==="first")return C?-1:1;if(S==="last")return C?1:-1;b=C&&M?0:C?S:-S}}if(b===0&&(b=y.sortingFn(m,f,h.id)),b!==0)return x&&(b*=-1),y.invertSorting&&(b*=-1),b}return m.index-f.index}),d.forEach(m=>{var f;r.push(m),(f=m.subRows)!=null&&f.length&&(m.subRows=s(m.subRows))}),d};return{rows:s(n.rows),flatRows:r,rowsById:n.rowsById}},G(e.options,"debugTable","getSortedRowModel",()=>e._autoResetPageIndex()))}/**
   * solid-table
   *
   * Copyright (c) TanStack
   *
   * This source code is licensed under the MIT license found in the
   * LICENSE.md file in the root directory of this source tree.
   *
   * @license MIT
   */function Wr(e,t){return e?typeof e=="function"?R(e,t):e:null}function Oa(e){const t=q({state:{},onStateChange:()=>{},renderFallbackValue:null,mergeOptions:(i,l)=>q(i,l)},e),n=$a(t),[o,r]=ts(n.initialState);return Ii(()=>{n.setOptions(i=>q(i,e,{state:q(o,e.state||{}),onStateChange:l=>{r(l),e.onStateChange==null||e.onStateChange(l)}}))}),n}var Va=V("<div><div><span>⟳</span><span>加载中..."),Ba=V("<div><div><span>共 <!> 条记录</span></div><div><span>第 <!> 页，共 <!> 页"),Ka=V("<div><div><table><thead></thead><tbody>"),Nr=V("<tr>"),Ha=V("<span>"),Wa=V("<th><div>"),Na=V("<td>");function Ga(e){const[t,n]=re(e,["data","columns","loading","pagination","pageSize","sortable","filterable","selectable","onRowSelect","onRowClick","class","height","sticky"]),o=Oa({get data(){return t.data||[]},get columns(){return t.columns},getCoreRowModel:Ma(),getSortedRowModel:t.sortable?za():void 0,getFilteredRowModel:t.filterable?Ta():void 0,getPaginationRowModel:t.pagination?La():void 0,initialState:{pagination:{pageSize:t.pageSize||10}}}),r=()=>({width:"100%",backgroundColor:"white",borderRadius:"4px",border:"1px solid",borderColor:"border.base",overflow:"hidden",boxShadow:"base"}),i=()=>({width:"100%",height:t.height||"auto",overflow:"auto",position:"relative"}),l=()=>({backgroundColor:"bg.page",borderBottom:"1px solid",borderColor:"border.base",position:t.sticky?"sticky":"static",top:0,zIndex:10}),s=()=>({padding:"12px 16px",textAlign:"left",fontSize:"14px",fontWeight:"500",color:"text.primary",borderRight:"1px solid",borderColor:"border.lighter",cursor:"pointer",userSelect:"none",_hover:{backgroundColor:"border.extra"},_last:{borderRight:"none"}}),a=u=>({backgroundColor:u?"white":"bg.page",borderBottom:"1px solid",borderColor:"border.lighter",transition:"all 0.2s",cursor:t.onRowClick?"pointer":"default",_hover:{backgroundColor:"primary.50"},_last:{borderBottom:"none"}}),d=()=>({padding:"12px 16px",fontSize:"14px",color:"text.regular",borderRight:"1px solid",borderColor:"border.lighter",_last:{borderRight:"none"}}),m=()=>({position:"absolute",top:0,left:0,right:0,bottom:0,backgroundColor:"rgba(255, 255, 255, 0.8)",display:"flex",alignItems:"center",justifyContent:"center",zIndex:100}),f=()=>({display:"flex",alignItems:"center",justifyContent:"space-between",padding:"16px",borderTop:"1px solid",borderColor:"border.base",backgroundColor:"white"}),p=u=>u==="asc"?"↑":u==="desc"?"↓":"↕";return(()=>{var u=Ka(),h=u.firstChild,y=h.firstChild,S=y.firstChild,x=S.nextSibling;return At(u,q({get class(){return Zt(c(r()),t.class)}},n),!1,!0),w(S,R(Me,{get each(){return o.getHeaderGroups()},children:b=>(()=>{var v=Nr();return w(v,R(Me,{get each(){return b.headers},children:_=>(()=>{var C=Wa(),M=C.firstChild;return Pi(C,"click",_.column.getToggleSortingHandler(),!0),w(M,()=>Wr(_.column.columnDef.header,_.getContext()),null),w(M,R(ce,{get when(){return ee(()=>!!t.sortable)()&&_.column.getCanSort()},get children(){var A=Ha();return w(A,()=>p(_.column.getIsSorted())),O(()=>g(A,c({fontSize:"12px",color:"text.secondary"}))),A}}),null),O(A=>{var P=c(s()),F=c({display:"flex",alignItems:"center",gap:"8px"});return P!==A.e&&g(C,A.e=P),F!==A.t&&g(M,A.t=F),A},{e:void 0,t:void 0}),C})()})),v})()})),w(x,R(Me,{get each(){return o.getRowModel().rows},children:(b,v)=>(()=>{var _=Nr();return _.$$click=()=>t.onRowClick?.(b.original),w(_,R(Me,{get each(){return b.getVisibleCells()},children:C=>(()=>{var M=Na();return w(M,()=>Wr(C.column.columnDef.cell,C.getContext())),O(()=>g(M,c(d()))),M})()})),O(()=>g(_,c(a(v()%2===0)))),_})()})),w(h,R(ce,{get when(){return t.loading},get children(){var b=Va(),v=b.firstChild,_=v.firstChild,C=_.nextSibling;return O(M=>{var A=c(m()),P=c({display:"flex",alignItems:"center",gap:"8px"}),F=c({animation:"spin 1s linear infinite",fontSize:"20px"}),D=c({color:"text.secondary"});return A!==M.e&&g(b,M.e=A),P!==M.t&&g(v,M.t=P),F!==M.a&&g(_,M.a=F),D!==M.o&&g(C,M.o=D),M},{e:void 0,t:void 0,a:void 0,o:void 0}),b}}),null),w(u,R(ce,{get when(){return t.pagination},get children(){var b=Ba(),v=b.firstChild,_=v.firstChild,C=_.firstChild,M=C.nextSibling;M.nextSibling;var A=v.nextSibling,P=A.firstChild,F=P.firstChild,D=F.nextSibling,T=D.nextSibling,k=T.nextSibling;return k.nextSibling,w(_,()=>o.getFilteredRowModel().rows.length,M),w(A,R(oe,{size:"small",variant:"default",get disabled(){return!o.getCanPreviousPage()},onClick:()=>o.previousPage(),children:"上一页"}),P),w(P,()=>o.getState().pagination.pageIndex+1,D),w(P,()=>o.getPageCount(),k),w(A,R(oe,{size:"small",variant:"default",get disabled(){return!o.getCanNextPage()},onClick:()=>o.nextPage(),children:"下一页"}),null),O(L=>{var B=c(f()),K=c({display:"flex",alignItems:"center",gap:"8px"}),j=c({fontSize:"14px",color:"text.secondary"}),H=c({display:"flex",alignItems:"center",gap:"8px"}),U=c({fontSize:"14px",color:"text.regular"});return B!==L.e&&g(b,L.e=B),K!==L.t&&g(v,L.t=K),j!==L.a&&g(_,L.a=j),H!==L.o&&g(A,L.o=H),U!==L.i&&g(P,L.i=U),L},{e:void 0,t:void 0,a:void 0,o:void 0,i:void 0}),b}}),null),O(b=>{var v=c(i()),_=c({width:"100%",borderCollapse:"collapse"}),C=c(l());return v!==b.e&&g(h,b.e=v),_!==b.t&&g(y,b.t=_),C!==b.a&&g(S,b.a=C),b},{e:void 0,t:void 0,a:void 0}),u})()}Wt(["click"]);var ja=V("<span>✕"),Ua=V("<div><span></span><span>"),qa=V("<div>"),Xa=V("<div style=pointer-events:auto>");const[Ya,zo]=W([]);let Za=0;const gn={success:(e,t)=>fn({...t,message:e,type:"success"}),warning:(e,t)=>fn({...t,message:e,type:"warning"}),info:(e,t)=>fn({...t,message:e,type:"info"}),error:(e,t)=>fn({...t,message:e,type:"error"}),show:fn,clear:()=>zo([])};function fn(e){const t=`message-${++Za}`,n={id:t,type:"info",duration:3e3,showClose:!1,visible:!0,...e};return zo(o=>[...o,n]),n.duration&&n.duration>0&&setTimeout(()=>{Xi(t)},n.duration),t}function Xi(e){zo(t=>t.filter(n=>n.id!==e))}function Ja(e){const t=()=>({...{display:"flex",alignItems:"center",gap:"8px",padding:"12px 16px",borderRadius:"4px",border:"1px solid",backgroundColor:"white",boxShadow:"light",fontSize:"14px",fontWeight:"400",minWidth:"300px",maxWidth:"500px",marginBottom:"8px",transform:"translateX(0)",opacity:1,transition:"all 0.3s ease",animation:"slideInRight 0.3s ease"},...{success:{color:"success.600",backgroundColor:"success.50",borderColor:"success.200"},warning:{color:"warning.600",backgroundColor:"warning.50",borderColor:"warning.200"},info:{color:"info.600",backgroundColor:"info.50",borderColor:"info.200"},error:{color:"danger.600",backgroundColor:"danger.50",borderColor:"danger.200"}}[e.message.type||"info"]}),n=()=>({fontSize:"16px",flexShrink:0}),o=()=>({marginLeft:"auto",cursor:"pointer",fontSize:"12px",padding:"2px",borderRadius:"2px",_hover:{backgroundColor:"rgba(0, 0, 0, 0.1)"}}),r=()=>{switch(e.message.type){case"success":return"✓";case"warning":return"⚠";case"error":return"✕";default:return"ℹ"}},i=()=>{e.message.onClose?.(),Xi(e.message.id)};return(()=>{var l=Ua(),s=l.firstChild,a=s.nextSibling;return w(s,r),w(a,()=>e.message.message),w(l,R(ce,{get when(){return e.message.showClose},get children(){var d=ja();return d.$$click=i,O(()=>g(d,c(o()))),d}}),null),O(d=>{var m=c(t()),f=`${20+e.index*60}px`,p=c(n()),u=c({flex:1});return m!==d.e&&g(l,d.e=m),f!==d.t&&ns(l,"top",d.t=f),p!==d.a&&g(s,d.a=p),u!==d.o&&g(a,d.o=u),d},{e:void 0,t:void 0,a:void 0,o:void 0}),l})()}function Qa(){const e=()=>({position:"fixed",top:0,right:"20px",zIndex:9999,pointerEvents:"none"});return R(Mo,{get children(){var t=qa();return w(t,R(Me,{get each(){return Ya()},children:(n,o)=>(()=>{var r=Xa();return w(r,R(Ja,{message:n,get index(){return o()}})),r})()})),O(()=>g(t,c(e()))),t}})}const Yi=document.createElement("style");Yi.textContent=`
  @keyframes slideInRight {
    from {
      transform: translateX(100%);
      opacity: 0;
    }
    to {
      transform: translateX(0);
      opacity: 1;
    }
  }
  
  @keyframes spin {
    from {
      transform: rotate(0deg);
    }
    to {
      transform: rotate(360deg);
    }
  }
`;document.head.appendChild(Yi);Wt(["click"]);const ec=!!rs;function tc(e){return(...t)=>{for(const n of e)n&&n(...t)}}const z=e=>typeof e=="function"&&!e.length?e():e,Gr=e=>Array.isArray(e)?e:e?[e]:[];function nc(e,...t){return typeof e=="function"?e(...t):e}const oc=ec?e=>os()?Q(e):e:Q;function rc(e,t,n,o){return e.addEventListener(t,n,o),oc(e.removeEventListener.bind(e,t,n,o))}function ic(e,t,n,o){const r=()=>{Gr(z(e)).forEach(i=>{i&&Gr(z(t)).forEach(l=>rc(i,l,n,o))})};typeof e=="function"?Y(r):O(r)}const lc=/((?:--)?(?:\w+-?)+)\s*:\s*([^;]*)/g;function jr(e){const t={};let n;for(;n=lc.exec(e);)t[n[1]]=n[2];return t}function Oo(e,t){if(typeof e=="string"){if(typeof t=="string")return`${e};${t}`;e=jr(e)}else typeof t=="string"&&(t=jr(t));return{...e,...t}}function Ye(...e){return tc(e)}function sc(e,t,n=-1){return n in e?[...e.slice(0,n),t,...e.slice(n)]:[...e,t]}function yo(e,t){const n=[...e],o=n.indexOf(t);return o!==-1&&n.splice(o,1),n}function ac(e){return typeof e=="number"}function jt(e){return Object.prototype.toString.call(e)==="[object String]"}function cc(e){return typeof e=="function"}function Vo(e){return t=>`${e()}-${t}`}function Ke(e,t){return e?e===t||e.contains(t):!1}function mn(e,t=!1){const{activeElement:n}=nt(e);if(!n?.nodeName)return null;if(Zi(n)&&n.contentDocument)return mn(n.contentDocument.body,t);if(t){const o=n.getAttribute("aria-activedescendant");if(o){const r=nt(n).getElementById(o);if(r)return r}}return n}function dc(e){return nt(e).defaultView||window}function nt(e){return e?e.ownerDocument||e:document}function Zi(e){return e.tagName==="IFRAME"}var Ji=(e=>(e.Escape="Escape",e.Enter="Enter",e.Tab="Tab",e.Space=" ",e.ArrowDown="ArrowDown",e.ArrowLeft="ArrowLeft",e.ArrowRight="ArrowRight",e.ArrowUp="ArrowUp",e.End="End",e.Home="Home",e.PageDown="PageDown",e.PageUp="PageUp",e))(Ji||{});function Bo(e){return typeof window<"u"&&window.navigator!=null?e.test(window.navigator.userAgentData?.platform||window.navigator.platform):!1}function Gn(){return Bo(/^Mac/i)}function uc(){return Bo(/^iPhone/i)}function gc(){return Bo(/^iPad/i)||Gn()&&navigator.maxTouchPoints>1}function fc(){return uc()||gc()}function pc(){return Gn()||fc()}function Se(e,t){return t&&(cc(t)?t(e):t[0](t[1],e)),e?.defaultPrevented}function ye(e){return t=>{for(const n of e)Se(t,n)}}function hc(e){return Gn()?e.metaKey&&!e.ctrlKey:e.ctrlKey&&!e.metaKey}function Pe(e){if(e)if(mc())e.focus({preventScroll:!0});else{const t=vc(e);e.focus(),xc(t)}}var $n=null;function mc(){if($n==null){$n=!1;try{document.createElement("div").focus({get preventScroll(){return $n=!0,!0}})}catch{}}return $n}function vc(e){let t=e.parentNode;const n=[],o=document.scrollingElement||document.documentElement;for(;t instanceof HTMLElement&&t!==o;)(t.offsetHeight<t.scrollHeight||t.offsetWidth<t.scrollWidth)&&n.push({element:t,scrollTop:t.scrollTop,scrollLeft:t.scrollLeft}),t=t.parentNode;return o instanceof HTMLElement&&n.push({element:o,scrollTop:o.scrollTop,scrollLeft:o.scrollLeft}),n}function xc(e){for(const{element:t,scrollTop:n,scrollLeft:o}of e)t.scrollTop=n,t.scrollLeft=o}var Qi=["input:not([type='hidden']):not([disabled])","select:not([disabled])","textarea:not([disabled])","button:not([disabled])","a[href]","area[href]","[tabindex]","iframe","object","embed","audio[controls]","video[controls]","[contenteditable]:not([contenteditable='false'])"],bc=[...Qi,'[tabindex]:not([tabindex="-1"]):not([disabled])'],Ko=`${Qi.join(":not([hidden]),")},[tabindex]:not([disabled]):not([hidden])`,yc=bc.join(':not([hidden]):not([tabindex="-1"]),');function el(e,t){const o=Array.from(e.querySelectorAll(Ko)).filter(Ur);return t&&Ur(e)&&o.unshift(e),o.forEach((r,i)=>{if(Zi(r)&&r.contentDocument){const l=r.contentDocument.body,s=el(l,!1);o.splice(i,1,...s)}}),o}function Ur(e){return tl(e)&&!Sc(e)}function tl(e){return e.matches(Ko)&&Ho(e)}function Sc(e){return Number.parseInt(e.getAttribute("tabindex")||"0",10)<0}function Ho(e,t){return e.nodeName!=="#comment"&&wc(e)&&Cc(e,t)&&(!e.parentElement||Ho(e.parentElement,e))}function wc(e){if(!(e instanceof HTMLElement)&&!(e instanceof SVGElement))return!1;const{display:t,visibility:n}=e.style;let o=t!=="none"&&n!=="hidden"&&n!=="collapse";if(o){if(!e.ownerDocument.defaultView)return o;const{getComputedStyle:r}=e.ownerDocument.defaultView,{display:i,visibility:l}=r(e);o=i!=="none"&&l!=="hidden"&&l!=="collapse"}return o}function Cc(e,t){return!e.hasAttribute("hidden")&&(e.nodeName==="DETAILS"&&t&&t.nodeName!=="SUMMARY"?e.hasAttribute("open"):!0)}function _c(e,t,n){const o=t?.tabbable?yc:Ko,r=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode(i){return t?.from?.contains(i)?NodeFilter.FILTER_REJECT:i.matches(o)&&Ho(i)&&(!t?.accept||t.accept(i))?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});return t?.from&&(r.currentNode=t.from),r}function qr(e){let t=e;for(;t&&!Rc(t);)t=t.parentElement;return t||document.scrollingElement||document.documentElement}function Rc(e){const t=window.getComputedStyle(e);return/(auto|scroll)/.test(t.overflow+t.overflowX+t.overflowY)}function Ic(){}function Pc(e,t){const[n,o]=e;let r=!1;const i=t.length;for(let l=i,s=0,a=l-1;s<l;a=s++){const[d,m]=t[s],[f,p]=t[a],[,u]=t[a===0?l-1:a-1]||[0,0],h=(m-p)*(n-d)-(d-f)*(o-m);if(p<m){if(o>=p&&o<m){if(h===0)return!0;h>0&&(o===p?o>u&&(r=!r):r=!r)}}else if(m<p){if(o>m&&o<=p){if(h===0)return!0;h<0&&(o===p?o<u&&(r=!r):r=!r)}}else if(o===m&&(n>=f&&n<=d||n>=d&&n<=f))return!0}return r}function fe(e,t){return q(e,t)}var pn=new Map,Xr=new Set;function Yr(){if(typeof window>"u")return;const e=n=>{if(!n.target)return;let o=pn.get(n.target);o||(o=new Set,pn.set(n.target,o),n.target.addEventListener("transitioncancel",t)),o.add(n.propertyName)},t=n=>{if(!n.target)return;const o=pn.get(n.target);if(o&&(o.delete(n.propertyName),o.size===0&&(n.target.removeEventListener("transitioncancel",t),pn.delete(n.target)),pn.size===0)){for(const r of Xr)r();Xr.clear()}};document.body.addEventListener("transitionrun",e),document.body.addEventListener("transitionend",t)}typeof document<"u"&&(document.readyState!=="loading"?Yr():document.addEventListener("DOMContentLoaded",Yr));function So(e,t){const n=Zr(e,t,"left"),o=Zr(e,t,"top"),r=t.offsetWidth,i=t.offsetHeight;let l=e.scrollLeft,s=e.scrollTop;const a=l+e.offsetWidth,d=s+e.offsetHeight;n<=l?l=n:n+r>a&&(l+=n+r-a),o<=s?s=o:o+i>d&&(s+=o+i-d),e.scrollLeft=l,e.scrollTop=s}function Zr(e,t,n){const o=n==="left"?"offsetLeft":"offsetTop";let r=0;for(;t.offsetParent&&(r+=t[o],t.offsetParent!==e);){if(t.offsetParent.contains(e)){r-=e[o];break}t=t.offsetParent}return r}function kc(e,t){if(document.contains(e)){const n=document.scrollingElement||document.documentElement;if(window.getComputedStyle(n).overflow==="hidden"){let r=qr(e);for(;e&&r&&e!==n&&r!==n;)So(r,e),e=r,r=qr(e)}else{const{left:r,top:i}=e.getBoundingClientRect();e?.scrollIntoView?.({block:"nearest"});const{left:l,top:s}=e.getBoundingClientRect();(Math.abs(r-l)>1||Math.abs(i-s)>1)&&e.scrollIntoView?.({block:"nearest"})}}}var $c={border:"0",clip:"rect(0 0 0 0)","clip-path":"inset(50%)",height:"1px",margin:"0 -1px -1px 0",overflow:"hidden",padding:"0",position:"absolute",width:"1px","white-space":"nowrap"},Mc=new Set(["Avst","Arab","Armi","Syrc","Samr","Mand","Thaa","Mend","Nkoo","Adlm","Rohg","Hebr"]),Fc=new Set(["ae","ar","arc","bcc","bqi","ckb","dv","fa","glk","he","ku","mzn","nqo","pnb","ps","sd","ug","ur","yi"]);function Ec(e){if(Intl.Locale){const n=new Intl.Locale(e).maximize().script??"";return Mc.has(n)}const t=e.split("-")[0];return Fc.has(t)}function Ac(e){return Ec(e)?"rtl":"ltr"}function nl(){let e=typeof navigator<"u"&&(navigator.language||navigator.userLanguage)||"en-US";try{Intl.DateTimeFormat.supportedLocalesOf([e])}catch{e="en-US"}return{locale:e,direction:Ac(e)}}var wo=nl(),vn=new Set;function Jr(){wo=nl();for(const e of vn)e(wo)}function Dc(){const[e,t]=W(wo),n=be(()=>e());return gt(()=>{vn.size===0&&window.addEventListener("languagechange",Jr),vn.add(t),Q(()=>{vn.delete(t),vn.size===0&&window.removeEventListener("languagechange",Jr)})}),{locale:()=>n().locale,direction:()=>n().direction}}var Tc=Xe();function Tt(){const e=Dc();return qe(Tc)||e}var lo=new Map;function Lc(e){const{locale:t}=Tt(),n=be(()=>t()+(e?Object.entries(e).sort((o,r)=>o[0]<r[0]?-1:1).join():""));return be(()=>{const o=n();let r;return lo.has(o)&&(r=lo.get(o)),r||(r=new Intl.Collator(t(),e),lo.set(o,r)),r})}function De(e){const[t,n]=re(e,["as"]);if(!t.as)throw new Error("[kobalte]: Polymorphic is missing the required `as` prop.");return R(is,q(n,{get component(){return t.as}}))}const zc=["top","right","bottom","left"],$t=Math.min,ze=Math.max,Bn=Math.round,Mn=Math.floor,Mt=e=>({x:e,y:e}),Oc={left:"right",right:"left",bottom:"top",top:"bottom"},Vc={start:"end",end:"start"};function Co(e,t,n){return ze(e,$t(t,n))}function Nt(e,t){return typeof e=="function"?e(t):e}function Ft(e){return e.split("-")[0]}function Jt(e){return e.split("-")[1]}function ol(e){return e==="x"?"y":"x"}function Wo(e){return e==="y"?"height":"width"}const Bc=new Set(["top","bottom"]);function dt(e){return Bc.has(Ft(e))?"y":"x"}function No(e){return ol(dt(e))}function Kc(e,t,n){n===void 0&&(n=!1);const o=Jt(e),r=No(e),i=Wo(r);let l=r==="x"?o===(n?"end":"start")?"right":"left":o==="start"?"bottom":"top";return t.reference[i]>t.floating[i]&&(l=Kn(l)),[l,Kn(l)]}function Hc(e){const t=Kn(e);return[_o(e),t,_o(t)]}function _o(e){return e.replace(/start|end/g,t=>Vc[t])}const Qr=["left","right"],ei=["right","left"],Wc=["top","bottom"],Nc=["bottom","top"];function Gc(e,t,n){switch(e){case"top":case"bottom":return n?t?ei:Qr:t?Qr:ei;case"left":case"right":return t?Wc:Nc;default:return[]}}function jc(e,t,n,o){const r=Jt(e);let i=Gc(Ft(e),n==="start",o);return r&&(i=i.map(l=>l+"-"+r),t&&(i=i.concat(i.map(_o)))),i}function Kn(e){return e.replace(/left|right|bottom|top/g,t=>Oc[t])}function Uc(e){return{top:0,right:0,bottom:0,left:0,...e}}function rl(e){return typeof e!="number"?Uc(e):{top:e,right:e,bottom:e,left:e}}function Hn(e){const{x:t,y:n,width:o,height:r}=e;return{width:o,height:r,top:n,left:t,right:t+o,bottom:n+r,x:t,y:n}}function ti(e,t,n){let{reference:o,floating:r}=e;const i=dt(t),l=No(t),s=Wo(l),a=Ft(t),d=i==="y",m=o.x+o.width/2-r.width/2,f=o.y+o.height/2-r.height/2,p=o[s]/2-r[s]/2;let u;switch(a){case"top":u={x:m,y:o.y-r.height};break;case"bottom":u={x:m,y:o.y+o.height};break;case"right":u={x:o.x+o.width,y:f};break;case"left":u={x:o.x-r.width,y:f};break;default:u={x:o.x,y:o.y}}switch(Jt(t)){case"start":u[l]-=p*(n&&d?-1:1);break;case"end":u[l]+=p*(n&&d?-1:1);break}return u}const qc=async(e,t,n)=>{const{placement:o="bottom",strategy:r="absolute",middleware:i=[],platform:l}=n,s=i.filter(Boolean),a=await(l.isRTL==null?void 0:l.isRTL(t));let d=await l.getElementRects({reference:e,floating:t,strategy:r}),{x:m,y:f}=ti(d,o,a),p=o,u={},h=0;for(let y=0;y<s.length;y++){const{name:S,fn:x}=s[y],{x:b,y:v,data:_,reset:C}=await x({x:m,y:f,initialPlacement:o,placement:p,strategy:r,middlewareData:u,rects:d,platform:l,elements:{reference:e,floating:t}});m=b??m,f=v??f,u={...u,[S]:{...u[S],..._}},C&&h<=50&&(h++,typeof C=="object"&&(C.placement&&(p=C.placement),C.rects&&(d=C.rects===!0?await l.getElementRects({reference:e,floating:t,strategy:r}):C.rects),{x:m,y:f}=ti(d,p,a)),y=-1)}return{x:m,y:f,placement:p,strategy:r,middlewareData:u}};async function bn(e,t){var n;t===void 0&&(t={});const{x:o,y:r,platform:i,rects:l,elements:s,strategy:a}=e,{boundary:d="clippingAncestors",rootBoundary:m="viewport",elementContext:f="floating",altBoundary:p=!1,padding:u=0}=Nt(t,e),h=rl(u),S=s[p?f==="floating"?"reference":"floating":f],x=Hn(await i.getClippingRect({element:(n=await(i.isElement==null?void 0:i.isElement(S)))==null||n?S:S.contextElement||await(i.getDocumentElement==null?void 0:i.getDocumentElement(s.floating)),boundary:d,rootBoundary:m,strategy:a})),b=f==="floating"?{x:o,y:r,width:l.floating.width,height:l.floating.height}:l.reference,v=await(i.getOffsetParent==null?void 0:i.getOffsetParent(s.floating)),_=await(i.isElement==null?void 0:i.isElement(v))?await(i.getScale==null?void 0:i.getScale(v))||{x:1,y:1}:{x:1,y:1},C=Hn(i.convertOffsetParentRelativeRectToViewportRelativeRect?await i.convertOffsetParentRelativeRectToViewportRelativeRect({elements:s,rect:b,offsetParent:v,strategy:a}):b);return{top:(x.top-C.top+h.top)/_.y,bottom:(C.bottom-x.bottom+h.bottom)/_.y,left:(x.left-C.left+h.left)/_.x,right:(C.right-x.right+h.right)/_.x}}const Xc=e=>({name:"arrow",options:e,async fn(t){const{x:n,y:o,placement:r,rects:i,platform:l,elements:s,middlewareData:a}=t,{element:d,padding:m=0}=Nt(e,t)||{};if(d==null)return{};const f=rl(m),p={x:n,y:o},u=No(r),h=Wo(u),y=await l.getDimensions(d),S=u==="y",x=S?"top":"left",b=S?"bottom":"right",v=S?"clientHeight":"clientWidth",_=i.reference[h]+i.reference[u]-p[u]-i.floating[h],C=p[u]-i.reference[u],M=await(l.getOffsetParent==null?void 0:l.getOffsetParent(d));let A=M?M[v]:0;(!A||!await(l.isElement==null?void 0:l.isElement(M)))&&(A=s.floating[v]||i.floating[h]);const P=_/2-C/2,F=A/2-y[h]/2-1,D=$t(f[x],F),T=$t(f[b],F),k=D,L=A-y[h]-T,B=A/2-y[h]/2+P,K=Co(k,B,L),j=!a.arrow&&Jt(r)!=null&&B!==K&&i.reference[h]/2-(B<k?D:T)-y[h]/2<0,H=j?B<k?B-k:B-L:0;return{[u]:p[u]+H,data:{[u]:K,centerOffset:B-K-H,...j&&{alignmentOffset:H}},reset:j}}}),Yc=function(e){return e===void 0&&(e={}),{name:"flip",options:e,async fn(t){var n,o;const{placement:r,middlewareData:i,rects:l,initialPlacement:s,platform:a,elements:d}=t,{mainAxis:m=!0,crossAxis:f=!0,fallbackPlacements:p,fallbackStrategy:u="bestFit",fallbackAxisSideDirection:h="none",flipAlignment:y=!0,...S}=Nt(e,t);if((n=i.arrow)!=null&&n.alignmentOffset)return{};const x=Ft(r),b=dt(s),v=Ft(s)===s,_=await(a.isRTL==null?void 0:a.isRTL(d.floating)),C=p||(v||!y?[Kn(s)]:Hc(s)),M=h!=="none";!p&&M&&C.push(...jc(s,y,h,_));const A=[s,...C],P=await bn(t,S),F=[];let D=((o=i.flip)==null?void 0:o.overflows)||[];if(m&&F.push(P[x]),f){const B=Kc(r,l,_);F.push(P[B[0]],P[B[1]])}if(D=[...D,{placement:r,overflows:F}],!F.every(B=>B<=0)){var T,k;const B=(((T=i.flip)==null?void 0:T.index)||0)+1,K=A[B];if(K&&(!(f==="alignment"?b!==dt(K):!1)||D.every(U=>dt(U.placement)===b?U.overflows[0]>0:!0)))return{data:{index:B,overflows:D},reset:{placement:K}};let j=(k=D.filter(H=>H.overflows[0]<=0).sort((H,U)=>H.overflows[1]-U.overflows[1])[0])==null?void 0:k.placement;if(!j)switch(u){case"bestFit":{var L;const H=(L=D.filter(U=>{if(M){const Z=dt(U.placement);return Z===b||Z==="y"}return!0}).map(U=>[U.placement,U.overflows.filter(Z=>Z>0).reduce((Z,J)=>Z+J,0)]).sort((U,Z)=>U[1]-Z[1])[0])==null?void 0:L[0];H&&(j=H);break}case"initialPlacement":j=s;break}if(r!==j)return{reset:{placement:j}}}return{}}}};function ni(e,t){return{top:e.top-t.height,right:e.right-t.width,bottom:e.bottom-t.height,left:e.left-t.width}}function oi(e){return zc.some(t=>e[t]>=0)}const Zc=function(e){return e===void 0&&(e={}),{name:"hide",options:e,async fn(t){const{rects:n}=t,{strategy:o="referenceHidden",...r}=Nt(e,t);switch(o){case"referenceHidden":{const i=await bn(t,{...r,elementContext:"reference"}),l=ni(i,n.reference);return{data:{referenceHiddenOffsets:l,referenceHidden:oi(l)}}}case"escaped":{const i=await bn(t,{...r,altBoundary:!0}),l=ni(i,n.floating);return{data:{escapedOffsets:l,escaped:oi(l)}}}default:return{}}}}},Jc=new Set(["left","top"]);async function Qc(e,t){const{placement:n,platform:o,elements:r}=e,i=await(o.isRTL==null?void 0:o.isRTL(r.floating)),l=Ft(n),s=Jt(n),a=dt(n)==="y",d=Jc.has(l)?-1:1,m=i&&a?-1:1,f=Nt(t,e);let{mainAxis:p,crossAxis:u,alignmentAxis:h}=typeof f=="number"?{mainAxis:f,crossAxis:0,alignmentAxis:null}:{mainAxis:f.mainAxis||0,crossAxis:f.crossAxis||0,alignmentAxis:f.alignmentAxis};return s&&typeof h=="number"&&(u=s==="end"?h*-1:h),a?{x:u*m,y:p*d}:{x:p*d,y:u*m}}const ed=function(e){return e===void 0&&(e=0),{name:"offset",options:e,async fn(t){var n,o;const{x:r,y:i,placement:l,middlewareData:s}=t,a=await Qc(t,e);return l===((n=s.offset)==null?void 0:n.placement)&&(o=s.arrow)!=null&&o.alignmentOffset?{}:{x:r+a.x,y:i+a.y,data:{...a,placement:l}}}}},td=function(e){return e===void 0&&(e={}),{name:"shift",options:e,async fn(t){const{x:n,y:o,placement:r}=t,{mainAxis:i=!0,crossAxis:l=!1,limiter:s={fn:S=>{let{x,y:b}=S;return{x,y:b}}},...a}=Nt(e,t),d={x:n,y:o},m=await bn(t,a),f=dt(Ft(r)),p=ol(f);let u=d[p],h=d[f];if(i){const S=p==="y"?"top":"left",x=p==="y"?"bottom":"right",b=u+m[S],v=u-m[x];u=Co(b,u,v)}if(l){const S=f==="y"?"top":"left",x=f==="y"?"bottom":"right",b=h+m[S],v=h-m[x];h=Co(b,h,v)}const y=s.fn({...t,[p]:u,[f]:h});return{...y,data:{x:y.x-n,y:y.y-o,enabled:{[p]:i,[f]:l}}}}}},nd=function(e){return e===void 0&&(e={}),{name:"size",options:e,async fn(t){var n,o;const{placement:r,rects:i,platform:l,elements:s}=t,{apply:a=()=>{},...d}=Nt(e,t),m=await bn(t,d),f=Ft(r),p=Jt(r),u=dt(r)==="y",{width:h,height:y}=i.floating;let S,x;f==="top"||f==="bottom"?(S=f,x=p===(await(l.isRTL==null?void 0:l.isRTL(s.floating))?"start":"end")?"left":"right"):(x=f,S=p==="end"?"top":"bottom");const b=y-m.top-m.bottom,v=h-m.left-m.right,_=$t(y-m[S],b),C=$t(h-m[x],v),M=!t.middlewareData.shift;let A=_,P=C;if((n=t.middlewareData.shift)!=null&&n.enabled.x&&(P=v),(o=t.middlewareData.shift)!=null&&o.enabled.y&&(A=b),M&&!p){const D=ze(m.left,0),T=ze(m.right,0),k=ze(m.top,0),L=ze(m.bottom,0);u?P=h-2*(D!==0||T!==0?D+T:ze(m.left,m.right)):A=y-2*(k!==0||L!==0?k+L:ze(m.top,m.bottom))}await a({...t,availableWidth:P,availableHeight:A});const F=await l.getDimensions(s.floating);return h!==F.width||y!==F.height?{reset:{rects:!0}}:{}}}};function jn(){return typeof window<"u"}function Et(e){return il(e)?(e.nodeName||"").toLowerCase():"#document"}function Oe(e){var t;return(e==null||(t=e.ownerDocument)==null?void 0:t.defaultView)||window}function ft(e){var t;return(t=(il(e)?e.ownerDocument:e.document)||window.document)==null?void 0:t.documentElement}function il(e){return jn()?e instanceof Node||e instanceof Oe(e).Node:!1}function ot(e){return jn()?e instanceof Element||e instanceof Oe(e).Element:!1}function rt(e){return jn()?e instanceof HTMLElement||e instanceof Oe(e).HTMLElement:!1}function ri(e){return!jn()||typeof ShadowRoot>"u"?!1:e instanceof ShadowRoot||e instanceof Oe(e).ShadowRoot}const od=new Set(["inline","contents"]);function Rn(e){const{overflow:t,overflowX:n,overflowY:o,display:r}=He(e);return/auto|scroll|overlay|hidden|clip/.test(t+o+n)&&!od.has(r)}const rd=new Set(["table","td","th"]);function id(e){return rd.has(Et(e))}const ld=[":popover-open",":modal"];function sd(e){return ld.some(t=>{try{return e.matches(t)}catch{return!1}})}const ad=["transform","translate","scale","rotate","perspective"],cd=["transform","translate","scale","rotate","perspective","filter"],dd=["paint","layout","strict","content"];function Go(e){const t=jo(),n=ot(e)?He(e):e;return ad.some(o=>n[o]?n[o]!=="none":!1)||(n.containerType?n.containerType!=="normal":!1)||!t&&(n.backdropFilter?n.backdropFilter!=="none":!1)||!t&&(n.filter?n.filter!=="none":!1)||cd.some(o=>(n.willChange||"").includes(o))||dd.some(o=>(n.contain||"").includes(o))}function ll(e){let t=Yt(e);for(;rt(t)&&!Un(t);){if(Go(t))return t;if(sd(t))return null;t=Yt(t)}return null}function jo(){return typeof CSS>"u"||!CSS.supports?!1:CSS.supports("-webkit-backdrop-filter","none")}const ud=new Set(["html","body","#document"]);function Un(e){return ud.has(Et(e))}function He(e){return Oe(e).getComputedStyle(e)}function qn(e){return ot(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.scrollX,scrollTop:e.scrollY}}function Yt(e){if(Et(e)==="html")return e;const t=e.assignedSlot||e.parentNode||ri(e)&&e.host||ft(e);return ri(t)?t.host:t}function sl(e){const t=Yt(e);return Un(t)?e.ownerDocument?e.ownerDocument.body:e.body:rt(t)&&Rn(t)?t:sl(t)}function yn(e,t,n){var o;t===void 0&&(t=[]),n===void 0&&(n=!0);const r=sl(e),i=r===((o=e.ownerDocument)==null?void 0:o.body),l=Oe(r);if(i){const s=gd(l);return t.concat(l,l.visualViewport||[],Rn(r)?r:[],s&&n?yn(s):[])}return t.concat(r,yn(r,[],n))}function gd(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}function al(e){const t=He(e);let n=parseFloat(t.width)||0,o=parseFloat(t.height)||0;const r=rt(e),i=r?e.offsetWidth:n,l=r?e.offsetHeight:o,s=Bn(n)!==i||Bn(o)!==l;return s&&(n=i,o=l),{width:n,height:o,$:s}}function Uo(e){return ot(e)?e:e.contextElement}function Xt(e){const t=Uo(e);if(!rt(t))return Mt(1);const n=t.getBoundingClientRect(),{width:o,height:r,$:i}=al(t);let l=(i?Bn(n.width):n.width)/o,s=(i?Bn(n.height):n.height)/r;return(!l||!Number.isFinite(l))&&(l=1),(!s||!Number.isFinite(s))&&(s=1),{x:l,y:s}}const fd=Mt(0);function cl(e){const t=Oe(e);return!jo()||!t.visualViewport?fd:{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}}function pd(e,t,n){return t===void 0&&(t=!1),!n||t&&n!==Oe(e)?!1:t}function Ht(e,t,n,o){t===void 0&&(t=!1),n===void 0&&(n=!1);const r=e.getBoundingClientRect(),i=Uo(e);let l=Mt(1);t&&(o?ot(o)&&(l=Xt(o)):l=Xt(e));const s=pd(i,n,o)?cl(i):Mt(0);let a=(r.left+s.x)/l.x,d=(r.top+s.y)/l.y,m=r.width/l.x,f=r.height/l.y;if(i){const p=Oe(i),u=o&&ot(o)?Oe(o):o;let h=p.frameElement;for(;h&&o&&u!==p;){const y=Xt(h),S=h.getBoundingClientRect(),x=He(h),b=S.left+(h.clientLeft+parseFloat(x.paddingLeft))*y.x,v=S.top+(h.clientTop+parseFloat(x.paddingTop))*y.y;a*=y.x,d*=y.y,m*=y.x,f*=y.y,a+=b,d+=v,h=Oe(h).frameElement}}return Hn({width:m,height:f,x:a,y:d})}const hd=[":popover-open",":modal"];function dl(e){let t=!1,n=0,o=0;function r(i){try{t=t||e.matches(i)}catch{}}if(hd.forEach(i=>{r(i)}),t){const i=ll(e);if(i){const l=i.getBoundingClientRect();n=l.x,o=l.y}}return[t,n,o]}function md(e){let{elements:t,rect:n,offsetParent:o,strategy:r}=e;const i=ft(o),[l]=t?dl(t.floating):[!1];if(o===i||l)return n;let s={scrollLeft:0,scrollTop:0},a=Mt(1);const d=Mt(0),m=rt(o);if((m||!m&&r!=="fixed")&&((Et(o)!=="body"||Rn(i))&&(s=qn(o)),rt(o))){const f=Ht(o);a=Xt(o),d.x=f.x+o.clientLeft,d.y=f.y+o.clientTop}return{width:n.width*a.x,height:n.height*a.y,x:n.x*a.x-s.scrollLeft*a.x+d.x,y:n.y*a.y-s.scrollTop*a.y+d.y}}function vd(e){return Array.from(e.getClientRects())}function ul(e){return Ht(ft(e)).left+qn(e).scrollLeft}function xd(e){const t=ft(e),n=qn(e),o=e.ownerDocument.body,r=ze(t.scrollWidth,t.clientWidth,o.scrollWidth,o.clientWidth),i=ze(t.scrollHeight,t.clientHeight,o.scrollHeight,o.clientHeight);let l=-n.scrollLeft+ul(e);const s=-n.scrollTop;return He(o).direction==="rtl"&&(l+=ze(t.clientWidth,o.clientWidth)-r),{width:r,height:i,x:l,y:s}}function bd(e,t){const n=Oe(e),o=ft(e),r=n.visualViewport;let i=o.clientWidth,l=o.clientHeight,s=0,a=0;if(r){i=r.width,l=r.height;const d=jo();(!d||d&&t==="fixed")&&(s=r.offsetLeft,a=r.offsetTop)}return{width:i,height:l,x:s,y:a}}function yd(e,t){const n=Ht(e,!0,t==="fixed"),o=n.top+e.clientTop,r=n.left+e.clientLeft,i=rt(e)?Xt(e):Mt(1),l=e.clientWidth*i.x,s=e.clientHeight*i.y,a=r*i.x,d=o*i.y;return{width:l,height:s,x:a,y:d}}function ii(e,t,n){let o;if(t==="viewport")o=bd(e,n);else if(t==="document")o=xd(ft(e));else if(ot(t))o=yd(t,n);else{const r=cl(e);o={...t,x:t.x-r.x,y:t.y-r.y}}return Hn(o)}function gl(e,t){const n=Yt(e);return n===t||!ot(n)||Un(n)?!1:He(n).position==="fixed"||gl(n,t)}function Sd(e,t){const n=t.get(e);if(n)return n;let o=yn(e,[],!1).filter(s=>ot(s)&&Et(s)!=="body"),r=null;const i=He(e).position==="fixed";let l=i?Yt(e):e;for(;ot(l)&&!Un(l);){const s=He(l),a=Go(l);!a&&s.position==="fixed"&&(r=null),(i?!a&&!r:!a&&s.position==="static"&&!!r&&["absolute","fixed"].includes(r.position)||Rn(l)&&!a&&gl(e,l))?o=o.filter(m=>m!==l):r=s,l=Yt(l)}return t.set(e,o),o}function wd(e){let{element:t,boundary:n,rootBoundary:o,strategy:r}=e;const l=[...n==="clippingAncestors"?Sd(t,this._c):[].concat(n),o],s=l[0],a=l.reduce((d,m)=>{const f=ii(t,m,r);return d.top=ze(f.top,d.top),d.right=$t(f.right,d.right),d.bottom=$t(f.bottom,d.bottom),d.left=ze(f.left,d.left),d},ii(t,s,r));return{width:a.right-a.left,height:a.bottom-a.top,x:a.left,y:a.top}}function Cd(e){const{width:t,height:n}=al(e);return{width:t,height:n}}function _d(e,t,n,o){const r=rt(t),i=ft(t),l=n==="fixed",s=Ht(e,!0,l,t);let a={scrollLeft:0,scrollTop:0};const d=Mt(0);if(r||!r&&!l)if((Et(t)!=="body"||Rn(i))&&(a=qn(t)),r){const y=Ht(t,!0,l,t);d.x=y.x+t.clientLeft,d.y=y.y+t.clientTop}else i&&(d.x=ul(i));let m=s.left+a.scrollLeft-d.x,f=s.top+a.scrollTop-d.y;const[p,u,h]=dl(o);return p&&(m+=u,f+=h,r&&(m+=t.clientLeft,f+=t.clientTop)),{x:m,y:f,width:s.width,height:s.height}}function li(e,t){return!rt(e)||He(e).position==="fixed"?null:t?t(e):e.offsetParent}function fl(e,t){const n=Oe(e);if(!rt(e))return n;let o=li(e,t);for(;o&&id(o)&&He(o).position==="static";)o=li(o,t);return o&&(Et(o)==="html"||Et(o)==="body"&&He(o).position==="static"&&!Go(o))?n:o||ll(e)||n}const Rd=async function(e){const t=this.getOffsetParent||fl,n=this.getDimensions;return{reference:_d(e.reference,await t(e.floating),e.strategy,e.floating),floating:{x:0,y:0,...await n(e.floating)}}};function Id(e){return He(e).direction==="rtl"}const pl={convertOffsetParentRelativeRectToViewportRelativeRect:md,getDocumentElement:ft,getClippingRect:wd,getOffsetParent:fl,getElementRects:Rd,getClientRects:vd,getDimensions:Cd,getScale:Xt,isElement:ot,isRTL:Id};function Pd(e,t){let n=null,o;const r=ft(e);function i(){var s;clearTimeout(o),(s=n)==null||s.disconnect(),n=null}function l(s,a){s===void 0&&(s=!1),a===void 0&&(a=1),i();const{left:d,top:m,width:f,height:p}=e.getBoundingClientRect();if(s||t(),!f||!p)return;const u=Mn(m),h=Mn(r.clientWidth-(d+f)),y=Mn(r.clientHeight-(m+p)),S=Mn(d),b={rootMargin:-u+"px "+-h+"px "+-y+"px "+-S+"px",threshold:ze(0,$t(1,a))||1};let v=!0;function _(C){const M=C[0].intersectionRatio;if(M!==a){if(!v)return l();M?l(!1,M):o=setTimeout(()=>{l(!1,1e-7)},100)}v=!1}try{n=new IntersectionObserver(_,{...b,root:r.ownerDocument})}catch{n=new IntersectionObserver(_,b)}n.observe(e)}return l(!0),i}function kd(e,t,n,o){o===void 0&&(o={});const{ancestorScroll:r=!0,ancestorResize:i=!0,elementResize:l=typeof ResizeObserver=="function",layoutShift:s=typeof IntersectionObserver=="function",animationFrame:a=!1}=o,d=Uo(e),m=r||i?[...d?yn(d):[],...yn(t)]:[];m.forEach(x=>{r&&x.addEventListener("scroll",n,{passive:!0}),i&&x.addEventListener("resize",n)});const f=d&&s?Pd(d,n):null;let p=-1,u=null;l&&(u=new ResizeObserver(x=>{let[b]=x;b&&b.target===d&&u&&(u.unobserve(t),cancelAnimationFrame(p),p=requestAnimationFrame(()=>{var v;(v=u)==null||v.observe(t)})),n()}),d&&!a&&u.observe(d),u.observe(t));let h,y=a?Ht(e):null;a&&S();function S(){const x=Ht(e);y&&(x.x!==y.x||x.y!==y.y||x.width!==y.width||x.height!==y.height)&&n(),y=x,h=requestAnimationFrame(S)}return n(),()=>{var x;m.forEach(b=>{r&&b.removeEventListener("scroll",n),i&&b.removeEventListener("resize",n)}),f?.(),(x=u)==null||x.disconnect(),u=null,a&&cancelAnimationFrame(h)}}const $d=td,Md=Yc,Fd=nd,Ed=Zc,Ad=Xc,Dd=(e,t,n)=>{const o=new Map,r={platform:pl,...n},i={...r.platform,_c:o};return qc(e,t,{...r,platform:i})};var Td=V('<svg display=block viewBox="0 0 30 30"style=transform:scale(1.02)><g><path fill=none d=M23,27.8c1.1,1.2,3.4,2.2,5,2.2h2H0h2c1.7,0,3.9-1,5-2.2l6.6-7.2c0.7-0.8,2-0.8,2.7,0L23,27.8L23,27.8z></path><path stroke=none d=M23,27.8c1.1,1.2,3.4,2.2,5,2.2h2H0h2c1.7,0,3.9-1,5-2.2l6.6-7.2c0.7-0.8,2-0.8,2.7,0L23,27.8L23,27.8z>'),qo=Xe();function Xo(){const e=qe(qo);if(e===void 0)throw new Error("[kobalte]: `usePopperContext` must be used within a `Popper` component");return e}var Ro=30,si=Ro/2,Ld={top:180,right:-90,bottom:0,left:90};function Yo(e){const t=Xo(),n=fe({size:Ro},e),[o,r]=re(n,["ref","style","size"]),i=()=>t.currentPlacement().split("-")[0],l=zd(t.contentRef),s=()=>l()?.getPropertyValue("background-color")||"none",a=()=>l()?.getPropertyValue(`border-${i()}-color`)||"none",d=()=>l()?.getPropertyValue(`border-${i()}-width`)||"0px",m=()=>Number.parseInt(d())*2*(Ro/o.size),f=()=>`rotate(${Ld[i()]} ${si} ${si}) translate(0 2)`;return R(De,q({as:"div",ref(p){var u=Ye(t.setArrowRef,o.ref);typeof u=="function"&&u(p)},"aria-hidden":"true",get style(){return Oo({position:"absolute","font-size":`${o.size}px`,width:"1em",height:"1em","pointer-events":"none",fill:s(),stroke:a(),"stroke-width":m()},o.style)}},r,{get children(){var p=Td(),u=p.firstChild,h=u.firstChild;return h.nextSibling,O(()=>ls(u,"transform",f())),p}}))}function zd(e){const[t,n]=W();return Y(()=>{const o=e();o&&n(dc(o).getComputedStyle(o))}),t}function Od(e){const t=Xo(),[n,o]=re(e,["ref","style"]);return R(De,q({as:"div",ref(r){var i=Ye(t.setPositionerRef,n.ref);typeof i=="function"&&i(r)},"data-popper-positioner":"",get style(){return Oo({position:"absolute",top:0,left:0,"min-width":"max-content"},n.style)}},o))}function ai(e){const{x:t=0,y:n=0,width:o=0,height:r=0}=e??{};if(typeof DOMRect=="function")return new DOMRect(t,n,o,r);const i={x:t,y:n,width:o,height:r,top:n,right:t+o,bottom:n+r,left:t};return{...i,toJSON:()=>i}}function Vd(e,t){return{contextElement:e,getBoundingClientRect:()=>{const o=t(e);return o?ai(o):e?e.getBoundingClientRect():ai()}}}function Bd(e){return/^(?:top|bottom|left|right)(?:-(?:start|end))?$/.test(e)}var Kd={top:"bottom",right:"left",bottom:"top",left:"right"};function Hd(e,t){const[n,o]=e.split("-"),r=Kd[n];return o?n==="left"||n==="right"?`${r} ${o==="start"?"top":"bottom"}`:o==="start"?`${r} ${t==="rtl"?"right":"left"}`:`${r} ${t==="rtl"?"left":"right"}`:`${r} center`}function Wd(e){const t=fe({getAnchorRect:p=>p?.getBoundingClientRect(),placement:"bottom",gutter:0,shift:0,flip:!0,slide:!0,overlap:!1,sameWidth:!1,fitViewport:!1,hideWhenDetached:!1,detachedPadding:0,arrowPadding:4,overflowPadding:8},e),[n,o]=W(),[r,i]=W(),[l,s]=W(t.placement),a=()=>Vd(t.anchorRef?.(),t.getAnchorRect),{direction:d}=Tt();async function m(){const p=a(),u=n(),h=r();if(!p||!u)return;const y=(h?.clientHeight||0)/2,S=typeof t.gutter=="number"?t.gutter+y:t.gutter??y;u.style.setProperty("--kb-popper-content-overflow-padding",`${t.overflowPadding}px`),p.getBoundingClientRect();const x=[ed(({placement:M})=>{const A=!!M.split("-")[1];return{mainAxis:S,crossAxis:A?void 0:t.shift,alignmentAxis:t.shift}})];if(t.flip!==!1){const M=typeof t.flip=="string"?t.flip.split(" "):void 0;if(M!==void 0&&!M.every(Bd))throw new Error("`flip` expects a spaced-delimited list of placements");x.push(Md({padding:t.overflowPadding,fallbackPlacements:M}))}(t.slide||t.overlap)&&x.push($d({mainAxis:t.slide,crossAxis:t.overlap,padding:t.overflowPadding})),x.push(Fd({padding:t.overflowPadding,apply({availableWidth:M,availableHeight:A,rects:P}){const F=Math.round(P.reference.width);M=Math.floor(M),A=Math.floor(A),u.style.setProperty("--kb-popper-anchor-width",`${F}px`),u.style.setProperty("--kb-popper-content-available-width",`${M}px`),u.style.setProperty("--kb-popper-content-available-height",`${A}px`),t.sameWidth&&(u.style.width=`${F}px`),t.fitViewport&&(u.style.maxWidth=`${M}px`,u.style.maxHeight=`${A}px`)}})),t.hideWhenDetached&&x.push(Ed({padding:t.detachedPadding})),h&&x.push(Ad({element:h,padding:t.arrowPadding}));const b=await Dd(p,u,{placement:t.placement,strategy:"absolute",middleware:x,platform:{...pl,isRTL:()=>d()==="rtl"}});if(s(b.placement),t.onCurrentPlacementChange?.(b.placement),!u)return;u.style.setProperty("--kb-popper-content-transform-origin",Hd(b.placement,d()));const v=Math.round(b.x),_=Math.round(b.y);let C;if(t.hideWhenDetached&&(C=b.middlewareData.hide?.referenceHidden?"hidden":"visible"),Object.assign(u.style,{top:"0",left:"0",transform:`translate3d(${v}px, ${_}px, 0)`,visibility:C}),h&&b.middlewareData.arrow){const{x:M,y:A}=b.middlewareData.arrow,P=b.placement.split("-")[0];Object.assign(h.style,{left:M!=null?`${M}px`:"",top:A!=null?`${A}px`:"",[P]:"100%"})}}Y(()=>{const p=a(),u=n();if(!p||!u)return;const h=kd(p,u,m,{elementResize:typeof ResizeObserver=="function"});Q(h)}),Y(()=>{const p=n(),u=t.contentRef?.();!p||!u||queueMicrotask(()=>{p.style.zIndex=getComputedStyle(u).zIndex})});const f={currentPlacement:l,contentRef:()=>t.contentRef?.(),setPositionerRef:o,setArrowRef:i};return R(qo.Provider,{value:f,get children(){return t.children}})}var hl=Object.assign(Wd,{Arrow:Yo,Context:qo,usePopperContext:Xo,Positioner:Od});function ml(e){let t=e.startIndex??0;const n=e.startLevel??0,o=[],r=a=>{if(a==null)return"";const d=e.getKey??"key",m=jt(d)?a[d]:d(a);return m!=null?String(m):""},i=a=>{if(a==null)return"";const d=e.getTextValue??"textValue",m=jt(d)?a[d]:d(a);return m!=null?String(m):""},l=a=>{if(a==null)return!1;const d=e.getDisabled??"disabled";return(jt(d)?a[d]:d(a))??!1},s=a=>{if(a!=null)return jt(e.getSectionChildren)?a[e.getSectionChildren]:e.getSectionChildren?.(a)};for(const a of e.dataSource){if(jt(a)||ac(a)){o.push({type:"item",rawValue:a,key:String(a),textValue:String(a),disabled:l(a),level:n,index:t}),t++;continue}if(s(a)!=null){o.push({type:"section",rawValue:a,key:"",textValue:"",disabled:!1,level:n,index:t}),t++;const d=s(a)??[];if(d.length>0){const m=ml({dataSource:d,getKey:e.getKey,getTextValue:e.getTextValue,getDisabled:e.getDisabled,getSectionChildren:e.getSectionChildren,startIndex:t,startLevel:n+1});o.push(...m),t+=m.length}}else o.push({type:"item",rawValue:a,key:r(a),textValue:i(a),disabled:l(a),level:n,index:t}),t++}return o}function Nd(e,t=[]){return be(()=>{const n=ml({dataSource:z(e.dataSource),getKey:z(e.getKey),getTextValue:z(e.getTextValue),getDisabled:z(e.getDisabled),getSectionChildren:z(e.getSectionChildren)});for(let o=0;o<t.length;o++)t[o]();return e.factory(n)})}function Xn(e){const[t,n]=W(e.defaultValue?.()),o=be(()=>e.value?.()!==void 0),r=be(()=>o()?e.value?.():t());return[r,l=>{ki(()=>{const s=nc(l,r());return Object.is(s,r())||(o()||n(s),e.onChange?.(s)),s})}]}function vl(e){const[t,n]=Xn(e);return[()=>t()??!1,n]}function Gd(e){const[t,n]=Xn(e);return[()=>t()??[],n]}var ct=class xl extends Set{anchorKey;currentKey;constructor(t,n,o){super(t),t instanceof xl?(this.anchorKey=n||t.anchorKey,this.currentKey=o||t.currentKey):(this.anchorKey=n,this.currentKey=o)}};function jd(e){const[t,n]=Xn(e);return[()=>t()??new ct,n]}function bl(e){return pc()?e.altKey:e.ctrlKey}function Ut(e){return Gn()?e.metaKey:e.ctrlKey}function ci(e){return new ct(e)}function Ud(e,t){if(e.size!==t.size)return!1;for(const n of e)if(!t.has(n))return!1;return!0}function qd(e){const t=fe({selectionMode:"none",selectionBehavior:"toggle"},e),[n,o]=W(!1),[r,i]=W(),l=be(()=>{const y=z(t.selectedKeys);return y!=null?ci(y):y}),s=be(()=>{const y=z(t.defaultSelectedKeys);return y!=null?ci(y):new ct}),[a,d]=jd({value:l,defaultValue:s,onChange:y=>t.onSelectionChange?.(y)}),[m,f]=W(z(t.selectionBehavior)),p=()=>z(t.selectionMode),u=()=>z(t.disallowEmptySelection)??!1,h=y=>{(z(t.allowDuplicateSelectionEvents)||!Ud(y,a()))&&d(y)};return Y(()=>{const y=a();z(t.selectionBehavior)==="replace"&&m()==="toggle"&&typeof y=="object"&&y.size===0&&f("replace")}),Y(()=>{f(z(t.selectionBehavior)??"toggle")}),{selectionMode:p,disallowEmptySelection:u,selectionBehavior:m,setSelectionBehavior:f,isFocused:n,setFocused:o,focusedKey:r,setFocusedKey:i,selectedKeys:a,setSelectedKeys:h}}function Xd(e){const[t,n]=W(""),[o,r]=W(-1);return{typeSelectHandlers:{onKeyDown:l=>{if(z(e.isDisabled))return;const s=z(e.keyboardDelegate),a=z(e.selectionManager);if(!s.getKeyForSearch)return;const d=Yd(l.key);if(!d||l.ctrlKey||l.metaKey)return;d===" "&&t().trim().length>0&&(l.preventDefault(),l.stopPropagation());let m=n(p=>p+d),f=s.getKeyForSearch(m,a.focusedKey())??s.getKeyForSearch(m);f==null&&Zd(m)&&(m=m[0],f=s.getKeyForSearch(m,a.focusedKey())??s.getKeyForSearch(m)),f!=null&&(a.setFocusedKey(f),e.onTypeSelect?.(f)),clearTimeout(o()),r(window.setTimeout(()=>n(""),500))}}}}function Yd(e){return e.length===1||!/^[A-Z]/i.test(e)?e:""}function Zd(e){return e.split("").every(t=>t===e[0])}function Jd(e,t,n){const r=q({selectOnFocus:()=>z(e.selectionManager).selectionBehavior()==="replace"},e),i=()=>t(),{direction:l}=Tt();let s={top:0,left:0};ic(()=>z(r.isVirtualized)?void 0:i(),"scroll",()=>{const S=i();S&&(s={top:S.scrollTop,left:S.scrollLeft})});const{typeSelectHandlers:a}=Xd({isDisabled:()=>z(r.disallowTypeAhead),keyboardDelegate:()=>z(r.keyboardDelegate),selectionManager:()=>z(r.selectionManager)}),d=()=>z(r.orientation)??"vertical",m=S=>{Se(S,a.onKeyDown),S.altKey&&S.key==="Tab"&&S.preventDefault();const x=t();if(!x?.contains(S.target))return;const b=z(r.selectionManager),v=z(r.selectOnFocus),_=P=>{P!=null&&(b.setFocusedKey(P),S.shiftKey&&b.selectionMode()==="multiple"?b.extendSelection(P):v&&!bl(S)&&b.replaceSelection(P))},C=z(r.keyboardDelegate),M=z(r.shouldFocusWrap),A=b.focusedKey();switch(S.key){case(d()==="vertical"?"ArrowDown":"ArrowRight"):{if(C.getKeyBelow){S.preventDefault();let P;A!=null?P=C.getKeyBelow(A):P=C.getFirstKey?.(),P==null&&M&&(P=C.getFirstKey?.(A)),_(P)}break}case(d()==="vertical"?"ArrowUp":"ArrowLeft"):{if(C.getKeyAbove){S.preventDefault();let P;A!=null?P=C.getKeyAbove(A):P=C.getLastKey?.(),P==null&&M&&(P=C.getLastKey?.(A)),_(P)}break}case(d()==="vertical"?"ArrowLeft":"ArrowUp"):{if(C.getKeyLeftOf){S.preventDefault();const P=l()==="rtl";let F;A!=null?F=C.getKeyLeftOf(A):F=P?C.getFirstKey?.():C.getLastKey?.(),_(F)}break}case(d()==="vertical"?"ArrowRight":"ArrowDown"):{if(C.getKeyRightOf){S.preventDefault();const P=l()==="rtl";let F;A!=null?F=C.getKeyRightOf(A):F=P?C.getLastKey?.():C.getFirstKey?.(),_(F)}break}case"Home":if(C.getFirstKey){S.preventDefault();const P=C.getFirstKey(A,Ut(S));P!=null&&(b.setFocusedKey(P),Ut(S)&&S.shiftKey&&b.selectionMode()==="multiple"?b.extendSelection(P):v&&b.replaceSelection(P))}break;case"End":if(C.getLastKey){S.preventDefault();const P=C.getLastKey(A,Ut(S));P!=null&&(b.setFocusedKey(P),Ut(S)&&S.shiftKey&&b.selectionMode()==="multiple"?b.extendSelection(P):v&&b.replaceSelection(P))}break;case"PageDown":if(C.getKeyPageBelow&&A!=null){S.preventDefault();const P=C.getKeyPageBelow(A);_(P)}break;case"PageUp":if(C.getKeyPageAbove&&A!=null){S.preventDefault();const P=C.getKeyPageAbove(A);_(P)}break;case"a":Ut(S)&&b.selectionMode()==="multiple"&&z(r.disallowSelectAll)!==!0&&(S.preventDefault(),b.selectAll());break;case"Escape":S.defaultPrevented||(S.preventDefault(),z(r.disallowEmptySelection)||b.clearSelection());break;case"Tab":if(!z(r.allowsTabNavigation)){if(S.shiftKey)x.focus();else{const P=_c(x,{tabbable:!0});let F,D;do D=P.lastChild(),D&&(F=D);while(D);F&&!F.contains(document.activeElement)&&Pe(F)}break}}},f=S=>{const x=z(r.selectionManager),b=z(r.keyboardDelegate),v=z(r.selectOnFocus);if(x.isFocused()){S.currentTarget.contains(S.target)||x.setFocused(!1);return}if(S.currentTarget.contains(S.target)){if(x.setFocused(!0),x.focusedKey()==null){const _=M=>{M!=null&&(x.setFocusedKey(M),v&&x.replaceSelection(M))},C=S.relatedTarget;C&&S.currentTarget.compareDocumentPosition(C)&Node.DOCUMENT_POSITION_FOLLOWING?_(x.lastSelectedKey()??b.getLastKey?.()):_(x.firstSelectedKey()??b.getFirstKey?.())}else if(!z(r.isVirtualized)){const _=i();if(_){_.scrollTop=s.top,_.scrollLeft=s.left;const C=_.querySelector(`[data-key="${x.focusedKey()}"]`);C&&(Pe(C),So(_,C))}}}},p=S=>{const x=z(r.selectionManager);S.currentTarget.contains(S.relatedTarget)||x.setFocused(!1)},u=S=>{i()===S.target&&S.preventDefault()},h=()=>{const S=z(r.autoFocus);if(!S)return;const x=z(r.selectionManager),b=z(r.keyboardDelegate);let v;S==="first"&&(v=b.getFirstKey?.()),S==="last"&&(v=b.getLastKey?.());const _=x.selectedKeys();_.size&&(v=_.values().next().value),x.setFocused(!0),x.setFocusedKey(v);const C=t();C&&v==null&&!z(r.shouldUseVirtualFocus)&&Pe(C)};return gt(()=>{r.deferAutoFocus?setTimeout(h,0):h()}),Y(_n([i,()=>z(r.isVirtualized),()=>z(r.selectionManager).focusedKey()],S=>{const[x,b,v]=S;if(b)v&&r.scrollToKey?.(v);else if(v&&x){const _=x.querySelector(`[data-key="${v}"]`);_&&So(x,_)}})),{tabIndex:be(()=>{if(!z(r.shouldUseVirtualFocus))return z(r.selectionManager).focusedKey()==null?0:-1}),onKeyDown:m,onMouseDown:u,onFocusIn:f,onFocusOut:p}}function yl(e,t){const n=()=>z(e.selectionManager),o=()=>z(e.key),r=()=>z(e.shouldUseVirtualFocus),i=b=>{n().selectionMode()!=="none"&&(n().selectionMode()==="single"?n().isSelected(o())&&!n().disallowEmptySelection()?n().toggleSelection(o()):n().replaceSelection(o()):b?.shiftKey?n().extendSelection(o()):n().selectionBehavior()==="toggle"||Ut(b)||"pointerType"in b&&b.pointerType==="touch"?n().toggleSelection(o()):n().replaceSelection(o()))},l=()=>n().isSelected(o()),s=()=>z(e.disabled)||n().isDisabled(o()),a=()=>!s()&&n().canSelectItem(o());let d=null;const m=b=>{a()&&(d=b.pointerType,b.pointerType==="mouse"&&b.button===0&&!z(e.shouldSelectOnPressUp)&&i(b))},f=b=>{a()&&b.pointerType==="mouse"&&b.button===0&&z(e.shouldSelectOnPressUp)&&z(e.allowsDifferentPressOrigin)&&i(b)},p=b=>{a()&&(z(e.shouldSelectOnPressUp)&&!z(e.allowsDifferentPressOrigin)||d!=="mouse")&&i(b)},u=b=>{!a()||!["Enter"," "].includes(b.key)||(bl(b)?n().toggleSelection(o()):i(b))},h=b=>{s()&&b.preventDefault()},y=b=>{const v=t();r()||s()||!v||b.target===v&&n().setFocusedKey(o())},S=be(()=>{if(!(r()||s()))return o()===n().focusedKey()?0:-1}),x=be(()=>z(e.virtualized)?void 0:o());return Y(_n([t,o,r,()=>n().focusedKey(),()=>n().isFocused()],([b,v,_,C,M])=>{b&&v===C&&M&&!_&&document.activeElement!==b&&(e.focus?e.focus():Pe(b))})),{isSelected:l,isDisabled:s,allowsSelection:a,tabIndex:S,dataKey:x,onPointerDown:m,onPointerUp:f,onClick:p,onKeyDown:u,onMouseDown:h,onFocus:y}}var Qd=class{collection;state;constructor(e,t){this.collection=e,this.state=t}selectionMode(){return this.state.selectionMode()}disallowEmptySelection(){return this.state.disallowEmptySelection()}selectionBehavior(){return this.state.selectionBehavior()}setSelectionBehavior(e){this.state.setSelectionBehavior(e)}isFocused(){return this.state.isFocused()}setFocused(e){this.state.setFocused(e)}focusedKey(){return this.state.focusedKey()}setFocusedKey(e){(e==null||this.collection().getItem(e))&&this.state.setFocusedKey(e)}selectedKeys(){return this.state.selectedKeys()}isSelected(e){if(this.state.selectionMode()==="none")return!1;const t=this.getKey(e);return t==null?!1:this.state.selectedKeys().has(t)}isEmpty(){return this.state.selectedKeys().size===0}isSelectAll(){if(this.isEmpty())return!1;const e=this.state.selectedKeys();return this.getAllSelectableKeys().every(t=>e.has(t))}firstSelectedKey(){let e;for(const t of this.state.selectedKeys()){const n=this.collection().getItem(t),o=n?.index!=null&&e?.index!=null&&n.index<e.index;(!e||o)&&(e=n)}return e?.key}lastSelectedKey(){let e;for(const t of this.state.selectedKeys()){const n=this.collection().getItem(t),o=n?.index!=null&&e?.index!=null&&n.index>e.index;(!e||o)&&(e=n)}return e?.key}extendSelection(e){if(this.selectionMode()==="none")return;if(this.selectionMode()==="single"){this.replaceSelection(e);return}const t=this.getKey(e);if(t==null)return;const n=this.state.selectedKeys(),o=n.anchorKey||t,r=new ct(n,o,t);for(const i of this.getKeyRange(o,n.currentKey||t))r.delete(i);for(const i of this.getKeyRange(t,o))this.canSelectItem(i)&&r.add(i);this.state.setSelectedKeys(r)}getKeyRange(e,t){const n=this.collection().getItem(e),o=this.collection().getItem(t);return n&&o?n.index!=null&&o.index!=null&&n.index<=o.index?this.getKeyRangeInternal(e,t):this.getKeyRangeInternal(t,e):[]}getKeyRangeInternal(e,t){const n=[];let o=e;for(;o!=null;){const r=this.collection().getItem(o);if(r&&r.type==="item"&&n.push(o),o===t)return n;o=this.collection().getKeyAfter(o)}return[]}getKey(e){const t=this.collection().getItem(e);return t?!t||t.type!=="item"?null:t.key:e}toggleSelection(e){if(this.selectionMode()==="none")return;if(this.selectionMode()==="single"&&!this.isSelected(e)){this.replaceSelection(e);return}const t=this.getKey(e);if(t==null)return;const n=new ct(this.state.selectedKeys());n.has(t)?n.delete(t):this.canSelectItem(t)&&(n.add(t),n.anchorKey=t,n.currentKey=t),!(this.disallowEmptySelection()&&n.size===0)&&this.state.setSelectedKeys(n)}replaceSelection(e){if(this.selectionMode()==="none")return;const t=this.getKey(e);if(t==null)return;const n=this.canSelectItem(t)?new ct([t],t,t):new ct;this.state.setSelectedKeys(n)}setSelectedKeys(e){if(this.selectionMode()==="none")return;const t=new ct;for(const n of e){const o=this.getKey(n);if(o!=null&&(t.add(o),this.selectionMode()==="single"))break}this.state.setSelectedKeys(t)}selectAll(){this.selectionMode()==="multiple"&&this.state.setSelectedKeys(new Set(this.getAllSelectableKeys()))}clearSelection(){const e=this.state.selectedKeys();!this.disallowEmptySelection()&&e.size>0&&this.state.setSelectedKeys(new ct)}toggleSelectAll(){this.isSelectAll()?this.clearSelection():this.selectAll()}select(e,t){this.selectionMode()!=="none"&&(this.selectionMode()==="single"?this.isSelected(e)&&!this.disallowEmptySelection()?this.toggleSelection(e):this.replaceSelection(e):this.selectionBehavior()==="toggle"||t&&t.pointerType==="touch"?this.toggleSelection(e):this.replaceSelection(e))}isSelectionEqual(e){if(e===this.state.selectedKeys())return!0;const t=this.selectedKeys();if(e.size!==t.size)return!1;for(const n of e)if(!t.has(n))return!1;for(const n of t)if(!e.has(n))return!1;return!0}canSelectItem(e){if(this.state.selectionMode()==="none")return!1;const t=this.collection().getItem(e);return t!=null&&!t.disabled}isDisabled(e){const t=this.collection().getItem(e);return!t||t.disabled}getAllSelectableKeys(){const e=[];return(n=>{for(;n!=null;){if(this.canSelectItem(n)){const o=this.collection().getItem(n);if(!o)continue;o.type==="item"&&e.push(n)}n=this.collection().getKeyAfter(n)}})(this.collection().getFirstKey()),e}},di=class{keyMap=new Map;iterable;firstKey;lastKey;constructor(e){this.iterable=e;for(const o of e)this.keyMap.set(o.key,o);if(this.keyMap.size===0)return;let t,n=0;for(const[o,r]of this.keyMap)t?(t.nextKey=o,r.prevKey=t.key):(this.firstKey=o,r.prevKey=void 0),r.type==="item"&&(r.index=n++),t=r,t.nextKey=void 0;this.lastKey=t.key}*[Symbol.iterator](){yield*this.iterable}getSize(){return this.keyMap.size}getKeys(){return this.keyMap.keys()}getKeyBefore(e){return this.keyMap.get(e)?.prevKey}getKeyAfter(e){return this.keyMap.get(e)?.nextKey}getFirstKey(){return this.firstKey}getLastKey(){return this.lastKey}getItem(e){return this.keyMap.get(e)}at(e){const t=[...this.getKeys()];return this.getItem(t[e])}};function eu(e){const t=qd(e),o=Nd({dataSource:()=>z(e.dataSource),getKey:()=>z(e.getKey),getTextValue:()=>z(e.getTextValue),getDisabled:()=>z(e.getDisabled),getSectionChildren:()=>z(e.getSectionChildren),factory:i=>e.filter?new di(e.filter(i)):new di(i)},[()=>e.filter]),r=new Qd(o,t);return Ii(()=>{const i=t.focusedKey();i!=null&&!o().getItem(i)&&t.setFocusedKey(void 0)}),{collection:o,selectionManager:()=>r}}var tu=class{collection;ref;collator;constructor(e,t,n){this.collection=e,this.ref=t,this.collator=n}getKeyBelow(e){let t=this.collection().getKeyAfter(e);for(;t!=null;){const n=this.collection().getItem(t);if(n&&n.type==="item"&&!n.disabled)return t;t=this.collection().getKeyAfter(t)}}getKeyAbove(e){let t=this.collection().getKeyBefore(e);for(;t!=null;){const n=this.collection().getItem(t);if(n&&n.type==="item"&&!n.disabled)return t;t=this.collection().getKeyBefore(t)}}getFirstKey(){let e=this.collection().getFirstKey();for(;e!=null;){const t=this.collection().getItem(e);if(t&&t.type==="item"&&!t.disabled)return e;e=this.collection().getKeyAfter(e)}}getLastKey(){let e=this.collection().getLastKey();for(;e!=null;){const t=this.collection().getItem(e);if(t&&t.type==="item"&&!t.disabled)return e;e=this.collection().getKeyBefore(e)}}getItem(e){return this.ref?.()?.querySelector(`[data-key="${e}"]`)??null}getKeyPageAbove(e){const t=this.ref?.();let n=this.getItem(e);if(!t||!n)return;const o=Math.max(0,n.offsetTop+n.offsetHeight-t.offsetHeight);let r=e;for(;r&&n&&n.offsetTop>o;)r=this.getKeyAbove(r),n=r!=null?this.getItem(r):null;return r}getKeyPageBelow(e){const t=this.ref?.();let n=this.getItem(e);if(!t||!n)return;const o=Math.min(t.scrollHeight,n.offsetTop-n.offsetHeight+t.offsetHeight);let r=e;for(;r&&n&&n.offsetTop<o;)r=this.getKeyBelow(r),n=r!=null?this.getItem(r):null;return r}getKeyForSearch(e,t){const n=this.collator?.();if(!n)return;let o=t!=null?this.getKeyBelow(t):this.getFirstKey();for(;o!=null;){const r=this.collection().getItem(o);if(r){const i=r.textValue.slice(0,e.length);if(r.textValue&&n.compare(i,e)===0)return o}o=this.getKeyBelow(o)}}};function nu(e,t,n){const o=Lc({usage:"search",sensitivity:"base"}),r=be(()=>{const i=z(e.keyboardDelegate);return i||new tu(e.collection,t,o)});return Jd({selectionManager:()=>z(e.selectionManager),keyboardDelegate:r,autoFocus:()=>z(e.autoFocus),deferAutoFocus:()=>z(e.deferAutoFocus),shouldFocusWrap:()=>z(e.shouldFocusWrap),disallowEmptySelection:()=>z(e.disallowEmptySelection),selectOnFocus:()=>z(e.selectOnFocus),disallowTypeAhead:()=>z(e.disallowTypeAhead),shouldUseVirtualFocus:()=>z(e.shouldUseVirtualFocus),allowsTabNavigation:()=>z(e.allowsTabNavigation),isVirtualized:()=>z(e.isVirtualized),scrollToKey:i=>z(e.scrollToKey)?.(i),orientation:()=>z(e.orientation)},t)}var Sl=Xe();function wl(){return qe(Sl)}function ou(){const e=wl();if(e===void 0)throw new Error("[kobalte]: `useDomCollectionContext` must be used within a `DomCollectionProvider` component");return e}function Cl(e,t){return!!(t.compareDocumentPosition(e)&Node.DOCUMENT_POSITION_PRECEDING)}function ru(e,t){const n=t.ref();if(!n)return-1;let o=e.length;if(!o)return-1;for(;o--;){const r=e[o]?.ref();if(r&&Cl(r,n))return o+1}return 0}function iu(e){const t=e.map((o,r)=>[r,o]);let n=!1;return t.sort(([o,r],[i,l])=>{const s=r.ref(),a=l.ref();return s===a||!s||!a?0:Cl(s,a)?(o>i&&(n=!0),-1):(o<i&&(n=!0),1)}),n?t.map(([o,r])=>r):e}function _l(e,t){const n=iu(e);e!==n&&t(n)}function lu(e){const t=e[0],n=e[e.length-1]?.ref();let o=t?.ref()?.parentElement;for(;o;){if(n&&o.contains(n))return o;o=o.parentElement}return nt(o).body}function su(e,t){Y(()=>{const n=setTimeout(()=>{_l(e(),t)});Q(()=>clearTimeout(n))})}function au(e,t){if(typeof IntersectionObserver!="function"){su(e,t);return}let n=[];Y(()=>{const o=()=>{const l=!!n.length;n=e(),l&&_l(e(),t)},r=lu(e()),i=new IntersectionObserver(o,{root:r});for(const l of e()){const s=l.ref();s&&i.observe(s)}Q(()=>i.disconnect())})}function cu(e={}){const[t,n]=Gd({value:()=>z(e.items),onChange:i=>e.onItemsChange?.(i)});au(t,n);const o=i=>(n(l=>{const s=ru(l,i);return sc(l,i,s)}),()=>{n(l=>{const s=l.filter(a=>a.ref()!==i.ref());return l.length===s.length?l:s})});return{DomCollectionProvider:i=>R(Sl.Provider,{value:{registerItem:o},get children(){return i.children}})}}function du(e){const t=ou(),n=fe({shouldRegisterItem:!0},e);Y(()=>{if(!n.shouldRegisterItem)return;const o=t.registerItem(n.getItem());Q(o)})}var Wn="data-kb-top-layer",Rl,Io=!1,ut=[];function Sn(e){return ut.findIndex(t=>t.node===e)}function uu(e){return ut[Sn(e)]}function gu(e){return ut[ut.length-1].node===e}function Il(){return ut.filter(e=>e.isPointerBlocking)}function fu(){return[...Il()].slice(-1)[0]}function Zo(){return Il().length>0}function Pl(e){const t=Sn(fu()?.node);return Sn(e)<t}function pu(e){ut.push(e)}function hu(e){const t=Sn(e);t<0||ut.splice(t,1)}function mu(){for(const{node:e}of ut)e.style.pointerEvents=Pl(e)?"none":"auto"}function vu(e){if(Zo()&&!Io){const t=nt(e);Rl=document.body.style.pointerEvents,t.body.style.pointerEvents="none",Io=!0}}function xu(e){if(Zo())return;const t=nt(e);t.body.style.pointerEvents=Rl,t.body.style.length===0&&t.body.removeAttribute("style"),Io=!1}var Le={layers:ut,isTopMostLayer:gu,hasPointerBlockingLayer:Zo,isBelowPointerBlockingLayer:Pl,addLayer:pu,removeLayer:hu,indexOf:Sn,find:uu,assignPointerEventToLayers:mu,disableBodyPointerEvents:vu,restoreBodyPointerEvents:xu},so="focusScope.autoFocusOnMount",ao="focusScope.autoFocusOnUnmount",ui={bubbles:!1,cancelable:!0},gi={stack:[],active(){return this.stack[0]},add(e){e!==this.active()&&this.active()?.pause(),this.stack=yo(this.stack,e),this.stack.unshift(e)},remove(e){this.stack=yo(this.stack,e),this.active()?.resume()}};function bu(e,t){const[n,o]=W(!1),r={pause(){o(!0)},resume(){o(!1)}};let i=null;const l=h=>e.onMountAutoFocus?.(h),s=h=>e.onUnmountAutoFocus?.(h),a=()=>nt(t()),d=()=>{const h=a().createElement("span");return h.setAttribute("data-focus-trap",""),h.tabIndex=0,Object.assign(h.style,$c),h},m=()=>{const h=t();return h?el(h,!0).filter(y=>!y.hasAttribute("data-focus-trap")):[]},f=()=>{const h=m();return h.length>0?h[0]:null},p=()=>{const h=m();return h.length>0?h[h.length-1]:null},u=()=>{const h=t();if(!h)return!1;const y=mn(h);return!y||Ke(h,y)?!1:tl(y)};Y(()=>{const h=t();if(!h)return;gi.add(r);const y=mn(h);if(!Ke(h,y)){const x=new CustomEvent(so,ui);h.addEventListener(so,l),h.dispatchEvent(x),x.defaultPrevented||setTimeout(()=>{Pe(f()),mn(h)===y&&Pe(h)},0)}Q(()=>{h.removeEventListener(so,l),setTimeout(()=>{const x=new CustomEvent(ao,ui);u()&&x.preventDefault(),h.addEventListener(ao,s),h.dispatchEvent(x),x.defaultPrevented||Pe(y??a().body),h.removeEventListener(ao,s),gi.remove(r)},0)})}),Y(()=>{const h=t();if(!h||!z(e.trapFocus)||n())return;const y=x=>{const b=x.target;b?.closest(`[${Wn}]`)||(Ke(h,b)?i=b:Pe(i))},S=x=>{const v=x.relatedTarget??mn(h);v?.closest(`[${Wn}]`)||Ke(h,v)||Pe(i)};a().addEventListener("focusin",y),a().addEventListener("focusout",S),Q(()=>{a().removeEventListener("focusin",y),a().removeEventListener("focusout",S)})}),Y(()=>{const h=t();if(!h||!z(e.trapFocus)||n())return;const y=d();h.insertAdjacentElement("afterbegin",y);const S=d();h.insertAdjacentElement("beforeend",S);function x(v){const _=f(),C=p();v.relatedTarget===_?Pe(C):Pe(_)}y.addEventListener("focusin",x),S.addEventListener("focusin",x);const b=new MutationObserver(v=>{for(const _ of v)_.previousSibling===S&&(S.remove(),h.insertAdjacentElement("beforeend",S)),_.nextSibling===y&&(y.remove(),h.insertAdjacentElement("afterbegin",y))});b.observe(h,{childList:!0,subtree:!1}),Q(()=>{y.removeEventListener("focusin",x),S.removeEventListener("focusin",x),y.remove(),S.remove(),b.disconnect()})})}var yu="data-live-announcer";function Su(e){Y(()=>{z(e.isDisabled)||Q(wu(z(e.targets),z(e.root)))})}var hn=new WeakMap,Be=[];function wu(e,t=document.body){const n=new Set(e),o=new Set,r=a=>{for(const p of a.querySelectorAll(`[${yu}], [${Wn}]`))n.add(p);const d=p=>{if(n.has(p)||p.parentElement&&o.has(p.parentElement)&&p.parentElement.getAttribute("role")!=="row")return NodeFilter.FILTER_REJECT;for(const u of n)if(p.contains(u))return NodeFilter.FILTER_SKIP;return NodeFilter.FILTER_ACCEPT},m=document.createTreeWalker(a,NodeFilter.SHOW_ELEMENT,{acceptNode:d}),f=d(a);if(f===NodeFilter.FILTER_ACCEPT&&i(a),f!==NodeFilter.FILTER_REJECT){let p=m.nextNode();for(;p!=null;)i(p),p=m.nextNode()}},i=a=>{const d=hn.get(a)??0;a.getAttribute("aria-hidden")==="true"&&d===0||(d===0&&a.setAttribute("aria-hidden","true"),o.add(a),hn.set(a,d+1))};Be.length&&Be[Be.length-1].disconnect(),r(t);const l=new MutationObserver(a=>{for(const d of a)if(!(d.type!=="childList"||d.addedNodes.length===0)&&![...n,...o].some(m=>m.contains(d.target))){for(const m of d.removedNodes)m instanceof Element&&(n.delete(m),o.delete(m));for(const m of d.addedNodes)(m instanceof HTMLElement||m instanceof SVGElement)&&(m.dataset.liveAnnouncer==="true"||m.dataset.reactAriaTopLayer==="true")?n.add(m):m instanceof Element&&r(m)}});l.observe(t,{childList:!0,subtree:!0});const s={observe(){l.observe(t,{childList:!0,subtree:!0})},disconnect(){l.disconnect()}};return Be.push(s),()=>{l.disconnect();for(const a of o){const d=hn.get(a);if(d==null)return;d===1?(a.removeAttribute("aria-hidden"),hn.delete(a)):hn.set(a,d-1)}s===Be[Be.length-1]?(Be.pop(),Be.length&&Be[Be.length-1].observe()):Be.splice(Be.indexOf(s),1)}}var fi="interactOutside.pointerDownOutside",pi="interactOutside.focusOutside";function Cu(e,t){let n,o=Ic;const r=()=>nt(t()),i=f=>e.onPointerDownOutside?.(f),l=f=>e.onFocusOutside?.(f),s=f=>e.onInteractOutside?.(f),a=f=>{const p=f.target;return!(p instanceof Element)||p.closest(`[${Wn}]`)||!Ke(r(),p)||Ke(t(),p)?!1:!e.shouldExcludeElement?.(p)},d=f=>{function p(){const u=t(),h=f.target;if(!u||!h||!a(f))return;const y=ye([i,s]);h.addEventListener(fi,y,{once:!0});const S=new CustomEvent(fi,{bubbles:!1,cancelable:!0,detail:{originalEvent:f,isContextMenu:f.button===2||hc(f)&&f.button===0}});h.dispatchEvent(S)}f.pointerType==="touch"?(r().removeEventListener("click",p),o=p,r().addEventListener("click",p,{once:!0})):p()},m=f=>{const p=t(),u=f.target;if(!p||!u||!a(f))return;const h=ye([l,s]);u.addEventListener(pi,h,{once:!0});const y=new CustomEvent(pi,{bubbles:!1,cancelable:!0,detail:{originalEvent:f,isContextMenu:!1}});u.dispatchEvent(y)};Y(()=>{z(e.isDisabled)||(n=window.setTimeout(()=>{r().addEventListener("pointerdown",d,!0)},0),r().addEventListener("focusin",m,!0),Q(()=>{window.clearTimeout(n),r().removeEventListener("click",o),r().removeEventListener("pointerdown",d,!0),r().removeEventListener("focusin",m,!0)}))})}function _u(e){const t=n=>{n.key===Ji.Escape&&e.onEscapeKeyDown?.(n)};Y(()=>{if(z(e.isDisabled))return;const n=e.ownerDocument?.()??nt();n.addEventListener("keydown",t),Q(()=>{n.removeEventListener("keydown",t)})})}var kl=Xe();function Ru(){return qe(kl)}function Iu(e){let t;const n=Ru(),[o,r]=re(e,["ref","disableOutsidePointerEvents","excludedElements","onEscapeKeyDown","onPointerDownOutside","onFocusOutside","onInteractOutside","onDismiss","bypassTopMostLayerCheck"]),i=new Set([]),l=f=>{i.add(f);const p=n?.registerNestedLayer(f);return()=>{i.delete(f),p?.()}};Cu({shouldExcludeElement:f=>t?o.excludedElements?.some(p=>Ke(p(),f))||[...i].some(p=>Ke(p,f)):!1,onPointerDownOutside:f=>{!t||Le.isBelowPointerBlockingLayer(t)||!o.bypassTopMostLayerCheck&&!Le.isTopMostLayer(t)||(o.onPointerDownOutside?.(f),o.onInteractOutside?.(f),f.defaultPrevented||o.onDismiss?.())},onFocusOutside:f=>{o.onFocusOutside?.(f),o.onInteractOutside?.(f),f.defaultPrevented||o.onDismiss?.()}},()=>t),_u({ownerDocument:()=>nt(t),onEscapeKeyDown:f=>{!t||!Le.isTopMostLayer(t)||(o.onEscapeKeyDown?.(f),!f.defaultPrevented&&o.onDismiss&&(f.preventDefault(),o.onDismiss()))}}),gt(()=>{if(!t)return;Le.addLayer({node:t,isPointerBlocking:o.disableOutsidePointerEvents,dismiss:o.onDismiss});const f=n?.registerNestedLayer(t);Le.assignPointerEventToLayers(),Le.disableBodyPointerEvents(t),Q(()=>{t&&(Le.removeLayer(t),f?.(),Le.assignPointerEventToLayers(),Le.restoreBodyPointerEvents(t))})}),Y(_n([()=>t,()=>o.disableOutsidePointerEvents],([f,p])=>{if(!f)return;const u=Le.find(f);u&&u.isPointerBlocking!==p&&(u.isPointerBlocking=p,Le.assignPointerEventToLayers()),p&&Le.disableBodyPointerEvents(f),Q(()=>{Le.restoreBodyPointerEvents(f)})},{defer:!0}));const m={registerNestedLayer:l};return R(kl.Provider,{value:m,get children(){return R(De,q({as:"div",ref(f){var p=Ye(u=>t=u,o.ref);typeof p=="function"&&p(f)}},r))}})}function $l(e={}){const[t,n]=vl({value:()=>z(e.open),defaultValue:()=>!!z(e.defaultOpen),onChange:l=>e.onOpenChange?.(l)}),o=()=>{n(!0)},r=()=>{n(!1)};return{isOpen:t,setIsOpen:n,open:o,close:r,toggle:()=>{t()?r():o()}}}function Jo(e,t){const[n,o]=W(hi(t?.()));return Y(()=>{o(e()?.tagName.toLowerCase()||hi(t?.()))}),n}function hi(e){return jt(e)?e:void 0}var Pu=Object.defineProperty,Qo=(e,t)=>{for(var n in t)Pu(e,n,{get:t[n],enumerable:!0})},ku={};Qo(ku,{Button:()=>Fu,Root:()=>er});var $u=["button","color","file","image","reset","submit"];function Mu(e){const t=e.tagName.toLowerCase();return t==="button"?!0:t==="input"&&e.type?$u.indexOf(e.type)!==-1:!1}function er(e){let t;const n=fe({type:"button"},e),[o,r]=re(n,["ref","type","disabled"]),i=Jo(()=>t,()=>"button"),l=be(()=>{const d=i();return d==null?!1:Mu({tagName:d,type:o.type})}),s=be(()=>i()==="input"),a=be(()=>i()==="a"&&t?.getAttribute("href")!=null);return R(De,q({as:"button",ref(d){var m=Ye(f=>t=f,o.ref);typeof m=="function"&&m(d)},get type(){return ee(()=>!!(l()||s()))()?o.type:void 0},get role(){return!l()&&!a()?"button":void 0},get tabIndex(){return!l()&&!a()&&!o.disabled?0:void 0},get disabled(){return ee(()=>!!(l()||s()))()?o.disabled:void 0},get"aria-disabled"(){return!l()&&!s()&&o.disabled?!0:void 0},get"data-disabled"(){return o.disabled?"":void 0}},r))}var Fu=er;function Eu(e={}){const[t,n]=vl({value:()=>z(e.isSelected),defaultValue:()=>!!z(e.defaultIsSelected),onChange:i=>e.onSelectedChange?.(i)});return{isSelected:t,setIsSelected:i=>{!z(e.isReadOnly)&&!z(e.isDisabled)&&n(i)},toggle:()=>{!z(e.isReadOnly)&&!z(e.isDisabled)&&n(!t())}}}function wn(e){return t=>(e(t),()=>e(void 0))}var Ie=e=>typeof e=="function"?e():e,Po=(e,t)=>{if(e.contains(t))return!0;let n=t;for(;n;){if(n===e)return!0;n=n._$host??n.parentElement}return!1},Fn=new Map,Au=e=>{Y(()=>{const t=Ie(e.style)??{},n=Ie(e.properties)??[],o={};for(const i in t)o[i]=e.element.style[i];const r=Fn.get(e.key);r?r.activeCount++:Fn.set(e.key,{activeCount:1,originalStyles:o,properties:n.map(i=>i.key)}),Object.assign(e.element.style,e.style);for(const i of n)e.element.style.setProperty(i.key,i.value);Q(()=>{const i=Fn.get(e.key);if(i){if(i.activeCount!==1){i.activeCount--;return}Fn.delete(e.key);for(const[l,s]of Object.entries(i.originalStyles))e.element.style[l]=s;for(const l of i.properties)e.element.style.removeProperty(l);e.element.style.length===0&&e.element.removeAttribute("style"),e.cleanup?.()}})})},mi=Au,Du=(e,t)=>{switch(t){case"x":return[e.clientWidth,e.scrollLeft,e.scrollWidth];case"y":return[e.clientHeight,e.scrollTop,e.scrollHeight]}},Tu=(e,t)=>{const n=getComputedStyle(e),o=t==="x"?n.overflowX:n.overflowY;return o==="auto"||o==="scroll"||e.tagName==="HTML"&&o==="visible"},Lu=(e,t,n)=>{const o=t==="x"&&window.getComputedStyle(e).direction==="rtl"?-1:1;let r=e,i=0,l=0,s=!1;do{const[a,d,m]=Du(r,t),f=m-a-o*d;(d!==0||f!==0)&&Tu(r,t)&&(i+=f,l+=d),r===(n??document.documentElement)?s=!0:r=r._$host??r.parentElement}while(r&&!s);return[i,l]},[vi,xi]=W([]),zu=e=>vi().indexOf(e)===vi().length-1,Ou=e=>{const t=q({element:null,enabled:!0,hideScrollbar:!0,preventScrollbarShift:!0,preventScrollbarShiftMode:"padding",restoreScrollPosition:!0,allowPinchZoom:!1},e),n=Dt();let o=[0,0],r=null,i=null;Y(()=>{Ie(t.enabled)&&(xi(d=>[...d,n]),Q(()=>{xi(d=>d.filter(m=>m!==n))}))}),Y(()=>{if(!Ie(t.enabled)||!Ie(t.hideScrollbar))return;const{body:d}=document,m=window.innerWidth-d.offsetWidth;if(Ie(t.preventScrollbarShift)){const f={overflow:"hidden"},p=[];m>0&&(Ie(t.preventScrollbarShiftMode)==="padding"?f.paddingRight=`calc(${window.getComputedStyle(d).paddingRight} + ${m}px)`:f.marginRight=`calc(${window.getComputedStyle(d).marginRight} + ${m}px)`,p.push({key:"--scrollbar-width",value:`${m}px`}));const u=window.scrollY,h=window.scrollX;mi({key:"prevent-scroll",element:d,style:f,properties:p,cleanup:()=>{Ie(t.restoreScrollPosition)&&m>0&&window.scrollTo(h,u)}})}else mi({key:"prevent-scroll",element:d,style:{overflow:"hidden"}})}),Y(()=>{!zu(n)||!Ie(t.enabled)||(document.addEventListener("wheel",s,{passive:!1}),document.addEventListener("touchstart",l,{passive:!1}),document.addEventListener("touchmove",a,{passive:!1}),Q(()=>{document.removeEventListener("wheel",s),document.removeEventListener("touchstart",l),document.removeEventListener("touchmove",a)}))});const l=d=>{o=bi(d),r=null,i=null},s=d=>{const m=d.target,f=Ie(t.element),p=Vu(d),u=Math.abs(p[0])>Math.abs(p[1])?"x":"y",h=u==="x"?p[0]:p[1],y=yi(m,u,h,f);let S;f&&Po(f,m)?S=!y:S=!0,S&&d.cancelable&&d.preventDefault()},a=d=>{const m=Ie(t.element),f=d.target;let p;if(d.touches.length===2)p=!Ie(t.allowPinchZoom);else{if(r==null||i===null){const u=bi(d).map((y,S)=>o[S]-y),h=Math.abs(u[0])>Math.abs(u[1])?"x":"y";r=h,i=h==="x"?u[0]:u[1]}if(f.type==="range")p=!1;else{const u=yi(f,r,i,m);m&&Po(m,f)?p=!u:p=!0}}p&&d.cancelable&&d.preventDefault()}},Vu=e=>[e.deltaX,e.deltaY],bi=e=>e.changedTouches[0]?[e.changedTouches[0].clientX,e.changedTouches[0].clientY]:[0,0],yi=(e,t,n,o)=>{const r=o!==null&&Po(o,e),[i,l]=Lu(e,t,r?o:void 0);return!(n>0&&Math.abs(i)<=1||n<0&&Math.abs(l)<1)},Bu=Ou,Ku=Bu,Hu=e=>{const t=be(()=>{const l=Ie(e.element);if(l)return getComputedStyle(l)}),n=()=>t()?.animationName??"none",[o,r]=W(Ie(e.show)?"present":"hidden");let i="none";return Y(l=>{const s=Ie(e.show);return ki(()=>{if(l===s)return s;const a=i,d=n();s?r("present"):d==="none"||t()?.display==="none"?r("hidden"):r(l===!0&&a!==d?"hiding":"hidden")}),s}),Y(()=>{const l=Ie(e.element);if(!l)return;const s=d=>{d.target===l&&(i=n())},a=d=>{const f=n().includes(d.animationName);d.target===l&&f&&o()==="hiding"&&r("hidden")};l.addEventListener("animationstart",s),l.addEventListener("animationcancel",a),l.addEventListener("animationend",a),Q(()=>{l.removeEventListener("animationstart",s),l.removeEventListener("animationcancel",a),l.removeEventListener("animationend",a)})}),{present:()=>o()==="present"||o()==="hiding",state:o,setState:r}},Wu=Hu,Nu=Wu,Gu=Xe();function Yn(){return qe(Gu)}var ju=Xe();function Ml(){return qe(ju)}var Fl=Xe();function El(){return qe(Fl)}function pt(){const e=El();if(e===void 0)throw new Error("[kobalte]: `useMenuContext` must be used within a `Menu` component");return e}var Al=Xe();function tr(){const e=qe(Al);if(e===void 0)throw new Error("[kobalte]: `useMenuItemContext` must be used within a `Menu.Item` component");return e}var Dl=Xe();function it(){const e=qe(Dl);if(e===void 0)throw new Error("[kobalte]: `useMenuRootContext` must be used within a `MenuRoot` component");return e}function nr(e){let t;const n=it(),o=pt(),r=fe({id:n.generateId(`item-${Dt()}`)},e),[i,l]=re(r,["ref","textValue","disabled","closeOnSelect","checked","indeterminate","onSelect","onPointerMove","onPointerLeave","onPointerDown","onPointerUp","onClick","onKeyDown","onMouseDown","onFocus"]),[s,a]=W(),[d,m]=W(),[f,p]=W(),u=()=>o.listState().selectionManager(),h=()=>l.id,y=()=>u().focusedKey()===h(),S=()=>{i.onSelect?.(),i.closeOnSelect&&setTimeout(()=>{o.close(!0)})};du({getItem:()=>({ref:()=>t,type:"item",key:h(),textValue:i.textValue??f()?.textContent??t?.textContent??"",disabled:i.disabled??!1})});const x=yl({key:h,selectionManager:u,shouldSelectOnPressUp:!0,allowsDifferentPressOrigin:!0,disabled:()=>i.disabled},()=>t),b=F=>{Se(F,i.onPointerMove),F.pointerType==="mouse"&&(i.disabled?o.onItemLeave(F):(o.onItemEnter(F),F.defaultPrevented||(Pe(F.currentTarget),o.listState().selectionManager().setFocused(!0),o.listState().selectionManager().setFocusedKey(h()))))},v=F=>{Se(F,i.onPointerLeave),F.pointerType==="mouse"&&o.onItemLeave(F)},_=F=>{Se(F,i.onPointerUp),!i.disabled&&F.button===0&&S()},C=F=>{if(Se(F,i.onKeyDown),!F.repeat&&!i.disabled)switch(F.key){case"Enter":case" ":S();break}},M=be(()=>{if(i.indeterminate)return"mixed";if(i.checked!=null)return i.checked}),A=be(()=>({"data-indeterminate":i.indeterminate?"":void 0,"data-checked":i.checked&&!i.indeterminate?"":void 0,"data-disabled":i.disabled?"":void 0,"data-highlighted":y()?"":void 0})),P={isChecked:()=>i.checked,dataset:A,setLabelRef:p,generateId:Vo(()=>l.id),registerLabel:wn(a),registerDescription:wn(m)};return R(Al.Provider,{value:P,get children(){return R(De,q({as:"div",ref(F){var D=Ye(T=>t=T,i.ref);typeof D=="function"&&D(F)},get tabIndex(){return x.tabIndex()},get"aria-checked"(){return M()},get"aria-disabled"(){return i.disabled},get"aria-labelledby"(){return s()},get"aria-describedby"(){return d()},get"data-key"(){return x.dataKey()},get onPointerDown(){return ye([i.onPointerDown,x.onPointerDown])},get onPointerUp(){return ye([_,x.onPointerUp])},get onClick(){return ye([i.onClick,x.onClick])},get onKeyDown(){return ye([C,x.onKeyDown])},get onMouseDown(){return ye([i.onMouseDown,x.onMouseDown])},get onFocus(){return ye([i.onFocus,x.onFocus])},onPointerMove:b,onPointerLeave:v},A,l))}})}function Tl(e){const t=fe({closeOnSelect:!1},e),[n,o]=re(t,["checked","defaultChecked","onChange","onSelect"]),r=Eu({isSelected:()=>n.checked,defaultIsSelected:()=>n.defaultChecked,onSelectedChange:l=>n.onChange?.(l),isDisabled:()=>o.disabled});return R(nr,q({role:"menuitemcheckbox",get checked(){return r.isSelected()},onSelect:()=>{n.onSelect?.(),r.toggle()}},o))}var Cn={next:(e,t)=>e==="ltr"?t==="horizontal"?"ArrowRight":"ArrowDown":t==="horizontal"?"ArrowLeft":"ArrowUp",previous:(e,t)=>Cn.next(e==="ltr"?"rtl":"ltr",t)},Si={first:e=>e==="horizontal"?"ArrowDown":"ArrowRight",last:e=>e==="horizontal"?"ArrowUp":"ArrowLeft"};function Ll(e){const t=it(),n=pt(),o=Yn(),{direction:r}=Tt(),i=fe({id:t.generateId("trigger")},e),[l,s]=re(i,["ref","id","disabled","onPointerDown","onClick","onKeyDown","onMouseOver","onFocus"]);let a=()=>t.value();o!==void 0&&(a=()=>t.value()??l.id,o.lastValue()===void 0&&o.setLastValue(a));const d=Jo(()=>n.triggerRef(),()=>"button"),m=be(()=>d()==="a"&&n.triggerRef()?.getAttribute("href")!=null);Y(_n(()=>o?.value(),x=>{m()&&x===a()&&n.triggerRef()?.focus()}));const f=()=>{o!==void 0?n.isOpen()?o.value()===a()&&o.closeMenu():(o.autoFocusMenu()||o.setAutoFocusMenu(!0),n.open(!1)):n.toggle(!0)},p=x=>{Se(x,l.onPointerDown),x.currentTarget.dataset.pointerType=x.pointerType,!l.disabled&&x.pointerType!=="touch"&&x.button===0&&f()},u=x=>{Se(x,l.onClick),l.disabled||x.currentTarget.dataset.pointerType==="touch"&&f()},h=x=>{if(Se(x,l.onKeyDown),!l.disabled){if(m())switch(x.key){case"Enter":case" ":return}switch(x.key){case"Enter":case" ":case Si.first(t.orientation()):x.stopPropagation(),x.preventDefault(),kc(x.currentTarget),n.open("first"),o?.setAutoFocusMenu(!0),o?.setValue(a);break;case Si.last(t.orientation()):x.stopPropagation(),x.preventDefault(),n.open("last");break;case Cn.next(r(),t.orientation()):if(o===void 0)break;x.stopPropagation(),x.preventDefault(),o.nextMenu();break;case Cn.previous(r(),t.orientation()):if(o===void 0)break;x.stopPropagation(),x.preventDefault(),o.previousMenu();break}}},y=x=>{Se(x,l.onMouseOver),n.triggerRef()?.dataset.pointerType!=="touch"&&!l.disabled&&o!==void 0&&o.value()!==void 0&&o.setValue(a)},S=x=>{Se(x,l.onFocus),o!==void 0&&x.currentTarget.dataset.pointerType!=="touch"&&o.setValue(a)};return Y(()=>Q(n.registerTriggerId(l.id))),R(er,q({ref(x){var b=Ye(n.setTriggerRef,l.ref);typeof b=="function"&&b(x)},get"data-kb-menu-value-trigger"(){return t.value()},get id(){return l.id},get disabled(){return l.disabled},"aria-haspopup":"true",get"aria-expanded"(){return n.isOpen()},get"aria-controls"(){return ee(()=>!!n.isOpen())()?n.contentId():void 0},get"data-highlighted"(){return a()!==void 0&&o?.value()===a()?!0:void 0},get tabIndex(){return o!==void 0?o.value()===a()||o.lastValue()===a()?0:-1:void 0},onPointerDown:p,onMouseOver:y,onClick:u,onKeyDown:h,onFocus:S,role:o!==void 0?"menuitem":void 0},()=>n.dataset(),s))}function zl(e){let t;const n=it(),o=pt(),r=Yn(),i=Ml(),{direction:l}=Tt(),s=fe({id:n.generateId(`content-${Dt()}`)},e),[a,d]=re(s,["ref","id","style","onOpenAutoFocus","onCloseAutoFocus","onEscapeKeyDown","onFocusOutside","onPointerEnter","onPointerMove","onKeyDown","onMouseDown","onFocusIn","onFocusOut"]);let m=0;const f=()=>o.parentMenuContext()==null&&r===void 0&&n.isModal(),p=nu({selectionManager:o.listState().selectionManager,collection:o.listState().collection,autoFocus:o.autoFocus,deferAutoFocus:!0,shouldFocusWrap:!0,disallowTypeAhead:()=>!o.listState().selectionManager().isFocused(),orientation:()=>n.orientation()==="horizontal"?"vertical":"horizontal"},()=>t);bu({trapFocus:()=>f()&&o.isOpen(),onMountAutoFocus:v=>{r===void 0&&a.onOpenAutoFocus?.(v)},onUnmountAutoFocus:a.onCloseAutoFocus},()=>t);const u=v=>{if(Ke(v.currentTarget,v.target)&&(v.key==="Tab"&&o.isOpen()&&v.preventDefault(),r!==void 0&&v.currentTarget.getAttribute("aria-haspopup")!=="true"))switch(v.key){case Cn.next(l(),n.orientation()):v.stopPropagation(),v.preventDefault(),o.close(!0),r.setAutoFocusMenu(!0),r.nextMenu();break;case Cn.previous(l(),n.orientation()):if(v.currentTarget.hasAttribute("data-closed"))break;v.stopPropagation(),v.preventDefault(),o.close(!0),r.setAutoFocusMenu(!0),r.previousMenu();break}},h=v=>{a.onEscapeKeyDown?.(v),r?.setAutoFocusMenu(!1),o.close(!0)},y=v=>{a.onFocusOutside?.(v),n.isModal()&&v.preventDefault()},S=v=>{Se(v,a.onPointerEnter),o.isOpen()&&(o.parentMenuContext()?.listState().selectionManager().setFocused(!1),o.parentMenuContext()?.listState().selectionManager().setFocusedKey(void 0))},x=v=>{if(Se(v,a.onPointerMove),v.pointerType!=="mouse")return;const _=v.target,C=m!==v.clientX;Ke(v.currentTarget,_)&&C&&(o.setPointerDir(v.clientX>m?"right":"left"),m=v.clientX)};Y(()=>Q(o.registerContentId(a.id))),Q(()=>o.setContentRef(void 0));const b={ref:Ye(v=>{o.setContentRef(v),t=v},a.ref),role:"menu",get id(){return a.id},get tabIndex(){return p.tabIndex()},get"aria-labelledby"(){return o.triggerId()},onKeyDown:ye([a.onKeyDown,p.onKeyDown,u]),onMouseDown:ye([a.onMouseDown,p.onMouseDown]),onFocusIn:ye([a.onFocusIn,p.onFocusIn]),onFocusOut:ye([a.onFocusOut,p.onFocusOut]),onPointerEnter:S,onPointerMove:x,get"data-orientation"(){return n.orientation()}};return R(ce,{get when(){return o.contentPresent()},get children(){return R(ce,{get when(){return i===void 0||o.parentMenuContext()!=null},get fallback(){return R(De,q({as:"div"},()=>o.dataset(),b,d))},get children(){return R(hl.Positioner,{get children(){return R(Iu,q({get disableOutsidePointerEvents(){return ee(()=>!!f())()&&o.isOpen()},get excludedElements(){return[o.triggerRef]},bypassTopMostLayerCheck:!0,get style(){return Oo({"--kb-menu-content-transform-origin":"var(--kb-popper-content-transform-origin)",position:"relative"},a.style)},onEscapeKeyDown:h,onFocusOutside:y,get onDismiss(){return o.close}},()=>o.dataset(),b,d))}})}})}})}function Uu(e){let t;const n=it(),o=pt(),[r,i]=re(e,["ref"]);return Ku({element:()=>t??null,enabled:()=>o.contentPresent()&&n.preventScroll()}),R(zl,q({ref(l){var s=Ye(a=>{t=a},r.ref);typeof s=="function"&&s(l)}},i))}var Ol=Xe();function qu(){const e=qe(Ol);if(e===void 0)throw new Error("[kobalte]: `useMenuGroupContext` must be used within a `Menu.Group` component");return e}function or(e){const t=it(),n=fe({id:t.generateId(`group-${Dt()}`)},e),[o,r]=W(),i={generateId:Vo(()=>n.id),registerLabelId:wn(r)};return R(Ol.Provider,{value:i,get children(){return R(De,q({as:"div",role:"group",get"aria-labelledby"(){return o()}},n))}})}function Vl(e){const t=qu(),n=fe({id:t.generateId("label")},e),[o,r]=re(n,["id"]);return Y(()=>Q(t.registerLabelId(o.id))),R(De,q({as:"span",get id(){return o.id},"aria-hidden":"true"},r))}function Bl(e){const t=pt(),n=fe({children:"▼"},e);return R(De,q({as:"span","aria-hidden":"true"},()=>t.dataset(),n))}function Kl(e){return R(nr,q({role:"menuitem",closeOnSelect:!0},e))}function Hl(e){const t=tr(),n=fe({id:t.generateId("description")},e),[o,r]=re(n,["id"]);return Y(()=>Q(t.registerDescription(o.id))),R(De,q({as:"div",get id(){return o.id}},()=>t.dataset(),r))}function Wl(e){const t=tr(),n=fe({id:t.generateId("indicator")},e),[o,r]=re(n,["forceMount"]);return R(ce,{get when(){return o.forceMount||t.isChecked()},get children(){return R(De,q({as:"div"},()=>t.dataset(),r))}})}function Nl(e){const t=tr(),n=fe({id:t.generateId("label")},e),[o,r]=re(n,["ref","id"]);return Y(()=>Q(t.registerLabel(o.id))),R(De,q({as:"div",ref(i){var l=Ye(t.setLabelRef,o.ref);typeof l=="function"&&l(i)},get id(){return o.id}},()=>t.dataset(),r))}function Gl(e){const t=pt();return R(ce,{get when(){return t.contentPresent()},get children(){return R(Mo,e)}})}var jl=Xe();function Xu(){const e=qe(jl);if(e===void 0)throw new Error("[kobalte]: `useMenuRadioGroupContext` must be used within a `Menu.RadioGroup` component");return e}function Ul(e){const n=it().generateId(`radiogroup-${Dt()}`),o=fe({id:n},e),[r,i]=re(o,["value","defaultValue","onChange","disabled"]),[l,s]=Xn({value:()=>r.value,defaultValue:()=>r.defaultValue,onChange:d=>r.onChange?.(d)}),a={isDisabled:()=>r.disabled,isSelectedValue:d=>d===l(),setSelectedValue:d=>s(d)};return R(jl.Provider,{value:a,get children(){return R(or,i)}})}function ql(e){const t=Xu(),n=fe({closeOnSelect:!1},e),[o,r]=re(n,["value","onSelect"]);return R(nr,q({role:"menuitemradio",get checked(){return t.isSelectedValue(o.value)},onSelect:()=>{o.onSelect?.(),t.setSelectedValue(o.value)}},r))}function Yu(e,t,n){const o=e.split("-")[0],r=n.getBoundingClientRect(),i=[],l=t.clientX,s=t.clientY;switch(o){case"top":i.push([l,s+5]),i.push([r.left,r.bottom]),i.push([r.left,r.top]),i.push([r.right,r.top]),i.push([r.right,r.bottom]);break;case"right":i.push([l-5,s]),i.push([r.left,r.top]),i.push([r.right,r.top]),i.push([r.right,r.bottom]),i.push([r.left,r.bottom]);break;case"bottom":i.push([l,s-5]),i.push([r.right,r.top]),i.push([r.right,r.bottom]),i.push([r.left,r.bottom]),i.push([r.left,r.top]);break;case"left":i.push([l+5,s]),i.push([r.right,r.bottom]),i.push([r.left,r.bottom]),i.push([r.left,r.top]),i.push([r.right,r.top]);break}return i}function Zu(e,t){return t?Pc([e.clientX,e.clientY],t):!1}function Xl(e){const t=it(),n=wl(),o=El(),r=Yn(),i=Ml(),l=fe({placement:t.orientation()==="horizontal"?"bottom-start":"right-start"},e),[s,a]=re(l,["open","defaultOpen","onOpenChange"]);let d=0,m=null,f="right";const[p,u]=W(),[h,y]=W(),[S,x]=W(),[b,v]=W(),[_,C]=W(!0),[M,A]=W(a.placement),[P,F]=W([]),[D,T]=W([]),{DomCollectionProvider:k}=cu({items:D,onItemsChange:T}),L=$l({open:()=>s.open,defaultOpen:()=>s.defaultOpen,onOpenChange:I=>s.onOpenChange?.(I)}),{present:B}=Nu({show:()=>t.forceMount()||L.isOpen(),element:()=>b()??null}),K=eu({selectionMode:"none",dataSource:D}),j=I=>{C(I),L.open()},H=(I=!1)=>{L.close(),I&&o&&o.close(!0)},U=I=>{C(I),L.toggle()},Z=()=>{const I=b();I&&(Pe(I),K.selectionManager().setFocused(!0),K.selectionManager().setFocusedKey(void 0))},J=()=>{i!=null?setTimeout(()=>Z()):Z()},E=I=>{F(se=>[...se,I]);const X=o?.registerNestedMenu(I);return()=>{F(se=>yo(se,I)),X?.()}},te=I=>f===m?.side&&Zu(I,m?.area),ae=I=>{te(I)&&I.preventDefault()},pe=I=>{te(I)||J()},he=I=>{te(I)&&I.preventDefault()};Su({isDisabled:()=>!(o==null&&L.isOpen()&&t.isModal()),targets:()=>[b(),...P()].filter(Boolean)}),Y(()=>{const I=b();if(!I||!o)return;const X=o.registerNestedMenu(I);Q(()=>{X()})}),Y(()=>{o===void 0&&r?.registerMenu(t.value(),[b(),...P()])}),Y(()=>{o!==void 0||r===void 0||(r.value()===t.value()?(S()?.focus(),r.autoFocusMenu()&&j(!0)):H())}),Y(()=>{o!==void 0||r===void 0||L.isOpen()&&r.setValue(t.value())}),Q(()=>{o===void 0&&r?.unregisterMenu(t.value())});const Ce={dataset:be(()=>({"data-expanded":L.isOpen()?"":void 0,"data-closed":L.isOpen()?void 0:""})),isOpen:L.isOpen,contentPresent:B,nestedMenus:P,currentPlacement:M,pointerGraceTimeoutId:()=>d,autoFocus:_,listState:()=>K,parentMenuContext:()=>o,triggerRef:S,contentRef:b,triggerId:p,contentId:h,setTriggerRef:x,setContentRef:v,open:j,close:H,toggle:U,focusContent:J,onItemEnter:ae,onItemLeave:pe,onTriggerLeave:he,setPointerDir:I=>f=I,setPointerGraceTimeoutId:I=>d=I,setPointerGraceIntent:I=>m=I,registerNestedMenu:E,registerItemToParentDomCollection:n?.registerItem,registerTriggerId:wn(u),registerContentId:wn(y)};return R(k,{get children(){return R(Fl.Provider,{value:Ce,get children(){return R(ce,{when:i===void 0,get fallback(){return a.children},get children(){return R(hl,q({anchorRef:S,contentRef:b,onCurrentPlacementChange:A},a))}})}})}})}function Yl(e){const{direction:t}=Tt();return R(Xl,q({get placement(){return t()==="rtl"?"left-start":"right-start"},flip:!0},e))}var Ju={close:(e,t)=>e==="ltr"?[t==="horizontal"?"ArrowLeft":"ArrowUp"]:[t==="horizontal"?"ArrowRight":"ArrowDown"]};function Zl(e){const t=pt(),n=it(),[o,r]=re(e,["onFocusOutside","onKeyDown"]),{direction:i}=Tt();return R(zl,q({onOpenAutoFocus:m=>{m.preventDefault()},onCloseAutoFocus:m=>{m.preventDefault()},onFocusOutside:m=>{o.onFocusOutside?.(m);const f=m.target;Ke(t.triggerRef(),f)||t.close()},onKeyDown:m=>{Se(m,o.onKeyDown);const f=Ke(m.currentTarget,m.target),p=Ju.close(i(),n.orientation()).includes(m.key),u=t.parentMenuContext()!=null;f&&p&&u&&(t.close(),Pe(t.triggerRef()))}},r))}var wi=["Enter"," "],Qu={open:(e,t)=>e==="ltr"?[...wi,t==="horizontal"?"ArrowRight":"ArrowDown"]:[...wi,t==="horizontal"?"ArrowLeft":"ArrowUp"]};function Jl(e){let t;const n=it(),o=pt(),r=fe({id:n.generateId(`sub-trigger-${Dt()}`)},e),[i,l]=re(r,["ref","id","textValue","disabled","onPointerMove","onPointerLeave","onPointerDown","onPointerUp","onClick","onKeyDown","onMouseDown","onFocus"]);let s=null;const a=()=>{s&&window.clearTimeout(s),s=null},{direction:d}=Tt(),m=()=>i.id,f=()=>{const v=o.parentMenuContext();if(v==null)throw new Error("[kobalte]: `Menu.SubTrigger` must be used within a `Menu.Sub` component");return v.listState().selectionManager()},p=()=>o.listState().collection(),u=()=>f().focusedKey()===m(),h=yl({key:m,selectionManager:f,shouldSelectOnPressUp:!0,allowsDifferentPressOrigin:!0,disabled:()=>i.disabled},()=>t),y=v=>{Se(v,i.onClick),!o.isOpen()&&!i.disabled&&o.open(!0)},S=v=>{if(Se(v,i.onPointerMove),v.pointerType!=="mouse")return;const _=o.parentMenuContext();if(_?.onItemEnter(v),!v.defaultPrevented){if(i.disabled){_?.onItemLeave(v);return}!o.isOpen()&&!s&&(o.parentMenuContext()?.setPointerGraceIntent(null),s=window.setTimeout(()=>{o.open(!1),a()},100)),_?.onItemEnter(v),v.defaultPrevented||(o.listState().selectionManager().isFocused()&&(o.listState().selectionManager().setFocused(!1),o.listState().selectionManager().setFocusedKey(void 0)),Pe(v.currentTarget),_?.listState().selectionManager().setFocused(!0),_?.listState().selectionManager().setFocusedKey(m()))}},x=v=>{if(Se(v,i.onPointerLeave),v.pointerType!=="mouse")return;a();const _=o.parentMenuContext(),C=o.contentRef();if(C){_?.setPointerGraceIntent({area:Yu(o.currentPlacement(),v,C),side:o.currentPlacement().split("-")[0]}),window.clearTimeout(_?.pointerGraceTimeoutId());const M=window.setTimeout(()=>{_?.setPointerGraceIntent(null)},300);_?.setPointerGraceTimeoutId(M)}else{if(_?.onTriggerLeave(v),v.defaultPrevented)return;_?.setPointerGraceIntent(null)}_?.onItemLeave(v)},b=v=>{Se(v,i.onKeyDown),!v.repeat&&(i.disabled||Qu.open(d(),n.orientation()).includes(v.key)&&(v.stopPropagation(),v.preventDefault(),f().setFocused(!1),f().setFocusedKey(void 0),o.isOpen()||o.open("first"),o.focusContent(),o.listState().selectionManager().setFocused(!0),o.listState().selectionManager().setFocusedKey(p().getFirstKey())))};return Y(()=>{if(o.registerItemToParentDomCollection==null)throw new Error("[kobalte]: `Menu.SubTrigger` must be used within a `Menu.Sub` component");const v=o.registerItemToParentDomCollection({ref:()=>t,type:"item",key:m(),textValue:i.textValue??t?.textContent??"",disabled:i.disabled??!1});Q(v)}),Y(_n(()=>o.parentMenuContext()?.pointerGraceTimeoutId(),v=>{Q(()=>{window.clearTimeout(v),o.parentMenuContext()?.setPointerGraceIntent(null)})})),Y(()=>Q(o.registerTriggerId(i.id))),Q(()=>{a()}),R(De,q({as:"div",ref(v){var _=Ye(C=>{o.setTriggerRef(C),t=C},i.ref);typeof _=="function"&&_(v)},get id(){return i.id},role:"menuitem",get tabIndex(){return h.tabIndex()},"aria-haspopup":"true",get"aria-expanded"(){return o.isOpen()},get"aria-controls"(){return ee(()=>!!o.isOpen())()?o.contentId():void 0},get"aria-disabled"(){return i.disabled},get"data-key"(){return h.dataKey()},get"data-highlighted"(){return u()?"":void 0},get"data-disabled"(){return i.disabled?"":void 0},get onPointerDown(){return ye([i.onPointerDown,h.onPointerDown])},get onPointerUp(){return ye([i.onPointerUp,h.onPointerUp])},get onClick(){return ye([y,h.onClick])},get onKeyDown(){return ye([b,h.onKeyDown])},get onMouseDown(){return ye([i.onMouseDown,h.onMouseDown])},get onFocus(){return ye([i.onFocus,h.onFocus])},onPointerMove:S,onPointerLeave:x},()=>o.dataset(),l))}function eg(e){const t=Yn(),n=`menu-${Dt()}`,o=fe({id:n,modal:!0},e),[r,i]=re(o,["id","modal","preventScroll","forceMount","open","defaultOpen","onOpenChange","value","orientation"]),l=$l({open:()=>r.open,defaultOpen:()=>r.defaultOpen,onOpenChange:a=>r.onOpenChange?.(a)}),s={isModal:()=>r.modal??!0,preventScroll:()=>r.preventScroll??s.isModal(),forceMount:()=>r.forceMount??!1,generateId:Vo(()=>r.id),value:()=>r.value,orientation:()=>r.orientation??t?.orientation()??"horizontal"};return R(Dl.Provider,{value:s,get children(){return R(Xl,q({get open(){return l.isOpen()},get onOpenChange(){return l.setIsOpen}},i))}})}var tg={};Qo(tg,{Root:()=>Zn,Separator:()=>ng});function Zn(e){let t;const n=fe({orientation:"horizontal"},e),[o,r]=re(n,["ref","orientation"]),i=Jo(()=>t,()=>"hr");return R(De,q({as:"hr",ref(l){var s=Ye(a=>t=a,o.ref);typeof s=="function"&&s(l)},get role(){return i()!=="hr"?"separator":void 0},get"aria-orientation"(){return o.orientation==="vertical"?"vertical":void 0},get"data-orientation"(){return o.orientation}},r))}var ng=Zn,og={};Qo(og,{Arrow:()=>Yo,CheckboxItem:()=>Tl,Content:()=>Ql,DropdownMenu:()=>qt,Group:()=>or,GroupLabel:()=>Vl,Icon:()=>Bl,Item:()=>Kl,ItemDescription:()=>Hl,ItemIndicator:()=>Wl,ItemLabel:()=>Nl,Portal:()=>Gl,RadioGroup:()=>Ul,RadioItem:()=>ql,Root:()=>es,Separator:()=>Zn,Sub:()=>Yl,SubContent:()=>Zl,SubTrigger:()=>Jl,Trigger:()=>Ll});function Ql(e){const t=it(),n=pt(),[o,r]=re(e,["onCloseAutoFocus","onInteractOutside"]);let i=!1;return R(Uu,q({onCloseAutoFocus:a=>{o.onCloseAutoFocus?.(a),i||Pe(n.triggerRef()),i=!1,a.preventDefault()},onInteractOutside:a=>{o.onInteractOutside?.(a),(!t.isModal()||a.detail.isContextMenu)&&(i=!0)}},r))}function es(e){const t=`dropdownmenu-${Dt()}`,n=fe({id:t},e);return R(eg,n)}var qt=Object.assign(es,{Arrow:Yo,CheckboxItem:Tl,Content:Ql,Group:or,GroupLabel:Vl,Icon:Bl,Item:Kl,ItemDescription:Hl,ItemIndicator:Wl,ItemLabel:Nl,Portal:Gl,RadioGroup:Ul,RadioItem:ql,Separator:Zn,Sub:Yl,SubContent:Zl,SubTrigger:Jl,Trigger:Ll}),Ci=V("<span>");function rg(e){const[t,n]=re(e,["items","trigger","placement","disabled","onSelect","class","children"]),o=()=>({display:"inline-flex",alignItems:"center",gap:"4px"}),r=()=>({backgroundColor:"white",borderRadius:"4px",border:"1px solid",borderColor:"border.base",boxShadow:"light",padding:"4px 0",minWidth:"120px",maxWidth:"300px",zIndex:1e3,animation:"fadeIn 0.2s ease"}),i=s=>({display:"flex",alignItems:"center",gap:"8px",padding:"8px 16px",fontSize:"14px",color:s.disabled?"text.placeholder":"text.regular",cursor:s.disabled?"not-allowed":"pointer",borderTop:s.divided?"1px solid":"none",borderColor:"border.lighter",marginTop:s.divided?"4px":"0",paddingTop:s.divided?"12px":"8px",transition:"all 0.2s",_hover:s.disabled?{}:{backgroundColor:"primary.50",color:"primary.600"},_focus:{backgroundColor:"primary.50",color:"primary.600",outline:"none"}}),l=s=>{s.disabled||(s.onClick?.(),t.onSelect?.(s.key))};return R(qt,{get placement(){return t.placement||"bottom-start"},get disabled(){return t.disabled},get children(){return[R(qt.Trigger,{get class(){return c(o())},get children(){return t.trigger||R(oe,{variant:"default",size:"small",children:"操作 ▼"})}}),R(qt.Portal,{get children(){return R(qt.Content,{get class(){return Zt(c(r()),t.class)},get children(){return[R(Me,{get each(){return t.items},children:s=>R(qt.Item,{get class(){return c(i(s))},get disabled(){return s.disabled},onSelect:()=>l(s),get children(){return[R(ce,{get when(){return s.icon},get children(){var a=Ci();return w(a,()=>s.icon),O(()=>g(a,c({fontSize:"14px"}))),a}}),(()=>{var a=Ci();return w(a,()=>s.label),a})()]}})}),ee(()=>t.children)]}})}})]}})}const ko=document.createElement("style");ko.textContent=`
  @keyframes fadeIn {
    from {
      opacity: 0;
      transform: translateY(-4px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
`;document.head.querySelector("style[data-dropdown]")||(ko.setAttribute("data-dropdown","true"),document.head.appendChild(ko));const $o=document.createElement("style");$o.textContent=`
  @keyframes popoverFadeIn {
    from {
      opacity: 0;
      transform: scale(0.95);
    }
    to {
      opacity: 1;
      transform: scale(1);
    }
  }
`;document.head.querySelector("style[data-popover]")||($o.setAttribute("data-popover","true"),document.head.appendChild($o));var ig=V("<span>量化平台"),lg=V("<div><aside><div><span>📈</span></div><nav></nav><div></div></aside><div><header><div><h1>量化交易平台</h1></div><div><div>用</div></div></header><main>"),En=V("<span>"),sg=V("<div>");function ag(e){const[t,n]=W(!1),[o,r]=W(""),i=ss(),l=[{id:"dashboard",label:"仪表盘",icon:"📊",path:"/dashboard"},{id:"market",label:"行情中心",icon:"📈",path:"/market",children:[{id:"realtime",label:"实时行情",icon:"⚡",path:"/market/realtime"},{id:"historical",label:"历史数据",icon:"📋",path:"/market/historical"}]},{id:"trading",label:"交易中心",icon:"💰",path:"/trading"},{id:"strategy",label:"策略中心",icon:"🧠",path:"/strategy"},{id:"account",label:"账户中心",icon:"👤",path:"/account"},{id:"settings",label:"系统设置",icon:"⚙️",path:"/settings"}],s=u=>i.pathname===u||i.pathname.startsWith(u+"/"),a=()=>({width:t()?"64px":"240px",height:"100vh",backgroundColor:"white",borderRight:"1px solid",borderColor:"border.base",transition:"width 0.3s ease",display:"flex",flexDirection:"column",position:"fixed",left:0,top:0,zIndex:1e3}),d=()=>({height:"60px",backgroundColor:"white",borderBottom:"1px solid",borderColor:"border.base",display:"flex",alignItems:"center",justifyContent:"space-between",padding:"0 20px",marginLeft:t()?"64px":"240px",transition:"margin-left 0.3s ease"}),m=()=>({marginLeft:t()?"64px":"240px",marginTop:"60px",minHeight:"calc(100vh - 60px)",backgroundColor:"bg.page",transition:"margin-left 0.3s ease"}),f=()=>({height:"60px",display:"flex",alignItems:"center",justifyContent:t()?"center":"flex-start",padding:t()?"0":"0 20px",borderBottom:"1px solid",borderColor:"border.base",fontSize:"18px",fontWeight:"600",color:"primary.500"}),p=(u,h=!1)=>({display:"flex",alignItems:"center",gap:"12px",padding:t()&&!h?"12px 0":"12px 20px",paddingLeft:h?"52px":t()?"0":"20px",justifyContent:t()&&!h?"center":"flex-start",color:s(u.path)?"primary.500":"text.regular",backgroundColor:s(u.path)?"primary.50":"transparent",borderRight:s(u.path)?"3px solid":"none",borderColor:"primary.500",textDecoration:"none",transition:"all 0.3s",cursor:"pointer",_hover:{backgroundColor:"primary.50",color:"primary.500"}});return(()=>{var u=lg(),h=u.firstChild,y=h.firstChild,S=y.firstChild,x=y.nextSibling,b=x.nextSibling,v=h.nextSibling,_=v.firstChild,C=_.firstChild,M=C.firstChild,A=C.nextSibling,P=A.firstChild,F=_.nextSibling;return w(y,R(ce,{get when(){return!t()},get children(){return ig()}}),null),w(x,R(Me,{each:l,children:D=>(()=>{var T=sg();return w(T,R(Lr,{get href(){return D.path},get class(){return c(p(D))},get title(){return ee(()=>!!t())()?D.label:void 0},get children(){return[(()=>{var k=En();return w(k,()=>D.icon),O(()=>g(k,c({fontSize:"16px"}))),k})(),R(ce,{get when(){return!t()},get children(){var k=En();return w(k,()=>D.label),O(()=>g(k,c({fontWeight:s(D.path)?"500":"400"}))),k}})]}}),null),w(T,R(ce,{get when(){return ee(()=>!!D.children)()&&!t()},get children(){return R(Me,{get each(){return D.children},children:k=>R(Lr,{get href(){return k.path},get class(){return c(p(k,!0))},get children(){return[(()=>{var L=En();return w(L,()=>k.icon),O(()=>g(L,c({fontSize:"14px"}))),L})(),(()=>{var L=En();return w(L,()=>k.label),O(()=>g(L,c({fontWeight:s(k.path)?"500":"400"}))),L})()]}})})}}),null),T})()})),w(b,R(oe,{variant:"text",size:"small",onClick:()=>n(!t()),get class(){return c({width:"100%"})},get children(){return t()?"→":"←"}})),w(A,R(Eo,{placeholder:"搜索...",size:"small",get value(){return o()},onInput:D=>r(D.currentTarget.value),get class(){return c({width:"200px"})}}),P),w(A,R(oe,{variant:"text",size:"small",children:"帮助"}),P),w(A,R(oe,{variant:"text",size:"small",children:"设置"}),P),w(F,()=>e.children),O(D=>{var T=c({display:"flex",minHeight:"100vh"}),k=c(a()),L=c(f()),B=c({fontSize:"20px",marginRight:"8px"}),K=c({flex:1,overflowY:"auto",padding:"8px 0"}),j=c({padding:"16px",borderTop:"1px solid",borderColor:"border.base"}),H=c({flex:1}),U=c(d()),Z=c({display:"flex",alignItems:"center",gap:"16px"}),J=c({fontSize:"18px",fontWeight:"500",color:"text.primary",margin:0}),E=c({display:"flex",alignItems:"center",gap:"16px"}),te=c({width:"32px",height:"32px",backgroundColor:"primary.500",borderRadius:"50%",display:"flex",alignItems:"center",justifyContent:"center",color:"white",fontSize:"14px",fontWeight:"500"}),ae=c(m());return T!==D.e&&g(u,D.e=T),k!==D.t&&g(h,D.t=k),L!==D.a&&g(y,D.a=L),B!==D.o&&g(S,D.o=B),K!==D.i&&g(x,D.i=K),j!==D.n&&g(b,D.n=j),H!==D.s&&g(v,D.s=H),U!==D.h&&g(_,D.h=U),Z!==D.r&&g(C,D.r=Z),J!==D.d&&g(M,D.d=J),E!==D.l&&g(A,D.l=E),te!==D.u&&g(P,D.u=te),ae!==D.c&&g(F,D.c=ae),D},{e:void 0,t:void 0,a:void 0,o:void 0,i:void 0,n:void 0,s:void 0,h:void 0,r:void 0,d:void 0,l:void 0,u:void 0,c:void 0}),u})()}var cg=V("<div>🎉 新版专业量化平台界面已成功加载！"),dg=V("<div><div><h1>投资仪表盘</h1><div></div></div><div></div><div><div><div><h3>资金曲线图</h3><div></div></div><div><div>📈</div><div>资金曲线图表</div><div>显示策略收益走势</div></div></div><div><div><h3>持仓概览</h3><span>查看全部 →</span></div><div></div></div></div><div><div><div><h3>今日行情</h3><span>更新时间: 15:30</span></div><div></div></div><div><div><h3>最新资讯</h3><span>查看更多 →</span></div><div></div></div></div><div><div>当前时间: </div><div><span>数据来源: 模拟数据</span><span>更新频率: 实时</span><div><div></div><span>系统正常"),co=V("<div>"),ug=V("<div><div><span></span><span>%</span></div><div><span></span><span>"),gg=V("<div><div><div></div><span></span></div><div><div></div><div> (<!>)"),fg=V("<div><div></div><div><span></span><span>");function An(){console.log("🔥 Dashboard组件已加载 - 新版本 - 时间戳:",Date.now());const[e,t]=W(new Date().toLocaleString("zh-CN"));let n;gt(()=>{n=setInterval(()=>{t(new Date().toLocaleString("zh-CN"))},1e3)}),Q(()=>{n&&clearInterval(n)});const o=[{title:"总资产",value:"¥1,000,000",change:"+2.34%",trend:"up",icon:"💰",description:"总资产",subValue:"¥1,000,000"},{title:"今日盈亏",value:"0",change:"+0.00%",trend:"neutral",icon:"📊",description:"今日盈亏",subValue:"0.00%"},{title:"持仓市值",value:"¥50,000",change:"+0.00%",trend:"neutral",icon:"📈",description:"持仓市值",subValue:"¥50,000"},{title:"可用资金",value:"2",change:"+0.00%",trend:"neutral",icon:"🔒",description:"持仓数量",subValue:"2"}],r=[{code:"000725",name:"京东方A",price:3.45,change:-1.43,changePercent:-29.3,volume:7340.75,amount:-6046,status:"持仓"},{code:"300519",name:"新光药业",price:68.94,change:-1.05,changePercent:-1.5,volume:410.75,amount:-1796,status:"持仓"},{code:"000831",name:"五矿稀土",price:26.09,change:-1.37,changePercent:-5,volume:7558.72,amount:-7688,status:"持仓"}],i=[{name:"上证指数",value:"3,245.67",change:"+23.45",percent:"+0.73%",trend:"up"},{name:"深证成指",value:"10,567.23",change:"+45.67",percent:"+0.43%",trend:"up"},{name:"创业板指",value:"2,234.56",change:"-8.90",percent:"-0.40%",trend:"down"},{name:"科创50",value:"1,123.45",change:"+15.23",percent:"+1.37%",trend:"up"}],l=[{title:"A股市场今日表现强劲，科技股领涨",time:"刚刚发布",type:"market"},{title:"央行宣布降准0.25个百分点",time:"30分钟前",type:"policy"},{title:"新能源板块持续活跃，多只个股涨停",time:"1小时前",type:"sector"}];return(()=>{var s=dg(),a=s.firstChild,d=a.firstChild,m=d.nextSibling,f=a.nextSibling,p=f.nextSibling,u=p.firstChild,h=u.firstChild,y=h.firstChild,S=y.nextSibling,x=h.nextSibling,b=x.firstChild,v=b.nextSibling,_=v.nextSibling,C=u.nextSibling,M=C.firstChild,A=M.firstChild,P=A.nextSibling,F=M.nextSibling,D=p.nextSibling,T=D.firstChild,k=T.firstChild,L=k.firstChild,B=L.nextSibling,K=k.nextSibling,j=T.nextSibling,H=j.firstChild,U=H.firstChild,Z=U.nextSibling,J=H.nextSibling,E=D.nextSibling,te=E.firstChild;te.firstChild;var ae=te.nextSibling,pe=ae.firstChild,he=pe.nextSibling,we=he.nextSibling,Ce=we.firstChild;return w(s,R(Vn,{get class(){return c({marginBottom:"20px",backgroundColor:"success.50",borderColor:"success.200"})},get children(){var I=cg();return O(()=>g(I,c({padding:"16px",textAlign:"center",color:"success.700",fontSize:"16px",fontWeight:"600"}))),I}}),a),w(m,R(oe,{variant:"primary",size:"small",children:"刷新"}),null),w(m,R(oe,{variant:"default",size:"small",children:"设置"}),null),w(m,R(oe,{variant:"success",size:"small",children:"新增策略"}),null),w(f,R(Me,{each:o,children:I=>R(Vn,{shadow:"hover",bodyStyle:{display:"flex",flexDirection:"column",alignItems:"center",textAlign:"center",padding:"24px 16px"},get children(){return[(()=>{var X=co();return w(X,()=>I.icon),O(()=>g(X,c({fontSize:"32px",marginBottom:"12px"}))),X})(),(()=>{var X=co();return w(X,()=>I.value),O(()=>g(X,c({fontSize:"28px",fontWeight:"600",color:"text.primary",marginBottom:"8px"}))),X})(),(()=>{var X=co();return w(X,()=>I.description),O(()=>g(X,c({fontSize:"14px",color:"text.secondary",marginBottom:"12px"}))),X})(),R(zn,{get type(){return ee(()=>I.trend==="up")()?"success":I.trend==="down"?"danger":"info"},effect:"light",size:"small",get children(){return I.change}})]}})})),w(S,R(oe,{variant:"primary",size:"sm",children:"日"}),null),w(S,R(oe,{size:"sm",children:"周"}),null),w(S,R(oe,{size:"sm",children:"月"}),null),w(F,R(Me,{each:r,children:I=>(()=>{var X=ug(),se=X.firstChild,me=se.firstChild,ue=me.nextSibling,ve=ue.firstChild,ie=se.nextSibling,ge=ie.firstChild,ke=ge.nextSibling;return w(me,()=>I.code),w(ue,()=>I.changePercent>0?"+":"",ve),w(ue,()=>I.changePercent,ve),w(ge,()=>I.name),w(ke,()=>I.status),O(de=>{var ne=c({padding:"8px",backgroundColor:"#fafafa",borderRadius:"4px",fontSize:"12px"}),_e=c({display:"flex",justifyContent:"space-between",alignItems:"center",marginBottom:"4px"}),$e=c({fontWeight:"600",color:"#262626"}),Fe=c({color:I.changePercent>0?"#52c41a":"#f5222d",fontWeight:"500"}),Ee=c({display:"flex",justifyContent:"space-between",color:"#8c8c8c"});return ne!==de.e&&g(X,de.e=ne),_e!==de.t&&g(se,de.t=_e),$e!==de.a&&g(me,de.a=$e),Fe!==de.o&&g(ue,de.o=Fe),Ee!==de.i&&g(ie,de.i=Ee),de},{e:void 0,t:void 0,a:void 0,o:void 0,i:void 0}),X})()})),w(K,R(Me,{each:i,children:I=>(()=>{var X=gg(),se=X.firstChild,me=se.firstChild,ue=me.nextSibling,ve=se.nextSibling,ie=ve.firstChild,ge=ie.nextSibling,ke=ge.firstChild,de=ke.nextSibling;return de.nextSibling,w(ue,()=>I.name),w(ie,()=>I.value),w(ge,()=>I.change,ke),w(ge,()=>I.percent,de),O(ne=>{var _e=c({display:"flex",alignItems:"center",justifyContent:"space-between",padding:"8px",backgroundColor:"#fafafa",borderRadius:"4px",fontSize:"12px"}),$e=c({display:"flex",alignItems:"center",gap:"8px"}),Fe=c({width:"6px",height:"6px",borderRadius:"50%",backgroundColor:I.trend==="up"?"#52c41a":"#f5222d"}),Ee=c({fontWeight:"500",color:"#262626"}),We=c({textAlign:"right"}),Ne=c({fontWeight:"600",color:"#262626",marginBottom:"2px"}),Ge=c({color:I.trend==="up"?"#52c41a":"#f5222d",fontSize:"11px"});return _e!==ne.e&&g(X,ne.e=_e),$e!==ne.t&&g(se,ne.t=$e),Fe!==ne.a&&g(me,ne.a=Fe),Ee!==ne.o&&g(ue,ne.o=Ee),We!==ne.i&&g(ve,ne.i=We),Ne!==ne.n&&g(ie,ne.n=Ne),Ge!==ne.s&&g(ge,ne.s=Ge),ne},{e:void 0,t:void 0,a:void 0,o:void 0,i:void 0,n:void 0,s:void 0}),X})()})),w(J,R(Me,{each:l,children:I=>(()=>{var X=fg(),se=X.firstChild,me=se.nextSibling,ue=me.firstChild,ve=ue.nextSibling;return w(se,()=>I.title),w(ue,()=>I.time),w(ve,()=>I.type),O(ie=>{var ge=c({padding:"8px",backgroundColor:"#fafafa",borderRadius:"4px",cursor:"pointer",transition:"background-color 0.2s",_hover:{backgroundColor:"#f0f0f0"}}),ke=c({fontSize:"12px",fontWeight:"500",color:"#262626",marginBottom:"4px",lineHeight:"1.4"}),de=c({display:"flex",alignItems:"center",justifyContent:"space-between"}),ne=c({fontSize:"11px",color:"#8c8c8c"}),_e=c({fontSize:"10px",color:"#1890ff",backgroundColor:"#e6f7ff",padding:"2px 6px",borderRadius:"2px"});return ge!==ie.e&&g(X,ie.e=ge),ke!==ie.t&&g(se,ie.t=ke),de!==ie.a&&g(me,ie.a=de),ne!==ie.o&&g(ue,ie.o=ne),_e!==ie.i&&g(ve,ie.i=_e),ie},{e:void 0,t:void 0,a:void 0,o:void 0,i:void 0}),X})()})),w(te,e,null),O(I=>{var X=c({padding:"20px",minHeight:"100vh",backgroundColor:"bg.page"}),se=c({display:"flex",alignItems:"center",justifyContent:"space-between",marginBottom:"20px"}),me=c({fontSize:"24px",fontWeight:"600",color:"text.primary",margin:0}),ue=c({display:"flex",alignItems:"center",gap:"12px"}),ve=c({display:"grid",gridTemplateColumns:"repeat(auto-fit, minmax(240px, 1fr))",gap:"20px",marginBottom:"24px"}),ie=c({display:"grid",gridTemplateColumns:"2fr 1fr",gap:"16px",marginBottom:"16px","@media (max-width: 768px)":{gridTemplateColumns:"1fr"}}),ge=c({backgroundColor:"white",borderRadius:"8px",border:"1px solid #e8e8e8",padding:"16px"}),ke=c({display:"flex",alignItems:"center",justifyContent:"space-between",marginBottom:"16px",flexWrap:"wrap",gap:"8px"}),de=c({fontSize:"16px",fontWeight:"600",color:"#262626",margin:0}),ne=c({display:"flex",alignItems:"center",gap:"8px"}),_e=c({height:"200px",backgroundColor:"#fafafa",borderRadius:"4px",display:"flex",alignItems:"center",justifyContent:"center",flexDirection:"column",gap:"8px"}),$e=c({fontSize:"48px"}),Fe=c({fontSize:"14px",color:"#8c8c8c"}),Ee=c({fontSize:"12px",color:"#8c8c8c"}),We=c({backgroundColor:"white",borderRadius:"8px",border:"1px solid #e8e8e8",padding:"16px"}),Ne=c({display:"flex",alignItems:"center",justifyContent:"space-between",marginBottom:"16px"}),Ge=c({fontSize:"16px",fontWeight:"600",color:"#262626",margin:0}),ht=c({fontSize:"12px",color:"#8c8c8c"}),mt=c({display:"flex",flexDirection:"column",gap:"8px"}),vt=c({display:"grid",gridTemplateColumns:"1fr 1fr",gap:"16px","@media (max-width: 768px)":{gridTemplateColumns:"1fr"}}),xt=c({backgroundColor:"white",borderRadius:"8px",border:"1px solid #e8e8e8",padding:"16px"}),bt=c({display:"flex",alignItems:"center",justifyContent:"space-between",marginBottom:"16px",flexWrap:"wrap",gap:"8px"}),yt=c({fontSize:"16px",fontWeight:"600",color:"#262626",margin:0}),Lt=c({fontSize:"12px",color:"#8c8c8c"}),zt=c({display:"flex",flexDirection:"column",gap:"8px"}),St=c({backgroundColor:"white",borderRadius:"8px",border:"1px solid #e8e8e8",padding:"16px"}),Ze=c({display:"flex",alignItems:"center",justifyContent:"space-between",marginBottom:"16px"}),Ot=c({fontSize:"16px",fontWeight:"600",color:"#262626",margin:0}),Qt=c({fontSize:"12px",color:"#1890ff",cursor:"pointer"}),Vt=c({display:"flex",flexDirection:"column",gap:"8px"}),en=c({display:"flex",alignItems:"center",justifyContent:"space-between",padding:"12px 16px",backgroundColor:"white",borderRadius:"8px",border:"1px solid #e8e8e8",fontSize:"12px",color:"#8c8c8c"}),$=c({display:"flex",alignItems:"center",gap:"16px"}),Ae=c({display:"flex",alignItems:"center",gap:"4px"}),Te=c({width:"6px",height:"6px",borderRadius:"50%",backgroundColor:"#52c41a"});return X!==I.e&&g(s,I.e=X),se!==I.t&&g(a,I.t=se),me!==I.a&&g(d,I.a=me),ue!==I.o&&g(m,I.o=ue),ve!==I.i&&g(f,I.i=ve),ie!==I.n&&g(p,I.n=ie),ge!==I.s&&g(u,I.s=ge),ke!==I.h&&g(h,I.h=ke),de!==I.r&&g(y,I.r=de),ne!==I.d&&g(S,I.d=ne),_e!==I.l&&g(x,I.l=_e),$e!==I.u&&g(b,I.u=$e),Fe!==I.c&&g(v,I.c=Fe),Ee!==I.w&&g(_,I.w=Ee),We!==I.m&&g(C,I.m=We),Ne!==I.f&&g(M,I.f=Ne),Ge!==I.y&&g(A,I.y=Ge),ht!==I.g&&g(P,I.g=ht),mt!==I.p&&g(F,I.p=mt),vt!==I.b&&g(D,I.b=vt),xt!==I.T&&g(T,I.T=xt),bt!==I.A&&g(k,I.A=bt),yt!==I.O&&g(L,I.O=yt),Lt!==I.I&&g(B,I.I=Lt),zt!==I.S&&g(K,I.S=zt),St!==I.W&&g(j,I.W=St),Ze!==I.C&&g(H,I.C=Ze),Ot!==I.B&&g(U,I.B=Ot),Qt!==I.v&&g(Z,I.v=Qt),Vt!==I.k&&g(J,I.k=Vt),en!==I.x&&g(E,I.x=en),$!==I.j&&g(ae,I.j=$),Ae!==I.q&&g(we,I.q=Ae),Te!==I.z&&g(Ce,I.z=Te),I},{e:void 0,t:void 0,a:void 0,o:void 0,i:void 0,n:void 0,s:void 0,h:void 0,r:void 0,d:void 0,l:void 0,u:void 0,c:void 0,w:void 0,m:void 0,f:void 0,y:void 0,g:void 0,p:void 0,b:void 0,T:void 0,A:void 0,O:void 0,I:void 0,S:void 0,W:void 0,C:void 0,B:void 0,v:void 0,k:void 0,x:void 0,j:void 0,q:void 0,z:void 0}),s})()}var pg=V("<div><div><h1>🔧 API 连接测试</h1><p>测试前端与后端API的连接状态</p></div><div><button>开始测试</button></div><div><div><h2>测试结果</h2></div><div></div></div><div><div><h2>配置信息</h2></div><div><div><div><h3>API Base URL</h3><p></p></div><div><h3>环境模式</h3><p></p></div><div><h3>代理配置</h3><p>/api → localhost:8000"),hg=V('<p>点击"开始测试"按钮运行API连接测试'),mg=V("<div>"),vg=V("<div><div><div></div><div><h3></h3><p>"),xg=V("<div>ms");const Dn={get:async(e,t)=>{const n=t?"?"+new URLSearchParams(t).toString():"",r=await fetch("https://api.yourdomain.com"+e+n);if(!r.ok)throw new Error(`HTTP ${r.status}`);return r.json().catch(()=>({}))}},Tn={SYSTEM:{HEALTH:"/v1/health"},MARKET:{OVERVIEW:"/v1/market/overview",SEARCH:"/v1/market/search"},AUTH:{PROFILE:"/v1/auth/profile"}};function bg(){const[e,t]=W([]),n=(r,i,l,s)=>{t(a=>[...a,{name:r,status:i,message:l,duration:s}])},o=async()=>{t([]);try{const r=Date.now();await Dn.get(Tn.SYSTEM.HEALTH);const i=Date.now()-r;n("系统健康检查","success","连接成功",i)}catch(r){n("系统健康检查","error",r.message||"连接失败")}try{const r=Date.now();await Dn.get(Tn.MARKET.OVERVIEW);const i=Date.now()-r;n("市场概览","success","数据获取成功",i)}catch(r){n("市场概览","error",r.message||"数据获取失败")}try{const r=Date.now();await Dn.get(Tn.MARKET.SEARCH,{q:"AAPL"});const i=Date.now()-r;n("股票搜索","success","搜索成功",i)}catch(r){n("股票搜索","error",r.message||"搜索失败")}try{const r=Date.now();await Dn.get(Tn.AUTH.PROFILE);const i=Date.now()-r;n("用户信息","success","获取成功",i)}catch(r){n("用户信息","error",r.message||"获取失败（预期，因为未登录）")}};return gt(()=>{console.log("ApiTest mounted")}),(()=>{var r=pg(),i=r.firstChild,l=i.firstChild,s=l.nextSibling,a=i.nextSibling,d=a.firstChild,m=a.nextSibling,f=m.firstChild,p=f.firstChild,u=f.nextSibling,h=m.nextSibling,y=h.firstChild,S=y.firstChild,x=y.nextSibling,b=x.firstChild,v=b.firstChild,_=v.firstChild,C=_.nextSibling,M=v.nextSibling,A=M.firstChild,P=A.nextSibling,F=M.nextSibling,D=F.firstChild,T=D.nextSibling;return d.$$click=o,w(u,(()=>{var k=ee(()=>e().length===0);return()=>k()?(()=>{var L=hg();return O(()=>g(L,c({color:"gray.500",textAlign:"center",padding:"40px 0"}))),L})():(()=>{var L=mg();return w(L,()=>e().map(B=>(()=>{var K=vg(),j=K.firstChild,H=j.firstChild,U=H.nextSibling,Z=U.firstChild,J=Z.nextSibling;return w(H,()=>B.status==="success"?"✅":"❌"),w(Z,()=>B.name),w(J,()=>B.message),w(K,(()=>{var E=ee(()=>!!B.duration);return()=>E()&&(()=>{var te=xg(),ae=te.firstChild;return w(te,()=>B.duration,ae),O(()=>g(te,c({fontSize:"12px",color:"gray.500",backgroundColor:"gray.100",padding:"4px 8px",borderRadius:"4px"}))),te})()})(),null),O(E=>{var te=c({display:"flex",alignItems:"center",justifyContent:"space-between",padding:"16px",borderRadius:"8px",border:"1px solid",borderColor:B.status==="success"?"green.200":"red.200",backgroundColor:B.status==="success"?"green.50":"red.50"}),ae=c({display:"flex",alignItems:"center",gap:"12px"}),pe=c({fontSize:"20px"}),he=c({fontSize:"16px",fontWeight:"600",color:"gray.900",marginBottom:"4px"}),we=c({fontSize:"14px",color:"gray.600"});return te!==E.e&&g(K,E.e=te),ae!==E.t&&g(j,E.t=ae),pe!==E.a&&g(H,E.a=pe),he!==E.o&&g(Z,E.o=he),we!==E.i&&g(J,E.i=we),E},{e:void 0,t:void 0,a:void 0,o:void 0,i:void 0}),K})())),O(()=>g(L,c({display:"flex",flexDirection:"column",gap:"16px"}))),L})()})()),w(C,()=>"https://api.yourdomain.com"),w(P,()=>"production"),O(k=>{var L=c({padding:"24px",maxWidth:"1200px",margin:"0 auto"}),B=c({marginBottom:"32px"}),K=c({fontSize:"32px",fontWeight:"bold",color:"gray.900",marginBottom:"8px"}),j=c({fontSize:"16px",color:"gray.600"}),H=c({marginBottom:"32px"}),U=c({padding:"12px 24px",backgroundColor:"blue.600",color:"white",border:"none",borderRadius:"8px",fontSize:"16px",fontWeight:"500",cursor:"pointer",transition:"all 0.2s",_hover:{backgroundColor:"blue.700"}}),Z=c({backgroundColor:"white",borderRadius:"12px",boxShadow:"0 1px 3px rgba(0, 0, 0, 0.1)",border:"1px solid #e5e7eb",overflow:"hidden"}),J=c({padding:"24px",borderBottom:"1px solid #e5e7eb"}),E=c({fontSize:"20px",fontWeight:"bold",color:"gray.900"}),te=c({padding:"24px"}),ae=c({marginTop:"32px",backgroundColor:"white",borderRadius:"12px",boxShadow:"0 1px 3px rgba(0, 0, 0, 0.1)",border:"1px solid #e5e7eb",overflow:"hidden"}),pe=c({padding:"24px",borderBottom:"1px solid #e5e7eb"}),he=c({fontSize:"20px",fontWeight:"bold",color:"gray.900"}),we=c({padding:"24px"}),Ce=c({display:"grid",gridTemplateColumns:"repeat(auto-fit, minmax(300px, 1fr))",gap:"16px"}),I=c({fontSize:"14px",fontWeight:"600",color:"gray.700",marginBottom:"8px"}),X=c({fontSize:"14px",color:"gray.600",fontFamily:"monospace",backgroundColor:"gray.100",padding:"8px",borderRadius:"4px"}),se=c({fontSize:"14px",fontWeight:"600",color:"gray.700",marginBottom:"8px"}),me=c({fontSize:"14px",color:"gray.600",fontFamily:"monospace",backgroundColor:"gray.100",padding:"8px",borderRadius:"4px"}),ue=c({fontSize:"14px",fontWeight:"600",color:"gray.700",marginBottom:"8px"}),ve=c({fontSize:"14px",color:"gray.600",fontFamily:"monospace",backgroundColor:"gray.100",padding:"8px",borderRadius:"4px"});return L!==k.e&&g(r,k.e=L),B!==k.t&&g(i,k.t=B),K!==k.a&&g(l,k.a=K),j!==k.o&&g(s,k.o=j),H!==k.i&&g(a,k.i=H),U!==k.n&&g(d,k.n=U),Z!==k.s&&g(m,k.s=Z),J!==k.h&&g(f,k.h=J),E!==k.r&&g(p,k.r=E),te!==k.d&&g(u,k.d=te),ae!==k.l&&g(h,k.l=ae),pe!==k.u&&g(y,k.u=pe),he!==k.c&&g(S,k.c=he),we!==k.w&&g(x,k.w=we),Ce!==k.m&&g(b,k.m=Ce),I!==k.f&&g(_,k.f=I),X!==k.y&&g(C,k.y=X),se!==k.g&&g(A,k.g=se),me!==k.p&&g(P,k.p=me),ue!==k.b&&g(D,k.b=ue),ve!==k.T&&g(T,k.T=ve),k},{e:void 0,t:void 0,a:void 0,o:void 0,i:void 0,n:void 0,s:void 0,h:void 0,r:void 0,d:void 0,l:void 0,u:void 0,c:void 0,w:void 0,m:void 0,f:void 0,y:void 0,g:void 0,p:void 0,b:void 0,T:void 0}),r})()}Wt(["click"]);class yg{constructor(){this.socket=null,this.reconnectAttempts=0,this.maxReconnectAttempts=5,this.reconnectDelay=1e3,this.connectionStatusSignal=W("disconnected"),this.marketDataSignal=W(new Map),this.connectionStatus=this.connectionStatusSignal[0],this.setConnectionStatus=this.connectionStatusSignal[1],this.marketData=this.marketDataSignal[0],this.setMarketData=this.marketDataSignal[1],this.connect()}connect(){try{this.setConnectionStatus("connecting"),console.log("尝试连接WebSocket服务器:","wss://api.yourdomain.com/ws"),this.socket=null}catch(t){console.error("WebSocket连接失败:",t),this.setConnectionStatus("error"),this.handleReconnect()}}handleReconnect(){this.reconnectAttempts<this.maxReconnectAttempts?(this.reconnectAttempts++,setTimeout(()=>{console.log(`尝试重连 (${this.reconnectAttempts}/${this.maxReconnectAttempts})`),this.connect()},this.reconnectDelay*this.reconnectAttempts)):(console.log("达到最大重连次数，停止重连"),this.setConnectionStatus("error"))}updateMarketData(t){this.setMarketData(n=>{const o=new Map(n);return o.set(t.symbol,t),o})}subscribeToMarketData(t){console.log("订阅市场数据:",t),this.socket&&this.socket.connected&&this.socket.emit("subscribe",{symbols:t})}unsubscribeFromMarketData(t){console.log("取消订阅市场数据:",t),this.socket&&this.socket.connected&&this.socket.emit("unsubscribe",{symbols:t})}reconnect(){console.log("手动重连WebSocket..."),this.disconnect(),this.reconnectAttempts=0,this.connect()}disconnect(){this.socket&&(this.socket.disconnect(),this.socket=null),this.setConnectionStatus("disconnected")}}const tt=new yg;function Sg(){return{connectionStatus:tt.connectionStatus,marketData:tt.marketData,subscribeToMarketData:tt.subscribeToMarketData.bind(tt),unsubscribeFromMarketData:tt.unsubscribeFromMarketData.bind(tt),disconnect:tt.disconnect.bind(tt),reconnect:tt.reconnect.bind(tt)}}var wg=V("<div><div><div><h1>行情分析</h1><div><span>沪深A股</span><span>数据更新: 15:30</span><div><div></div><span></span></div></div></div><div></div></div><div><div><h2>市场概览</h2><div></div></div><div><div><div>上证指数</div><div>3,247.89</div><div>-12.34 (-0.38%)</div></div><div><div>深证成指</div><div>10,567.23</div><div>+45.67 (+0.43%)</div></div><div><div>创业板指</div><div>2,234.56</div><div>-8.90 (-0.40%)</div></div><div><div>科创50</div><div>1,123.45</div><div>+15.23 (+1.37%)</div></div></div></div><div><div><h3>市场筛选</h3><span>共 <!> 只股票</span></div><div><div></div><div><span>板块:</span></div></div></div><div><div><h2>股票列表</h2></div><div><table><thead><tr><th>代码</th><th>名称</th><th>现价</th><th>涨跌额</th><th>涨跌幅</th><th>成交量</th><th>最高价</th><th>最低价</th><th>操作</th></tr></thead><tbody></tbody></table></div><div><div>共 <!> 条数据</div><div><span>1"),Cg=V("<tr><td></td><td></td><td></td><td></td><td>%</td><td></td><td></td><td></td><td><div>"),_g=V("<div><div><h3> 详细信息</h3></div><div><div>📊</div><p>K线图表和技术指标</p><p>这里将显示选中股票的详细分析图表");function _i(){const e=[{symbol:"000725",name:"京东方A",price:3.45,change:-1.43,changePercent:-29.3,volume:7340750,high:3.52,low:3.4,open:3.48,marketCap:12e10},{symbol:"300519",name:"新光药业",price:68.94,change:-1.05,changePercent:-1.5,volume:410750,high:70.1,low:68.5,open:69.9,marketCap:28e9},{symbol:"000831",name:"五矿稀土",price:26.09,change:-1.37,changePercent:-5,volume:7558720,high:27.5,low:25.8,open:27.2,marketCap:45e9},{symbol:"000001",name:"平安银行",price:12.45,change:.23,changePercent:1.88,volume:1568e4,high:12.58,low:12.2,open:12.3,marketCap:24e10},{symbol:"000002",name:"万科A",price:8.76,change:-.15,changePercent:-1.68,volume:895e4,high:8.95,low:8.65,open:8.85,marketCap:98e9}],[t,n]=W(e),o=Sg(),[r,i]=W("AAPL"),[l,s]=W("");Y(()=>{const f=o.marketData();f.size>0&&n(p=>p.map(u=>{const h=f.get(u.symbol);return h?{...u,price:h.price,change:h.change,changePercent:h.changePercent,volume:h.volume}:u}))}),gt(()=>{console.log("Market page mounted");const f=e.map(h=>h.symbol);o.subscribeToMarketData(f);let p;setTimeout(()=>{p=setInterval(()=>{o.connectionStatus()!=="connected"&&n(h=>h.map(y=>({...y,price:Math.max(.01,y.price+(Math.random()-.5)*2),change:y.change+(Math.random()-.5)*.5,changePercent:y.changePercent+(Math.random()-.5)*.2,volume:Math.max(0,y.volume+Math.floor((Math.random()-.5)*1e5))})))},3e3)},2e3),Q(()=>{p&&clearInterval(p),o.unsubscribeFromMarketData(f)})});const a=()=>{const f=l().toLowerCase();return t().filter(p=>p.symbol.toLowerCase().includes(f)||p.name.toLowerCase().includes(f))},d=(f,p=2)=>new Intl.NumberFormat("zh-CN",{minimumFractionDigits:p,maximumFractionDigits:p}).format(f),m=f=>f>=1e6?`${(f/1e6).toFixed(1)}M`:f>=1e3?`${(f/1e3).toFixed(1)}K`:f.toString();return(()=>{var f=wg(),p=f.firstChild,u=p.firstChild,h=u.firstChild,y=h.nextSibling,S=y.firstChild,x=S.nextSibling,b=x.nextSibling,v=b.firstChild,_=v.nextSibling,C=u.nextSibling,M=p.nextSibling,A=M.firstChild,P=A.firstChild,F=P.nextSibling,D=A.nextSibling,T=D.firstChild,k=T.firstChild,L=k.nextSibling,B=L.nextSibling,K=T.nextSibling,j=K.firstChild,H=j.nextSibling,U=H.nextSibling,Z=K.nextSibling,J=Z.firstChild,E=J.nextSibling,te=E.nextSibling,ae=Z.nextSibling,pe=ae.firstChild,he=pe.nextSibling,we=he.nextSibling,Ce=M.nextSibling,I=Ce.firstChild,X=I.firstChild,se=X.nextSibling,me=se.firstChild,ue=me.nextSibling;ue.nextSibling;var ve=I.nextSibling,ie=ve.firstChild,ge=ie.nextSibling,ke=ge.firstChild,de=Ce.nextSibling,ne=de.firstChild,_e=ne.firstChild,$e=ne.nextSibling,Fe=$e.firstChild,Ee=Fe.firstChild,We=Ee.firstChild,Ne=We.firstChild,Ge=Ne.nextSibling,ht=Ge.nextSibling,mt=ht.nextSibling,vt=mt.nextSibling,xt=vt.nextSibling,bt=xt.nextSibling,yt=bt.nextSibling,Lt=yt.nextSibling,zt=Ee.nextSibling,St=$e.nextSibling,Ze=St.firstChild,Ot=Ze.firstChild,Qt=Ot.nextSibling;Qt.nextSibling;var Vt=Ze.nextSibling,en=Vt.firstChild;return w(_,()=>o.connectionStatus()==="connected"?"实时连接":"模拟数据"),w(C,(()=>{var $=ee(()=>o.connectionStatus()!=="connected");return()=>$()&&R(oe,{variant:"primary",onClick:()=>o.reconnect(),children:"重新连接"})})(),null),w(C,R(oe,{children:"导出数据"}),null),w(C,R(oe,{variant:"success",children:"自选股"}),null),w(F,R(oe,{size:"sm",variant:"primary",children:"日"}),null),w(F,R(oe,{size:"sm",children:"周"}),null),w(F,R(oe,{size:"sm",children:"月"}),null),w(se,()=>a().length,ue),w(ie,R(Eo,{placeholder:"搜索股票代码或名称",get value(){return l()},onInput:$=>s($.currentTarget.value)}),null),w(ie,R(oe,{variant:"primary",children:"搜索"}),null),w(ge,R(oe,{size:"sm",variant:"primary",children:"全部"}),null),w(ge,R(oe,{size:"sm",children:"沪A"}),null),w(ge,R(oe,{size:"sm",children:"深A"}),null),w(ge,R(oe,{size:"sm",children:"创业板"}),null),w(zt,()=>a().map($=>(()=>{var Ae=Cg(),Te=Ae.firstChild,Je=Te.nextSibling,wt=Je.nextSibling,Qe=wt.nextSibling,je=Qe.nextSibling,Bt=je.firstChild,Ct=je.nextSibling,xe=Ct.nextSibling,lt=xe.nextSibling,_t=lt.nextSibling,st=_t.firstChild;return Ae.$$click=()=>i($.symbol),w(Te,()=>$.symbol),w(Je,()=>$.name),w(wt,()=>d($.price)),w(Qe,()=>$.change>=0?"+":"",null),w(Qe,()=>d($.change),null),w(je,()=>$.changePercent>=0?"+":"",Bt),w(je,()=>d($.changePercent),Bt),w(Ct,()=>m($.volume)),w(xe,()=>d($.high)),w(lt,()=>d($.low)),w(st,R(oe,{size:"sm",variant:"danger",children:"卖"}),null),w(st,R(oe,{size:"sm",variant:"success",children:"买"}),null),O(le=>{var Rt=c({borderBottom:"1px solid #f0f0f0",cursor:"pointer",transition:"background-color 0.2s",backgroundColor:r()===$.symbol?"#e6f7ff":"transparent",_hover:{backgroundColor:"#fafafa"}}),It=c({padding:"12px 16px",fontSize:"14px",fontWeight:"600",color:"#262626"}),tn=c({padding:"12px 16px",fontSize:"14px",color:"#262626"}),nn=c({padding:"12px 16px",textAlign:"right",fontSize:"14px",fontWeight:"600",color:"#262626"}),on=c({padding:"12px 16px",textAlign:"right",fontSize:"14px",fontWeight:"500",color:$.change>=0?"#52c41a":"#f5222d"}),rn=c({padding:"12px 16px",textAlign:"right",fontSize:"14px",fontWeight:"500",color:$.changePercent>=0?"#52c41a":"#f5222d"}),ln=c({padding:"12px 16px",textAlign:"right",fontSize:"14px",color:"#8c8c8c"}),sn=c({padding:"12px 16px",textAlign:"right",fontSize:"14px",color:"#8c8c8c"}),an=c({padding:"12px 16px",textAlign:"right",fontSize:"14px",color:"#8c8c8c"}),cn=c({padding:"12px 16px",textAlign:"right"}),dn=c({display:"flex",gap:"4px",justifyContent:"flex-end"});return Rt!==le.e&&g(Ae,le.e=Rt),It!==le.t&&g(Te,le.t=It),tn!==le.a&&g(Je,le.a=tn),nn!==le.o&&g(wt,le.o=nn),on!==le.i&&g(Qe,le.i=on),rn!==le.n&&g(je,le.n=rn),ln!==le.s&&g(Ct,le.s=ln),sn!==le.h&&g(xe,le.h=sn),an!==le.r&&g(lt,le.r=an),cn!==le.d&&g(_t,le.d=cn),dn!==le.l&&g(st,le.l=dn),le},{e:void 0,t:void 0,a:void 0,o:void 0,i:void 0,n:void 0,s:void 0,h:void 0,r:void 0,d:void 0,l:void 0}),Ae})())),w(Ze,()=>a().length,Qt),w(Vt,R(oe,{size:"sm",children:"上一页"}),en),w(Vt,R(oe,{size:"sm",children:"下一页"}),null),w(f,(()=>{var $=ee(()=>!!r());return()=>$()&&(()=>{var Ae=_g(),Te=Ae.firstChild,Je=Te.firstChild,wt=Je.firstChild,Qe=Te.nextSibling,je=Qe.firstChild,Bt=je.nextSibling,Ct=Bt.nextSibling;return w(Je,r,wt),O(xe=>{var lt=c({marginTop:"24px",backgroundColor:"white",borderRadius:"8px",border:"1px solid #e8e8e8",overflow:"hidden"}),_t=c({padding:"20px",borderBottom:"1px solid #e8e8e8"}),st=c({fontSize:"16px",fontWeight:"600",color:"#262626",margin:"0"}),le=c({padding:"20px",textAlign:"center",color:"#8c8c8c"}),Rt=c({fontSize:"48px",marginBottom:"16px"}),It=c({fontSize:"12px"});return lt!==xe.e&&g(Ae,xe.e=lt),_t!==xe.t&&g(Te,xe.t=_t),st!==xe.a&&g(Je,xe.a=st),le!==xe.o&&g(Qe,xe.o=le),Rt!==xe.i&&g(je,xe.i=Rt),It!==xe.n&&g(Ct,xe.n=It),xe},{e:void 0,t:void 0,a:void 0,o:void 0,i:void 0,n:void 0}),Ae})()})(),null),O($=>{var Ae=c({padding:"16px",backgroundColor:"#f0f2f5",minHeight:"100%"}),Te=c({display:"flex",alignItems:"center",justifyContent:"space-between",marginBottom:"16px"}),Je=c({fontSize:"20px",fontWeight:"600",color:"#262626",margin:0,marginBottom:"4px"}),wt=c({display:"flex",alignItems:"center",gap:"12px",fontSize:"12px",color:"#8c8c8c"}),Qe=c({padding:"2px 6px",backgroundColor:"#f6ffed",color:"#52c41a",borderRadius:"2px",fontSize:"11px"}),je=c({display:"flex",alignItems:"center",gap:"4px"}),Bt=c({width:"6px",height:"6px",borderRadius:"50%",backgroundColor:o.connectionStatus()==="connected"?"#52c41a":"#faad14"}),Ct=c({display:"flex",alignItems:"center",gap:"8px"}),xe=c({marginBottom:"16px",backgroundColor:"white",borderRadius:"8px",border:"1px solid #e8e8e8",padding:"16px"}),lt=c({display:"flex",alignItems:"center",justifyContent:"space-between",marginBottom:"12px"}),_t=c({fontSize:"16px",fontWeight:"600",color:"#262626",margin:0}),st=c({display:"flex",alignItems:"center",gap:"8px"}),le=c({display:"grid",gridTemplateColumns:"repeat(auto-fit, minmax(150px, 1fr))",gap:"16px"}),Rt=c({padding:"12px",backgroundColor:"#fafafa",borderRadius:"4px",textAlign:"center"}),It=c({fontSize:"12px",color:"#8c8c8c",marginBottom:"4px"}),tn=c({fontSize:"16px",fontWeight:"600",color:"#f5222d",marginBottom:"2px"}),nn=c({fontSize:"11px",color:"#f5222d"}),on=c({padding:"12px",backgroundColor:"#fafafa",borderRadius:"4px",textAlign:"center"}),rn=c({fontSize:"12px",color:"#8c8c8c",marginBottom:"4px"}),ln=c({fontSize:"16px",fontWeight:"600",color:"#52c41a",marginBottom:"2px"}),sn=c({fontSize:"11px",color:"#52c41a"}),an=c({padding:"12px",backgroundColor:"#fafafa",borderRadius:"4px",textAlign:"center"}),cn=c({fontSize:"12px",color:"#8c8c8c",marginBottom:"4px"}),dn=c({fontSize:"16px",fontWeight:"600",color:"#f5222d",marginBottom:"2px"}),rr=c({fontSize:"11px",color:"#f5222d"}),ir=c({padding:"12px",backgroundColor:"#fafafa",borderRadius:"4px",textAlign:"center"}),lr=c({fontSize:"12px",color:"#8c8c8c",marginBottom:"4px"}),sr=c({fontSize:"16px",fontWeight:"600",color:"#52c41a",marginBottom:"2px"}),ar=c({fontSize:"11px",color:"#52c41a"}),cr=c({marginBottom:"16px",backgroundColor:"white",borderRadius:"8px",border:"1px solid #e8e8e8",padding:"12px 16px"}),dr=c({display:"flex",alignItems:"center",justifyContent:"space-between",marginBottom:"12px"}),ur=c({fontSize:"14px",fontWeight:"600",color:"#262626",margin:0}),gr=c({fontSize:"12px",color:"#8c8c8c"}),fr=c({display:"flex",alignItems:"center",justifyContent:"space-between"}),pr=c({display:"flex",alignItems:"center",gap:"8px"}),hr=c({display:"flex",alignItems:"center",gap:"6px"}),mr=c({fontSize:"12px",color:"#8c8c8c",marginRight:"4px"}),vr=c({backgroundColor:"white",borderRadius:"8px",border:"1px solid #e8e8e8",overflow:"hidden"}),xr=c({padding:"16px 20px",borderBottom:"1px solid #e8e8e8",backgroundColor:"#fafafa"}),br=c({fontSize:"16px",fontWeight:"600",color:"#262626",margin:"0"}),yr=c({overflowX:"auto"}),Sr=c({width:"100%",borderCollapse:"collapse"}),wr=c({backgroundColor:"#fafafa"}),Cr=c({padding:"12px 16px",textAlign:"left",fontSize:"12px",fontWeight:"600",color:"#8c8c8c"}),_r=c({padding:"12px 16px",textAlign:"left",fontSize:"12px",fontWeight:"600",color:"#8c8c8c"}),Rr=c({padding:"12px 16px",textAlign:"right",fontSize:"12px",fontWeight:"600",color:"#8c8c8c"}),Ir=c({padding:"12px 16px",textAlign:"right",fontSize:"12px",fontWeight:"600",color:"#8c8c8c"}),Pr=c({padding:"12px 16px",textAlign:"right",fontSize:"12px",fontWeight:"600",color:"#8c8c8c"}),kr=c({padding:"12px 16px",textAlign:"right",fontSize:"12px",fontWeight:"600",color:"#8c8c8c"}),$r=c({padding:"12px 16px",textAlign:"right",fontSize:"12px",fontWeight:"600",color:"#8c8c8c"}),Mr=c({padding:"12px 16px",textAlign:"right",fontSize:"12px",fontWeight:"600",color:"#8c8c8c"}),Fr=c({padding:"12px 16px",textAlign:"right",fontSize:"12px",fontWeight:"600",color:"#8c8c8c"}),Er=c({padding:"16px 20px",borderTop:"1px solid #f0f0f0",display:"flex",justifyContent:"space-between",alignItems:"center"}),Ar=c({fontSize:"14px",color:"#8c8c8c"}),Dr=c({display:"flex",gap:"8px",alignItems:"center"}),Tr=c({padding:"4px 8px",backgroundColor:"primary.500",color:"white",borderRadius:"4px",fontSize:"12px"});return Ae!==$.e&&g(f,$.e=Ae),Te!==$.t&&g(p,$.t=Te),Je!==$.a&&g(h,$.a=Je),wt!==$.o&&g(y,$.o=wt),Qe!==$.i&&g(S,$.i=Qe),je!==$.n&&g(b,$.n=je),Bt!==$.s&&g(v,$.s=Bt),Ct!==$.h&&g(C,$.h=Ct),xe!==$.r&&g(M,$.r=xe),lt!==$.d&&g(A,$.d=lt),_t!==$.l&&g(P,$.l=_t),st!==$.u&&g(F,$.u=st),le!==$.c&&g(D,$.c=le),Rt!==$.w&&g(T,$.w=Rt),It!==$.m&&g(k,$.m=It),tn!==$.f&&g(L,$.f=tn),nn!==$.y&&g(B,$.y=nn),on!==$.g&&g(K,$.g=on),rn!==$.p&&g(j,$.p=rn),ln!==$.b&&g(H,$.b=ln),sn!==$.T&&g(U,$.T=sn),an!==$.A&&g(Z,$.A=an),cn!==$.O&&g(J,$.O=cn),dn!==$.I&&g(E,$.I=dn),rr!==$.S&&g(te,$.S=rr),ir!==$.W&&g(ae,$.W=ir),lr!==$.C&&g(pe,$.C=lr),sr!==$.B&&g(he,$.B=sr),ar!==$.v&&g(we,$.v=ar),cr!==$.k&&g(Ce,$.k=cr),dr!==$.x&&g(I,$.x=dr),ur!==$.j&&g(X,$.j=ur),gr!==$.q&&g(se,$.q=gr),fr!==$.z&&g(ve,$.z=fr),pr!==$.P&&g(ie,$.P=pr),hr!==$.H&&g(ge,$.H=hr),mr!==$.F&&g(ke,$.F=mr),vr!==$.M&&g(de,$.M=vr),xr!==$.D&&g(ne,$.D=xr),br!==$.R&&g(_e,$.R=br),yr!==$.E&&g($e,$.E=yr),Sr!==$.L&&g(Fe,$.L=Sr),wr!==$.N&&g(We,$.N=wr),Cr!==$.G&&g(Ne,$.G=Cr),_r!==$.U&&g(Ge,$.U=_r),Rr!==$.K&&g(ht,$.K=Rr),Ir!==$.V&&g(mt,$.V=Ir),Pr!==$.Y&&g(vt,$.Y=Pr),kr!==$.J&&g(xt,$.J=kr),$r!==$.Q&&g(bt,$.Q=$r),Mr!==$.Z&&g(yt,$.Z=Mr),Fr!==$.X&&g(Lt,$.X=Fr),Er!==$._&&g(St,$._=Er),Ar!==$.$&&g(Ze,$.$=Ar),Dr!==$.te&&g(Vt,$.te=Dr),Tr!==$.tt&&g(en,$.tt=Tr),$},{e:void 0,t:void 0,a:void 0,o:void 0,i:void 0,n:void 0,s:void 0,h:void 0,r:void 0,d:void 0,l:void 0,u:void 0,c:void 0,w:void 0,m:void 0,f:void 0,y:void 0,g:void 0,p:void 0,b:void 0,T:void 0,A:void 0,O:void 0,I:void 0,S:void 0,W:void 0,C:void 0,B:void 0,v:void 0,k:void 0,x:void 0,j:void 0,q:void 0,z:void 0,P:void 0,H:void 0,F:void 0,M:void 0,D:void 0,R:void 0,E:void 0,L:void 0,N:void 0,G:void 0,U:void 0,K:void 0,V:void 0,Y:void 0,J:void 0,Q:void 0,Z:void 0,X:void 0,_:void 0,$:void 0,te:void 0,tt:void 0}),f})()}Wt(["click"]);var Ln=V("<span>"),Rg=V("<span>¥"),uo=V("<div>"),Ig=V("<div><div><div><h1>实时行情</h1><div></div></div></div><div><div>"),Pg=V("<div><span>");function kg(){const[e,t]=W(""),[n,o]=W("all"),[r,i]=W(!1),[l,s]=W([]),[a,d]=W([]),m=[{code:"000001",name:"平安银行",price:12.45,change:.23,changePercent:1.88,volume:125e6,turnover:155e7,high:12.68,low:12.2,open:12.3,status:"trading"},{code:"000002",name:"万科A",price:18.76,change:-.45,changePercent:-2.34,volume:89e6,turnover:167e7,high:19.2,low:18.5,open:19.1,status:"trading"},{code:"600036",name:"招商银行",price:35.67,change:1.23,changePercent:3.57,volume:67e6,turnover:239e7,high:36,low:34.8,open:35,status:"trading"},{code:"600519",name:"贵州茅台",price:1678.9,change:-12.5,changePercent:-.74,volume:21e5,turnover:352e7,high:1695,low:1670,open:1685,status:"trading"},{code:"000858",name:"五粮液",price:156.78,change:2.34,changePercent:1.52,volume:156e5,turnover:244e7,high:158.9,low:154.2,open:155,status:"suspended"}],f=[{name:"上证指数",value:3245.67,change:23.45,changePercent:.73},{name:"深证成指",value:12456.78,change:-45.23,changePercent:-.36},{name:"创业板指",value:2567.89,change:12.34,changePercent:.48},{name:"科创50",value:1234.56,change:-8.9,changePercent:-.72}],p=[{key:"all",label:"全部板块",onClick:()=>o("all")},{key:"bank",label:"银行",onClick:()=>o("bank")},{key:"tech",label:"科技",onClick:()=>o("tech")},{key:"consumer",label:"消费",onClick:()=>o("consumer")},{key:"healthcare",label:"医药",onClick:()=>o("healthcare")}],u=[{accessorKey:"code",header:"代码",cell:x=>(()=>{var b=Ln();return w(b,()=>x.getValue()),O(()=>g(b,c({fontFamily:"monospace",fontWeight:"500"}))),b})()},{accessorKey:"name",header:"名称",cell:x=>(()=>{var b=Ln();return w(b,()=>x.getValue()),O(()=>g(b,c({fontWeight:"500",color:"text.primary"}))),b})()},{accessorKey:"price",header:"现价",cell:x=>{const b=x.row.original,v=b.change>0?"danger.500":b.change<0?"success.500":"text.regular";return(()=>{var _=Rg();return _.firstChild,w(_,()=>x.getValue().toFixed(2),null),O(()=>g(_,c({fontFamily:"monospace",fontWeight:"600",color:v}))),_})()}},{accessorKey:"change",header:"涨跌额",cell:x=>{const b=x.getValue(),v=b>0?"danger.500":b<0?"success.500":"text.regular",_=b>0?"+":"";return(()=>{var C=Ln();return w(C,_,null),w(C,()=>b.toFixed(2),null),O(()=>g(C,c({fontFamily:"monospace",color:v}))),C})()}},{accessorKey:"changePercent",header:"涨跌幅",cell:x=>{const b=x.getValue(),v=b>0?"danger":b<0?"success":"info",_=b>0?"+":"";return R(zn,{type:v,effect:"light",size:"small",get children(){return[_,ee(()=>b.toFixed(2)),"%"]}})}},{accessorKey:"volume",header:"成交量",cell:x=>{const b=x.getValue(),v=b>1e8?`${(b/1e8).toFixed(1)}亿`:`${(b/1e4).toFixed(0)}万`;return(()=>{var _=Ln();return w(_,v),O(()=>g(_,c({fontFamily:"monospace",fontSize:"13px"}))),_})()}},{accessorKey:"status",header:"状态",cell:x=>{const b=x.getValue(),v={trading:{label:"交易中",type:"success"},suspended:{label:"停牌",type:"warning"},closed:{label:"休市",type:"info"}},_=v[b]||v.closed;return R(zn,{get type(){return _.type},effect:"light",size:"small",get children(){return _.label}})}},{id:"actions",header:"操作",cell:x=>(()=>{var b=uo();return w(b,R(oe,{size:"small",variant:"primary",onClick:()=>h(x.row.original),children:"买入"}),null),w(b,R(oe,{size:"small",variant:"danger",onClick:()=>y(x.row.original),children:"卖出"}),null),O(()=>g(b,c({display:"flex",gap:"8px"}))),b})()}],h=x=>{gn.success(`买入 ${x.name} 操作已提交`)},y=x=>{gn.error(`卖出 ${x.name} 操作已提交`)},S=()=>{i(!0),gn.info("正在刷新数据..."),setTimeout(()=>{i(!1),gn.success("数据刷新完成")},1500)};return gt(()=>{s(m),d(f)}),(()=>{var x=Ig(),b=x.firstChild,v=b.firstChild,_=v.firstChild,C=_.nextSibling,M=b.nextSibling,A=M.firstChild;return w(C,R(Eo,{placeholder:"搜索股票代码或名称...",get value(){return e()},onInput:P=>t(P.currentTarget.value),clearable:!0,onClear:()=>t(""),get class(){return c({width:"240px"})}}),null),w(C,R(rg,{items:p,get trigger(){return R(oe,{variant:"default",size:"default",get children(){return[ee(()=>p.find(P=>P.key===n())?.label)," ▼"]}})}}),null),w(C,R(oe,{variant:"primary",onClick:S,get loading(){return r()},children:"刷新"}),null),w(A,R(Me,{get each(){return a()},children:P=>R(Vn,{shadow:"hover",bodyStyle:{padding:"16px",textAlign:"center"},get children(){return[(()=>{var F=uo();return w(F,()=>P.name),O(()=>g(F,c({fontSize:"14px",color:"text.secondary",marginBottom:"8px"}))),F})(),(()=>{var F=uo();return w(F,()=>P.value.toFixed(2)),O(()=>g(F,c({fontSize:"20px",fontWeight:"600",color:"text.primary",marginBottom:"4px",fontFamily:"monospace"}))),F})(),(()=>{var F=Pg(),D=F.firstChild;return w(D,()=>P.change>0?"+":"",null),w(D,()=>P.change.toFixed(2),null),w(F,R(zn,{get type(){return ee(()=>P.changePercent>0)()?"danger":P.changePercent<0?"success":"info"},effect:"light",size:"small",get children(){return[ee(()=>P.changePercent>0?"+":""),ee(()=>P.changePercent.toFixed(2)),"%"]}}),null),O(T=>{var k=c({display:"flex",alignItems:"center",justifyContent:"center",gap:"8px"}),L=c({fontSize:"12px",color:P.change>0?"danger.500":P.change<0?"success.500":"text.regular",fontFamily:"monospace"});return k!==T.e&&g(F,T.e=k),L!==T.t&&g(D,T.t=L),T},{e:void 0,t:void 0}),F})()]}})})),w(x,R(Vn,{header:"股票列表",shadow:"always",get children(){return R(Ga,{get data(){return l()},columns:u,get loading(){return r()},pagination:!0,pageSize:10,sortable:!0,sticky:!0,height:"600px",onRowClick:P=>gn.info(`查看 ${P.name} 详情`)})}}),null),O(P=>{var F=c({padding:"20px",minHeight:"100vh",backgroundColor:"bg.page"}),D=c({marginBottom:"20px"}),T=c({display:"flex",alignItems:"center",justifyContent:"space-between",marginBottom:"16px"}),k=c({fontSize:"24px",fontWeight:"600",color:"text.primary",margin:0}),L=c({display:"flex",alignItems:"center",gap:"12px"}),B=c({marginBottom:"24px"}),K=c({display:"grid",gridTemplateColumns:"repeat(auto-fit, minmax(200px, 1fr))",gap:"16px"});return F!==P.e&&g(x,P.e=F),D!==P.t&&g(b,P.t=D),T!==P.a&&g(v,P.a=T),k!==P.o&&g(_,P.o=k),L!==P.i&&g(C,P.i=L),B!==P.n&&g(M,P.n=B),K!==P.s&&g(A,P.s=K),P},{e:void 0,t:void 0,a:void 0,o:void 0,i:void 0,n:void 0,s:void 0}),x})()}var $g=V("<div><div>");function Mg(e){const[t,n]=W();let o;return gt(async()=>{const r=t();if(r)try{const l="/libs/monaco-editor/min/vs";console.log(`Monaco Editor 配置: 本地模式, 路径: ${l}`),zr.config({paths:{vs:l}});const s=await zr.init();e.language==="python"&&s.languages.registerCompletionItemProvider("python",{provideCompletionItems:(a,d)=>{const m=a.getWordUntilPosition(d),f={startLineNumber:d.lineNumber,endLineNumber:d.lineNumber,startColumn:m.startColumn,endColumn:m.endColumn};return{suggestions:[{label:"def",kind:s.languages.CompletionItemKind.Keyword,insertText:"def ${1:function_name}(${2:parameters}):\n    ${3:pass}",insertTextRules:s.languages.CompletionItemInsertTextRule.InsertAsSnippet,documentation:"Define a function",range:f},{label:"initialize",kind:s.languages.CompletionItemKind.Function,insertText:"def initialize(context):\n    ${1:pass}",insertTextRules:s.languages.CompletionItemInsertTextRule.InsertAsSnippet,documentation:"策略初始化函数",range:f},{label:"handle_data",kind:s.languages.CompletionItemKind.Function,insertText:"def handle_data(context, data):\n    ${1:pass}",insertTextRules:s.languages.CompletionItemInsertTextRule.InsertAsSnippet,documentation:"主要的交易逻辑函数",range:f},{label:"order_target_percent",kind:s.languages.CompletionItemKind.Function,insertText:"order_target_percent(${1:security}, ${2:percent})",insertTextRules:s.languages.CompletionItemInsertTextRule.InsertAsSnippet,documentation:"下单到目标百分比",range:f},{label:"attribute_history",kind:s.languages.CompletionItemKind.Function,insertText:"attribute_history(${1:security}, ${2:count}, ${3:unit}, ${4:fields})",insertTextRules:s.languages.CompletionItemInsertTextRule.InsertAsSnippet,documentation:"获取历史数据",range:f},{label:"log.info",kind:s.languages.CompletionItemKind.Function,insertText:"log.info(${1:message})",insertTextRules:s.languages.CompletionItemInsertTextRule.InsertAsSnippet,documentation:"输出日志信息",range:f}]}}}),o=s.editor.create(r,{value:e.value||"",language:e.language||"python",theme:e.theme||"vs",fontSize:13,lineNumbers:"on",roundedSelection:!1,scrollBeyondLastLine:!1,minimap:{enabled:!0},automaticLayout:!0,tabSize:4,insertSpaces:!0,wordWrap:"on",folding:!0,renderLineHighlight:"all",selectOnLineNumbers:!0,matchBrackets:"always",...e.options}),o.onDidChangeModelContent(()=>{e.onChange&&o&&e.onChange(o.getValue())}),o.addCommand(s.KeyMod.CtrlCmd|s.KeyCode.KeyS,()=>{console.log("保存策略快捷键触发")}),o.addCommand(s.KeyMod.CtrlCmd|s.KeyCode.Enter,()=>{console.log("运行策略快捷键触发")})}catch(i){console.error("Monaco Editor 初始化失败:",i)}}),Q(()=>{o&&o.dispose()}),(()=>{var r=$g(),i=r.firstChild;return as(n,i),O(l=>{var s=c({width:"100%",height:`${e.height||400}px`,border:"1px solid #e5e7eb",borderRadius:"6px",overflow:"hidden"}),a=c({width:"100%",height:"100%"});return s!==l.e&&g(r,l.e=s),a!==l.t&&g(i,l.t=a),l},{e:void 0,t:void 0}),r})()}var Fg=V('<div><div><h1>🧠 策略编辑器</h1><p>创建和编辑量化交易策略</p></div><div><div><div><h2>策略代码</h2><div><button>运行回测</button><button>保存策略</button></div></div><div></div></div><div><div><h2>回测结果</h2></div><div><div><div>📊</div><p>等待回测结果</p><p>点击"运行回测"开始策略测试</p></div></div></div></div><div><h3>策略模板</h3><div><button><div>双均线策略</div><div>基于移动平均线的经典策略</div></button><button><div>RSI策略</div><div>基于相对强弱指标的策略</div></button><button><div>布林带策略</div><div>利用布林带进行交易决策</div></button><button><div>机器学习策略</div><div>基于AI模型的量化策略');function Eg(){const[e,t]=W(`# 量化策略示例
# 这是一个简单的移动平均线策略

def initialize(context):
    # 设置基准和股票
    g.benchmark = '000300.XSHG'
    g.stock = '000001.XSHE'
    
def handle_data(context, data):
    # 获取历史价格
    hist = attribute_history(g.stock, 20, '1d', ['close'])
    ma5 = hist['close'][-5:].mean()
    ma20 = hist['close'][-20:].mean()
    current_price = data[g.stock].close
    
    # 交易逻辑
    if ma5 > ma20 and current_price > ma5:
        # 金叉买入信号
        order_target_percent(g.stock, 0.8)
        log.info(f"买入信号，价格: {current_price}")
    elif ma5 < ma20:
        # 死叉卖出信号
        order_target_percent(g.stock, 0)
        log.info(f"卖出信号，价格: {current_price}")
`);return(()=>{var n=Fg(),o=n.firstChild,r=o.firstChild,i=r.nextSibling,l=o.nextSibling,s=l.firstChild,a=s.firstChild,d=a.firstChild,m=d.nextSibling,f=m.firstChild,p=f.nextSibling,u=a.nextSibling,h=s.nextSibling,y=h.firstChild,S=y.firstChild,x=y.nextSibling,b=x.firstChild,v=b.firstChild,_=v.nextSibling,C=_.nextSibling,M=l.nextSibling,A=M.firstChild,P=A.nextSibling,F=P.firstChild,D=F.firstChild,T=D.nextSibling,k=F.nextSibling,L=k.firstChild,B=L.nextSibling,K=k.nextSibling,j=K.firstChild,H=j.nextSibling,U=K.nextSibling,Z=U.firstChild,J=Z.nextSibling;return w(u,R(Mg,{get value(){return e()},language:"python",theme:"vs",height:500,onChange:t,options:{minimap:{enabled:!0},fontSize:14,wordWrap:"on",automaticLayout:!0}})),O(E=>{var te=c({padding:"24px",maxWidth:"1400px",margin:"0 auto",height:"100%"}),ae=c({marginBottom:"32px"}),pe=c({fontSize:"32px",fontWeight:"bold",color:"gray.900",marginBottom:"8px"}),he=c({fontSize:"16px",color:"gray.600"}),we=c({display:"grid",gridTemplateColumns:"1fr 1fr",gap:"24px",height:"calc(100vh - 200px)"}),Ce=c({backgroundColor:"white",borderRadius:"12px",boxShadow:"0 1px 3px rgba(0, 0, 0, 0.1)",border:"1px solid #e5e7eb",overflow:"hidden",display:"flex",flexDirection:"column"}),I=c({padding:"16px 20px",borderBottom:"1px solid #e5e7eb",backgroundColor:"#f9fafb",display:"flex",justifyContent:"space-between",alignItems:"center"}),X=c({fontSize:"16px",fontWeight:"600",color:"gray.900"}),se=c({display:"flex",gap:"8px"}),me=c({padding:"6px 12px",backgroundColor:"blue.600",color:"white",border:"none",borderRadius:"6px",fontSize:"12px",fontWeight:"500",cursor:"pointer",_hover:{backgroundColor:"blue.700"}}),ue=c({padding:"6px 12px",backgroundColor:"green.600",color:"white",border:"none",borderRadius:"6px",fontSize:"12px",fontWeight:"500",cursor:"pointer",_hover:{backgroundColor:"green.700"}}),ve=c({flex:1,padding:"8px"}),ie=c({backgroundColor:"white",borderRadius:"12px",boxShadow:"0 1px 3px rgba(0, 0, 0, 0.1)",border:"1px solid #e5e7eb",overflow:"hidden",display:"flex",flexDirection:"column"}),ge=c({padding:"16px 20px",borderBottom:"1px solid #e5e7eb",backgroundColor:"#f9fafb"}),ke=c({fontSize:"16px",fontWeight:"600",color:"gray.900"}),de=c({flex:1,padding:"20px",display:"flex",alignItems:"center",justifyContent:"center"}),ne=c({textAlign:"center",color:"gray.500"}),_e=c({fontSize:"48px",marginBottom:"16px"}),$e=c({fontSize:"16px",marginBottom:"8px"}),Fe=c({fontSize:"14px"}),Ee=c({marginTop:"24px",backgroundColor:"white",borderRadius:"12px",boxShadow:"0 1px 3px rgba(0, 0, 0, 0.1)",border:"1px solid #e5e7eb",padding:"20px"}),We=c({fontSize:"18px",fontWeight:"600",color:"gray.900",marginBottom:"16px"}),Ne=c({display:"grid",gridTemplateColumns:"repeat(auto-fit, minmax(200px, 1fr))",gap:"12px"}),Ge=c({padding:"12px 16px",border:"1px solid #e5e7eb",borderRadius:"8px",backgroundColor:"white",textAlign:"left",cursor:"pointer",transition:"all 0.2s",_hover:{borderColor:"blue.500",backgroundColor:"blue.50"}}),ht=c({fontSize:"14px",fontWeight:"500",color:"gray.900",marginBottom:"4px"}),mt=c({fontSize:"12px",color:"gray.600"}),vt=c({padding:"12px 16px",border:"1px solid #e5e7eb",borderRadius:"8px",backgroundColor:"white",textAlign:"left",cursor:"pointer",transition:"all 0.2s",_hover:{borderColor:"blue.500",backgroundColor:"blue.50"}}),xt=c({fontSize:"14px",fontWeight:"500",color:"gray.900",marginBottom:"4px"}),bt=c({fontSize:"12px",color:"gray.600"}),yt=c({padding:"12px 16px",border:"1px solid #e5e7eb",borderRadius:"8px",backgroundColor:"white",textAlign:"left",cursor:"pointer",transition:"all 0.2s",_hover:{borderColor:"blue.500",backgroundColor:"blue.50"}}),Lt=c({fontSize:"14px",fontWeight:"500",color:"gray.900",marginBottom:"4px"}),zt=c({fontSize:"12px",color:"gray.600"}),St=c({padding:"12px 16px",border:"1px solid #e5e7eb",borderRadius:"8px",backgroundColor:"white",textAlign:"left",cursor:"pointer",transition:"all 0.2s",_hover:{borderColor:"blue.500",backgroundColor:"blue.50"}}),Ze=c({fontSize:"14px",fontWeight:"500",color:"gray.900",marginBottom:"4px"}),Ot=c({fontSize:"12px",color:"gray.600"});return te!==E.e&&g(n,E.e=te),ae!==E.t&&g(o,E.t=ae),pe!==E.a&&g(r,E.a=pe),he!==E.o&&g(i,E.o=he),we!==E.i&&g(l,E.i=we),Ce!==E.n&&g(s,E.n=Ce),I!==E.s&&g(a,E.s=I),X!==E.h&&g(d,E.h=X),se!==E.r&&g(m,E.r=se),me!==E.d&&g(f,E.d=me),ue!==E.l&&g(p,E.l=ue),ve!==E.u&&g(u,E.u=ve),ie!==E.c&&g(h,E.c=ie),ge!==E.w&&g(y,E.w=ge),ke!==E.m&&g(S,E.m=ke),de!==E.f&&g(x,E.f=de),ne!==E.y&&g(b,E.y=ne),_e!==E.g&&g(v,E.g=_e),$e!==E.p&&g(_,E.p=$e),Fe!==E.b&&g(C,E.b=Fe),Ee!==E.T&&g(M,E.T=Ee),We!==E.A&&g(A,E.A=We),Ne!==E.O&&g(P,E.O=Ne),Ge!==E.I&&g(F,E.I=Ge),ht!==E.S&&g(D,E.S=ht),mt!==E.W&&g(T,E.W=mt),vt!==E.C&&g(k,E.C=vt),xt!==E.B&&g(L,E.B=xt),bt!==E.v&&g(B,E.v=bt),yt!==E.k&&g(K,E.k=yt),Lt!==E.x&&g(j,E.x=Lt),zt!==E.j&&g(H,E.j=zt),St!==E.q&&g(U,E.q=St),Ze!==E.z&&g(Z,E.z=Ze),Ot!==E.P&&g(J,E.P=Ot),E},{e:void 0,t:void 0,a:void 0,o:void 0,i:void 0,n:void 0,s:void 0,h:void 0,r:void 0,d:void 0,l:void 0,u:void 0,c:void 0,w:void 0,m:void 0,f:void 0,y:void 0,g:void 0,p:void 0,b:void 0,T:void 0,A:void 0,O:void 0,I:void 0,S:void 0,W:void 0,C:void 0,B:void 0,v:void 0,k:void 0,x:void 0,j:void 0,q:void 0,z:void 0,P:void 0}),n})()}var Ag=V("<button>"),Dg=V("<div>");const go=e=>{const[t,n]=re(e,["variant","size","loading","icon","fullWidth","children","class","disabled"]),o=c({display:"inline-flex",alignItems:"center",justifyContent:"center",gap:"8px",borderRadius:"8px",fontWeight:"500",transition:"all 0.2s",cursor:"pointer",border:"none",outline:"none",textDecoration:"none",userSelect:"none",_focus:{boxShadow:"0 0 0 3px rgba(59, 130, 246, 0.1)"},_disabled:{opacity:.6,cursor:"not-allowed"}}),r={primary:c({backgroundColor:"blue.600",color:"white",_hover:{backgroundColor:"blue.700"},_active:{backgroundColor:"blue.800"}}),secondary:c({backgroundColor:"gray.100",color:"gray.900",_hover:{backgroundColor:"gray.200"},_active:{backgroundColor:"gray.300"}}),success:c({backgroundColor:"green.600",color:"white",_hover:{backgroundColor:"green.700"},_active:{backgroundColor:"green.800"}}),warning:c({backgroundColor:"yellow.500",color:"white",_hover:{backgroundColor:"yellow.600"},_active:{backgroundColor:"yellow.700"}}),danger:c({backgroundColor:"red.600",color:"white",_hover:{backgroundColor:"red.700"},_active:{backgroundColor:"red.800"}}),ghost:c({backgroundColor:"transparent",color:"gray.700",border:"1px solid",borderColor:"gray.300",_hover:{backgroundColor:"gray.50",borderColor:"gray.400"},_active:{backgroundColor:"gray.100"}})},i={sm:c({padding:"6px 12px",fontSize:"14px",minHeight:"32px"}),md:c({padding:"8px 16px",fontSize:"14px",minHeight:"40px"}),lg:c({padding:"12px 24px",fontSize:"16px",minHeight:"48px"})},l=c({width:"100%"}),s=c({width:"16px",height:"16px",border:"2px solid currentColor",borderTopColor:"transparent",borderRadius:"50%",animation:"spin 1s linear infinite"}),a=t.variant||"primary",d=t.size||"md";return(()=>{var m=Ag();return At(m,q({get class(){return Kt(o,r[a],i[d],t.fullWidth&&l,t.class)},get disabled(){return t.disabled||t.loading}},n),!1,!0),w(m,(()=>{var f=ee(()=>!!t.loading);return()=>f()&&(()=>{var p=Dg();return g(p,s),p})()})(),null),w(m,(()=>{var f=ee(()=>!!(!t.loading&&t.icon));return()=>f()&&t.icon})(),null),w(m,()=>t.children,null),m})()};var Tg=V("<div><div>"),Lg=V("<div><div><div>"),zg=V("<h3>"),Og=V("<p>");const Gt=e=>{const[t,n]=re(e,["title","subtitle","headerAction","padding","shadow","border","hover","children","class"]),o=c({backgroundColor:"white",borderRadius:"12px",overflow:"hidden",transition:"all 0.2s"}),r={none:"",sm:c({boxShadow:"0 1px 2px rgba(0, 0, 0, 0.05)"}),md:c({boxShadow:"0 4px 6px rgba(0, 0, 0, 0.07)"}),lg:c({boxShadow:"0 10px 15px rgba(0, 0, 0, 0.1)"})},i=c({border:"1px solid",borderColor:"gray.200"}),l=c({_hover:{transform:"translateY(-2px)",boxShadow:"0 8px 25px rgba(0, 0, 0, 0.1)"}}),s={none:"",sm:c({padding:"16px"}),md:c({padding:"24px"}),lg:c({padding:"32px"})},a=c({padding:"24px 24px 0 24px",marginBottom:"16px"}),d=c({fontSize:"18px",fontWeight:"600",color:"gray.900",marginBottom:"4px"}),m=c({fontSize:"14px",color:"gray.600"}),f=c({display:"flex",justifyContent:"space-between",alignItems:"flex-start"}),p=t.shadow||"md",u=t.padding||"md";return(()=>{var h=Tg(),y=h.firstChild;return At(h,q({get class(){return Kt(o,r[p],t.border&&i,t.hover&&l,t.class)}},n),!1,!0),w(h,(()=>{var S=ee(()=>!!(t.title||t.subtitle||t.headerAction));return()=>S()&&(()=>{var x=Lg(),b=x.firstChild,v=b.firstChild;return g(x,a),w(v,(()=>{var _=ee(()=>!!t.title);return()=>_()&&(()=>{var C=zg();return g(C,d),w(C,()=>t.title),C})()})(),null),w(v,(()=>{var _=ee(()=>!!t.subtitle);return()=>_()&&(()=>{var C=Og();return g(C,m),w(C,()=>t.subtitle),C})()})(),null),w(b,(()=>{var _=ee(()=>!!t.headerAction);return()=>_()&&t.headerAction})(),null),O(()=>g(b,t.headerAction?f:"")),x})()})(),y),w(y,()=>t.children),O(()=>g(y,s[u])),h})()};var Vg=V('<button aria-label="Close modal">×'),Bg=V("<div><h2><span>"),Kg=V("<div>"),Hg=V("<div><div><div>");const Wg=e=>{Y(()=>{if(e.isOpen){const f=p=>{p.key==="Escape"&&e.closable!==!1&&e.onClose()};document.addEventListener("keydown",f),document.body.style.overflow="hidden",Q(()=>{document.removeEventListener("keydown",f),document.body.style.overflow=""})}});const t=c({position:"fixed",top:0,left:0,right:0,bottom:0,backgroundColor:"rgba(0, 0, 0, 0.5)",display:"flex",alignItems:"center",justifyContent:"center",zIndex:1e3,padding:"16px"}),n=c({backgroundColor:"white",borderRadius:"12px",boxShadow:"0 20px 25px rgba(0, 0, 0, 0.1)",maxHeight:"90vh",overflow:"hidden",display:"flex",flexDirection:"column",animation:"modalEnter 0.2s ease-out"}),o={sm:c({width:"400px",maxWidth:"90vw"}),md:c({width:"600px",maxWidth:"90vw"}),lg:c({width:"800px",maxWidth:"90vw"}),xl:c({width:"1000px",maxWidth:"90vw"})},r=c({padding:"24px 24px 0 24px",borderBottom:"1px solid",borderColor:"gray.200",paddingBottom:"16px",marginBottom:"24px"}),i=c({fontSize:"20px",fontWeight:"600",color:"gray.900",margin:0,display:"flex",justifyContent:"space-between",alignItems:"center"}),l=c({background:"none",border:"none",fontSize:"24px",cursor:"pointer",color:"gray.400",padding:"4px",borderRadius:"4px",_hover:{color:"gray.600",backgroundColor:"gray.100"}}),s=c({padding:"0 24px",flex:1,overflow:"auto"}),a=c({padding:"16px 24px 24px 24px",borderTop:"1px solid",borderColor:"gray.200",marginTop:"24px",display:"flex",justifyContent:"flex-end",gap:"12px"}),d=e.size||"md",m=f=>{f.target===f.currentTarget&&e.maskClosable!==!1&&e.onClose()};return R(ce,{get when(){return e.isOpen},get children(){return R(Mo,{get children(){var f=Hg(),p=f.firstChild,u=p.firstChild;return f.$$click=m,g(f,t),p.$$click=h=>h.stopPropagation(),w(p,R(ce,{get when(){return e.title||e.closable!==!1},get children(){var h=Bg(),y=h.firstChild,S=y.firstChild;return g(h,r),g(y,i),w(S,()=>e.title),w(y,R(ce,{get when(){return e.closable!==!1},get children(){var x=Vg();return Pi(x,"click",e.onClose,!0),g(x,l),x}}),null),h}}),u),g(u,s),w(u,()=>e.children),w(p,R(ce,{get when(){return e.footer},get children(){var h=Kg();return g(h,a),w(h,()=>e.footer),h}}),null),O(()=>g(p,Kt(n,o[d],e.class))),f}})}})};Wt(["click"]);var Ng=V("<label>"),fo=V("<div>"),Gg=V("<div><div><input>");const po=e=>{const[t,n]=re(e,["label","error","helperText","leftIcon","rightIcon","size","fullWidth","class"]),o=c({display:"flex",flexDirection:"column",gap:"6px"}),r=c({width:"100%"}),i=c({fontSize:"14px",fontWeight:"500",color:"gray.700"}),l=c({position:"relative",display:"flex",alignItems:"center"}),s=c({width:"100%",border:"1px solid",borderColor:"gray.300",borderRadius:"8px",transition:"all 0.2s",backgroundColor:"white",color:"gray.900",_focus:{outline:"none",borderColor:"blue.500",boxShadow:"0 0 0 3px rgba(59, 130, 246, 0.1)"},_disabled:{backgroundColor:"gray.100",color:"gray.500",cursor:"not-allowed"},_placeholder:{color:"gray.400"}}),a=c({borderColor:"red.500",_focus:{borderColor:"red.500",boxShadow:"0 0 0 3px rgba(239, 68, 68, 0.1)"}}),d={sm:c({padding:"8px 12px",fontSize:"14px",minHeight:"36px"}),md:c({padding:"10px 14px",fontSize:"14px",minHeight:"40px"}),lg:c({padding:"12px 16px",fontSize:"16px",minHeight:"48px"})},m=c({position:"absolute",top:"50%",transform:"translateY(-50%)",color:"gray.400",pointerEvents:"none",zIndex:1}),f=c({left:"12px"}),p=c({right:"12px"}),u=c({paddingLeft:"40px"}),h=c({paddingRight:"40px"}),y=c({fontSize:"12px",color:"gray.600"}),S=c({fontSize:"12px",color:"red.600"}),x=t.size||"md";return(()=>{var b=Gg(),v=b.firstChild,_=v.firstChild;return w(b,R(ce,{get when(){return t.label},get children(){var C=Ng();return g(C,i),w(C,()=>t.label),C}}),v),g(v,l),w(v,R(ce,{get when(){return t.leftIcon},get children(){var C=fo();return w(C,()=>t.leftIcon),O(()=>g(C,Kt(m,f))),C}}),_),At(_,q({get class(){return Kt(s,d[x],t.error?a:void 0,t.leftIcon?u:void 0,t.rightIcon?h:void 0,t.class)}},n),!1,!1),w(v,R(ce,{get when(){return t.rightIcon},get children(){var C=fo();return w(C,()=>t.rightIcon),O(()=>g(C,Kt(m,p))),C}}),null),w(b,R(ce,{get when(){return t.error||t.helperText},get children(){var C=fo();return w(C,()=>t.error||t.helperText),O(()=>g(C,t.error?S:y)),C}}),null),O(()=>g(b,Kt(o,t.fullWidth?r:void 0))),b})()};var jg=V("<div><h3>账户总值</h3><p>¥"),Ug=V("<div><h3>总盈亏</h3><p>¥</p><p>%"),qg=V("<div><h3>可用资金</h3><p>¥"),Xg=V("<div><h3>已用保证金</h3><p>¥"),Yg=V("<div><table><thead><tr><th>代码</th><th>数量</th><th>盈亏</th></tr></thead><tbody>"),Zg=V("<div><table><thead><tr><th>代码</th><th>类型</th><th>状态</th></tr></thead><tbody>"),Jg=V("<div><div><div><label>交易类型</label><select aria-label=交易类型><option value=buy>买入</option><option value=sell>卖出</option></select></div><div><label>订单类型</label><select aria-label=订单类型><option value=market>市价单</option><option value=limit>限价单</option></select></div></div><div>"),Qg=V("<div slot=footer>"),ef=V("<div><div><div><h1>💼 </h1><p>管理您的交易订单和持仓</p></div></div><div></div><div>"),tf=V("<span>➕"),nf=V("<tr><td></td><td></td><td>¥<br><span>%"),of=V("<tr><td><br><span>股 @ ¥</span></td><td><span></span></td><td><span>");const rf=()=>({t:e=>e});function lf(){const{t:e}=rf(),[t,n]=W([{id:"1",symbol:"AAPL",type:"buy",quantity:100,price:150.25,status:"filled",timestamp:new Date("2024-01-15T10:30:00")},{id:"2",symbol:"TSLA",type:"sell",quantity:50,price:245.8,status:"pending",timestamp:new Date("2024-01-15T11:15:00")},{id:"3",symbol:"MSFT",type:"buy",quantity:75,price:310.45,status:"filled",timestamp:new Date("2024-01-15T09:45:00")}]),[o,r]=W([{symbol:"AAPL",quantity:100,avgPrice:150.25,currentPrice:152.3,unrealizedPnL:205,unrealizedPnLPercent:1.36},{symbol:"MSFT",quantity:75,avgPrice:310.45,currentPrice:308.9,unrealizedPnL:-116.25,unrealizedPnLPercent:-.5}]),[i,l]=W(!1),[s,a]=W({symbol:"",type:"buy",quantity:"",price:"",orderType:"limit"}),[d]=W({totalValue:45678.9,totalPnL:88.75,totalPnLPercent:.19,buyingPower:12345.67,marginUsed:5432.1});gt(()=>{console.log("Trading page mounted");const p=setInterval(()=>{r(u=>u.map(h=>{const y=(Math.random()-.5)*2,S=h.currentPrice+y,x=(S-h.avgPrice)*h.quantity,b=x/(h.avgPrice*h.quantity)*100;return{...h,currentPrice:S,unrealizedPnL:x,unrealizedPnLPercent:b}}))},3e3);return()=>clearInterval(p)});const m=()=>{const p=s();if(!p.symbol||!p.quantity||!p.price&&p.orderType==="limit"){alert("请填写完整的订单信息");return}const u={id:Date.now().toString(),symbol:p.symbol.toUpperCase(),type:p.type,quantity:parseInt(p.quantity),price:p.orderType==="market"?0:parseFloat(p.price),status:"pending",timestamp:new Date};n(h=>[u,...h]),l(!1),a({symbol:"",type:"buy",quantity:"",price:"",orderType:"limit"})},f=d();return(()=>{var p=ef(),u=p.firstChild,h=u.firstChild,y=h.firstChild;y.firstChild;var S=y.nextSibling,x=u.nextSibling,b=x.nextSibling;return w(y,()=>e("nav.trading"),null),w(u,R(go,{variant:"primary",size:"lg",get icon(){return tf()},onClick:()=>l(!0),children:"新建订单"}),null),w(x,R(Gt,{padding:"md",shadow:"md",get children(){var v=jg(),_=v.firstChild,C=_.nextSibling;return C.firstChild,w(C,()=>f.totalValue.toLocaleString(),null),O(M=>{var A=c({textAlign:"center"}),P=c({fontSize:"14px",fontWeight:"500",color:"gray.600",marginBottom:"8px"}),F=c({fontSize:"24px",fontWeight:"bold",color:"gray.900"});return A!==M.e&&g(v,M.e=A),P!==M.t&&g(_,M.t=P),F!==M.a&&g(C,M.a=F),M},{e:void 0,t:void 0,a:void 0}),v}}),null),w(x,R(Gt,{padding:"md",shadow:"md",get children(){var v=Ug(),_=v.firstChild,C=_.nextSibling,M=C.firstChild,A=C.nextSibling,P=A.firstChild;return w(C,()=>f.totalPnL>=0?"+":"",M),w(C,()=>Math.abs(f.totalPnL).toFixed(2),null),w(A,()=>f.totalPnL>=0?"+":"",P),w(A,()=>f.totalPnLPercent.toFixed(2),P),O(F=>{var D=c({textAlign:"center"}),T=c({fontSize:"14px",fontWeight:"500",color:"gray.600",marginBottom:"8px"}),k=c({fontSize:"24px",fontWeight:"bold",color:f.totalPnL>=0?"green.600":"red.600"}),L=c({fontSize:"12px",color:f.totalPnL>=0?"green.600":"red.600"});return D!==F.e&&g(v,F.e=D),T!==F.t&&g(_,F.t=T),k!==F.a&&g(C,F.a=k),L!==F.o&&g(A,F.o=L),F},{e:void 0,t:void 0,a:void 0,o:void 0}),v}}),null),w(x,R(Gt,{padding:"md",shadow:"md",get children(){var v=qg(),_=v.firstChild,C=_.nextSibling;return C.firstChild,w(C,()=>f.buyingPower.toLocaleString(),null),O(M=>{var A=c({textAlign:"center"}),P=c({fontSize:"14px",fontWeight:"500",color:"gray.600",marginBottom:"8px"}),F=c({fontSize:"24px",fontWeight:"bold",color:"gray.900"});return A!==M.e&&g(v,M.e=A),P!==M.t&&g(_,M.t=P),F!==M.a&&g(C,M.a=F),M},{e:void 0,t:void 0,a:void 0}),v}}),null),w(x,R(Gt,{padding:"md",shadow:"md",get children(){var v=Xg(),_=v.firstChild,C=_.nextSibling;return C.firstChild,w(C,()=>f.marginUsed.toLocaleString(),null),O(M=>{var A=c({textAlign:"center"}),P=c({fontSize:"14px",fontWeight:"500",color:"gray.600",marginBottom:"8px"}),F=c({fontSize:"24px",fontWeight:"bold",color:"orange.600"});return A!==M.e&&g(v,M.e=A),P!==M.t&&g(_,M.t=P),F!==M.a&&g(C,M.a=F),M},{e:void 0,t:void 0,a:void 0}),v}}),null),w(b,R(Gt,{title:"当前持仓",padding:"none",shadow:"md",get children(){var v=Yg(),_=v.firstChild,C=_.firstChild,M=C.firstChild,A=M.firstChild,P=A.nextSibling,F=P.nextSibling,D=C.nextSibling;return w(D,R(Me,{get each(){return o()},children:T=>(()=>{var k=nf(),L=k.firstChild,B=L.nextSibling,K=B.nextSibling,j=K.firstChild,H=j.nextSibling,U=H.nextSibling,Z=U.firstChild;return w(L,()=>T.symbol),w(B,()=>T.quantity),w(K,()=>T.unrealizedPnL>=0?"+":"",j),w(K,()=>Math.abs(T.unrealizedPnL).toFixed(2),H),w(U,()=>T.unrealizedPnL>=0?"+":"",Z),w(U,()=>T.unrealizedPnLPercent.toFixed(2),Z),O(J=>{var E=c({borderBottom:"1px solid",borderColor:"gray.200"}),te=c({padding:"12px 16px",fontSize:"14px",fontWeight:"500",color:"gray.900"}),ae=c({padding:"12px 16px",textAlign:"right",fontSize:"14px",color:"gray.700"}),pe=c({padding:"12px 16px",textAlign:"right",fontSize:"14px",fontWeight:"500",color:T.unrealizedPnL>=0?"green.600":"red.600"}),he=c({fontSize:"12px"});return E!==J.e&&g(k,J.e=E),te!==J.t&&g(L,J.t=te),ae!==J.a&&g(B,J.a=ae),pe!==J.o&&g(K,J.o=pe),he!==J.i&&g(U,J.i=he),J},{e:void 0,t:void 0,a:void 0,o:void 0,i:void 0}),k})()})),O(T=>{var k=c({overflowX:"auto"}),L=c({width:"100%",borderCollapse:"collapse"}),B=c({backgroundColor:"gray.50"}),K=c({padding:"12px 16px",textAlign:"left",fontSize:"12px",fontWeight:"600",color:"gray.600"}),j=c({padding:"12px 16px",textAlign:"right",fontSize:"12px",fontWeight:"600",color:"gray.600"}),H=c({padding:"12px 16px",textAlign:"right",fontSize:"12px",fontWeight:"600",color:"gray.600"});return k!==T.e&&g(v,T.e=k),L!==T.t&&g(_,T.t=L),B!==T.a&&g(M,T.a=B),K!==T.o&&g(A,T.o=K),j!==T.i&&g(P,T.i=j),H!==T.n&&g(F,T.n=H),T},{e:void 0,t:void 0,a:void 0,o:void 0,i:void 0,n:void 0}),v}}),null),w(b,R(Gt,{title:"订单历史",padding:"none",shadow:"md",get children(){var v=Zg(),_=v.firstChild,C=_.firstChild,M=C.firstChild,A=M.firstChild,P=A.nextSibling,F=P.nextSibling,D=C.nextSibling;return w(D,R(Me,{get each(){return t()},children:T=>(()=>{var k=of(),L=k.firstChild,B=L.firstChild,K=B.nextSibling,j=K.firstChild,H=L.nextSibling,U=H.firstChild,Z=H.nextSibling,J=Z.firstChild;return w(L,()=>T.symbol,B),w(K,()=>T.quantity,j),w(K,()=>T.price,null),w(U,()=>T.type==="buy"?"买入":"卖出"),w(J,(()=>{var E=ee(()=>T.status==="filled");return()=>E()?"已成交":T.status==="pending"?"待成交":"已取消"})()),O(E=>{var te=c({borderBottom:"1px solid",borderColor:"gray.200"}),ae=c({padding:"12px 16px",fontSize:"14px",fontWeight:"500",color:"gray.900"}),pe=c({fontSize:"12px",color:"gray.600"}),he=c({padding:"12px 16px",textAlign:"center",fontSize:"14px"}),we=c({padding:"4px 8px",borderRadius:"4px",fontSize:"12px",fontWeight:"500",backgroundColor:T.type==="buy"?"green.100":"red.100",color:T.type==="buy"?"green.800":"red.800"}),Ce=c({padding:"12px 16px",textAlign:"right",fontSize:"14px"}),I=c({padding:"4px 8px",borderRadius:"4px",fontSize:"12px",fontWeight:"500",backgroundColor:T.status==="filled"?"green.100":T.status==="pending"?"yellow.100":"red.100",color:T.status==="filled"?"green.800":T.status==="pending"?"yellow.800":"red.800"});return te!==E.e&&g(k,E.e=te),ae!==E.t&&g(L,E.t=ae),pe!==E.a&&g(K,E.a=pe),he!==E.o&&g(H,E.o=he),we!==E.i&&g(U,E.i=we),Ce!==E.n&&g(Z,E.n=Ce),I!==E.s&&g(J,E.s=I),E},{e:void 0,t:void 0,a:void 0,o:void 0,i:void 0,n:void 0,s:void 0}),k})()})),O(T=>{var k=c({overflowX:"auto"}),L=c({width:"100%",borderCollapse:"collapse"}),B=c({backgroundColor:"gray.50"}),K=c({padding:"12px 16px",textAlign:"left",fontSize:"12px",fontWeight:"600",color:"gray.600"}),j=c({padding:"12px 16px",textAlign:"center",fontSize:"12px",fontWeight:"600",color:"gray.600"}),H=c({padding:"12px 16px",textAlign:"right",fontSize:"12px",fontWeight:"600",color:"gray.600"});return k!==T.e&&g(v,T.e=k),L!==T.t&&g(_,T.t=L),B!==T.a&&g(M,T.a=B),K!==T.o&&g(A,T.o=K),j!==T.i&&g(P,T.i=j),H!==T.n&&g(F,T.n=H),T},{e:void 0,t:void 0,a:void 0,o:void 0,i:void 0,n:void 0}),v}}),null),w(p,R(Wg,{get isOpen(){return i()},onClose:()=>l(!1),title:"新建订单",size:"md",get children(){return[(()=>{var v=Jg(),_=v.firstChild,C=_.firstChild,M=C.firstChild,A=M.nextSibling,P=C.nextSibling,F=P.firstChild,D=F.nextSibling,T=_.nextSibling;return w(v,R(po,{label:"股票代码",placeholder:"例如: AAPL",get value(){return s().symbol},onInput:k=>a(L=>({...L,symbol:k.currentTarget.value}))}),_),A.addEventListener("change",k=>a(L=>({...L,type:k.currentTarget.value}))),D.addEventListener("change",k=>a(L=>({...L,orderType:k.currentTarget.value}))),w(T,R(po,{label:"数量",type:"number",placeholder:"100",get value(){return s().quantity},onInput:k=>a(L=>({...L,quantity:k.currentTarget.value}))}),null),w(T,(()=>{var k=ee(()=>s().orderType==="limit");return()=>k()&&R(po,{label:"价格",type:"number",step:"0.01",placeholder:"150.25",get value(){return s().price},onInput:L=>a(B=>({...B,price:L.currentTarget.value}))})})(),null),O(k=>{var L=c({display:"flex",flexDirection:"column",gap:"16px"}),B=c({display:"grid",gridTemplateColumns:"1fr 1fr",gap:"16px"}),K=c({display:"block",fontSize:"14px",fontWeight:"500",color:"gray.700",marginBottom:"8px"}),j=c({width:"100%",padding:"10px 14px",border:"1px solid",borderColor:"gray.300",borderRadius:"8px",backgroundColor:"white",color:"gray.900"}),H=c({display:"block",fontSize:"14px",fontWeight:"500",color:"gray.700",marginBottom:"8px"}),U=c({width:"100%",padding:"10px 14px",border:"1px solid",borderColor:"gray.300",borderRadius:"8px",backgroundColor:"white",color:"gray.900"}),Z=c({display:"grid",gridTemplateColumns:"1fr 1fr",gap:"16px"});return L!==k.e&&g(v,k.e=L),B!==k.t&&g(_,k.t=B),K!==k.a&&g(M,k.a=K),j!==k.o&&g(A,k.o=j),H!==k.i&&g(F,k.i=H),U!==k.n&&g(D,k.n=U),Z!==k.s&&g(T,k.s=Z),k},{e:void 0,t:void 0,a:void 0,o:void 0,i:void 0,n:void 0,s:void 0}),O(()=>A.value=s().type),O(()=>D.value=s().orderType),v})(),(()=>{var v=Qg();return w(v,R(go,{variant:"ghost",onClick:()=>l(!1),children:"取消"}),null),w(v,R(go,{variant:"primary",onClick:m,children:"提交订单"}),null),v})()]}}),null),O(v=>{var _=c({padding:"24px",maxWidth:"1400px",margin:"0 auto",backgroundColor:"gray.50",minHeight:"100vh"}),C=c({marginBottom:"32px",display:"flex",justifyContent:"space-between",alignItems:"center"}),M=c({fontSize:"32px",fontWeight:"bold",color:"gray.900",marginBottom:"8px"}),A=c({fontSize:"16px",color:"gray.600"}),P=c({display:"grid",gridTemplateColumns:"repeat(auto-fit, minmax(200px, 1fr))",gap:"24px",marginBottom:"32px"}),F=c({display:"grid",gridTemplateColumns:"1fr 1fr",gap:"24px","@media (max-width: 1024px)":{gridTemplateColumns:"1fr"}});return _!==v.e&&g(p,v.e=_),C!==v.t&&g(u,v.t=C),M!==v.a&&g(y,v.a=M),A!==v.o&&g(S,v.o=A),P!==v.i&&g(x,v.i=P),F!==v.n&&g(b,v.n=F),v},{e:void 0,t:void 0,a:void 0,o:void 0,i:void 0,n:void 0}),p})()}function sf(){return[R(cs,{get children(){return R(ag,{get children(){return[R(et,{path:"/",component:An}),R(et,{path:"/dashboard",component:An}),R(et,{path:"/market",component:_i}),R(et,{path:"/market/realtime",component:kg}),R(et,{path:"/market/historical",component:_i}),R(et,{path:"/trading",component:lf}),R(et,{path:"/strategy",component:Eg}),R(et,{path:"/account",component:An}),R(et,{path:"/settings",component:An}),R(et,{path:"/api-test",component:bg})]}})}}),R(Qa,{})]}const Ri=document.getElementById("root");Ri&&ds(()=>R(sf,{}),Ri);console.log("🚀 量化交易前端平台启动成功"),console.log("📊 基于 SolidJS + Panda CSS"),console.log("⚡ 极致性能，专业体验");
