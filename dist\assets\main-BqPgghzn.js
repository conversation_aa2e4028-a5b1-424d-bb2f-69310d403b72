import{m as _e,s as Be,t as $,a as Pe,i as n,b as re,c as R,d as t,e as rt,f as de,g as x,S as se,u as Ir,A as sr,F as Xe,o as ht,h as yt,j as fr,k as Wr,P as Rr,l as Ar,R as Ie,n as Tr,r as Br}from"./vendor-solid-CJXiRbFN.js";import{l as cr}from"./vendor-editor-l7stcynF.js";(function(){const r=document.createElement("link").relList;if(r&&r.supports&&r.supports("modulepreload"))return;for(const a of document.querySelectorAll('link[rel="modulepreload"]'))c(a);new MutationObserver(a=>{for(const h of a)if(h.type==="childList")for(const u of h.addedNodes)u.tagName==="LINK"&&u.rel==="modulepreload"&&c(u)}).observe(document,{childList:!0,subtree:!0});function i(a){const h={};return a.integrity&&(h.integrity=a.integrity),a.referrerPolicy&&(h.referrerPolicy=a.referrerPolicy),a.crossOrigin==="use-credentials"?h.credentials="include":a.crossOrigin==="anonymous"?h.credentials="omit":h.credentials="same-origin",h}function c(a){if(a.ep)return;a.ep=!0;const h=i(a);fetch(a.href,h)}})();function mt(o){return typeof o=="object"&&o!=null&&!Array.isArray(o)}function br(o){return Object.fromEntries(Object.entries(o??{}).filter(([r,i])=>i!==void 0))}var Pr=o=>o==="base";function Lr(o){return o.slice().filter(r=>!Pr(r))}function gr(o){return String.fromCharCode(o+(o>25?39:97))}function Dr(o){let r="",i;for(i=Math.abs(o);i>52;i=i/52|0)r=gr(i%52)+r;return gr(i%52)+r}function jr(o,r){let i=r.length;for(;i;)o=o*33^r.charCodeAt(--i);return o}function Er(o){return Dr(jr(5381,o)>>>0)}var mr=/\s*!(important)?/i;function Mr(o){return typeof o=="string"?mr.test(o):!1}function Or(o){return typeof o=="string"?o.replace(mr,"").trim():o}function yr(o){return typeof o=="string"?o.replaceAll(" ","_"):o}var Ct=o=>{const r=new Map;return(...c)=>{const a=JSON.stringify(c);if(r.has(a))return r.get(a);const h=o(...c);return r.set(a,h),h}};function Rt(...o){return o.filter(Boolean).reduce((i,c)=>(Object.keys(c).forEach(a=>{const h=i[a],u=c[a];mt(h)&&mt(u)?i[a]=Rt(h,u):i[a]=u}),i),{})}var Fr=o=>o!=null;function Cr(o,r,i={}){const{stop:c,getKey:a}=i;function h(u,p=[]){if(mt(u)||Array.isArray(u)){const S={};for(const[z,A]of Object.entries(u)){const v=a?.(z,A)??z,b=[...p,v];if(c?.(u,b))return r(u,p);const w=h(A,b);Fr(w)&&(S[v]=w)}return S}return r(u,p)}return h(o)}function Hr(o,r){return o.reduce((i,c,a)=>{const h=r[a];return c!=null&&(i[h]=c),i},{})}function Sr(o,r,i=!0){const{utility:c,conditions:a}=r,{hasShorthand:h,resolveShorthand:u}=c;return Cr(o,p=>Array.isArray(p)?Hr(p,a.breakpoints.keys):p,{stop:p=>Array.isArray(p),getKey:i?p=>h?u(p):p:void 0})}var Vr={shift:o=>o,finalize:o=>o,breakpoints:{keys:[]}},qr=o=>typeof o=="string"?o.replaceAll(/[\n\s]+/g," "):o;function Nr(o){const{utility:r,hash:i,conditions:c=Vr}=o,a=u=>[r.prefix,u].filter(Boolean).join("-"),h=(u,p)=>{let S;if(i){const z=[...c.finalize(u),p];S=a(r.toHash(z,Er))}else S=[...c.finalize(u),a(p)].join(":");return S};return Ct(({base:u,...p}={})=>{const S=Object.assign(p,u),z=Sr(S,o),A=new Set;return Cr(z,(v,b)=>{const w=Mr(v);if(v==null)return;const[f,...I]=c.shift(b),T=Lr(I),j=r.transform(f,Or(qr(v)));let F=h(T,j.className);w&&(F=`${F}!`),A.add(F)}),Array.from(A).join(" ")})}function Kr(...o){return o.flat().filter(r=>mt(r)&&Object.keys(br(r)).length>0)}function Xr(o){function r(a){const h=Kr(...a);return h.length===1?h:h.map(u=>Sr(u,o))}function i(...a){return Rt(...r(a))}function c(...a){return Object.assign({},...r(a))}return{mergeCss:Ct(i),assignCss:c}}var Yr=/([A-Z])/g,Ur=/^ms-/,Gr=Ct(o=>o.startsWith("--")?o:o.replace(Yr,"-$1").replace(Ur,"-ms-").toLowerCase()),Qr="cm,mm,Q,in,pc,pt,px,em,ex,ch,rem,lh,rlh,vw,vh,vmin,vmax,vb,vi,svw,svh,lvw,lvh,dvw,dvh,cqw,cqh,cqi,cqb,cqmin,cqmax,%";`${Qr.split(",").join("|")}`;function Zr(o,...r){const i=Object.getOwnPropertyDescriptors(o),c=Object.keys(i),a=u=>{const p={};for(let S=0;S<u.length;S++){const z=u[S];i[z]&&(Object.defineProperty(p,z,i[z]),delete i[z])}return p},h=u=>a(Array.isArray(u)?u:c.filter(u));return r.map(h).concat(a(c))}var Jr=(...o)=>o.filter(Boolean).reduce((r,i)=>Array.from(new Set([...r,...i])),[]);const eo="_dark,_light,_hover,_focus,_focusWithin,_focusVisible,_disabled,_active,_visited,_target,_readOnly,_readWrite,_empty,_checked,_enabled,_expanded,_highlighted,_before,_after,_firstLetter,_firstLine,_marker,_selection,_file,_backdrop,_first,_last,_only,_even,_odd,_firstOfType,_lastOfType,_onlyOfType,_peerFocus,_peerHover,_peerActive,_peerFocusWithin,_peerFocusVisible,_peerDisabled,_peerChecked,_peerInvalid,_peerExpanded,_peerPlaceholderShown,_groupFocus,_groupHover,_groupActive,_groupFocusWithin,_groupFocusVisible,_groupDisabled,_groupChecked,_groupExpanded,_groupInvalid,_indeterminate,_required,_valid,_invalid,_autofill,_inRange,_outOfRange,_placeholder,_placeholderShown,_pressed,_selected,_default,_optional,_open,_closed,_fullscreen,_loading,_currentPage,_currentStep,_motionReduce,_motionSafe,_print,_landscape,_portrait,_osDark,_osLight,_highContrast,_lessContrast,_moreContrast,_ltr,_rtl,_scrollbar,_scrollbarThumb,_scrollbarTrack,_horizontal,_vertical,_starting,sm,smOnly,smDown,md,mdOnly,mdDown,lg,lgOnly,lgDown,xl,xlOnly,xlDown,2xl,2xlOnly,2xlDown,smToMd,smToLg,smToXl,smTo2xl,mdToLg,mdToXl,mdTo2xl,lgToXl,lgTo2xl,xlTo2xl,@/xs,@/sm,@/md,@/lg,@/xl,@/2xl,@/3xl,@/4xl,@/5xl,@/6xl,@/7xl,@/8xl,base",wr=new Set(eo.split(","));function ur(o){return wr.has(o)||/^@|&|&$/.test(o)}const to=/^_/,ro=/&|@/;function oo(o){return o.map(r=>wr.has(r)?r.replace(to,""):ro.test(r)?`[${yr(r.trim())}]`:r)}function io(o){return o.sort((r,i)=>{const c=ur(r),a=ur(i);return c&&!a?1:!c&&a?-1:0})}const no="aspectRatio:aspect,boxDecorationBreak:decoration,zIndex:z,boxSizing:box,objectPosition:obj-pos,objectFit:obj-fit,overscrollBehavior:overscroll,overscrollBehaviorX:overscroll-x,overscrollBehaviorY:overscroll-y,position:pos/1,top:top,left:left,insetInline:inset-x/insetX,insetBlock:inset-y/insetY,inset:inset,insetBlockEnd:inset-b,insetBlockStart:inset-t,insetInlineEnd:end/insetEnd/1,insetInlineStart:start/insetStart/1,right:right,bottom:bottom,float:float,visibility:vis,display:d,hideFrom:hide,hideBelow:show,flexBasis:basis,flex:flex,flexDirection:flex/flexDir,flexGrow:grow,flexShrink:shrink,gridTemplateColumns:grid-cols,gridTemplateRows:grid-rows,gridColumn:col-span,gridRow:row-span,gridColumnStart:col-start,gridColumnEnd:col-end,gridAutoFlow:grid-flow,gridAutoColumns:auto-cols,gridAutoRows:auto-rows,gap:gap,gridGap:gap,gridRowGap:gap-x,gridColumnGap:gap-y,rowGap:gap-x,columnGap:gap-y,justifyContent:justify,alignContent:content,alignItems:items,alignSelf:self,padding:p/1,paddingLeft:pl/1,paddingRight:pr/1,paddingTop:pt/1,paddingBottom:pb/1,paddingBlock:py/1/paddingY,paddingBlockEnd:pb,paddingBlockStart:pt,paddingInline:px/paddingX/1,paddingInlineEnd:pe/1/paddingEnd,paddingInlineStart:ps/1/paddingStart,marginLeft:ml/1,marginRight:mr/1,marginTop:mt/1,marginBottom:mb/1,margin:m/1,marginBlock:my/1/marginY,marginBlockEnd:mb,marginBlockStart:mt,marginInline:mx/1/marginX,marginInlineEnd:me/1/marginEnd,marginInlineStart:ms/1/marginStart,spaceX:space-x,spaceY:space-y,outlineWidth:ring-width/ringWidth,outlineColor:ring-color/ringColor,outline:ring/1,outlineOffset:ring-offset/ringOffset,divideX:divide-x,divideY:divide-y,divideColor:divide-color,divideStyle:divide-style,width:w/1,inlineSize:w,minWidth:min-w/minW,minInlineSize:min-w,maxWidth:max-w/maxW,maxInlineSize:max-w,height:h/1,blockSize:h,minHeight:min-h/minH,minBlockSize:min-h,maxHeight:max-h/maxH,maxBlockSize:max-b,color:text,fontFamily:font,fontSize:fs,fontWeight:fw,fontSmoothing:smoothing,fontVariantNumeric:numeric,letterSpacing:tracking,lineHeight:leading,textAlign:text-align,textDecoration:text-decor,textDecorationColor:text-decor-color,textEmphasisColor:text-emphasis-color,textDecorationStyle:decoration-style,textDecorationThickness:decoration-thickness,textUnderlineOffset:underline-offset,textTransform:text-transform,textIndent:indent,textShadow:text-shadow,textShadowColor:text-shadow/textShadowColor,textOverflow:text-overflow,verticalAlign:v-align,wordBreak:break,textWrap:text-wrap,truncate:truncate,lineClamp:clamp,listStyleType:list-type,listStylePosition:list-pos,listStyleImage:list-img,backgroundPosition:bg-pos/bgPosition,backgroundPositionX:bg-pos-x/bgPositionX,backgroundPositionY:bg-pos-y/bgPositionY,backgroundAttachment:bg-attach/bgAttachment,backgroundClip:bg-clip/bgClip,background:bg/1,backgroundColor:bg/bgColor,backgroundOrigin:bg-origin/bgOrigin,backgroundImage:bg-img/bgImage,backgroundRepeat:bg-repeat/bgRepeat,backgroundBlendMode:bg-blend/bgBlendMode,backgroundSize:bg-size/bgSize,backgroundGradient:bg-gradient/bgGradient,textGradient:text-gradient,gradientFromPosition:gradient-from-pos,gradientToPosition:gradient-to-pos,gradientFrom:gradient-from,gradientTo:gradient-to,gradientVia:gradient-via,gradientViaPosition:gradient-via-pos,borderRadius:rounded/1,borderTopLeftRadius:rounded-tl/roundedTopLeft,borderTopRightRadius:rounded-tr/roundedTopRight,borderBottomRightRadius:rounded-br/roundedBottomRight,borderBottomLeftRadius:rounded-bl/roundedBottomLeft,borderTopRadius:rounded-t/roundedTop,borderRightRadius:rounded-r/roundedRight,borderBottomRadius:rounded-b/roundedBottom,borderLeftRadius:rounded-l/roundedLeft,borderStartStartRadius:rounded-ss/roundedStartStart,borderStartEndRadius:rounded-se/roundedStartEnd,borderStartRadius:rounded-s/roundedStart,borderEndStartRadius:rounded-es/roundedEndStart,borderEndEndRadius:rounded-ee/roundedEndEnd,borderEndRadius:rounded-e/roundedEnd,border:border,borderWidth:border-w,borderTopWidth:border-tw,borderLeftWidth:border-lw,borderRightWidth:border-rw,borderBottomWidth:border-bw,borderColor:border,borderInline:border-x/borderX,borderInlineWidth:border-x/borderXWidth,borderInlineColor:border-x/borderXColor,borderBlock:border-y/borderY,borderBlockWidth:border-y/borderYWidth,borderBlockColor:border-y/borderYColor,borderLeft:border-l,borderLeftColor:border-l,borderInlineStart:border-s/borderStart,borderInlineStartWidth:border-s/borderStartWidth,borderInlineStartColor:border-s/borderStartColor,borderRight:border-r,borderRightColor:border-r,borderInlineEnd:border-e/borderEnd,borderInlineEndWidth:border-e/borderEndWidth,borderInlineEndColor:border-e/borderEndColor,borderTop:border-t,borderTopColor:border-t,borderBottom:border-b,borderBottomColor:border-b,borderBlockEnd:border-be,borderBlockEndColor:border-be,borderBlockStart:border-bs,borderBlockStartColor:border-bs,boxShadow:shadow/1,boxShadowColor:shadow-color/shadowColor,mixBlendMode:mix-blend,filter:filter,brightness:brightness,contrast:contrast,grayscale:grayscale,hueRotate:hue-rotate,invert:invert,saturate:saturate,sepia:sepia,dropShadow:drop-shadow,blur:blur,backdropFilter:backdrop,backdropBlur:backdrop-blur,backdropBrightness:backdrop-brightness,backdropContrast:backdrop-contrast,backdropGrayscale:backdrop-grayscale,backdropHueRotate:backdrop-hue-rotate,backdropInvert:backdrop-invert,backdropOpacity:backdrop-opacity,backdropSaturate:backdrop-saturate,backdropSepia:backdrop-sepia,borderCollapse:border,borderSpacing:border-spacing,borderSpacingX:border-spacing-x,borderSpacingY:border-spacing-y,tableLayout:table,transitionTimingFunction:ease,transitionDelay:delay,transitionDuration:duration,transitionProperty:transition-prop,transition:transition,animation:animation,animationName:animation-name,animationTimingFunction:animation-ease,animationDuration:animation-duration,animationDelay:animation-delay,transformOrigin:origin,rotate:rotate,rotateX:rotate-x,rotateY:rotate-y,rotateZ:rotate-z,scale:scale,scaleX:scale-x,scaleY:scale-y,translate:translate,translateX:translate-x/x,translateY:translate-y/y,translateZ:translate-z/z,accentColor:accent,caretColor:caret,scrollBehavior:scroll,scrollbar:scrollbar,scrollMargin:scroll-m,scrollMarginLeft:scroll-ml,scrollMarginRight:scroll-mr,scrollMarginTop:scroll-mt,scrollMarginBottom:scroll-mb,scrollMarginBlock:scroll-my/scrollMarginY,scrollMarginBlockEnd:scroll-mb,scrollMarginBlockStart:scroll-mt,scrollMarginInline:scroll-mx/scrollMarginX,scrollMarginInlineEnd:scroll-me,scrollMarginInlineStart:scroll-ms,scrollPadding:scroll-p,scrollPaddingBlock:scroll-pb/scrollPaddingY,scrollPaddingBlockStart:scroll-pt,scrollPaddingBlockEnd:scroll-pb,scrollPaddingInline:scroll-px/scrollPaddingX,scrollPaddingInlineEnd:scroll-pe,scrollPaddingInlineStart:scroll-ps,scrollPaddingLeft:scroll-pl,scrollPaddingRight:scroll-pr,scrollPaddingTop:scroll-pt,scrollPaddingBottom:scroll-pb,scrollSnapAlign:snap-align,scrollSnapStop:snap-stop,scrollSnapType:snap-type,scrollSnapStrictness:snap-strictness,scrollSnapMargin:snap-m,scrollSnapMarginTop:snap-mt,scrollSnapMarginBottom:snap-mb,scrollSnapMarginLeft:snap-ml,scrollSnapMarginRight:snap-mr,touchAction:touch,userSelect:select,fill:fill,stroke:stroke,strokeWidth:stroke-w,srOnly:sr,debug:debug,appearance:appearance,backfaceVisibility:backface,clipPath:clip-path,hyphens:hyphens,mask:mask,maskImage:mask-image,maskSize:mask-size,textSizeAdjust:text-adjust,container:cq,containerName:cq-name,containerType:cq-type,textStyle:textStyle",_r=new Map,kr=new Map;no.split(",").forEach(o=>{const[r,i]=o.split(":"),[c,...a]=i.split("/");_r.set(r,c),a.length&&a.forEach(h=>{kr.set(h==="1"?c:h,r)})});const pr=o=>kr.get(o)||o,zr={conditions:{shift:io,finalize:oo,breakpoints:{keys:["base","sm","md","lg","xl","2xl"]}},utility:{transform:(o,r)=>{const i=pr(o);return{className:`${_r.get(i)||Gr(i)}_${yr(r)}`}},hasShorthand:!0,toHash:(o,r)=>r(o.join(":")),resolveShorthand:pr}},lo=Nr(zr),e=(...o)=>lo(Je(...o));e.raw=(...o)=>Je(...o);const{mergeCss:Je}=Xr(zr);function et(){let o="",r=0,i;for(;r<arguments.length;)(i=arguments[r++])&&typeof i=="string"&&(o&&(o+=" "),o+=i);return o}const hr=o=>({base:{},variants:{},defaultVariants:{},compoundVariants:[],...o});function St(o){const{base:r,variants:i,defaultVariants:c,compoundVariants:a}=hr(o),h=b=>({...c,...br(b)});function u(b={}){const w=h(b);let f={...r};for(const[T,j]of Object.entries(w))i[T]?.[j]&&(f=Je(f,i[T][j]));const I=ao(a,w);return Je(f,I)}function p(b){const w=hr(b.config),f=Jr(b.variantKeys,Object.keys(i));return St({base:Je(r,w.base),variants:Object.fromEntries(f.map(I=>[I,Je(i[I],w.variants[I])])),defaultVariants:Rt(c,w.defaultVariants),compoundVariants:[...a,...w.compoundVariants]})}function S(b){return e(u(b))}const z=Object.keys(i);function A(b){return Zr(b,z)}const v=Object.fromEntries(Object.entries(i).map(([b,w])=>[b,Object.keys(w)]));return Object.assign(Ct(S),{__cva__:!0,variantMap:v,variantKeys:z,raw:u,config:o,merge:p,splitVariantProps:A,getVariantProps:h})}function ao(o,r){let i={};return o.forEach(c=>{Object.entries(c).every(([h,u])=>h==="css"?!0:(Array.isArray(u)?u:[u]).some(S=>r[h]===S))&&(i=Je(i,c.css))}),i}function $r(o){var r,i,c="";if(typeof o=="string"||typeof o=="number")c+=o;else if(typeof o=="object")if(Array.isArray(o)){var a=o.length;for(r=0;r<a;r++)o[r]&&(i=$r(o[r]))&&(c&&(c+=" "),c+=i)}else for(i in o)o[i]&&(c&&(c+=" "),c+=i);return c}function wt(){for(var o,r,i=0,c="",a=arguments.length;i<a;i++)(o=arguments[i])&&(r=$r(o))&&(c&&(c+=" "),c+=r);return c}var so=$("<button>"),co=$("<span>⟳");function Te(o){const r=_e({variant:"default",size:"default",type:"button"},o),[i,c]=Be(r,["variant","size","loading","disabled","icon","round","circle","plain","children","class"]),a=()=>{const h={display:"inline-flex",alignItems:"center",justifyContent:"center",gap:"8px",fontWeight:"500",borderRadius:i.round?"20px":i.circle?"50%":"4px",border:"1px solid",cursor:"pointer",transition:"all 0.3s",outline:"none",userSelect:"none",verticalAlign:"middle",whiteSpace:"nowrap",textDecoration:"none",_focus:{outline:"2px solid",outlineOffset:"2px"},_disabled:{cursor:"not-allowed",opacity:"0.5"}},u={large:{height:"40px",padding:i.circle?"0":"12px 20px",fontSize:"14px",minWidth:i.circle?"40px":"auto"},default:{height:"32px",padding:i.circle?"0":"8px 16px",fontSize:"14px",minWidth:i.circle?"32px":"auto"},small:{height:"24px",padding:i.circle?"0":"4px 12px",fontSize:"12px",minWidth:i.circle?"24px":"auto"}},p={primary:i.plain?{color:"primary.500",backgroundColor:"primary.50",borderColor:"primary.200",_hover:{backgroundColor:"primary.500",color:"white",borderColor:"primary.500"},_focus:{outlineColor:"primary.500"}}:{color:"white",backgroundColor:"primary.500",borderColor:"primary.500",_hover:{backgroundColor:"primary.600",borderColor:"primary.600"},_focus:{outlineColor:"primary.500"}},success:i.plain?{color:"success.500",backgroundColor:"success.50",borderColor:"success.200",_hover:{backgroundColor:"success.500",color:"white",borderColor:"success.500"},_focus:{outlineColor:"success.500"}}:{color:"white",backgroundColor:"success.500",borderColor:"success.500",_hover:{backgroundColor:"success.600",borderColor:"success.600"},_focus:{outlineColor:"success.500"}},warning:i.plain?{color:"warning.500",backgroundColor:"warning.50",borderColor:"warning.200",_hover:{backgroundColor:"warning.500",color:"white",borderColor:"warning.500"},_focus:{outlineColor:"warning.500"}}:{color:"white",backgroundColor:"warning.500",borderColor:"warning.500",_hover:{backgroundColor:"warning.600",borderColor:"warning.600"},_focus:{outlineColor:"warning.500"}},danger:i.plain?{color:"danger.500",backgroundColor:"danger.50",borderColor:"danger.200",_hover:{backgroundColor:"danger.500",color:"white",borderColor:"danger.500"},_focus:{outlineColor:"danger.500"}}:{color:"white",backgroundColor:"danger.500",borderColor:"danger.500",_hover:{backgroundColor:"danger.600",borderColor:"danger.600"},_focus:{outlineColor:"danger.500"}},info:i.plain?{color:"info.500",backgroundColor:"info.50",borderColor:"info.200",_hover:{backgroundColor:"info.500",color:"white",borderColor:"info.500"},_focus:{outlineColor:"info.500"}}:{color:"white",backgroundColor:"info.500",borderColor:"info.500",_hover:{backgroundColor:"info.600",borderColor:"info.600"},_focus:{outlineColor:"info.500"}},text:{color:"primary.500",backgroundColor:"transparent",borderColor:"transparent",_hover:{color:"primary.600",backgroundColor:"primary.50"},_focus:{outlineColor:"primary.500"}},default:{color:"text.regular",backgroundColor:"white",borderColor:"border.base",_hover:{color:"primary.500",borderColor:"primary.300"},_focus:{outlineColor:"primary.500"}}};return{...h,...u[i.size],...p[i.variant]}};return(()=>{var h=so();return Pe(h,_e({get class(){return wt(e(a()),i.class)},get disabled(){return i.disabled||i.loading}},c),!1,!0),n(h,(()=>{var u=re(()=>!!i.loading);return()=>u()&&(()=>{var p=co();return R(()=>t(p,e({animation:"spin 1s linear infinite"}))),p})()})(),null),n(h,(()=>{var u=re(()=>!!(i.icon&&!i.loading));return()=>u()&&i.icon})(),null),n(h,(()=>{var u=re(()=>!!(i.children&&!i.circle));return()=>u()&&i.children})(),null),h})()}var _t=$("<span>"),go=$("<span>✕"),uo=$("<div><input>");function po(o){const r=_e({size:"default",type:"text"},o),[i,c]=Be(r,["size","disabled","clearable","prefixIcon","suffixIcon","showPassword","error","onClear","class","value"]),[a,h]=de(!1),[u,p]=de(!1),S=()=>{const f={position:"relative",display:"inline-flex",alignItems:"center",width:"100%",borderRadius:"4px",border:"1px solid",backgroundColor:"white",transition:"all 0.3s",_focusWithin:{borderColor:"primary.500",boxShadow:"0 0 0 2px rgba(64, 158, 255, 0.2)"}},I={large:{height:"40px",fontSize:"14px"},default:{height:"32px",fontSize:"14px"},small:{height:"24px",fontSize:"12px"}},T={borderColor:i.error?"danger.500":u()?"primary.500":"border.base",_hover:i.disabled?{}:{borderColor:i.error?"danger.600":"border.light"},_disabled:{backgroundColor:"bg.page",borderColor:"border.lighter",cursor:"not-allowed"}};return{...f,...I[i.size],...T}},z=()=>{const f=i.prefixIcon?"32px":"12px",I=i.clearable||i.suffixIcon||i.showPassword?"32px":"12px";return{width:"100%",height:"100%",border:"none",outline:"none",backgroundColor:"transparent",color:"text.primary",fontSize:"inherit",paddingLeft:f,paddingRight:I,_placeholder:{color:"text.placeholder"},_disabled:{cursor:"not-allowed",color:"text.placeholder"}}},A=f=>({position:"absolute",top:"50%",transform:"translateY(-50%)",[f==="prefix"?"left":"right"]:"8px",color:"text.secondary",fontSize:"14px",cursor:f==="suffix"&&i.clearable?"pointer":"default",zIndex:1}),v=()=>{i.onClear?.()},b=()=>{h(!a())},w=()=>i.showPassword?a()?"text":"password":c.type||"text";return(()=>{var f=uo(),I=f.firstChild;return n(f,x(se,{get when(){return i.prefixIcon},get children(){var T=_t();return n(T,()=>i.prefixIcon),R(()=>t(T,e(A("prefix")))),T}}),I),Pe(I,_e(c,{get type(){return w()},get class(){return e(z())},get disabled(){return i.disabled},get value(){return i.value},onFocus:()=>p(!0),onBlur:()=>p(!1)}),!1,!1),n(f,x(se,{get when(){return re(()=>!!i.clearable)()&&i.value},get children(){var T=go();return T.$$click=v,R(()=>t(T,e({...A("suffix"),cursor:"pointer",_hover:{color:"text.primary"}}))),T}}),null),n(f,x(se,{get when(){return i.showPassword},get children(){var T=_t();return T.$$click=b,n(T,()=>a()?"👁️":"👁️‍🗨️"),R(()=>t(T,e({...A("suffix"),cursor:"pointer",_hover:{color:"text.primary"}}))),T}}),null),n(f,x(se,{get when(){return re(()=>!!(i.suffixIcon&&!i.clearable))()&&!i.showPassword},get children(){var T=_t();return n(T,()=>i.suffixIcon),R(()=>t(T,e(A("suffix")))),T}}),null),R(()=>t(f,wt(e(S()),i.class))),f})()}rt(["click"]);var ho=$("<div>"),xo=$("<div><div>");function xr(o){const[r,i]=Be(o,["header","shadow","bodyStyle","headerStyle","children","class"]),c=()=>({...{backgroundColor:"white",borderRadius:"4px",border:"1px solid",borderColor:"border.lighter",overflow:"hidden",transition:"all 0.3s"},...{always:{boxShadow:"base"},hover:{_hover:{boxShadow:"base"}},never:{}}[r.shadow||"always"]}),a=()=>({padding:"18px 20px",borderBottom:"1px solid",borderColor:"border.lighter",backgroundColor:"bg.page",fontSize:"16px",fontWeight:"500",color:"text.primary",...r.headerStyle}),h=()=>({padding:"20px",...r.bodyStyle});return(()=>{var u=xo(),p=u.firstChild;return Pe(u,_e({get class(){return wt(e(c()),r.class)}},i),!1,!0),n(u,x(se,{get when(){return r.header},get children(){var S=ho();return n(S,(()=>{var z=re(()=>typeof r.header=="string");return()=>(z(),r.header)})()),R(()=>t(S,e(a()))),S}}),p),n(p,()=>r.children),R(()=>t(p,e(h()))),u})()}var vo=$("<span>✕"),fo=$("<span>");function bo(o){const[r,i]=Be(o,["type","size","effect","closable","round","onClose","children","class"]),c=()=>{const u={display:"inline-flex",alignItems:"center",gap:"4px",borderRadius:r.round?"16px":"4px",border:"1px solid",fontSize:"12px",fontWeight:"400",lineHeight:1,whiteSpace:"nowrap",verticalAlign:"middle"},p={large:{height:"32px",padding:"0 12px",fontSize:"14px"},default:{height:"24px",padding:"0 8px",fontSize:"12px"},small:{height:"20px",padding:"0 6px",fontSize:"11px"}},z={primary:"primary",success:"success",warning:"warning",danger:"danger",info:"info"}[r.type||"primary"],A={dark:{color:"white",backgroundColor:`${z}.500`,borderColor:`${z}.500`},light:{color:`${z}.600`,backgroundColor:`${z}.50`,borderColor:`${z}.200`},plain:{color:`${z}.500`,backgroundColor:"transparent",borderColor:`${z}.500`}};return{...u,...p[r.size||"default"],...A[r.effect||"light"]}},a=()=>({cursor:"pointer",fontSize:"10px",marginLeft:"4px",borderRadius:"50%",width:"14px",height:"14px",display:"flex",alignItems:"center",justifyContent:"center",_hover:{backgroundColor:"rgba(0, 0, 0, 0.1)"}}),h=u=>{u.stopPropagation(),r.onClose?.()};return(()=>{var u=fo();return Pe(u,_e({get class(){return wt(e(c()),r.class)}},i),!1,!0),n(u,()=>r.children,null),n(u,x(se,{get when(){return r.closable},get children(){var p=vo();return p.$$click=h,R(()=>t(p,e(a()))),p}}),null),u})()}rt(["click"]);var mo=$("<span>量化平台"),yo=$("<div><aside><div><span>📈</span></div><nav></nav><div></div></aside><div><header><div><h1>量化交易平台</h1></div><div><div>用</div></div></header><main>"),xt=$("<span>"),Co=$("<div>");function So(o){const[r,i]=de(!1),[c,a]=de(""),h=Ir(),u=[{id:"dashboard",label:"仪表盘",icon:"📊",path:"/dashboard"},{id:"market",label:"行情中心",icon:"📈",path:"/market",children:[{id:"realtime",label:"实时行情",icon:"⚡",path:"/market/realtime"},{id:"historical",label:"历史数据",icon:"📋",path:"/market/historical"}]},{id:"trading",label:"交易中心",icon:"💰",path:"/trading"},{id:"strategy",label:"策略中心",icon:"🧠",path:"/strategy"},{id:"account",label:"账户中心",icon:"👤",path:"/account"},{id:"settings",label:"系统设置",icon:"⚙️",path:"/settings"}],p=w=>h.pathname===w||h.pathname.startsWith(w+"/"),S=()=>({width:r()?"64px":"240px",height:"100vh",backgroundColor:"white",borderRight:"1px solid",borderColor:"border.base",transition:"width 0.3s ease",display:"flex",flexDirection:"column",position:"fixed",left:0,top:0,zIndex:1e3}),z=()=>({height:"60px",backgroundColor:"white",borderBottom:"1px solid",borderColor:"border.base",display:"flex",alignItems:"center",justifyContent:"space-between",padding:"0 20px",marginLeft:r()?"64px":"240px",transition:"margin-left 0.3s ease"}),A=()=>({marginLeft:r()?"64px":"240px",marginTop:"60px",minHeight:"calc(100vh - 60px)",backgroundColor:"bg.page",transition:"margin-left 0.3s ease"}),v=()=>({height:"60px",display:"flex",alignItems:"center",justifyContent:r()?"center":"flex-start",padding:r()?"0":"0 20px",borderBottom:"1px solid",borderColor:"border.base",fontSize:"18px",fontWeight:"600",color:"primary.500"}),b=(w,f=!1)=>({display:"flex",alignItems:"center",gap:"12px",padding:r()&&!f?"12px 0":"12px 20px",paddingLeft:f?"52px":r()?"0":"20px",justifyContent:r()&&!f?"center":"flex-start",color:p(w.path)?"primary.500":"text.regular",backgroundColor:p(w.path)?"primary.50":"transparent",borderRight:p(w.path)?"3px solid":"none",borderColor:"primary.500",textDecoration:"none",transition:"all 0.3s",cursor:"pointer",_hover:{backgroundColor:"primary.50",color:"primary.500"}});return(()=>{var w=yo(),f=w.firstChild,I=f.firstChild,T=I.firstChild,j=I.nextSibling,F=j.nextSibling,y=f.nextSibling,B=y.firstChild,C=B.firstChild,W=C.firstChild,P=C.nextSibling,E=P.firstChild,L=B.nextSibling;return n(I,x(se,{get when(){return!r()},get children(){return mo()}}),null),n(j,x(Xe,{each:u,children:_=>(()=>{var m=Co();return n(m,x(sr,{get href(){return _.path},get class(){return e(b(_))},get title(){return re(()=>!!r())()?_.label:void 0},get children(){return[(()=>{var s=xt();return n(s,()=>_.icon),R(()=>t(s,e({fontSize:"16px"}))),s})(),x(se,{get when(){return!r()},get children(){var s=xt();return n(s,()=>_.label),R(()=>t(s,e({fontWeight:p(_.path)?"500":"400"}))),s}})]}}),null),n(m,x(se,{get when(){return re(()=>!!_.children)()&&!r()},get children(){return x(Xe,{get each(){return _.children},children:s=>x(sr,{get href(){return s.path},get class(){return e(b(s,!0))},get children(){return[(()=>{var k=xt();return n(k,()=>s.icon),R(()=>t(k,e({fontSize:"14px"}))),k})(),(()=>{var k=xt();return n(k,()=>s.label),R(()=>t(k,e({fontWeight:p(s.path)?"500":"400"}))),k})()]}})})}}),null),m})()})),n(F,x(Te,{variant:"text",size:"small",onClick:()=>i(!r()),get class(){return e({width:"100%"})},get children(){return r()?"→":"←"}})),n(P,x(po,{placeholder:"搜索...",size:"small",get value(){return c()},onInput:_=>a(_.currentTarget.value),get class(){return e({width:"200px"})}}),E),n(P,x(Te,{variant:"text",size:"small",children:"帮助"}),E),n(P,x(Te,{variant:"text",size:"small",children:"设置"}),E),n(L,()=>o.children),R(_=>{var m=e({display:"flex",minHeight:"100vh"}),s=e(S()),k=e(v()),M=e({fontSize:"20px",marginRight:"8px"}),D=e({flex:1,overflowY:"auto",padding:"8px 0"}),V=e({padding:"16px",borderTop:"1px solid",borderColor:"border.base"}),H=e({flex:1}),G=e(z()),U=e({display:"flex",alignItems:"center",gap:"16px"}),q=e({fontSize:"18px",fontWeight:"500",color:"text.primary",margin:0}),d=e({display:"flex",alignItems:"center",gap:"16px"}),Y=e({width:"32px",height:"32px",backgroundColor:"primary.500",borderRadius:"50%",display:"flex",alignItems:"center",justifyContent:"center",color:"white",fontSize:"14px",fontWeight:"500"}),Z=e(A());return m!==_.e&&t(w,_.e=m),s!==_.t&&t(f,_.t=s),k!==_.a&&t(I,_.a=k),M!==_.o&&t(T,_.o=M),D!==_.i&&t(j,_.i=D),V!==_.n&&t(F,_.n=V),H!==_.s&&t(y,_.s=H),G!==_.h&&t(B,_.h=G),U!==_.r&&t(C,_.r=U),q!==_.d&&t(W,_.d=q),d!==_.l&&t(P,_.l=d),Y!==_.u&&t(E,_.u=Y),Z!==_.c&&t(L,_.c=Z),_},{e:void 0,t:void 0,a:void 0,o:void 0,i:void 0,n:void 0,s:void 0,h:void 0,r:void 0,d:void 0,l:void 0,u:void 0,c:void 0}),w})()}var wo=$("<div><div><h1>投资仪表盘</h1><div></div></div><div></div><div><div><div><h3>资金曲线图</h3><div></div></div><div><div>📈</div><div>资金曲线图表</div><div>显示策略收益走势</div></div></div><div><div><h3>持仓概览</h3><span>查看全部 →</span></div><div></div></div></div><div><div><div><h3>今日行情</h3><span>更新时间: 15:30</span></div><div></div></div><div><div><h3>最新资讯</h3><span>查看更多 →</span></div><div></div></div></div><div><div>当前时间: </div><div><span>数据来源: 模拟数据</span><span>更新频率: 实时</span><div><div></div><span>系统正常"),kt=$("<div>"),_o=$("<div><div><span></span><span>%</span></div><div><span></span><span>"),ko=$("<div><div><div></div><span></span></div><div><div></div><div> (<!>)"),zo=$("<div><div></div><div><span></span><span>");function vt(){console.log("🔥 Dashboard组件已加载 - 新版本 - 时间戳:",Date.now());const[o,r]=de(new Date().toLocaleString("zh-CN"));let i;ht(()=>{i=setInterval(()=>{r(new Date().toLocaleString("zh-CN"))},1e3)}),yt(()=>{i&&clearInterval(i)});const c=[{title:"总资产",value:"¥1,000,000",change:"+2.34%",trend:"up",icon:"💰",description:"总资产",subValue:"¥1,000,000"},{title:"今日盈亏",value:"0",change:"+0.00%",trend:"neutral",icon:"📊",description:"今日盈亏",subValue:"0.00%"},{title:"持仓市值",value:"¥50,000",change:"+0.00%",trend:"neutral",icon:"📈",description:"持仓市值",subValue:"¥50,000"},{title:"可用资金",value:"2",change:"+0.00%",trend:"neutral",icon:"🔒",description:"持仓数量",subValue:"2"}],a=[{code:"000725",name:"京东方A",price:3.45,change:-1.43,changePercent:-29.3,volume:7340.75,amount:-6046,status:"持仓"},{code:"300519",name:"新光药业",price:68.94,change:-1.05,changePercent:-1.5,volume:410.75,amount:-1796,status:"持仓"},{code:"000831",name:"五矿稀土",price:26.09,change:-1.37,changePercent:-5,volume:7558.72,amount:-7688,status:"持仓"}],h=[{name:"上证指数",value:"3,245.67",change:"+23.45",percent:"+0.73%",trend:"up"},{name:"深证成指",value:"10,567.23",change:"+45.67",percent:"+0.43%",trend:"up"},{name:"创业板指",value:"2,234.56",change:"-8.90",percent:"-0.40%",trend:"down"},{name:"科创50",value:"1,123.45",change:"+15.23",percent:"+1.37%",trend:"up"}],u=[{title:"A股市场今日表现强劲，科技股领涨",time:"刚刚发布",type:"market"},{title:"央行宣布降准0.25个百分点",time:"30分钟前",type:"policy"},{title:"新能源板块持续活跃，多只个股涨停",time:"1小时前",type:"sector"}];return(()=>{var p=wo(),S=p.firstChild,z=S.firstChild,A=z.nextSibling,v=S.nextSibling,b=v.nextSibling,w=b.firstChild,f=w.firstChild,I=f.firstChild,T=I.nextSibling,j=f.nextSibling,F=j.firstChild,y=F.nextSibling,B=y.nextSibling,C=w.nextSibling,W=C.firstChild,P=W.firstChild,E=P.nextSibling,L=W.nextSibling,_=b.nextSibling,m=_.firstChild,s=m.firstChild,k=s.firstChild,M=k.nextSibling,D=s.nextSibling,V=m.nextSibling,H=V.firstChild,G=H.firstChild,U=G.nextSibling,q=H.nextSibling,d=_.nextSibling,Y=d.firstChild;Y.firstChild;var Z=Y.nextSibling,le=Z.firstChild,ae=le.nextSibling,ce=ae.nextSibling,xe=ce.firstChild;return n(p,x(xr,{get class(){return e({marginBottom:"20px",backgroundColor:"success.50",borderColor:"success.200"})},bodyStyle:{padding:"16px",textAlign:"center",color:"success.700",fontSize:"16px",fontWeight:"600"},children:"🎉 新版专业量化平台界面已成功加载！"}),S),n(A,x(Te,{variant:"primary",size:"small",children:"刷新"}),null),n(A,x(Te,{variant:"default",size:"small",children:"设置"}),null),n(A,x(Te,{variant:"success",size:"small",children:"新增策略"}),null),n(v,x(Xe,{each:c,children:g=>x(xr,{shadow:"hover",bodyStyle:{display:"flex",flexDirection:"column",alignItems:"center",textAlign:"center",padding:"24px 16px"},get children(){return[(()=>{var O=kt();return n(O,()=>g.icon),R(()=>t(O,e({fontSize:"32px",marginBottom:"12px"}))),O})(),(()=>{var O=kt();return n(O,()=>g.value),R(()=>t(O,e({fontSize:"28px",fontWeight:"600",color:"text.primary",marginBottom:"8px"}))),O})(),(()=>{var O=kt();return n(O,()=>g.description),R(()=>t(O,e({fontSize:"14px",color:"text.secondary",marginBottom:"12px"}))),O})(),x(bo,{get type(){return re(()=>g.trend==="up")()?"success":g.trend==="down"?"danger":"info"},effect:"light",size:"small",get children(){return g.change}})]}})})),n(T,x(Te,{variant:"primary",size:"sm",children:"日"}),null),n(T,x(Te,{size:"sm",children:"周"}),null),n(T,x(Te,{size:"sm",children:"月"}),null),n(L,x(Xe,{each:a,children:g=>(()=>{var O=_o(),J=O.firstChild,oe=J.firstChild,ee=oe.nextSibling,ie=ee.firstChild,K=J.nextSibling,te=K.firstChild,ue=te.nextSibling;return n(oe,()=>g.code),n(ee,()=>g.changePercent>0?"+":"",ie),n(ee,()=>g.changePercent,ie),n(te,()=>g.name),n(ue,()=>g.status),R(Q=>{var N=e({padding:"8px",backgroundColor:"#fafafa",borderRadius:"4px",fontSize:"12px"}),ge=e({display:"flex",justifyContent:"space-between",alignItems:"center",marginBottom:"4px"}),pe=e({fontWeight:"600",color:"#262626"}),ve=e({color:g.changePercent>0?"#52c41a":"#f5222d",fontWeight:"500"}),fe=e({display:"flex",justifyContent:"space-between",color:"#8c8c8c"});return N!==Q.e&&t(O,Q.e=N),ge!==Q.t&&t(J,Q.t=ge),pe!==Q.a&&t(oe,Q.a=pe),ve!==Q.o&&t(ee,Q.o=ve),fe!==Q.i&&t(K,Q.i=fe),Q},{e:void 0,t:void 0,a:void 0,o:void 0,i:void 0}),O})()})),n(D,x(Xe,{each:h,children:g=>(()=>{var O=ko(),J=O.firstChild,oe=J.firstChild,ee=oe.nextSibling,ie=J.nextSibling,K=ie.firstChild,te=K.nextSibling,ue=te.firstChild,Q=ue.nextSibling;return Q.nextSibling,n(ee,()=>g.name),n(K,()=>g.value),n(te,()=>g.change,ue),n(te,()=>g.percent,Q),R(N=>{var ge=e({display:"flex",alignItems:"center",justifyContent:"space-between",padding:"8px",backgroundColor:"#fafafa",borderRadius:"4px",fontSize:"12px"}),pe=e({display:"flex",alignItems:"center",gap:"8px"}),ve=e({width:"6px",height:"6px",borderRadius:"50%",backgroundColor:g.trend==="up"?"#52c41a":"#f5222d"}),fe=e({fontWeight:"500",color:"#262626"}),ye=e({textAlign:"right"}),Ce=e({fontWeight:"600",color:"#262626",marginBottom:"2px"}),Se=e({color:g.trend==="up"?"#52c41a":"#f5222d",fontSize:"11px"});return ge!==N.e&&t(O,N.e=ge),pe!==N.t&&t(J,N.t=pe),ve!==N.a&&t(oe,N.a=ve),fe!==N.o&&t(ee,N.o=fe),ye!==N.i&&t(ie,N.i=ye),Ce!==N.n&&t(K,N.n=Ce),Se!==N.s&&t(te,N.s=Se),N},{e:void 0,t:void 0,a:void 0,o:void 0,i:void 0,n:void 0,s:void 0}),O})()})),n(q,x(Xe,{each:u,children:g=>(()=>{var O=zo(),J=O.firstChild,oe=J.nextSibling,ee=oe.firstChild,ie=ee.nextSibling;return n(J,()=>g.title),n(ee,()=>g.time),n(ie,()=>g.type),R(K=>{var te=e({padding:"8px",backgroundColor:"#fafafa",borderRadius:"4px",cursor:"pointer",transition:"background-color 0.2s",_hover:{backgroundColor:"#f0f0f0"}}),ue=e({fontSize:"12px",fontWeight:"500",color:"#262626",marginBottom:"4px",lineHeight:"1.4"}),Q=e({display:"flex",alignItems:"center",justifyContent:"space-between"}),N=e({fontSize:"11px",color:"#8c8c8c"}),ge=e({fontSize:"10px",color:"#1890ff",backgroundColor:"#e6f7ff",padding:"2px 6px",borderRadius:"2px"});return te!==K.e&&t(O,K.e=te),ue!==K.t&&t(J,K.t=ue),Q!==K.a&&t(oe,K.a=Q),N!==K.o&&t(ee,K.o=N),ge!==K.i&&t(ie,K.i=ge),K},{e:void 0,t:void 0,a:void 0,o:void 0,i:void 0}),O})()})),n(Y,o,null),R(g=>{var O=e({padding:"20px",minHeight:"100vh",backgroundColor:"bg.page"}),J=e({display:"flex",alignItems:"center",justifyContent:"space-between",marginBottom:"20px"}),oe=e({fontSize:"24px",fontWeight:"600",color:"text.primary",margin:0}),ee=e({display:"flex",alignItems:"center",gap:"12px"}),ie=e({display:"grid",gridTemplateColumns:"repeat(auto-fit, minmax(240px, 1fr))",gap:"20px",marginBottom:"24px"}),K=e({display:"grid",gridTemplateColumns:"2fr 1fr",gap:"16px",marginBottom:"16px","@media (max-width: 768px)":{gridTemplateColumns:"1fr"}}),te=e({backgroundColor:"white",borderRadius:"8px",border:"1px solid #e8e8e8",padding:"16px"}),ue=e({display:"flex",alignItems:"center",justifyContent:"space-between",marginBottom:"16px",flexWrap:"wrap",gap:"8px"}),Q=e({fontSize:"16px",fontWeight:"600",color:"#262626",margin:0}),N=e({display:"flex",alignItems:"center",gap:"8px"}),ge=e({height:"200px",backgroundColor:"#fafafa",borderRadius:"4px",display:"flex",alignItems:"center",justifyContent:"center",flexDirection:"column",gap:"8px"}),pe=e({fontSize:"48px"}),ve=e({fontSize:"14px",color:"#8c8c8c"}),fe=e({fontSize:"12px",color:"#8c8c8c"}),ye=e({backgroundColor:"white",borderRadius:"8px",border:"1px solid #e8e8e8",padding:"16px"}),Ce=e({display:"flex",alignItems:"center",justifyContent:"space-between",marginBottom:"16px"}),Se=e({fontSize:"16px",fontWeight:"600",color:"#262626",margin:0}),Le=e({fontSize:"12px",color:"#8c8c8c"}),De=e({display:"flex",flexDirection:"column",gap:"8px"}),je=e({display:"grid",gridTemplateColumns:"1fr 1fr",gap:"16px","@media (max-width: 768px)":{gridTemplateColumns:"1fr"}}),Ee=e({backgroundColor:"white",borderRadius:"8px",border:"1px solid #e8e8e8",padding:"16px"}),Me=e({display:"flex",alignItems:"center",justifyContent:"space-between",marginBottom:"16px",flexWrap:"wrap",gap:"8px"}),Oe=e({fontSize:"16px",fontWeight:"600",color:"#262626",margin:0}),Ye=e({fontSize:"12px",color:"#8c8c8c"}),Ue=e({display:"flex",flexDirection:"column",gap:"8px"}),Fe=e({backgroundColor:"white",borderRadius:"8px",border:"1px solid #e8e8e8",padding:"16px"}),ke=e({display:"flex",alignItems:"center",justifyContent:"space-between",marginBottom:"16px"}),Ge=e({fontSize:"16px",fontWeight:"600",color:"#262626",margin:0}),ot=e({fontSize:"12px",color:"#1890ff",cursor:"pointer"}),Qe=e({display:"flex",flexDirection:"column",gap:"8px"}),it=e({display:"flex",alignItems:"center",justifyContent:"space-between",padding:"12px 16px",backgroundColor:"white",borderRadius:"8px",border:"1px solid #e8e8e8",fontSize:"12px",color:"#8c8c8c"}),l=e({display:"flex",alignItems:"center",gap:"16px"}),be=e({display:"flex",alignItems:"center",gap:"4px"}),me=e({width:"6px",height:"6px",borderRadius:"50%",backgroundColor:"#52c41a"});return O!==g.e&&t(p,g.e=O),J!==g.t&&t(S,g.t=J),oe!==g.a&&t(z,g.a=oe),ee!==g.o&&t(A,g.o=ee),ie!==g.i&&t(v,g.i=ie),K!==g.n&&t(b,g.n=K),te!==g.s&&t(w,g.s=te),ue!==g.h&&t(f,g.h=ue),Q!==g.r&&t(I,g.r=Q),N!==g.d&&t(T,g.d=N),ge!==g.l&&t(j,g.l=ge),pe!==g.u&&t(F,g.u=pe),ve!==g.c&&t(y,g.c=ve),fe!==g.w&&t(B,g.w=fe),ye!==g.m&&t(C,g.m=ye),Ce!==g.f&&t(W,g.f=Ce),Se!==g.y&&t(P,g.y=Se),Le!==g.g&&t(E,g.g=Le),De!==g.p&&t(L,g.p=De),je!==g.b&&t(_,g.b=je),Ee!==g.T&&t(m,g.T=Ee),Me!==g.A&&t(s,g.A=Me),Oe!==g.O&&t(k,g.O=Oe),Ye!==g.I&&t(M,g.I=Ye),Ue!==g.S&&t(D,g.S=Ue),Fe!==g.W&&t(V,g.W=Fe),ke!==g.C&&t(H,g.C=ke),Ge!==g.B&&t(G,g.B=Ge),ot!==g.v&&t(U,g.v=ot),Qe!==g.k&&t(q,g.k=Qe),it!==g.x&&t(d,g.x=it),l!==g.j&&t(Z,g.j=l),be!==g.q&&t(ce,g.q=be),me!==g.z&&t(xe,g.z=me),g},{e:void 0,t:void 0,a:void 0,o:void 0,i:void 0,n:void 0,s:void 0,h:void 0,r:void 0,d:void 0,l:void 0,u:void 0,c:void 0,w:void 0,m:void 0,f:void 0,y:void 0,g:void 0,p:void 0,b:void 0,T:void 0,A:void 0,O:void 0,I:void 0,S:void 0,W:void 0,C:void 0,B:void 0,v:void 0,k:void 0,x:void 0,j:void 0,q:void 0,z:void 0}),p})()}var $o=$("<div><div><h1>🔧 API 连接测试</h1><p>测试前端与后端API的连接状态</p></div><div><button>开始测试</button></div><div><div><h2>测试结果</h2></div><div></div></div><div><div><h2>配置信息</h2></div><div><div><div><h3>API Base URL</h3><p></p></div><div><h3>环境模式</h3><p></p></div><div><h3>代理配置</h3><p>/api → localhost:8000"),Io=$('<p>点击"开始测试"按钮运行API连接测试'),Wo=$("<div>"),Ro=$("<div><div><div></div><div><h3></h3><p>"),Ao=$("<div>ms");const ft={get:async(o,r)=>{const i=r?"?"+new URLSearchParams(r).toString():"",a=await fetch("https://api.yourdomain.com"+o+i);if(!a.ok)throw new Error(`HTTP ${a.status}`);return a.json().catch(()=>({}))}},bt={SYSTEM:{HEALTH:"/v1/health"},MARKET:{OVERVIEW:"/v1/market/overview",SEARCH:"/v1/market/search"},AUTH:{PROFILE:"/v1/auth/profile"}};function To(){const[o,r]=de([]),i=(a,h,u,p)=>{r(S=>[...S,{name:a,status:h,message:u,duration:p}])},c=async()=>{r([]);try{const a=Date.now();await ft.get(bt.SYSTEM.HEALTH);const h=Date.now()-a;i("系统健康检查","success","连接成功",h)}catch(a){i("系统健康检查","error",a.message||"连接失败")}try{const a=Date.now();await ft.get(bt.MARKET.OVERVIEW);const h=Date.now()-a;i("市场概览","success","数据获取成功",h)}catch(a){i("市场概览","error",a.message||"数据获取失败")}try{const a=Date.now();await ft.get(bt.MARKET.SEARCH,{q:"AAPL"});const h=Date.now()-a;i("股票搜索","success","搜索成功",h)}catch(a){i("股票搜索","error",a.message||"搜索失败")}try{const a=Date.now();await ft.get(bt.AUTH.PROFILE);const h=Date.now()-a;i("用户信息","success","获取成功",h)}catch(a){i("用户信息","error",a.message||"获取失败（预期，因为未登录）")}};return ht(()=>{console.log("ApiTest mounted")}),(()=>{var a=$o(),h=a.firstChild,u=h.firstChild,p=u.nextSibling,S=h.nextSibling,z=S.firstChild,A=S.nextSibling,v=A.firstChild,b=v.firstChild,w=v.nextSibling,f=A.nextSibling,I=f.firstChild,T=I.firstChild,j=I.nextSibling,F=j.firstChild,y=F.firstChild,B=y.firstChild,C=B.nextSibling,W=y.nextSibling,P=W.firstChild,E=P.nextSibling,L=W.nextSibling,_=L.firstChild,m=_.nextSibling;return z.$$click=c,n(w,(()=>{var s=re(()=>o().length===0);return()=>s()?(()=>{var k=Io();return R(()=>t(k,e({color:"gray.500",textAlign:"center",padding:"40px 0"}))),k})():(()=>{var k=Wo();return n(k,()=>o().map(M=>(()=>{var D=Ro(),V=D.firstChild,H=V.firstChild,G=H.nextSibling,U=G.firstChild,q=U.nextSibling;return n(H,()=>M.status==="success"?"✅":"❌"),n(U,()=>M.name),n(q,()=>M.message),n(D,(()=>{var d=re(()=>!!M.duration);return()=>d()&&(()=>{var Y=Ao(),Z=Y.firstChild;return n(Y,()=>M.duration,Z),R(()=>t(Y,e({fontSize:"12px",color:"gray.500",backgroundColor:"gray.100",padding:"4px 8px",borderRadius:"4px"}))),Y})()})(),null),R(d=>{var Y=e({display:"flex",alignItems:"center",justifyContent:"space-between",padding:"16px",borderRadius:"8px",border:"1px solid",borderColor:M.status==="success"?"green.200":"red.200",backgroundColor:M.status==="success"?"green.50":"red.50"}),Z=e({display:"flex",alignItems:"center",gap:"12px"}),le=e({fontSize:"20px"}),ae=e({fontSize:"16px",fontWeight:"600",color:"gray.900",marginBottom:"4px"}),ce=e({fontSize:"14px",color:"gray.600"});return Y!==d.e&&t(D,d.e=Y),Z!==d.t&&t(V,d.t=Z),le!==d.a&&t(H,d.a=le),ae!==d.o&&t(U,d.o=ae),ce!==d.i&&t(q,d.i=ce),d},{e:void 0,t:void 0,a:void 0,o:void 0,i:void 0}),D})())),R(()=>t(k,e({display:"flex",flexDirection:"column",gap:"16px"}))),k})()})()),n(C,()=>"https://api.yourdomain.com"),n(E,()=>"production"),R(s=>{var k=e({padding:"24px",maxWidth:"1200px",margin:"0 auto"}),M=e({marginBottom:"32px"}),D=e({fontSize:"32px",fontWeight:"bold",color:"gray.900",marginBottom:"8px"}),V=e({fontSize:"16px",color:"gray.600"}),H=e({marginBottom:"32px"}),G=e({padding:"12px 24px",backgroundColor:"blue.600",color:"white",border:"none",borderRadius:"8px",fontSize:"16px",fontWeight:"500",cursor:"pointer",transition:"all 0.2s",_hover:{backgroundColor:"blue.700"}}),U=e({backgroundColor:"white",borderRadius:"12px",boxShadow:"0 1px 3px rgba(0, 0, 0, 0.1)",border:"1px solid #e5e7eb",overflow:"hidden"}),q=e({padding:"24px",borderBottom:"1px solid #e5e7eb"}),d=e({fontSize:"20px",fontWeight:"bold",color:"gray.900"}),Y=e({padding:"24px"}),Z=e({marginTop:"32px",backgroundColor:"white",borderRadius:"12px",boxShadow:"0 1px 3px rgba(0, 0, 0, 0.1)",border:"1px solid #e5e7eb",overflow:"hidden"}),le=e({padding:"24px",borderBottom:"1px solid #e5e7eb"}),ae=e({fontSize:"20px",fontWeight:"bold",color:"gray.900"}),ce=e({padding:"24px"}),xe=e({display:"grid",gridTemplateColumns:"repeat(auto-fit, minmax(300px, 1fr))",gap:"16px"}),g=e({fontSize:"14px",fontWeight:"600",color:"gray.700",marginBottom:"8px"}),O=e({fontSize:"14px",color:"gray.600",fontFamily:"monospace",backgroundColor:"gray.100",padding:"8px",borderRadius:"4px"}),J=e({fontSize:"14px",fontWeight:"600",color:"gray.700",marginBottom:"8px"}),oe=e({fontSize:"14px",color:"gray.600",fontFamily:"monospace",backgroundColor:"gray.100",padding:"8px",borderRadius:"4px"}),ee=e({fontSize:"14px",fontWeight:"600",color:"gray.700",marginBottom:"8px"}),ie=e({fontSize:"14px",color:"gray.600",fontFamily:"monospace",backgroundColor:"gray.100",padding:"8px",borderRadius:"4px"});return k!==s.e&&t(a,s.e=k),M!==s.t&&t(h,s.t=M),D!==s.a&&t(u,s.a=D),V!==s.o&&t(p,s.o=V),H!==s.i&&t(S,s.i=H),G!==s.n&&t(z,s.n=G),U!==s.s&&t(A,s.s=U),q!==s.h&&t(v,s.h=q),d!==s.r&&t(b,s.r=d),Y!==s.d&&t(w,s.d=Y),Z!==s.l&&t(f,s.l=Z),le!==s.u&&t(I,s.u=le),ae!==s.c&&t(T,s.c=ae),ce!==s.w&&t(j,s.w=ce),xe!==s.m&&t(F,s.m=xe),g!==s.f&&t(B,s.f=g),O!==s.y&&t(C,s.y=O),J!==s.g&&t(P,s.g=J),oe!==s.p&&t(E,s.p=oe),ee!==s.b&&t(_,s.b=ee),ie!==s.T&&t(m,s.T=ie),s},{e:void 0,t:void 0,a:void 0,o:void 0,i:void 0,n:void 0,s:void 0,h:void 0,r:void 0,d:void 0,l:void 0,u:void 0,c:void 0,w:void 0,m:void 0,f:void 0,y:void 0,g:void 0,p:void 0,b:void 0,T:void 0}),a})()}rt(["click"]);class Bo{constructor(){this.socket=null,this.reconnectAttempts=0,this.maxReconnectAttempts=5,this.reconnectDelay=1e3,this.connectionStatusSignal=de("disconnected"),this.marketDataSignal=de(new Map),this.connectionStatus=this.connectionStatusSignal[0],this.setConnectionStatus=this.connectionStatusSignal[1],this.marketData=this.marketDataSignal[0],this.setMarketData=this.marketDataSignal[1],this.connect()}connect(){try{this.setConnectionStatus("connecting"),console.log("尝试连接WebSocket服务器:","wss://api.yourdomain.com/ws"),this.socket=null}catch(r){console.error("WebSocket连接失败:",r),this.setConnectionStatus("error"),this.handleReconnect()}}handleReconnect(){this.reconnectAttempts<this.maxReconnectAttempts?(this.reconnectAttempts++,setTimeout(()=>{console.log(`尝试重连 (${this.reconnectAttempts}/${this.maxReconnectAttempts})`),this.connect()},this.reconnectDelay*this.reconnectAttempts)):(console.log("达到最大重连次数，停止重连"),this.setConnectionStatus("error"))}updateMarketData(r){this.setMarketData(i=>{const c=new Map(i);return c.set(r.symbol,r),c})}subscribeToMarketData(r){console.log("订阅市场数据:",r),this.socket&&this.socket.connected&&this.socket.emit("subscribe",{symbols:r})}unsubscribeFromMarketData(r){console.log("取消订阅市场数据:",r),this.socket&&this.socket.connected&&this.socket.emit("unsubscribe",{symbols:r})}reconnect(){console.log("手动重连WebSocket..."),this.disconnect(),this.reconnectAttempts=0,this.connect()}disconnect(){this.socket&&(this.socket.disconnect(),this.socket=null),this.setConnectionStatus("disconnected")}}const We=new Bo;function Po(){return{connectionStatus:We.connectionStatus,marketData:We.marketData,subscribeToMarketData:We.subscribeToMarketData.bind(We),unsubscribeFromMarketData:We.unsubscribeFromMarketData.bind(We),disconnect:We.disconnect.bind(We),reconnect:We.reconnect.bind(We)}}var Lo=$("<button>"),Do=$("<span>");const jo=St({base:{display:"inline-flex",alignItems:"center",justifyContent:"center",gap:"6px",borderRadius:"4px",fontWeight:"500",cursor:"pointer",transition:"all 0.15s ease",borderWidth:"1px",borderStyle:"solid",userSelect:"none",_disabled:{opacity:.6,cursor:"not-allowed"}},variants:{variant:{default:{backgroundColor:"white",color:"#262626",borderColor:"#d9d9d9",_hover:{backgroundColor:"#f5f5f5"},_active:{backgroundColor:"#eee"}},primary:{backgroundColor:"primary.500",color:"white",borderColor:"primary.500",_hover:{backgroundColor:"primary.600",borderColor:"primary.600"},_active:{backgroundColor:"primary.700",borderColor:"primary.700"}},success:{backgroundColor:"success.500",color:"white",borderColor:"success.500",_hover:{backgroundColor:"success.600",borderColor:"success.600"},_active:{backgroundColor:"success.700",borderColor:"success.700"}},warning:{backgroundColor:"warning.500",color:"white",borderColor:"warning.500"},danger:{backgroundColor:"danger.500",color:"white",borderColor:"danger.500",_hover:{backgroundColor:"danger.600",borderColor:"danger.600"}},plain:{backgroundColor:"white",color:"primary.500",borderColor:"primary.200",_hover:{backgroundColor:"primary.50"}},text:{backgroundColor:"transparent",color:"primary.500",borderColor:"transparent",_hover:{backgroundColor:"primary.50"},_active:{backgroundColor:"primary.100"}}},size:{sm:{height:"28px",fontSize:"12px",px:"10px"},md:{height:"32px",fontSize:"13px",px:"12px"},lg:{height:"36px",fontSize:"14px",px:"14px"}},block:{true:{width:"100%"},false:{}}},defaultVariants:{variant:"default",size:"md",block:!1}});function he(o){const[r,i]=Be(o,["class","children","variant","size","loading","block"]);return(()=>{var c=Lo();return Pe(c,_e({get class(){return e(jo({variant:r.variant,size:r.size,block:!!r.block}),r.class||"")},get disabled(){return r.loading||i.disabled}},i),!1,!0),n(c,(()=>{var a=re(()=>!!r.loading);return()=>a()&&(()=>{var h=Do();return R(()=>t(h,e({width:"14px",height:"14px",borderRadius:"50%",borderWidth:"2px",borderStyle:"solid",borderTopColor:"white",borderLeftColor:"white",borderRightColor:"transparent",borderBottomColor:"transparent",animation:"spin 0.8s linear infinite"}))),h})()})(),null),n(c,()=>r.children,null),c})()}(()=>{if(typeof document>"u")return null;const o=document.createElement("style");return o.textContent="@keyframes spin{to{transform:rotate(360deg)}}",document.head.appendChild(o),o})();var Eo=$("<div><input>"),Mo=$("<button type=button>×");const Oo=St({base:{display:"inline-flex",alignItems:"center",borderWidth:"1px",borderStyle:"solid",borderColor:"#d9d9d9",borderRadius:"4px",backgroundColor:"white",transition:"all 0.15s ease",_focusWithin:{borderColor:"primary.500",boxShadow:"0 0 0 1px token(colors.primary.500 / 20%)"}},variants:{size:{sm:{height:"28px",fontSize:"12px",px:"8px",gap:"6px"},md:{height:"32px",fontSize:"13px",px:"10px",gap:"6px"},lg:{height:"36px",fontSize:"14px",px:"12px",gap:"8px"}},status:{default:{},success:{borderColor:"success.500"},warning:{borderColor:"warning.500"},danger:{borderColor:"danger.500"}}},defaultVariants:{size:"md",status:"default"}}),Fo=St({base:{flex:1,border:"none",outline:"none",background:"transparent",color:"#262626",height:"100%","::placeholder":{color:"#bfbfbf"}}});function Ho(o){const[r,i]=Be(o,["class","status","size","clearable","prefix","suffix"]);return(()=>{var c=Eo(),a=c.firstChild;return n(c,()=>r.prefix,a),Pe(a,_e({get class(){return e(Fo())}},i),!1,!1),n(c,(()=>{var h=re(()=>!!r.clearable);return()=>h()&&(()=>{var u=Mo();return u.$$click=()=>i.onInput?.({currentTarget:{value:""}}),R(()=>t(u,e({color:"#8c8c8c",_hover:{color:"#595959"}}))),u})()})(),null),n(c,()=>r.suffix,null),R(()=>t(c,e(Oo({status:r.status,size:r.size}),r.class||""))),c})()}rt(["click"]);var Vo=$("<div><div><div><h1>行情分析</h1><div><span>沪深A股</span><span>数据更新: 15:30</span><div><div></div><span></span></div></div></div><div></div></div><div><div><h2>市场概览</h2><div></div></div><div><div><div>上证指数</div><div>3,247.89</div><div>-12.34 (-0.38%)</div></div><div><div>深证成指</div><div>10,567.23</div><div>+45.67 (+0.43%)</div></div><div><div>创业板指</div><div>2,234.56</div><div>-8.90 (-0.40%)</div></div><div><div>科创50</div><div>1,123.45</div><div>+15.23 (+1.37%)</div></div></div></div><div><div><h3>市场筛选</h3><span>共 <!> 只股票</span></div><div><div></div><div><span>板块:</span></div></div></div><div><div><h2>股票列表</h2></div><div><table><thead><tr><th>代码</th><th>名称</th><th>现价</th><th>涨跌额</th><th>涨跌幅</th><th>成交量</th><th>最高价</th><th>最低价</th><th>操作</th></tr></thead><tbody></tbody></table></div><div><div>共 <!> 条数据</div><div><span>1"),qo=$("<tr><td></td><td></td><td></td><td></td><td>%</td><td></td><td></td><td></td><td><div>"),No=$("<div><div><h3> 详细信息</h3></div><div><div>📊</div><p>K线图表和技术指标</p><p>这里将显示选中股票的详细分析图表");function zt(){const o=[{symbol:"000725",name:"京东方A",price:3.45,change:-1.43,changePercent:-29.3,volume:7340750,high:3.52,low:3.4,open:3.48,marketCap:12e10},{symbol:"300519",name:"新光药业",price:68.94,change:-1.05,changePercent:-1.5,volume:410750,high:70.1,low:68.5,open:69.9,marketCap:28e9},{symbol:"000831",name:"五矿稀土",price:26.09,change:-1.37,changePercent:-5,volume:7558720,high:27.5,low:25.8,open:27.2,marketCap:45e9},{symbol:"000001",name:"平安银行",price:12.45,change:.23,changePercent:1.88,volume:1568e4,high:12.58,low:12.2,open:12.3,marketCap:24e10},{symbol:"000002",name:"万科A",price:8.76,change:-.15,changePercent:-1.68,volume:895e4,high:8.95,low:8.65,open:8.85,marketCap:98e9}],[r,i]=de(o),c=Po(),[a,h]=de("AAPL"),[u,p]=de("");fr(()=>{const v=c.marketData();v.size>0&&i(b=>b.map(w=>{const f=v.get(w.symbol);return f?{...w,price:f.price,change:f.change,changePercent:f.changePercent,volume:f.volume}:w}))}),ht(()=>{console.log("Market page mounted");const v=o.map(f=>f.symbol);c.subscribeToMarketData(v);let b;setTimeout(()=>{b=setInterval(()=>{c.connectionStatus()!=="connected"&&i(f=>f.map(I=>({...I,price:Math.max(.01,I.price+(Math.random()-.5)*2),change:I.change+(Math.random()-.5)*.5,changePercent:I.changePercent+(Math.random()-.5)*.2,volume:Math.max(0,I.volume+Math.floor((Math.random()-.5)*1e5))})))},3e3)},2e3),yt(()=>{b&&clearInterval(b),c.unsubscribeFromMarketData(v)})});const S=()=>{const v=u().toLowerCase();return r().filter(b=>b.symbol.toLowerCase().includes(v)||b.name.toLowerCase().includes(v))},z=(v,b=2)=>new Intl.NumberFormat("zh-CN",{minimumFractionDigits:b,maximumFractionDigits:b}).format(v),A=v=>v>=1e6?`${(v/1e6).toFixed(1)}M`:v>=1e3?`${(v/1e3).toFixed(1)}K`:v.toString();return(()=>{var v=Vo(),b=v.firstChild,w=b.firstChild,f=w.firstChild,I=f.nextSibling,T=I.firstChild,j=T.nextSibling,F=j.nextSibling,y=F.firstChild,B=y.nextSibling,C=w.nextSibling,W=b.nextSibling,P=W.firstChild,E=P.firstChild,L=E.nextSibling,_=P.nextSibling,m=_.firstChild,s=m.firstChild,k=s.nextSibling,M=k.nextSibling,D=m.nextSibling,V=D.firstChild,H=V.nextSibling,G=H.nextSibling,U=D.nextSibling,q=U.firstChild,d=q.nextSibling,Y=d.nextSibling,Z=U.nextSibling,le=Z.firstChild,ae=le.nextSibling,ce=ae.nextSibling,xe=W.nextSibling,g=xe.firstChild,O=g.firstChild,J=O.nextSibling,oe=J.firstChild,ee=oe.nextSibling;ee.nextSibling;var ie=g.nextSibling,K=ie.firstChild,te=K.nextSibling,ue=te.firstChild,Q=xe.nextSibling,N=Q.firstChild,ge=N.firstChild,pe=N.nextSibling,ve=pe.firstChild,fe=ve.firstChild,ye=fe.firstChild,Ce=ye.firstChild,Se=Ce.nextSibling,Le=Se.nextSibling,De=Le.nextSibling,je=De.nextSibling,Ee=je.nextSibling,Me=Ee.nextSibling,Oe=Me.nextSibling,Ye=Oe.nextSibling,Ue=fe.nextSibling,Fe=pe.nextSibling,ke=Fe.firstChild,Ge=ke.firstChild,ot=Ge.nextSibling;ot.nextSibling;var Qe=ke.nextSibling,it=Qe.firstChild;return n(B,()=>c.connectionStatus()==="connected"?"实时连接":"模拟数据"),n(C,(()=>{var l=re(()=>c.connectionStatus()!=="connected");return()=>l()&&x(he,{variant:"primary",onClick:()=>c.reconnect(),children:"重新连接"})})(),null),n(C,x(he,{children:"导出数据"}),null),n(C,x(he,{variant:"success",children:"自选股"}),null),n(L,x(he,{size:"sm",variant:"primary",children:"日"}),null),n(L,x(he,{size:"sm",children:"周"}),null),n(L,x(he,{size:"sm",children:"月"}),null),n(J,()=>S().length,ee),n(K,x(Ho,{placeholder:"搜索股票代码或名称",get value(){return u()},onInput:l=>p(l.currentTarget.value)}),null),n(K,x(he,{variant:"primary",children:"搜索"}),null),n(te,x(he,{size:"sm",variant:"primary",children:"全部"}),null),n(te,x(he,{size:"sm",children:"沪A"}),null),n(te,x(he,{size:"sm",children:"深A"}),null),n(te,x(he,{size:"sm",children:"创业板"}),null),n(Ue,()=>S().map(l=>(()=>{var be=qo(),me=be.firstChild,ze=me.nextSibling,He=ze.nextSibling,$e=He.nextSibling,we=$e.nextSibling,Ze=we.firstChild,Ve=we.nextSibling,ne=Ve.nextSibling,Re=ne.nextSibling,qe=Re.nextSibling,Ae=qe.firstChild;return be.$$click=()=>h(l.symbol),n(me,()=>l.symbol),n(ze,()=>l.name),n(He,()=>z(l.price)),n($e,()=>l.change>=0?"+":"",null),n($e,()=>z(l.change),null),n(we,()=>l.changePercent>=0?"+":"",Ze),n(we,()=>z(l.changePercent),Ze),n(Ve,()=>A(l.volume)),n(ne,()=>z(l.high)),n(Re,()=>z(l.low)),n(Ae,x(he,{size:"sm",variant:"danger",children:"卖"}),null),n(Ae,x(he,{size:"sm",variant:"success",children:"买"}),null),R(X=>{var Ne=e({borderBottom:"1px solid #f0f0f0",cursor:"pointer",transition:"background-color 0.2s",backgroundColor:a()===l.symbol?"#e6f7ff":"transparent",_hover:{backgroundColor:"#fafafa"}}),Ke=e({padding:"12px 16px",fontSize:"14px",fontWeight:"600",color:"#262626"}),nt=e({padding:"12px 16px",fontSize:"14px",color:"#262626"}),lt=e({padding:"12px 16px",textAlign:"right",fontSize:"14px",fontWeight:"600",color:"#262626"}),at=e({padding:"12px 16px",textAlign:"right",fontSize:"14px",fontWeight:"500",color:l.change>=0?"#52c41a":"#f5222d"}),dt=e({padding:"12px 16px",textAlign:"right",fontSize:"14px",fontWeight:"500",color:l.changePercent>=0?"#52c41a":"#f5222d"}),st=e({padding:"12px 16px",textAlign:"right",fontSize:"14px",color:"#8c8c8c"}),ct=e({padding:"12px 16px",textAlign:"right",fontSize:"14px",color:"#8c8c8c"}),gt=e({padding:"12px 16px",textAlign:"right",fontSize:"14px",color:"#8c8c8c"}),ut=e({padding:"12px 16px",textAlign:"right"}),pt=e({display:"flex",gap:"4px",justifyContent:"flex-end"});return Ne!==X.e&&t(be,X.e=Ne),Ke!==X.t&&t(me,X.t=Ke),nt!==X.a&&t(ze,X.a=nt),lt!==X.o&&t(He,X.o=lt),at!==X.i&&t($e,X.i=at),dt!==X.n&&t(we,X.n=dt),st!==X.s&&t(Ve,X.s=st),ct!==X.h&&t(ne,X.h=ct),gt!==X.r&&t(Re,X.r=gt),ut!==X.d&&t(qe,X.d=ut),pt!==X.l&&t(Ae,X.l=pt),X},{e:void 0,t:void 0,a:void 0,o:void 0,i:void 0,n:void 0,s:void 0,h:void 0,r:void 0,d:void 0,l:void 0}),be})())),n(ke,()=>S().length,ot),n(Qe,x(he,{size:"sm",children:"上一页"}),it),n(Qe,x(he,{size:"sm",children:"下一页"}),null),n(v,(()=>{var l=re(()=>!!a());return()=>l()&&(()=>{var be=No(),me=be.firstChild,ze=me.firstChild,He=ze.firstChild,$e=me.nextSibling,we=$e.firstChild,Ze=we.nextSibling,Ve=Ze.nextSibling;return n(ze,a,He),R(ne=>{var Re=e({marginTop:"24px",backgroundColor:"white",borderRadius:"8px",border:"1px solid #e8e8e8",overflow:"hidden"}),qe=e({padding:"20px",borderBottom:"1px solid #e8e8e8"}),Ae=e({fontSize:"16px",fontWeight:"600",color:"#262626",margin:"0"}),X=e({padding:"20px",textAlign:"center",color:"#8c8c8c"}),Ne=e({fontSize:"48px",marginBottom:"16px"}),Ke=e({fontSize:"12px"});return Re!==ne.e&&t(be,ne.e=Re),qe!==ne.t&&t(me,ne.t=qe),Ae!==ne.a&&t(ze,ne.a=Ae),X!==ne.o&&t($e,ne.o=X),Ne!==ne.i&&t(we,ne.i=Ne),Ke!==ne.n&&t(Ve,ne.n=Ke),ne},{e:void 0,t:void 0,a:void 0,o:void 0,i:void 0,n:void 0}),be})()})(),null),R(l=>{var be=e({padding:"16px",backgroundColor:"#f0f2f5",minHeight:"100%"}),me=e({display:"flex",alignItems:"center",justifyContent:"space-between",marginBottom:"16px"}),ze=e({fontSize:"20px",fontWeight:"600",color:"#262626",margin:0,marginBottom:"4px"}),He=e({display:"flex",alignItems:"center",gap:"12px",fontSize:"12px",color:"#8c8c8c"}),$e=e({padding:"2px 6px",backgroundColor:"#f6ffed",color:"#52c41a",borderRadius:"2px",fontSize:"11px"}),we=e({display:"flex",alignItems:"center",gap:"4px"}),Ze=e({width:"6px",height:"6px",borderRadius:"50%",backgroundColor:c.connectionStatus()==="connected"?"#52c41a":"#faad14"}),Ve=e({display:"flex",alignItems:"center",gap:"8px"}),ne=e({marginBottom:"16px",backgroundColor:"white",borderRadius:"8px",border:"1px solid #e8e8e8",padding:"16px"}),Re=e({display:"flex",alignItems:"center",justifyContent:"space-between",marginBottom:"12px"}),qe=e({fontSize:"16px",fontWeight:"600",color:"#262626",margin:0}),Ae=e({display:"flex",alignItems:"center",gap:"8px"}),X=e({display:"grid",gridTemplateColumns:"repeat(auto-fit, minmax(150px, 1fr))",gap:"16px"}),Ne=e({padding:"12px",backgroundColor:"#fafafa",borderRadius:"4px",textAlign:"center"}),Ke=e({fontSize:"12px",color:"#8c8c8c",marginBottom:"4px"}),nt=e({fontSize:"16px",fontWeight:"600",color:"#f5222d",marginBottom:"2px"}),lt=e({fontSize:"11px",color:"#f5222d"}),at=e({padding:"12px",backgroundColor:"#fafafa",borderRadius:"4px",textAlign:"center"}),dt=e({fontSize:"12px",color:"#8c8c8c",marginBottom:"4px"}),st=e({fontSize:"16px",fontWeight:"600",color:"#52c41a",marginBottom:"2px"}),ct=e({fontSize:"11px",color:"#52c41a"}),gt=e({padding:"12px",backgroundColor:"#fafafa",borderRadius:"4px",textAlign:"center"}),ut=e({fontSize:"12px",color:"#8c8c8c",marginBottom:"4px"}),pt=e({fontSize:"16px",fontWeight:"600",color:"#f5222d",marginBottom:"2px"}),At=e({fontSize:"11px",color:"#f5222d"}),Tt=e({padding:"12px",backgroundColor:"#fafafa",borderRadius:"4px",textAlign:"center"}),Bt=e({fontSize:"12px",color:"#8c8c8c",marginBottom:"4px"}),Pt=e({fontSize:"16px",fontWeight:"600",color:"#52c41a",marginBottom:"2px"}),Lt=e({fontSize:"11px",color:"#52c41a"}),Dt=e({marginBottom:"16px",backgroundColor:"white",borderRadius:"8px",border:"1px solid #e8e8e8",padding:"12px 16px"}),jt=e({display:"flex",alignItems:"center",justifyContent:"space-between",marginBottom:"12px"}),Et=e({fontSize:"14px",fontWeight:"600",color:"#262626",margin:0}),Mt=e({fontSize:"12px",color:"#8c8c8c"}),Ot=e({display:"flex",alignItems:"center",justifyContent:"space-between"}),Ft=e({display:"flex",alignItems:"center",gap:"8px"}),Ht=e({display:"flex",alignItems:"center",gap:"6px"}),Vt=e({fontSize:"12px",color:"#8c8c8c",marginRight:"4px"}),qt=e({backgroundColor:"white",borderRadius:"8px",border:"1px solid #e8e8e8",overflow:"hidden"}),Nt=e({padding:"16px 20px",borderBottom:"1px solid #e8e8e8",backgroundColor:"#fafafa"}),Kt=e({fontSize:"16px",fontWeight:"600",color:"#262626",margin:"0"}),Xt=e({overflowX:"auto"}),Yt=e({width:"100%",borderCollapse:"collapse"}),Ut=e({backgroundColor:"#fafafa"}),Gt=e({padding:"12px 16px",textAlign:"left",fontSize:"12px",fontWeight:"600",color:"#8c8c8c"}),Qt=e({padding:"12px 16px",textAlign:"left",fontSize:"12px",fontWeight:"600",color:"#8c8c8c"}),Zt=e({padding:"12px 16px",textAlign:"right",fontSize:"12px",fontWeight:"600",color:"#8c8c8c"}),Jt=e({padding:"12px 16px",textAlign:"right",fontSize:"12px",fontWeight:"600",color:"#8c8c8c"}),er=e({padding:"12px 16px",textAlign:"right",fontSize:"12px",fontWeight:"600",color:"#8c8c8c"}),tr=e({padding:"12px 16px",textAlign:"right",fontSize:"12px",fontWeight:"600",color:"#8c8c8c"}),rr=e({padding:"12px 16px",textAlign:"right",fontSize:"12px",fontWeight:"600",color:"#8c8c8c"}),or=e({padding:"12px 16px",textAlign:"right",fontSize:"12px",fontWeight:"600",color:"#8c8c8c"}),ir=e({padding:"12px 16px",textAlign:"right",fontSize:"12px",fontWeight:"600",color:"#8c8c8c"}),nr=e({padding:"16px 20px",borderTop:"1px solid #f0f0f0",display:"flex",justifyContent:"space-between",alignItems:"center"}),lr=e({fontSize:"14px",color:"#8c8c8c"}),ar=e({display:"flex",gap:"8px",alignItems:"center"}),dr=e({padding:"4px 8px",backgroundColor:"primary.500",color:"white",borderRadius:"4px",fontSize:"12px"});return be!==l.e&&t(v,l.e=be),me!==l.t&&t(b,l.t=me),ze!==l.a&&t(f,l.a=ze),He!==l.o&&t(I,l.o=He),$e!==l.i&&t(T,l.i=$e),we!==l.n&&t(F,l.n=we),Ze!==l.s&&t(y,l.s=Ze),Ve!==l.h&&t(C,l.h=Ve),ne!==l.r&&t(W,l.r=ne),Re!==l.d&&t(P,l.d=Re),qe!==l.l&&t(E,l.l=qe),Ae!==l.u&&t(L,l.u=Ae),X!==l.c&&t(_,l.c=X),Ne!==l.w&&t(m,l.w=Ne),Ke!==l.m&&t(s,l.m=Ke),nt!==l.f&&t(k,l.f=nt),lt!==l.y&&t(M,l.y=lt),at!==l.g&&t(D,l.g=at),dt!==l.p&&t(V,l.p=dt),st!==l.b&&t(H,l.b=st),ct!==l.T&&t(G,l.T=ct),gt!==l.A&&t(U,l.A=gt),ut!==l.O&&t(q,l.O=ut),pt!==l.I&&t(d,l.I=pt),At!==l.S&&t(Y,l.S=At),Tt!==l.W&&t(Z,l.W=Tt),Bt!==l.C&&t(le,l.C=Bt),Pt!==l.B&&t(ae,l.B=Pt),Lt!==l.v&&t(ce,l.v=Lt),Dt!==l.k&&t(xe,l.k=Dt),jt!==l.x&&t(g,l.x=jt),Et!==l.j&&t(O,l.j=Et),Mt!==l.q&&t(J,l.q=Mt),Ot!==l.z&&t(ie,l.z=Ot),Ft!==l.P&&t(K,l.P=Ft),Ht!==l.H&&t(te,l.H=Ht),Vt!==l.F&&t(ue,l.F=Vt),qt!==l.M&&t(Q,l.M=qt),Nt!==l.D&&t(N,l.D=Nt),Kt!==l.R&&t(ge,l.R=Kt),Xt!==l.E&&t(pe,l.E=Xt),Yt!==l.L&&t(ve,l.L=Yt),Ut!==l.N&&t(ye,l.N=Ut),Gt!==l.G&&t(Ce,l.G=Gt),Qt!==l.U&&t(Se,l.U=Qt),Zt!==l.K&&t(Le,l.K=Zt),Jt!==l.V&&t(De,l.V=Jt),er!==l.Y&&t(je,l.Y=er),tr!==l.J&&t(Ee,l.J=tr),rr!==l.Q&&t(Me,l.Q=rr),or!==l.Z&&t(Oe,l.Z=or),ir!==l.X&&t(Ye,l.X=ir),nr!==l._&&t(Fe,l._=nr),lr!==l.$&&t(ke,l.$=lr),ar!==l.te&&t(Qe,l.te=ar),dr!==l.tt&&t(it,l.tt=dr),l},{e:void 0,t:void 0,a:void 0,o:void 0,i:void 0,n:void 0,s:void 0,h:void 0,r:void 0,d:void 0,l:void 0,u:void 0,c:void 0,w:void 0,m:void 0,f:void 0,y:void 0,g:void 0,p:void 0,b:void 0,T:void 0,A:void 0,O:void 0,I:void 0,S:void 0,W:void 0,C:void 0,B:void 0,v:void 0,k:void 0,x:void 0,j:void 0,q:void 0,z:void 0,P:void 0,H:void 0,F:void 0,M:void 0,D:void 0,R:void 0,E:void 0,L:void 0,N:void 0,G:void 0,U:void 0,K:void 0,V:void 0,Y:void 0,J:void 0,Q:void 0,Z:void 0,X:void 0,_:void 0,$:void 0,te:void 0,tt:void 0}),v})()}rt(["click"]);var Ko=$("<div><div>");function Xo(o){const[r,i]=de();let c;return ht(async()=>{const a=r();if(a)try{const u="/libs/monaco-editor/min/vs";console.log(`Monaco Editor 配置: 本地模式, 路径: ${u}`),cr.config({paths:{vs:u}});const p=await cr.init();o.language==="python"&&p.languages.registerCompletionItemProvider("python",{provideCompletionItems:(S,z)=>{const A=S.getWordUntilPosition(z),v={startLineNumber:z.lineNumber,endLineNumber:z.lineNumber,startColumn:A.startColumn,endColumn:A.endColumn};return{suggestions:[{label:"def",kind:p.languages.CompletionItemKind.Keyword,insertText:"def ${1:function_name}(${2:parameters}):\n    ${3:pass}",insertTextRules:p.languages.CompletionItemInsertTextRule.InsertAsSnippet,documentation:"Define a function",range:v},{label:"initialize",kind:p.languages.CompletionItemKind.Function,insertText:"def initialize(context):\n    ${1:pass}",insertTextRules:p.languages.CompletionItemInsertTextRule.InsertAsSnippet,documentation:"策略初始化函数",range:v},{label:"handle_data",kind:p.languages.CompletionItemKind.Function,insertText:"def handle_data(context, data):\n    ${1:pass}",insertTextRules:p.languages.CompletionItemInsertTextRule.InsertAsSnippet,documentation:"主要的交易逻辑函数",range:v},{label:"order_target_percent",kind:p.languages.CompletionItemKind.Function,insertText:"order_target_percent(${1:security}, ${2:percent})",insertTextRules:p.languages.CompletionItemInsertTextRule.InsertAsSnippet,documentation:"下单到目标百分比",range:v},{label:"attribute_history",kind:p.languages.CompletionItemKind.Function,insertText:"attribute_history(${1:security}, ${2:count}, ${3:unit}, ${4:fields})",insertTextRules:p.languages.CompletionItemInsertTextRule.InsertAsSnippet,documentation:"获取历史数据",range:v},{label:"log.info",kind:p.languages.CompletionItemKind.Function,insertText:"log.info(${1:message})",insertTextRules:p.languages.CompletionItemInsertTextRule.InsertAsSnippet,documentation:"输出日志信息",range:v}]}}}),c=p.editor.create(a,{value:o.value||"",language:o.language||"python",theme:o.theme||"vs",fontSize:13,lineNumbers:"on",roundedSelection:!1,scrollBeyondLastLine:!1,minimap:{enabled:!0},automaticLayout:!0,tabSize:4,insertSpaces:!0,wordWrap:"on",folding:!0,renderLineHighlight:"all",selectOnLineNumbers:!0,matchBrackets:"always",...o.options}),c.onDidChangeModelContent(()=>{o.onChange&&c&&o.onChange(c.getValue())}),c.addCommand(p.KeyMod.CtrlCmd|p.KeyCode.KeyS,()=>{console.log("保存策略快捷键触发")}),c.addCommand(p.KeyMod.CtrlCmd|p.KeyCode.Enter,()=>{console.log("运行策略快捷键触发")})}catch(h){console.error("Monaco Editor 初始化失败:",h)}}),yt(()=>{c&&c.dispose()}),(()=>{var a=Ko(),h=a.firstChild;return Wr(i,h),R(u=>{var p=e({width:"100%",height:`${o.height||400}px`,border:"1px solid #e5e7eb",borderRadius:"6px",overflow:"hidden"}),S=e({width:"100%",height:"100%"});return p!==u.e&&t(a,u.e=p),S!==u.t&&t(h,u.t=S),u},{e:void 0,t:void 0}),a})()}var Yo=$('<div><div><h1>🧠 策略编辑器</h1><p>创建和编辑量化交易策略</p></div><div><div><div><h2>策略代码</h2><div><button>运行回测</button><button>保存策略</button></div></div><div></div></div><div><div><h2>回测结果</h2></div><div><div><div>📊</div><p>等待回测结果</p><p>点击"运行回测"开始策略测试</p></div></div></div></div><div><h3>策略模板</h3><div><button><div>双均线策略</div><div>基于移动平均线的经典策略</div></button><button><div>RSI策略</div><div>基于相对强弱指标的策略</div></button><button><div>布林带策略</div><div>利用布林带进行交易决策</div></button><button><div>机器学习策略</div><div>基于AI模型的量化策略');function Uo(){const[o,r]=de(`# 量化策略示例
# 这是一个简单的移动平均线策略

def initialize(context):
    # 设置基准和股票
    g.benchmark = '000300.XSHG'
    g.stock = '000001.XSHE'
    
def handle_data(context, data):
    # 获取历史价格
    hist = attribute_history(g.stock, 20, '1d', ['close'])
    ma5 = hist['close'][-5:].mean()
    ma20 = hist['close'][-20:].mean()
    current_price = data[g.stock].close
    
    # 交易逻辑
    if ma5 > ma20 and current_price > ma5:
        # 金叉买入信号
        order_target_percent(g.stock, 0.8)
        log.info(f"买入信号，价格: {current_price}")
    elif ma5 < ma20:
        # 死叉卖出信号
        order_target_percent(g.stock, 0)
        log.info(f"卖出信号，价格: {current_price}")
`);return(()=>{var i=Yo(),c=i.firstChild,a=c.firstChild,h=a.nextSibling,u=c.nextSibling,p=u.firstChild,S=p.firstChild,z=S.firstChild,A=z.nextSibling,v=A.firstChild,b=v.nextSibling,w=S.nextSibling,f=p.nextSibling,I=f.firstChild,T=I.firstChild,j=I.nextSibling,F=j.firstChild,y=F.firstChild,B=y.nextSibling,C=B.nextSibling,W=u.nextSibling,P=W.firstChild,E=P.nextSibling,L=E.firstChild,_=L.firstChild,m=_.nextSibling,s=L.nextSibling,k=s.firstChild,M=k.nextSibling,D=s.nextSibling,V=D.firstChild,H=V.nextSibling,G=D.nextSibling,U=G.firstChild,q=U.nextSibling;return n(w,x(Xo,{get value(){return o()},language:"python",theme:"vs",height:500,onChange:r,options:{minimap:{enabled:!0},fontSize:14,wordWrap:"on",automaticLayout:!0}})),R(d=>{var Y=e({padding:"24px",maxWidth:"1400px",margin:"0 auto",height:"100%"}),Z=e({marginBottom:"32px"}),le=e({fontSize:"32px",fontWeight:"bold",color:"gray.900",marginBottom:"8px"}),ae=e({fontSize:"16px",color:"gray.600"}),ce=e({display:"grid",gridTemplateColumns:"1fr 1fr",gap:"24px",height:"calc(100vh - 200px)"}),xe=e({backgroundColor:"white",borderRadius:"12px",boxShadow:"0 1px 3px rgba(0, 0, 0, 0.1)",border:"1px solid #e5e7eb",overflow:"hidden",display:"flex",flexDirection:"column"}),g=e({padding:"16px 20px",borderBottom:"1px solid #e5e7eb",backgroundColor:"#f9fafb",display:"flex",justifyContent:"space-between",alignItems:"center"}),O=e({fontSize:"16px",fontWeight:"600",color:"gray.900"}),J=e({display:"flex",gap:"8px"}),oe=e({padding:"6px 12px",backgroundColor:"blue.600",color:"white",border:"none",borderRadius:"6px",fontSize:"12px",fontWeight:"500",cursor:"pointer",_hover:{backgroundColor:"blue.700"}}),ee=e({padding:"6px 12px",backgroundColor:"green.600",color:"white",border:"none",borderRadius:"6px",fontSize:"12px",fontWeight:"500",cursor:"pointer",_hover:{backgroundColor:"green.700"}}),ie=e({flex:1,padding:"8px"}),K=e({backgroundColor:"white",borderRadius:"12px",boxShadow:"0 1px 3px rgba(0, 0, 0, 0.1)",border:"1px solid #e5e7eb",overflow:"hidden",display:"flex",flexDirection:"column"}),te=e({padding:"16px 20px",borderBottom:"1px solid #e5e7eb",backgroundColor:"#f9fafb"}),ue=e({fontSize:"16px",fontWeight:"600",color:"gray.900"}),Q=e({flex:1,padding:"20px",display:"flex",alignItems:"center",justifyContent:"center"}),N=e({textAlign:"center",color:"gray.500"}),ge=e({fontSize:"48px",marginBottom:"16px"}),pe=e({fontSize:"16px",marginBottom:"8px"}),ve=e({fontSize:"14px"}),fe=e({marginTop:"24px",backgroundColor:"white",borderRadius:"12px",boxShadow:"0 1px 3px rgba(0, 0, 0, 0.1)",border:"1px solid #e5e7eb",padding:"20px"}),ye=e({fontSize:"18px",fontWeight:"600",color:"gray.900",marginBottom:"16px"}),Ce=e({display:"grid",gridTemplateColumns:"repeat(auto-fit, minmax(200px, 1fr))",gap:"12px"}),Se=e({padding:"12px 16px",border:"1px solid #e5e7eb",borderRadius:"8px",backgroundColor:"white",textAlign:"left",cursor:"pointer",transition:"all 0.2s",_hover:{borderColor:"blue.500",backgroundColor:"blue.50"}}),Le=e({fontSize:"14px",fontWeight:"500",color:"gray.900",marginBottom:"4px"}),De=e({fontSize:"12px",color:"gray.600"}),je=e({padding:"12px 16px",border:"1px solid #e5e7eb",borderRadius:"8px",backgroundColor:"white",textAlign:"left",cursor:"pointer",transition:"all 0.2s",_hover:{borderColor:"blue.500",backgroundColor:"blue.50"}}),Ee=e({fontSize:"14px",fontWeight:"500",color:"gray.900",marginBottom:"4px"}),Me=e({fontSize:"12px",color:"gray.600"}),Oe=e({padding:"12px 16px",border:"1px solid #e5e7eb",borderRadius:"8px",backgroundColor:"white",textAlign:"left",cursor:"pointer",transition:"all 0.2s",_hover:{borderColor:"blue.500",backgroundColor:"blue.50"}}),Ye=e({fontSize:"14px",fontWeight:"500",color:"gray.900",marginBottom:"4px"}),Ue=e({fontSize:"12px",color:"gray.600"}),Fe=e({padding:"12px 16px",border:"1px solid #e5e7eb",borderRadius:"8px",backgroundColor:"white",textAlign:"left",cursor:"pointer",transition:"all 0.2s",_hover:{borderColor:"blue.500",backgroundColor:"blue.50"}}),ke=e({fontSize:"14px",fontWeight:"500",color:"gray.900",marginBottom:"4px"}),Ge=e({fontSize:"12px",color:"gray.600"});return Y!==d.e&&t(i,d.e=Y),Z!==d.t&&t(c,d.t=Z),le!==d.a&&t(a,d.a=le),ae!==d.o&&t(h,d.o=ae),ce!==d.i&&t(u,d.i=ce),xe!==d.n&&t(p,d.n=xe),g!==d.s&&t(S,d.s=g),O!==d.h&&t(z,d.h=O),J!==d.r&&t(A,d.r=J),oe!==d.d&&t(v,d.d=oe),ee!==d.l&&t(b,d.l=ee),ie!==d.u&&t(w,d.u=ie),K!==d.c&&t(f,d.c=K),te!==d.w&&t(I,d.w=te),ue!==d.m&&t(T,d.m=ue),Q!==d.f&&t(j,d.f=Q),N!==d.y&&t(F,d.y=N),ge!==d.g&&t(y,d.g=ge),pe!==d.p&&t(B,d.p=pe),ve!==d.b&&t(C,d.b=ve),fe!==d.T&&t(W,d.T=fe),ye!==d.A&&t(P,d.A=ye),Ce!==d.O&&t(E,d.O=Ce),Se!==d.I&&t(L,d.I=Se),Le!==d.S&&t(_,d.S=Le),De!==d.W&&t(m,d.W=De),je!==d.C&&t(s,d.C=je),Ee!==d.B&&t(k,d.B=Ee),Me!==d.v&&t(M,d.v=Me),Oe!==d.k&&t(D,d.k=Oe),Ye!==d.x&&t(V,d.x=Ye),Ue!==d.j&&t(H,d.j=Ue),Fe!==d.q&&t(G,d.q=Fe),ke!==d.z&&t(U,d.z=ke),Ge!==d.P&&t(q,d.P=Ge),d},{e:void 0,t:void 0,a:void 0,o:void 0,i:void 0,n:void 0,s:void 0,h:void 0,r:void 0,d:void 0,l:void 0,u:void 0,c:void 0,w:void 0,m:void 0,f:void 0,y:void 0,g:void 0,p:void 0,b:void 0,T:void 0,A:void 0,O:void 0,I:void 0,S:void 0,W:void 0,C:void 0,B:void 0,v:void 0,k:void 0,x:void 0,j:void 0,q:void 0,z:void 0,P:void 0}),i})()}var Go=$("<button>"),Qo=$("<div>");const $t=o=>{const[r,i]=Be(o,["variant","size","loading","icon","fullWidth","children","class","disabled"]),c=e({display:"inline-flex",alignItems:"center",justifyContent:"center",gap:"8px",borderRadius:"8px",fontWeight:"500",transition:"all 0.2s",cursor:"pointer",border:"none",outline:"none",textDecoration:"none",userSelect:"none",_focus:{boxShadow:"0 0 0 3px rgba(59, 130, 246, 0.1)"},_disabled:{opacity:.6,cursor:"not-allowed"}}),a={primary:e({backgroundColor:"blue.600",color:"white",_hover:{backgroundColor:"blue.700"},_active:{backgroundColor:"blue.800"}}),secondary:e({backgroundColor:"gray.100",color:"gray.900",_hover:{backgroundColor:"gray.200"},_active:{backgroundColor:"gray.300"}}),success:e({backgroundColor:"green.600",color:"white",_hover:{backgroundColor:"green.700"},_active:{backgroundColor:"green.800"}}),warning:e({backgroundColor:"yellow.500",color:"white",_hover:{backgroundColor:"yellow.600"},_active:{backgroundColor:"yellow.700"}}),danger:e({backgroundColor:"red.600",color:"white",_hover:{backgroundColor:"red.700"},_active:{backgroundColor:"red.800"}}),ghost:e({backgroundColor:"transparent",color:"gray.700",border:"1px solid",borderColor:"gray.300",_hover:{backgroundColor:"gray.50",borderColor:"gray.400"},_active:{backgroundColor:"gray.100"}})},h={sm:e({padding:"6px 12px",fontSize:"14px",minHeight:"32px"}),md:e({padding:"8px 16px",fontSize:"14px",minHeight:"40px"}),lg:e({padding:"12px 24px",fontSize:"16px",minHeight:"48px"})},u=e({width:"100%"}),p=e({width:"16px",height:"16px",border:"2px solid currentColor",borderTopColor:"transparent",borderRadius:"50%",animation:"spin 1s linear infinite"}),S=r.variant||"primary",z=r.size||"md";return(()=>{var A=Go();return Pe(A,_e({get class(){return et(c,a[S],h[z],r.fullWidth&&u,r.class)},get disabled(){return r.disabled||r.loading}},i),!1,!0),n(A,(()=>{var v=re(()=>!!r.loading);return()=>v()&&(()=>{var b=Qo();return t(b,p),b})()})(),null),n(A,(()=>{var v=re(()=>!!(!r.loading&&r.icon));return()=>v()&&r.icon})(),null),n(A,()=>r.children,null),A})()};var Zo=$("<div><div>"),Jo=$("<div><div><div>"),ei=$("<h3>"),ti=$("<p>");const tt=o=>{const[r,i]=Be(o,["title","subtitle","headerAction","padding","shadow","border","hover","children","class"]),c=e({backgroundColor:"white",borderRadius:"12px",overflow:"hidden",transition:"all 0.2s"}),a={none:"",sm:e({boxShadow:"0 1px 2px rgba(0, 0, 0, 0.05)"}),md:e({boxShadow:"0 4px 6px rgba(0, 0, 0, 0.07)"}),lg:e({boxShadow:"0 10px 15px rgba(0, 0, 0, 0.1)"})},h=e({border:"1px solid",borderColor:"gray.200"}),u=e({_hover:{transform:"translateY(-2px)",boxShadow:"0 8px 25px rgba(0, 0, 0, 0.1)"}}),p={none:"",sm:e({padding:"16px"}),md:e({padding:"24px"}),lg:e({padding:"32px"})},S=e({padding:"24px 24px 0 24px",marginBottom:"16px"}),z=e({fontSize:"18px",fontWeight:"600",color:"gray.900",marginBottom:"4px"}),A=e({fontSize:"14px",color:"gray.600"}),v=e({display:"flex",justifyContent:"space-between",alignItems:"flex-start"}),b=r.shadow||"md",w=r.padding||"md";return(()=>{var f=Zo(),I=f.firstChild;return Pe(f,_e({get class(){return et(c,a[b],r.border&&h,r.hover&&u,r.class)}},i),!1,!0),n(f,(()=>{var T=re(()=>!!(r.title||r.subtitle||r.headerAction));return()=>T()&&(()=>{var j=Jo(),F=j.firstChild,y=F.firstChild;return t(j,S),n(y,(()=>{var B=re(()=>!!r.title);return()=>B()&&(()=>{var C=ei();return t(C,z),n(C,()=>r.title),C})()})(),null),n(y,(()=>{var B=re(()=>!!r.subtitle);return()=>B()&&(()=>{var C=ti();return t(C,A),n(C,()=>r.subtitle),C})()})(),null),n(F,(()=>{var B=re(()=>!!r.headerAction);return()=>B()&&r.headerAction})(),null),R(()=>t(F,r.headerAction?v:"")),j})()})(),I),n(I,()=>r.children),R(()=>t(I,p[w])),f})()};var ri=$('<button aria-label="Close modal">×'),oi=$("<div><h2><span>"),ii=$("<div>"),ni=$("<div><div><div>");const li=o=>{fr(()=>{if(o.isOpen){const v=b=>{b.key==="Escape"&&o.closable!==!1&&o.onClose()};document.addEventListener("keydown",v),document.body.style.overflow="hidden",yt(()=>{document.removeEventListener("keydown",v),document.body.style.overflow=""})}});const r=e({position:"fixed",top:0,left:0,right:0,bottom:0,backgroundColor:"rgba(0, 0, 0, 0.5)",display:"flex",alignItems:"center",justifyContent:"center",zIndex:1e3,padding:"16px"}),i=e({backgroundColor:"white",borderRadius:"12px",boxShadow:"0 20px 25px rgba(0, 0, 0, 0.1)",maxHeight:"90vh",overflow:"hidden",display:"flex",flexDirection:"column",animation:"modalEnter 0.2s ease-out"}),c={sm:e({width:"400px",maxWidth:"90vw"}),md:e({width:"600px",maxWidth:"90vw"}),lg:e({width:"800px",maxWidth:"90vw"}),xl:e({width:"1000px",maxWidth:"90vw"})},a=e({padding:"24px 24px 0 24px",borderBottom:"1px solid",borderColor:"gray.200",paddingBottom:"16px",marginBottom:"24px"}),h=e({fontSize:"20px",fontWeight:"600",color:"gray.900",margin:0,display:"flex",justifyContent:"space-between",alignItems:"center"}),u=e({background:"none",border:"none",fontSize:"24px",cursor:"pointer",color:"gray.400",padding:"4px",borderRadius:"4px",_hover:{color:"gray.600",backgroundColor:"gray.100"}}),p=e({padding:"0 24px",flex:1,overflow:"auto"}),S=e({padding:"16px 24px 24px 24px",borderTop:"1px solid",borderColor:"gray.200",marginTop:"24px",display:"flex",justifyContent:"flex-end",gap:"12px"}),z=o.size||"md",A=v=>{v.target===v.currentTarget&&o.maskClosable!==!1&&o.onClose()};return x(se,{get when(){return o.isOpen},get children(){return x(Rr,{get children(){var v=ni(),b=v.firstChild,w=b.firstChild;return v.$$click=A,t(v,r),b.$$click=f=>f.stopPropagation(),n(b,x(se,{get when(){return o.title||o.closable!==!1},get children(){var f=oi(),I=f.firstChild,T=I.firstChild;return t(f,a),t(I,h),n(T,()=>o.title),n(I,x(se,{get when(){return o.closable!==!1},get children(){var j=ri();return Ar(j,"click",o.onClose,!0),t(j,u),j}}),null),f}}),w),t(w,p),n(w,()=>o.children),n(b,x(se,{get when(){return o.footer},get children(){var f=ii();return t(f,S),n(f,()=>o.footer),f}}),null),R(()=>t(b,et(i,c[z],o.class))),v}})}})};rt(["click"]);var ai=$("<label>"),It=$("<div>"),di=$("<div><div><input>");const Wt=o=>{const[r,i]=Be(o,["label","error","helperText","leftIcon","rightIcon","size","fullWidth","class"]),c=e({display:"flex",flexDirection:"column",gap:"6px"}),a=e({width:"100%"}),h=e({fontSize:"14px",fontWeight:"500",color:"gray.700"}),u=e({position:"relative",display:"flex",alignItems:"center"}),p=e({width:"100%",border:"1px solid",borderColor:"gray.300",borderRadius:"8px",transition:"all 0.2s",backgroundColor:"white",color:"gray.900",_focus:{outline:"none",borderColor:"blue.500",boxShadow:"0 0 0 3px rgba(59, 130, 246, 0.1)"},_disabled:{backgroundColor:"gray.100",color:"gray.500",cursor:"not-allowed"},_placeholder:{color:"gray.400"}}),S=e({borderColor:"red.500",_focus:{borderColor:"red.500",boxShadow:"0 0 0 3px rgba(239, 68, 68, 0.1)"}}),z={sm:e({padding:"8px 12px",fontSize:"14px",minHeight:"36px"}),md:e({padding:"10px 14px",fontSize:"14px",minHeight:"40px"}),lg:e({padding:"12px 16px",fontSize:"16px",minHeight:"48px"})},A=e({position:"absolute",top:"50%",transform:"translateY(-50%)",color:"gray.400",pointerEvents:"none",zIndex:1}),v=e({left:"12px"}),b=e({right:"12px"}),w=e({paddingLeft:"40px"}),f=e({paddingRight:"40px"}),I=e({fontSize:"12px",color:"gray.600"}),T=e({fontSize:"12px",color:"red.600"}),j=r.size||"md";return(()=>{var F=di(),y=F.firstChild,B=y.firstChild;return n(F,x(se,{get when(){return r.label},get children(){var C=ai();return t(C,h),n(C,()=>r.label),C}}),y),t(y,u),n(y,x(se,{get when(){return r.leftIcon},get children(){var C=It();return n(C,()=>r.leftIcon),R(()=>t(C,et(A,v))),C}}),B),Pe(B,_e({get class(){return et(p,z[j],r.error?S:void 0,r.leftIcon?w:void 0,r.rightIcon?f:void 0,r.class)}},i),!1,!1),n(y,x(se,{get when(){return r.rightIcon},get children(){var C=It();return n(C,()=>r.rightIcon),R(()=>t(C,et(A,b))),C}}),null),n(F,x(se,{get when(){return r.error||r.helperText},get children(){var C=It();return n(C,()=>r.error||r.helperText),R(()=>t(C,r.error?T:I)),C}}),null),R(()=>t(F,et(c,r.fullWidth?a:void 0))),F})()};var si=$("<div><h3>账户总值</h3><p>¥"),ci=$("<div><h3>总盈亏</h3><p>¥</p><p>%"),gi=$("<div><h3>可用资金</h3><p>¥"),ui=$("<div><h3>已用保证金</h3><p>¥"),pi=$("<div><table><thead><tr><th>代码</th><th>数量</th><th>盈亏</th></tr></thead><tbody>"),hi=$("<div><table><thead><tr><th>代码</th><th>类型</th><th>状态</th></tr></thead><tbody>"),xi=$("<div><div><div><label>交易类型</label><select aria-label=交易类型><option value=buy>买入</option><option value=sell>卖出</option></select></div><div><label>订单类型</label><select aria-label=订单类型><option value=market>市价单</option><option value=limit>限价单</option></select></div></div><div>"),vi=$("<div slot=footer>"),fi=$("<div><div><div><h1>💼 </h1><p>管理您的交易订单和持仓</p></div></div><div></div><div>"),bi=$("<span>➕"),mi=$("<tr><td></td><td></td><td>¥<br><span>%"),yi=$("<tr><td><br><span>股 @ ¥</span></td><td><span></span></td><td><span>");const Ci=()=>({t:o=>o});function Si(){const{t:o}=Ci(),[r,i]=de([{id:"1",symbol:"AAPL",type:"buy",quantity:100,price:150.25,status:"filled",timestamp:new Date("2024-01-15T10:30:00")},{id:"2",symbol:"TSLA",type:"sell",quantity:50,price:245.8,status:"pending",timestamp:new Date("2024-01-15T11:15:00")},{id:"3",symbol:"MSFT",type:"buy",quantity:75,price:310.45,status:"filled",timestamp:new Date("2024-01-15T09:45:00")}]),[c,a]=de([{symbol:"AAPL",quantity:100,avgPrice:150.25,currentPrice:152.3,unrealizedPnL:205,unrealizedPnLPercent:1.36},{symbol:"MSFT",quantity:75,avgPrice:310.45,currentPrice:308.9,unrealizedPnL:-116.25,unrealizedPnLPercent:-.5}]),[h,u]=de(!1),[p,S]=de({symbol:"",type:"buy",quantity:"",price:"",orderType:"limit"}),[z]=de({totalValue:45678.9,totalPnL:88.75,totalPnLPercent:.19,buyingPower:12345.67,marginUsed:5432.1});ht(()=>{console.log("Trading page mounted");const b=setInterval(()=>{a(w=>w.map(f=>{const I=(Math.random()-.5)*2,T=f.currentPrice+I,j=(T-f.avgPrice)*f.quantity,F=j/(f.avgPrice*f.quantity)*100;return{...f,currentPrice:T,unrealizedPnL:j,unrealizedPnLPercent:F}}))},3e3);return()=>clearInterval(b)});const A=()=>{const b=p();if(!b.symbol||!b.quantity||!b.price&&b.orderType==="limit"){alert("请填写完整的订单信息");return}const w={id:Date.now().toString(),symbol:b.symbol.toUpperCase(),type:b.type,quantity:parseInt(b.quantity),price:b.orderType==="market"?0:parseFloat(b.price),status:"pending",timestamp:new Date};i(f=>[w,...f]),u(!1),S({symbol:"",type:"buy",quantity:"",price:"",orderType:"limit"})},v=z();return(()=>{var b=fi(),w=b.firstChild,f=w.firstChild,I=f.firstChild;I.firstChild;var T=I.nextSibling,j=w.nextSibling,F=j.nextSibling;return n(I,()=>o("nav.trading"),null),n(w,x($t,{variant:"primary",size:"lg",get icon(){return bi()},onClick:()=>u(!0),children:"新建订单"}),null),n(j,x(tt,{padding:"md",shadow:"md",get children(){var y=si(),B=y.firstChild,C=B.nextSibling;return C.firstChild,n(C,()=>v.totalValue.toLocaleString(),null),R(W=>{var P=e({textAlign:"center"}),E=e({fontSize:"14px",fontWeight:"500",color:"gray.600",marginBottom:"8px"}),L=e({fontSize:"24px",fontWeight:"bold",color:"gray.900"});return P!==W.e&&t(y,W.e=P),E!==W.t&&t(B,W.t=E),L!==W.a&&t(C,W.a=L),W},{e:void 0,t:void 0,a:void 0}),y}}),null),n(j,x(tt,{padding:"md",shadow:"md",get children(){var y=ci(),B=y.firstChild,C=B.nextSibling,W=C.firstChild,P=C.nextSibling,E=P.firstChild;return n(C,()=>v.totalPnL>=0?"+":"",W),n(C,()=>Math.abs(v.totalPnL).toFixed(2),null),n(P,()=>v.totalPnL>=0?"+":"",E),n(P,()=>v.totalPnLPercent.toFixed(2),E),R(L=>{var _=e({textAlign:"center"}),m=e({fontSize:"14px",fontWeight:"500",color:"gray.600",marginBottom:"8px"}),s=e({fontSize:"24px",fontWeight:"bold",color:v.totalPnL>=0?"green.600":"red.600"}),k=e({fontSize:"12px",color:v.totalPnL>=0?"green.600":"red.600"});return _!==L.e&&t(y,L.e=_),m!==L.t&&t(B,L.t=m),s!==L.a&&t(C,L.a=s),k!==L.o&&t(P,L.o=k),L},{e:void 0,t:void 0,a:void 0,o:void 0}),y}}),null),n(j,x(tt,{padding:"md",shadow:"md",get children(){var y=gi(),B=y.firstChild,C=B.nextSibling;return C.firstChild,n(C,()=>v.buyingPower.toLocaleString(),null),R(W=>{var P=e({textAlign:"center"}),E=e({fontSize:"14px",fontWeight:"500",color:"gray.600",marginBottom:"8px"}),L=e({fontSize:"24px",fontWeight:"bold",color:"gray.900"});return P!==W.e&&t(y,W.e=P),E!==W.t&&t(B,W.t=E),L!==W.a&&t(C,W.a=L),W},{e:void 0,t:void 0,a:void 0}),y}}),null),n(j,x(tt,{padding:"md",shadow:"md",get children(){var y=ui(),B=y.firstChild,C=B.nextSibling;return C.firstChild,n(C,()=>v.marginUsed.toLocaleString(),null),R(W=>{var P=e({textAlign:"center"}),E=e({fontSize:"14px",fontWeight:"500",color:"gray.600",marginBottom:"8px"}),L=e({fontSize:"24px",fontWeight:"bold",color:"orange.600"});return P!==W.e&&t(y,W.e=P),E!==W.t&&t(B,W.t=E),L!==W.a&&t(C,W.a=L),W},{e:void 0,t:void 0,a:void 0}),y}}),null),n(F,x(tt,{title:"当前持仓",padding:"none",shadow:"md",get children(){var y=pi(),B=y.firstChild,C=B.firstChild,W=C.firstChild,P=W.firstChild,E=P.nextSibling,L=E.nextSibling,_=C.nextSibling;return n(_,x(Xe,{get each(){return c()},children:m=>(()=>{var s=mi(),k=s.firstChild,M=k.nextSibling,D=M.nextSibling,V=D.firstChild,H=V.nextSibling,G=H.nextSibling,U=G.firstChild;return n(k,()=>m.symbol),n(M,()=>m.quantity),n(D,()=>m.unrealizedPnL>=0?"+":"",V),n(D,()=>Math.abs(m.unrealizedPnL).toFixed(2),H),n(G,()=>m.unrealizedPnL>=0?"+":"",U),n(G,()=>m.unrealizedPnLPercent.toFixed(2),U),R(q=>{var d=e({borderBottom:"1px solid",borderColor:"gray.200"}),Y=e({padding:"12px 16px",fontSize:"14px",fontWeight:"500",color:"gray.900"}),Z=e({padding:"12px 16px",textAlign:"right",fontSize:"14px",color:"gray.700"}),le=e({padding:"12px 16px",textAlign:"right",fontSize:"14px",fontWeight:"500",color:m.unrealizedPnL>=0?"green.600":"red.600"}),ae=e({fontSize:"12px"});return d!==q.e&&t(s,q.e=d),Y!==q.t&&t(k,q.t=Y),Z!==q.a&&t(M,q.a=Z),le!==q.o&&t(D,q.o=le),ae!==q.i&&t(G,q.i=ae),q},{e:void 0,t:void 0,a:void 0,o:void 0,i:void 0}),s})()})),R(m=>{var s=e({overflowX:"auto"}),k=e({width:"100%",borderCollapse:"collapse"}),M=e({backgroundColor:"gray.50"}),D=e({padding:"12px 16px",textAlign:"left",fontSize:"12px",fontWeight:"600",color:"gray.600"}),V=e({padding:"12px 16px",textAlign:"right",fontSize:"12px",fontWeight:"600",color:"gray.600"}),H=e({padding:"12px 16px",textAlign:"right",fontSize:"12px",fontWeight:"600",color:"gray.600"});return s!==m.e&&t(y,m.e=s),k!==m.t&&t(B,m.t=k),M!==m.a&&t(W,m.a=M),D!==m.o&&t(P,m.o=D),V!==m.i&&t(E,m.i=V),H!==m.n&&t(L,m.n=H),m},{e:void 0,t:void 0,a:void 0,o:void 0,i:void 0,n:void 0}),y}}),null),n(F,x(tt,{title:"订单历史",padding:"none",shadow:"md",get children(){var y=hi(),B=y.firstChild,C=B.firstChild,W=C.firstChild,P=W.firstChild,E=P.nextSibling,L=E.nextSibling,_=C.nextSibling;return n(_,x(Xe,{get each(){return r()},children:m=>(()=>{var s=yi(),k=s.firstChild,M=k.firstChild,D=M.nextSibling,V=D.firstChild,H=k.nextSibling,G=H.firstChild,U=H.nextSibling,q=U.firstChild;return n(k,()=>m.symbol,M),n(D,()=>m.quantity,V),n(D,()=>m.price,null),n(G,()=>m.type==="buy"?"买入":"卖出"),n(q,(()=>{var d=re(()=>m.status==="filled");return()=>d()?"已成交":m.status==="pending"?"待成交":"已取消"})()),R(d=>{var Y=e({borderBottom:"1px solid",borderColor:"gray.200"}),Z=e({padding:"12px 16px",fontSize:"14px",fontWeight:"500",color:"gray.900"}),le=e({fontSize:"12px",color:"gray.600"}),ae=e({padding:"12px 16px",textAlign:"center",fontSize:"14px"}),ce=e({padding:"4px 8px",borderRadius:"4px",fontSize:"12px",fontWeight:"500",backgroundColor:m.type==="buy"?"green.100":"red.100",color:m.type==="buy"?"green.800":"red.800"}),xe=e({padding:"12px 16px",textAlign:"right",fontSize:"14px"}),g=e({padding:"4px 8px",borderRadius:"4px",fontSize:"12px",fontWeight:"500",backgroundColor:m.status==="filled"?"green.100":m.status==="pending"?"yellow.100":"red.100",color:m.status==="filled"?"green.800":m.status==="pending"?"yellow.800":"red.800"});return Y!==d.e&&t(s,d.e=Y),Z!==d.t&&t(k,d.t=Z),le!==d.a&&t(D,d.a=le),ae!==d.o&&t(H,d.o=ae),ce!==d.i&&t(G,d.i=ce),xe!==d.n&&t(U,d.n=xe),g!==d.s&&t(q,d.s=g),d},{e:void 0,t:void 0,a:void 0,o:void 0,i:void 0,n:void 0,s:void 0}),s})()})),R(m=>{var s=e({overflowX:"auto"}),k=e({width:"100%",borderCollapse:"collapse"}),M=e({backgroundColor:"gray.50"}),D=e({padding:"12px 16px",textAlign:"left",fontSize:"12px",fontWeight:"600",color:"gray.600"}),V=e({padding:"12px 16px",textAlign:"center",fontSize:"12px",fontWeight:"600",color:"gray.600"}),H=e({padding:"12px 16px",textAlign:"right",fontSize:"12px",fontWeight:"600",color:"gray.600"});return s!==m.e&&t(y,m.e=s),k!==m.t&&t(B,m.t=k),M!==m.a&&t(W,m.a=M),D!==m.o&&t(P,m.o=D),V!==m.i&&t(E,m.i=V),H!==m.n&&t(L,m.n=H),m},{e:void 0,t:void 0,a:void 0,o:void 0,i:void 0,n:void 0}),y}}),null),n(b,x(li,{get isOpen(){return h()},onClose:()=>u(!1),title:"新建订单",size:"md",get children(){return[(()=>{var y=xi(),B=y.firstChild,C=B.firstChild,W=C.firstChild,P=W.nextSibling,E=C.nextSibling,L=E.firstChild,_=L.nextSibling,m=B.nextSibling;return n(y,x(Wt,{label:"股票代码",placeholder:"例如: AAPL",get value(){return p().symbol},onInput:s=>S(k=>({...k,symbol:s.currentTarget.value}))}),B),P.addEventListener("change",s=>S(k=>({...k,type:s.currentTarget.value}))),_.addEventListener("change",s=>S(k=>({...k,orderType:s.currentTarget.value}))),n(m,x(Wt,{label:"数量",type:"number",placeholder:"100",get value(){return p().quantity},onInput:s=>S(k=>({...k,quantity:s.currentTarget.value}))}),null),n(m,(()=>{var s=re(()=>p().orderType==="limit");return()=>s()&&x(Wt,{label:"价格",type:"number",step:"0.01",placeholder:"150.25",get value(){return p().price},onInput:k=>S(M=>({...M,price:k.currentTarget.value}))})})(),null),R(s=>{var k=e({display:"flex",flexDirection:"column",gap:"16px"}),M=e({display:"grid",gridTemplateColumns:"1fr 1fr",gap:"16px"}),D=e({display:"block",fontSize:"14px",fontWeight:"500",color:"gray.700",marginBottom:"8px"}),V=e({width:"100%",padding:"10px 14px",border:"1px solid",borderColor:"gray.300",borderRadius:"8px",backgroundColor:"white",color:"gray.900"}),H=e({display:"block",fontSize:"14px",fontWeight:"500",color:"gray.700",marginBottom:"8px"}),G=e({width:"100%",padding:"10px 14px",border:"1px solid",borderColor:"gray.300",borderRadius:"8px",backgroundColor:"white",color:"gray.900"}),U=e({display:"grid",gridTemplateColumns:"1fr 1fr",gap:"16px"});return k!==s.e&&t(y,s.e=k),M!==s.t&&t(B,s.t=M),D!==s.a&&t(W,s.a=D),V!==s.o&&t(P,s.o=V),H!==s.i&&t(L,s.i=H),G!==s.n&&t(_,s.n=G),U!==s.s&&t(m,s.s=U),s},{e:void 0,t:void 0,a:void 0,o:void 0,i:void 0,n:void 0,s:void 0}),R(()=>P.value=p().type),R(()=>_.value=p().orderType),y})(),(()=>{var y=vi();return n(y,x($t,{variant:"ghost",onClick:()=>u(!1),children:"取消"}),null),n(y,x($t,{variant:"primary",onClick:A,children:"提交订单"}),null),y})()]}}),null),R(y=>{var B=e({padding:"24px",maxWidth:"1400px",margin:"0 auto",backgroundColor:"gray.50",minHeight:"100vh"}),C=e({marginBottom:"32px",display:"flex",justifyContent:"space-between",alignItems:"center"}),W=e({fontSize:"32px",fontWeight:"bold",color:"gray.900",marginBottom:"8px"}),P=e({fontSize:"16px",color:"gray.600"}),E=e({display:"grid",gridTemplateColumns:"repeat(auto-fit, minmax(200px, 1fr))",gap:"24px",marginBottom:"32px"}),L=e({display:"grid",gridTemplateColumns:"1fr 1fr",gap:"24px","@media (max-width: 1024px)":{gridTemplateColumns:"1fr"}});return B!==y.e&&t(b,y.e=B),C!==y.t&&t(w,y.t=C),W!==y.a&&t(I,y.a=W),P!==y.o&&t(T,y.o=P),E!==y.i&&t(j,y.i=E),L!==y.n&&t(F,y.n=L),y},{e:void 0,t:void 0,a:void 0,o:void 0,i:void 0,n:void 0}),b})()}function wi(){return x(Tr,{get children(){return x(So,{get children(){return[x(Ie,{path:"/",component:vt}),x(Ie,{path:"/dashboard",component:vt}),x(Ie,{path:"/market",component:zt}),x(Ie,{path:"/market/realtime",component:zt}),x(Ie,{path:"/market/historical",component:zt}),x(Ie,{path:"/trading",component:Si}),x(Ie,{path:"/strategy",component:Uo}),x(Ie,{path:"/account",component:vt}),x(Ie,{path:"/settings",component:vt}),x(Ie,{path:"/api-test",component:To})]}})}})}const vr=document.getElementById("root");vr&&Br(()=>x(wi,{}),vr);console.log("🚀 量化交易前端平台启动成功"),console.log("📊 基于 SolidJS + Panda CSS"),console.log("⚡ 极致性能，专业体验");
