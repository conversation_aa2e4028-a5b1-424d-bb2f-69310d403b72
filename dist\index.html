<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/favicon.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta name="description" content="轻量级量化交易前端平台 - 基于SolidJS构建的高性能量化交易界面" />
    <meta name="keywords" content="量化交易,SolidJS,前端,金融,投资,策略" />
    <meta name="author" content="量化交易前端平台" />
    
    <!-- PWA相关 -->
    <link rel="manifest" href="/manifest.json" />
    <link rel="apple-touch-icon" href="/icons/icon.svg" />
    <meta name="theme-color" content="#3b82f6" />
    <!-- Progressive Web App capability -->
    <meta name="mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-status-bar-style" content="default" />
    <meta name="apple-mobile-web-app-title" content="量化交易" />

    <!-- 预连接优化 -->
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    
    <!-- 字体 -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet" />
    
    <title>量化交易前端平台</title>
    
    <style>
      /* 加载动画 */
      .loading-container {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: #ffffff;
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 9999;
      }
      
      .loading-spinner {
        width: 40px;
        height: 40px;
        border: 3px solid #e5e7eb;
        border-top-color: #3b82f6;
        border-radius: 50%;
        animation: spin 1s linear infinite;
      }
      
      .loading-text {
        margin-top: 16px;
        color: #6b7280;
        font-size: 14px;
        font-family: 'Inter', sans-serif;
      }
      
      @keyframes spin {
        to {
          transform: rotate(360deg);
        }
      }
      
      /* 隐藏加载动画 */
      .loaded .loading-container {
        display: none;
      }
    </style>
    <script type="module" crossorigin src="/assets/main-CDJB8AX6.js"></script>
    <link rel="modulepreload" crossorigin href="/assets/vendor-solid-CRKygbqg.js">
    <link rel="modulepreload" crossorigin href="/assets/vendor-editor-l7stcynF.js">
    <link rel="stylesheet" crossorigin href="/assets/vendor-editor-Bwtp0z3V.css">
    <link rel="stylesheet" crossorigin href="/assets/main-B9kd6qlA.css">
  </head>
  <body>
    <!-- 加载动画 -->
    <div class="loading-container">
      <div>
        <div class="loading-spinner"></div>
        <div class="loading-text">加载中...</div>
      </div>
    </div>
    
    <!-- 应用根节点 -->
    <div id="root"></div>
    
    <!-- 应用脚本 -->
    
    <script>
      // 应用加载完成后隐藏加载动画
      window.addEventListener('load', function() {
        document.body.classList.add('loaded');
      });
      
      // 注册Service Worker (PWA) - 仅在生产环境
      if ('serviceWorker' in navigator && location.hostname !== 'localhost' && location.hostname !== '127.0.0.1') {
        window.addEventListener('load', function() {
          navigator.serviceWorker.register('/sw.js')
            .then(function(registration) {
              console.log('SW registered: ', registration);
            })
            .catch(function(registrationError) {
              console.log('SW registration failed: ', registrationError);
            });
        });
      }
    </script>
  </body>
</html>
