import { Router, Route } from '@solidjs/router';
import { AppLayout } from './components/layout/AppLayout';
import Dashboard from './pages/Dashboard';
import ApiTest from './pages/ApiTest';
import Market from './pages/Market';
import StrategyEditor from './pages/StrategyEditor';
import Trading from './pages/Trading';

function App() {
  return (
    <Router>
      <AppLayout>
        <Route path="/" component={Dashboard} />
        <Route path="/dashboard" component={Dashboard} />
        <Route path="/market" component={Market} />
        <Route path="/market/realtime" component={Market} />
        <Route path="/market/historical" component={Market} />
        <Route path="/trading" component={Trading} />
        <Route path="/strategy" component={StrategyEditor} />
        <Route path="/account" component={Dashboard} />
        <Route path="/settings" component={Dashboard} />
        <Route path="/api-test" component={ApiTest} />
      </AppLayout>
    </Router>
  );
}

export default App;