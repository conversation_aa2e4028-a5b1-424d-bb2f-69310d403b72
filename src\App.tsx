import { Router, Route } from '@solidjs/router';
import { AppLayout } from './components/layout/AppLayout';
import { MessageContainer } from './components/ui';
import Dashboard from './pages/Dashboard_simple';

function App() {
  return (
    <>
      <Router>
        <Route path="/" component={AppLayout}>
          <Route path="/" component={Dashboard} />
          <Route path="/dashboard" component={Dashboard} />
          <Route path="/*" component={Dashboard} />
        </Route>
      </Router>
      <MessageContainer />
    </>
  );
}

export default App;