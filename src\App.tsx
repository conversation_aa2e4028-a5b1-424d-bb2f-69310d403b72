import { Router, Route } from '@solidjs/router';
import Layout from './components/Layout';
import Dashboard from './pages/Dashboard';
import ApiTest from './pages/ApiTest';
import Market from './pages/Market';
import StrategyEditor from './pages/StrategyEditor';

function App() {
  return (
    <Router>
      <Route path="/" component={Layout}>
        <Route path="/" component={Dashboard} />
        <Route path="/dashboard" component={Dashboard} />
        <Route path="/market" component={Market} />
        <Route path="/strategy-editor" component={StrategyEditor} />
        <Route path="/api-test" component={ApiTest} />
      </Route>
    </Router>
  );
}

export default App;