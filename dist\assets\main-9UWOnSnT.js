import{d as Rt,c as xe,u as qo,t as R,i as l,m as ue,a as B,b as t,e as D,A as No,o as St,f as It,F as Ue,g as To,h as Xo,s as Bt,j as Pt,k as Dt,P as Ko,S as He,l as Yo,R as Ke,n as Vo,r as Uo}from"./vendor-solid-BmRsd-Qu.js";import{l as _o}from"./vendor-editor-l7stcynF.js";(function(){const r=document.createElement("link").relList;if(r&&r.supports&&r.supports("modulepreload"))return;for(const s of document.querySelectorAll('link[rel="modulepreload"]'))b(s);new MutationObserver(s=>{for(const v of s)if(v.type==="childList")for(const m of v.addedNodes)m.tagName==="LINK"&&m.rel==="modulepreload"&&b(m)}).observe(document,{childList:!0,subtree:!0});function g(s){const v={};return s.integrity&&(v.integrity=s.integrity),s.referrerPolicy&&(v.referrerPolicy=s.referrerPolicy),s.crossOrigin==="use-credentials"?v.credentials="include":s.crossOrigin==="anonymous"?v.credentials="omit":v.credentials="same-origin",v}function b(s){if(s.ep)return;s.ep=!0;const v=g(s);fetch(s.href,v)}})();function $t(i){return typeof i=="object"&&i!=null&&!Array.isArray(i)}function Go(i){return Object.fromEntries(Object.entries(i??{}).filter(([r,g])=>g!==void 0))}var Qo=i=>i==="base";function Zo(i){return i.slice().filter(r=>!Qo(r))}function zo(i){return String.fromCharCode(i+(i>25?39:97))}function Jo(i){let r="",g;for(g=Math.abs(i);g>52;g=g/52|0)r=zo(g%52)+r;return zo(g%52)+r}function ei(i,r){let g=r.length;for(;g;)i=i*33^r.charCodeAt(--g);return i}function ti(i){return Jo(ei(5381,i)>>>0)}var Bo=/\s*!(important)?/i;function oi(i){return typeof i=="string"?Bo.test(i):!1}function ii(i){return typeof i=="string"?i.replace(Bo,"").trim():i}function Po(i){return typeof i=="string"?i.replaceAll(" ","_"):i}var Lt=i=>{const r=new Map;return(...b)=>{const s=JSON.stringify(b);if(r.has(s))return r.get(s);const v=i(...b);return r.set(s,v),v}};function Do(...i){return i.filter(Boolean).reduce((g,b)=>(Object.keys(b).forEach(s=>{const v=g[s],m=b[s];$t(v)&&$t(m)?g[s]=Do(v,m):g[s]=m}),g),{})}var ri=i=>i!=null;function Lo(i,r,g={}){const{stop:b,getKey:s}=g;function v(m,u=[]){if($t(m)||Array.isArray(m)){const w={};for(const[W,L]of Object.entries(m)){const x=s?.(W,L)??W,S=[...u,x];if(b?.(m,S))return r(m,u);const A=v(L,S);ri(A)&&(w[x]=A)}return w}return r(m,u)}return v(i)}function ni(i,r){return i.reduce((g,b,s)=>{const v=r[s];return b!=null&&(g[v]=b),g},{})}function Mo(i,r,g=!0){const{utility:b,conditions:s}=r,{hasShorthand:v,resolveShorthand:m}=b;return Lo(i,u=>Array.isArray(u)?ni(u,s.breakpoints.keys):u,{stop:u=>Array.isArray(u),getKey:g?u=>v?m(u):u:void 0})}var li={shift:i=>i,finalize:i=>i,breakpoints:{keys:[]}},di=i=>typeof i=="string"?i.replaceAll(/[\n\s]+/g," "):i;function ai(i){const{utility:r,hash:g,conditions:b=li}=i,s=m=>[r.prefix,m].filter(Boolean).join("-"),v=(m,u)=>{let w;if(g){const W=[...b.finalize(m),u];w=s(r.toHash(W,ti))}else w=[...b.finalize(m),s(u)].join(":");return w};return Lt(({base:m,...u}={})=>{const w=Object.assign(u,m),W=Mo(w,i),L=new Set;return Lo(W,(x,S)=>{const A=oi(x);if(x==null)return;const[y,...I]=b.shift(S),V=Zo(I),E=r.transform(y,ii(di(x)));let j=v(V,E.className);A&&(j=`${j}!`),L.add(j)}),Array.from(L).join(" ")})}function si(...i){return i.flat().filter(r=>$t(r)&&Object.keys(Go(r)).length>0)}function ci(i){function r(s){const v=si(...s);return v.length===1?v:v.map(m=>Mo(m,i))}function g(...s){return Do(...r(s))}function b(...s){return Object.assign({},...r(s))}return{mergeCss:Lt(g),assignCss:b}}var gi=/([A-Z])/g,xi=/^ms-/,ui=Lt(i=>i.startsWith("--")?i:i.replace(gi,"-$1").replace(xi,"-ms-").toLowerCase()),vi="cm,mm,Q,in,pc,pt,px,em,ex,ch,rem,lh,rlh,vw,vh,vmin,vmax,vb,vi,svw,svh,lvw,lvh,dvw,dvh,cqw,cqh,cqi,cqb,cqmin,cqmax,%";`${vi.split(",").join("|")}`;const pi="_dark,_light,_hover,_focus,_focusWithin,_focusVisible,_disabled,_active,_visited,_target,_readOnly,_readWrite,_empty,_checked,_enabled,_expanded,_highlighted,_before,_after,_firstLetter,_firstLine,_marker,_selection,_file,_backdrop,_first,_last,_only,_even,_odd,_firstOfType,_lastOfType,_onlyOfType,_peerFocus,_peerHover,_peerActive,_peerFocusWithin,_peerFocusVisible,_peerDisabled,_peerChecked,_peerInvalid,_peerExpanded,_peerPlaceholderShown,_groupFocus,_groupHover,_groupActive,_groupFocusWithin,_groupFocusVisible,_groupDisabled,_groupChecked,_groupExpanded,_groupInvalid,_indeterminate,_required,_valid,_invalid,_autofill,_inRange,_outOfRange,_placeholder,_placeholderShown,_pressed,_selected,_default,_optional,_open,_closed,_fullscreen,_loading,_currentPage,_currentStep,_motionReduce,_motionSafe,_print,_landscape,_portrait,_osDark,_osLight,_highContrast,_lessContrast,_moreContrast,_ltr,_rtl,_scrollbar,_scrollbarThumb,_scrollbarTrack,_horizontal,_vertical,_starting,sm,smOnly,smDown,md,mdOnly,mdDown,lg,lgOnly,lgDown,xl,xlOnly,xlDown,2xl,2xlOnly,2xlDown,smToMd,smToLg,smToXl,smTo2xl,mdToLg,mdToXl,mdTo2xl,lgToXl,lgTo2xl,xlTo2xl,@/xs,@/sm,@/md,@/lg,@/xl,@/2xl,@/3xl,@/4xl,@/5xl,@/6xl,@/7xl,@/8xl,base",Eo=new Set(pi.split(","));function $o(i){return Eo.has(i)||/^@|&|&$/.test(i)}const bi=/^_/,hi=/&|@/;function fi(i){return i.map(r=>Eo.has(r)?r.replace(bi,""):hi.test(r)?`[${Po(r.trim())}]`:r)}function mi(i){return i.sort((r,g)=>{const b=$o(r),s=$o(g);return b&&!s?1:!b&&s?-1:0})}const Si="aspectRatio:aspect,boxDecorationBreak:decoration,zIndex:z,boxSizing:box,objectPosition:obj-pos,objectFit:obj-fit,overscrollBehavior:overscroll,overscrollBehaviorX:overscroll-x,overscrollBehaviorY:overscroll-y,position:pos/1,top:top,left:left,insetInline:inset-x/insetX,insetBlock:inset-y/insetY,inset:inset,insetBlockEnd:inset-b,insetBlockStart:inset-t,insetInlineEnd:end/insetEnd/1,insetInlineStart:start/insetStart/1,right:right,bottom:bottom,float:float,visibility:vis,display:d,hideFrom:hide,hideBelow:show,flexBasis:basis,flex:flex,flexDirection:flex/flexDir,flexGrow:grow,flexShrink:shrink,gridTemplateColumns:grid-cols,gridTemplateRows:grid-rows,gridColumn:col-span,gridRow:row-span,gridColumnStart:col-start,gridColumnEnd:col-end,gridAutoFlow:grid-flow,gridAutoColumns:auto-cols,gridAutoRows:auto-rows,gap:gap,gridGap:gap,gridRowGap:gap-x,gridColumnGap:gap-y,rowGap:gap-x,columnGap:gap-y,justifyContent:justify,alignContent:content,alignItems:items,alignSelf:self,padding:p/1,paddingLeft:pl/1,paddingRight:pr/1,paddingTop:pt/1,paddingBottom:pb/1,paddingBlock:py/1/paddingY,paddingBlockEnd:pb,paddingBlockStart:pt,paddingInline:px/paddingX/1,paddingInlineEnd:pe/1/paddingEnd,paddingInlineStart:ps/1/paddingStart,marginLeft:ml/1,marginRight:mr/1,marginTop:mt/1,marginBottom:mb/1,margin:m/1,marginBlock:my/1/marginY,marginBlockEnd:mb,marginBlockStart:mt,marginInline:mx/1/marginX,marginInlineEnd:me/1/marginEnd,marginInlineStart:ms/1/marginStart,spaceX:space-x,spaceY:space-y,outlineWidth:ring-width/ringWidth,outlineColor:ring-color/ringColor,outline:ring/1,outlineOffset:ring-offset/ringOffset,divideX:divide-x,divideY:divide-y,divideColor:divide-color,divideStyle:divide-style,width:w/1,inlineSize:w,minWidth:min-w/minW,minInlineSize:min-w,maxWidth:max-w/maxW,maxInlineSize:max-w,height:h/1,blockSize:h,minHeight:min-h/minH,minBlockSize:min-h,maxHeight:max-h/maxH,maxBlockSize:max-b,color:text,fontFamily:font,fontSize:fs,fontWeight:fw,fontSmoothing:smoothing,fontVariantNumeric:numeric,letterSpacing:tracking,lineHeight:leading,textAlign:text-align,textDecoration:text-decor,textDecorationColor:text-decor-color,textEmphasisColor:text-emphasis-color,textDecorationStyle:decoration-style,textDecorationThickness:decoration-thickness,textUnderlineOffset:underline-offset,textTransform:text-transform,textIndent:indent,textShadow:text-shadow,textShadowColor:text-shadow/textShadowColor,textOverflow:text-overflow,verticalAlign:v-align,wordBreak:break,textWrap:text-wrap,truncate:truncate,lineClamp:clamp,listStyleType:list-type,listStylePosition:list-pos,listStyleImage:list-img,backgroundPosition:bg-pos/bgPosition,backgroundPositionX:bg-pos-x/bgPositionX,backgroundPositionY:bg-pos-y/bgPositionY,backgroundAttachment:bg-attach/bgAttachment,backgroundClip:bg-clip/bgClip,background:bg/1,backgroundColor:bg/bgColor,backgroundOrigin:bg-origin/bgOrigin,backgroundImage:bg-img/bgImage,backgroundRepeat:bg-repeat/bgRepeat,backgroundBlendMode:bg-blend/bgBlendMode,backgroundSize:bg-size/bgSize,backgroundGradient:bg-gradient/bgGradient,textGradient:text-gradient,gradientFromPosition:gradient-from-pos,gradientToPosition:gradient-to-pos,gradientFrom:gradient-from,gradientTo:gradient-to,gradientVia:gradient-via,gradientViaPosition:gradient-via-pos,borderRadius:rounded/1,borderTopLeftRadius:rounded-tl/roundedTopLeft,borderTopRightRadius:rounded-tr/roundedTopRight,borderBottomRightRadius:rounded-br/roundedBottomRight,borderBottomLeftRadius:rounded-bl/roundedBottomLeft,borderTopRadius:rounded-t/roundedTop,borderRightRadius:rounded-r/roundedRight,borderBottomRadius:rounded-b/roundedBottom,borderLeftRadius:rounded-l/roundedLeft,borderStartStartRadius:rounded-ss/roundedStartStart,borderStartEndRadius:rounded-se/roundedStartEnd,borderStartRadius:rounded-s/roundedStart,borderEndStartRadius:rounded-es/roundedEndStart,borderEndEndRadius:rounded-ee/roundedEndEnd,borderEndRadius:rounded-e/roundedEnd,border:border,borderWidth:border-w,borderTopWidth:border-tw,borderLeftWidth:border-lw,borderRightWidth:border-rw,borderBottomWidth:border-bw,borderColor:border,borderInline:border-x/borderX,borderInlineWidth:border-x/borderXWidth,borderInlineColor:border-x/borderXColor,borderBlock:border-y/borderY,borderBlockWidth:border-y/borderYWidth,borderBlockColor:border-y/borderYColor,borderLeft:border-l,borderLeftColor:border-l,borderInlineStart:border-s/borderStart,borderInlineStartWidth:border-s/borderStartWidth,borderInlineStartColor:border-s/borderStartColor,borderRight:border-r,borderRightColor:border-r,borderInlineEnd:border-e/borderEnd,borderInlineEndWidth:border-e/borderEndWidth,borderInlineEndColor:border-e/borderEndColor,borderTop:border-t,borderTopColor:border-t,borderBottom:border-b,borderBottomColor:border-b,borderBlockEnd:border-be,borderBlockEndColor:border-be,borderBlockStart:border-bs,borderBlockStartColor:border-bs,boxShadow:shadow/1,boxShadowColor:shadow-color/shadowColor,mixBlendMode:mix-blend,filter:filter,brightness:brightness,contrast:contrast,grayscale:grayscale,hueRotate:hue-rotate,invert:invert,saturate:saturate,sepia:sepia,dropShadow:drop-shadow,blur:blur,backdropFilter:backdrop,backdropBlur:backdrop-blur,backdropBrightness:backdrop-brightness,backdropContrast:backdrop-contrast,backdropGrayscale:backdrop-grayscale,backdropHueRotate:backdrop-hue-rotate,backdropInvert:backdrop-invert,backdropOpacity:backdrop-opacity,backdropSaturate:backdrop-saturate,backdropSepia:backdrop-sepia,borderCollapse:border,borderSpacing:border-spacing,borderSpacingX:border-spacing-x,borderSpacingY:border-spacing-y,tableLayout:table,transitionTimingFunction:ease,transitionDelay:delay,transitionDuration:duration,transitionProperty:transition-prop,transition:transition,animation:animation,animationName:animation-name,animationTimingFunction:animation-ease,animationDuration:animation-duration,animationDelay:animation-delay,transformOrigin:origin,rotate:rotate,rotateX:rotate-x,rotateY:rotate-y,rotateZ:rotate-z,scale:scale,scaleX:scale-x,scaleY:scale-y,translate:translate,translateX:translate-x/x,translateY:translate-y/y,translateZ:translate-z/z,accentColor:accent,caretColor:caret,scrollBehavior:scroll,scrollbar:scrollbar,scrollMargin:scroll-m,scrollMarginLeft:scroll-ml,scrollMarginRight:scroll-mr,scrollMarginTop:scroll-mt,scrollMarginBottom:scroll-mb,scrollMarginBlock:scroll-my/scrollMarginY,scrollMarginBlockEnd:scroll-mb,scrollMarginBlockStart:scroll-mt,scrollMarginInline:scroll-mx/scrollMarginX,scrollMarginInlineEnd:scroll-me,scrollMarginInlineStart:scroll-ms,scrollPadding:scroll-p,scrollPaddingBlock:scroll-pb/scrollPaddingY,scrollPaddingBlockStart:scroll-pt,scrollPaddingBlockEnd:scroll-pb,scrollPaddingInline:scroll-px/scrollPaddingX,scrollPaddingInlineEnd:scroll-pe,scrollPaddingInlineStart:scroll-ps,scrollPaddingLeft:scroll-pl,scrollPaddingRight:scroll-pr,scrollPaddingTop:scroll-pt,scrollPaddingBottom:scroll-pb,scrollSnapAlign:snap-align,scrollSnapStop:snap-stop,scrollSnapType:snap-type,scrollSnapStrictness:snap-strictness,scrollSnapMargin:snap-m,scrollSnapMarginTop:snap-mt,scrollSnapMarginBottom:snap-mb,scrollSnapMarginLeft:snap-ml,scrollSnapMarginRight:snap-mr,touchAction:touch,userSelect:select,fill:fill,stroke:stroke,strokeWidth:stroke-w,srOnly:sr,debug:debug,appearance:appearance,backfaceVisibility:backface,clipPath:clip-path,hyphens:hyphens,mask:mask,maskImage:mask-image,maskSize:mask-size,textSizeAdjust:text-adjust,container:cq,containerName:cq-name,containerType:cq-type,textStyle:textStyle",jo=new Map,Oo=new Map;Si.split(",").forEach(i=>{const[r,g]=i.split(":"),[b,...s]=g.split("/");jo.set(r,b),s.length&&s.forEach(v=>{Oo.set(v==="1"?b:v,r)})});const Ro=i=>Oo.get(i)||i,Fo={conditions:{shift:mi,finalize:fi,breakpoints:{keys:["base","sm","md","lg","xl","2xl"]}},utility:{transform:(i,r)=>{const g=Ro(i);return{className:`${jo.get(g)||ui(g)}_${Po(r)}`}},hasShorthand:!0,toHash:(i,r)=>r(i.join(":")),resolveShorthand:Ro}},yi=ai(Fo),e=(...i)=>yi(Ho(...i));e.raw=(...i)=>Ho(...i);const{mergeCss:Ho}=ci(Fo);function Ye(){let i="",r=0,g;for(;r<arguments.length;)(g=arguments[r++])&&typeof g=="string"&&(i&&(i+=" "),i+=g);return i}var Ci=R("<div><aside><div><div>量</div></div><nav></nav><div><button></button></div></aside><main><header><div><h1></h1></div><div><button>帮助</button><button>设置</button><div>用</div></div></header><div>"),wi=R("<div><div>量化平台</div><div>专业版 v2.0"),Io=R("<span>");function ki(i){const[r,g]=xe(!1),b=qo(),s=[{id:"dashboard",label:"仪表板",icon:"📊",path:"/dashboard"},{id:"market",label:"行情分析",icon:"📈",path:"/market"},{id:"strategy-editor",label:"策略编辑器",icon:"🧠",path:"/strategy-editor"},{id:"api-test",label:"API测试",icon:"🔧",path:"/api-test"}],v=u=>b.pathname===u||u==="/dashboard"&&b.pathname==="/",m=()=>s.find(w=>v(w.path))?.label||"仪表板";return(()=>{var u=Ci(),w=u.firstChild,W=w.firstChild,L=W.firstChild,x=W.nextSibling,S=x.nextSibling,A=S.firstChild,y=w.nextSibling,I=y.firstChild,V=I.firstChild,E=V.firstChild,j=V.nextSibling,h=j.firstChild,z=h.nextSibling,f=z.nextSibling,k=I.nextSibling;return l(W,(()=>{var c=ue(()=>!r());return()=>c()&&(()=>{var _=wi(),$=_.firstChild,U=$.nextSibling;return B(p=>{var a=e({fontSize:"14px",fontWeight:"600",color:"#262626",lineHeight:1.2}),C=e({fontSize:"11px",color:"#8c8c8c",lineHeight:1});return a!==p.e&&t($,p.e=a),C!==p.t&&t(U,p.t=C),p},{e:void 0,t:void 0}),_})()})(),null),l(x,()=>s.map(c=>D(No,{get href(){return c.path},get class(){return e({width:"100%",padding:r()?"12px 20px":"12px 16px",border:"none",backgroundColor:v(c.path)?"#e6f7ff":"transparent",color:v(c.path)?"#1890ff":"#595959",fontSize:"14px",textDecoration:"none",cursor:"pointer",transition:"all 0.2s",display:"flex",alignItems:"center",gap:"12px",borderLeft:v(c.path)?"3px solid #1890ff":"3px solid transparent",_hover:{backgroundColor:"#f5f5f5",color:"#1890ff"}})},get children(){return[(()=>{var _=Io();return l(_,()=>c.icon),B(()=>t(_,e({fontSize:"16px"}))),_})(),ue(()=>ue(()=>!r())()&&(()=>{var _=Io();return l(_,()=>c.label),B(()=>t(_,e({fontWeight:v(c.path)?"500":"400"}))),_})())]}}))),A.$$click=()=>g(!r()),l(A,()=>r()?"→":"←"),l(E,m),l(k,()=>i.children),B(c=>{var _=e({display:"flex",minHeight:"100vh",backgroundColor:"#f5f5f5"}),$=e({width:r()?"64px":"240px",backgroundColor:"white",borderRight:"1px solid #e8e8e8",transition:"width 0.3s ease",display:"flex",flexDirection:"column",position:"fixed",height:"100vh",zIndex:1e3}),U=e({padding:"12px 16px",borderBottom:"1px solid #e8e8e8",display:"flex",alignItems:"center",gap:"8px"}),p=e({width:"28px",height:"28px",backgroundColor:"#1890ff",borderRadius:"4px",display:"flex",alignItems:"center",justifyContent:"center",color:"white",fontSize:"14px",fontWeight:"bold"}),a=e({flex:1,padding:"8px 0",overflowY:"auto"}),C=e({padding:"16px",borderTop:"1px solid #e8e8e8"}),M=e({width:"100%",padding:"8px",border:"1px solid #d9d9d9",backgroundColor:"white",borderRadius:"4px",cursor:"pointer",fontSize:"12px",color:"#595959",_hover:{borderColor:"#1890ff",color:"#1890ff"}}),P=e({flex:1,marginLeft:r()?"64px":"240px",transition:"margin-left 0.3s ease",display:"flex",flexDirection:"column"}),F=e({backgroundColor:"white",borderBottom:"1px solid #e8e8e8",padding:"0 24px",height:"64px",display:"flex",alignItems:"center",justifyContent:"space-between"}),O=e({display:"flex",alignItems:"center",gap:"16px"}),K=e({fontSize:"18px",fontWeight:"500",color:"#262626",margin:0}),G=e({display:"flex",alignItems:"center",gap:"16px"}),N=e({padding:"6px 12px",border:"1px solid #d9d9d9",backgroundColor:"white",borderRadius:"4px",fontSize:"14px",cursor:"pointer",_hover:{borderColor:"#1890ff",color:"#1890ff"}}),n=e({padding:"6px 12px",border:"1px solid #d9d9d9",backgroundColor:"white",borderRadius:"4px",fontSize:"14px",cursor:"pointer",_hover:{borderColor:"#1890ff",color:"#1890ff"}}),Y=e({width:"32px",height:"32px",backgroundColor:"#1890ff",borderRadius:"50%",display:"flex",alignItems:"center",justifyContent:"center",color:"white",fontSize:"14px",fontWeight:"500"}),Q=e({flex:1,backgroundColor:"#f5f5f5",overflow:"auto"});return _!==c.e&&t(u,c.e=_),$!==c.t&&t(w,c.t=$),U!==c.a&&t(W,c.a=U),p!==c.o&&t(L,c.o=p),a!==c.i&&t(x,c.i=a),C!==c.n&&t(S,c.n=C),M!==c.s&&t(A,c.s=M),P!==c.h&&t(y,c.h=P),F!==c.r&&t(I,c.r=F),O!==c.d&&t(V,c.d=O),K!==c.l&&t(E,c.l=K),G!==c.u&&t(j,c.u=G),N!==c.c&&t(h,c.c=N),n!==c.w&&t(z,c.w=n),Y!==c.m&&t(f,c.m=Y),Q!==c.f&&t(k,c.f=Q),c},{e:void 0,t:void 0,a:void 0,o:void 0,i:void 0,n:void 0,s:void 0,h:void 0,r:void 0,d:void 0,l:void 0,u:void 0,c:void 0,w:void 0,m:void 0,f:void 0}),u})()}Rt(["click"]);var _i=R("<div><div><h1>投资仪表盘</h1><div><button>刷新</button><button>设置</button><button>新增策略</button></div></div><div></div><div><div><div><h3>资金曲线图</h3><div><button>日</button><button>周</button><button>月</button></div></div><div><div>📈</div><div>资金曲线图表</div><div>显示策略收益走势</div></div></div><div><div><h3>持仓概览</h3><span>查看全部 →</span></div><div></div></div></div><div><div><div><h3>今日行情</h3><span>更新时间: 15:30</span></div><div></div></div><div><div><h3>最新资讯</h3><span>查看更多 →</span></div><div></div></div></div><div><div>当前时间: </div><div><span>数据来源: 模拟数据</span><span>更新频率: 实时</span><div><div></div><span>系统正常"),zi=R("<div><div></div><div></div><div></div><div>"),$i=R("<div><div><span></span><span>%</span></div><div><span></span><span>"),Ri=R("<div><div><div></div><span></span></div><div><div></div><div> (<!>)"),Ii=R("<div><div></div><div><span></span><span>");function Wo(){const[i,r]=xe(new Date().toLocaleString("zh-CN"));let g;St(()=>{g=setInterval(()=>{r(new Date().toLocaleString("zh-CN"))},1e3)}),It(()=>{g&&clearInterval(g)});const b=[{title:"总资产",value:"¥1,000,000",change:"+2.34%",trend:"up",icon:"💰",description:"总资产",subValue:"¥1,000,000"},{title:"今日盈亏",value:"0",change:"+0.00%",trend:"neutral",icon:"📊",description:"今日盈亏",subValue:"0.00%"},{title:"持仓市值",value:"¥50,000",change:"+0.00%",trend:"neutral",icon:"📈",description:"持仓市值",subValue:"¥50,000"},{title:"可用资金",value:"2",change:"+0.00%",trend:"neutral",icon:"🔒",description:"持仓数量",subValue:"2"}],s=[{code:"000725",name:"京东方A",price:3.45,change:-1.43,changePercent:-29.3,volume:7340.75,amount:-6046,status:"持仓"},{code:"300519",name:"新光药业",price:68.94,change:-1.05,changePercent:-1.5,volume:410.75,amount:-1796,status:"持仓"},{code:"000831",name:"五矿稀土",price:26.09,change:-1.37,changePercent:-5,volume:7558.72,amount:-7688,status:"持仓"}],v=[{name:"上证指数",value:"3,245.67",change:"+23.45",percent:"+0.73%",trend:"up"},{name:"深证成指",value:"10,567.23",change:"+45.67",percent:"+0.43%",trend:"up"},{name:"创业板指",value:"2,234.56",change:"-8.90",percent:"-0.40%",trend:"down"},{name:"科创50",value:"1,123.45",change:"+15.23",percent:"+1.37%",trend:"up"}],m=[{title:"A股市场今日表现强劲，科技股领涨",time:"刚刚发布",type:"market"},{title:"央行宣布降准0.25个百分点",time:"30分钟前",type:"policy"},{title:"新能源板块持续活跃，多只个股涨停",time:"1小时前",type:"sector"}];return(()=>{var u=_i(),w=u.firstChild,W=w.firstChild,L=W.nextSibling,x=L.firstChild,S=x.nextSibling,A=S.nextSibling,y=w.nextSibling,I=y.nextSibling,V=I.firstChild,E=V.firstChild,j=E.firstChild,h=j.nextSibling,z=h.firstChild,f=z.nextSibling,k=f.nextSibling,c=E.nextSibling,_=c.firstChild,$=_.nextSibling,U=$.nextSibling,p=V.nextSibling,a=p.firstChild,C=a.firstChild,M=C.nextSibling,P=a.nextSibling,F=I.nextSibling,O=F.firstChild,K=O.firstChild,G=K.firstChild,N=G.nextSibling,n=K.nextSibling,Y=O.nextSibling,Q=Y.firstChild,oe=Q.firstChild,de=oe.nextSibling,ce=Q.nextSibling,be=F.nextSibling,ge=be.firstChild;ge.firstChild;var ye=ge.nextSibling,ke=ye.firstChild,We=ke.nextSibling,fe=We.nextSibling,Ce=fe.firstChild;return l(y,D(Ue,{each:b,children:d=>(()=>{var Z=zi(),ee=Z.firstChild,te=ee.nextSibling,J=te.nextSibling,ie=J.nextSibling;return l(ee,()=>d.icon),l(te,()=>d.value),l(J,()=>d.description),l(ie,()=>d.change),B(T=>{var re=e({backgroundColor:"white",borderRadius:"8px",padding:"16px",border:"1px solid #e8e8e8",display:"flex",flexDirection:"column",alignItems:"center",textAlign:"center",transition:"all 0.2s ease",_hover:{boxShadow:"0 2px 8px rgba(0,0,0,0.1)"}}),ae=e({fontSize:"24px",marginBottom:"8px"}),X=e({fontSize:"24px",fontWeight:"600",color:"#262626",marginBottom:"4px"}),H=e({fontSize:"12px",color:"#8c8c8c",marginBottom:"8px"}),ne=e({fontSize:"12px",color:d.trend==="up"?"#52c41a":d.trend==="down"?"#f5222d":"#8c8c8c",fontWeight:"500"});return re!==T.e&&t(Z,T.e=re),ae!==T.t&&t(ee,T.t=ae),X!==T.a&&t(te,T.a=X),H!==T.o&&t(J,T.o=H),ne!==T.i&&t(ie,T.i=ne),T},{e:void 0,t:void 0,a:void 0,o:void 0,i:void 0}),Z})()})),l(P,D(Ue,{each:s,children:d=>(()=>{var Z=$i(),ee=Z.firstChild,te=ee.firstChild,J=te.nextSibling,ie=J.firstChild,T=ee.nextSibling,re=T.firstChild,ae=re.nextSibling;return l(te,()=>d.code),l(J,()=>d.changePercent>0?"+":"",ie),l(J,()=>d.changePercent,ie),l(re,()=>d.name),l(ae,()=>d.status),B(X=>{var H=e({padding:"8px",backgroundColor:"#fafafa",borderRadius:"4px",fontSize:"12px"}),ne=e({display:"flex",justifyContent:"space-between",alignItems:"center",marginBottom:"4px"}),ve=e({fontWeight:"600",color:"#262626"}),he=e({color:d.changePercent>0?"#52c41a":"#f5222d",fontWeight:"500"}),pe=e({display:"flex",justifyContent:"space-between",color:"#8c8c8c"});return H!==X.e&&t(Z,X.e=H),ne!==X.t&&t(ee,X.t=ne),ve!==X.a&&t(te,X.a=ve),he!==X.o&&t(J,X.o=he),pe!==X.i&&t(T,X.i=pe),X},{e:void 0,t:void 0,a:void 0,o:void 0,i:void 0}),Z})()})),l(n,D(Ue,{each:v,children:d=>(()=>{var Z=Ri(),ee=Z.firstChild,te=ee.firstChild,J=te.nextSibling,ie=ee.nextSibling,T=ie.firstChild,re=T.nextSibling,ae=re.firstChild,X=ae.nextSibling;return X.nextSibling,l(J,()=>d.name),l(T,()=>d.value),l(re,()=>d.change,ae),l(re,()=>d.percent,X),B(H=>{var ne=e({display:"flex",alignItems:"center",justifyContent:"space-between",padding:"8px",backgroundColor:"#fafafa",borderRadius:"4px",fontSize:"12px"}),ve=e({display:"flex",alignItems:"center",gap:"8px"}),he=e({width:"6px",height:"6px",borderRadius:"50%",backgroundColor:d.trend==="up"?"#52c41a":"#f5222d"}),pe=e({fontWeight:"500",color:"#262626"}),me=e({textAlign:"right"}),_e=e({fontWeight:"600",color:"#262626",marginBottom:"2px"}),Se=e({color:d.trend==="up"?"#52c41a":"#f5222d",fontSize:"11px"});return ne!==H.e&&t(Z,H.e=ne),ve!==H.t&&t(ee,H.t=ve),he!==H.a&&t(te,H.a=he),pe!==H.o&&t(J,H.o=pe),me!==H.i&&t(ie,H.i=me),_e!==H.n&&t(T,H.n=_e),Se!==H.s&&t(re,H.s=Se),H},{e:void 0,t:void 0,a:void 0,o:void 0,i:void 0,n:void 0,s:void 0}),Z})()})),l(ce,D(Ue,{each:m,children:d=>(()=>{var Z=Ii(),ee=Z.firstChild,te=ee.nextSibling,J=te.firstChild,ie=J.nextSibling;return l(ee,()=>d.title),l(J,()=>d.time),l(ie,()=>d.type),B(T=>{var re=e({padding:"8px",backgroundColor:"#fafafa",borderRadius:"4px",cursor:"pointer",transition:"background-color 0.2s",_hover:{backgroundColor:"#f0f0f0"}}),ae=e({fontSize:"12px",fontWeight:"500",color:"#262626",marginBottom:"4px",lineHeight:"1.4"}),X=e({display:"flex",alignItems:"center",justifyContent:"space-between"}),H=e({fontSize:"11px",color:"#8c8c8c"}),ne=e({fontSize:"10px",color:"#1890ff",backgroundColor:"#e6f7ff",padding:"2px 6px",borderRadius:"2px"});return re!==T.e&&t(Z,T.e=re),ae!==T.t&&t(ee,T.t=ae),X!==T.a&&t(te,T.a=X),H!==T.o&&t(J,T.o=H),ne!==T.i&&t(ie,T.i=ne),T},{e:void 0,t:void 0,a:void 0,o:void 0,i:void 0}),Z})()})),l(ge,i,null),B(d=>{var Z=e({display:"flex",flexDirection:"column",gap:"16px",width:"100%",padding:"16px",backgroundColor:"#f0f2f5",minHeight:"100%"}),ee=e({display:"flex",alignItems:"center",justifyContent:"space-between",marginBottom:"8px"}),te=e({fontSize:"20px",fontWeight:"600",color:"#262626",margin:0}),J=e({display:"flex",alignItems:"center",gap:"8px"}),ie=e({padding:"6px 12px",backgroundColor:"#1890ff",color:"white",border:"none",borderRadius:"4px",fontSize:"12px",cursor:"pointer"}),T=e({padding:"6px 12px",backgroundColor:"white",color:"#262626",border:"1px solid #d9d9d9",borderRadius:"4px",fontSize:"12px",cursor:"pointer"}),re=e({padding:"6px 12px",backgroundColor:"#52c41a",color:"white",border:"none",borderRadius:"4px",fontSize:"12px",cursor:"pointer"}),ae=e({display:"grid",gridTemplateColumns:"repeat(4, 1fr)",gap:"16px",marginBottom:"16px"}),X=e({display:"grid",gridTemplateColumns:"2fr 1fr",gap:"16px",marginBottom:"16px"}),H=e({backgroundColor:"white",borderRadius:"8px",border:"1px solid #e8e8e8",padding:"16px"}),ne=e({display:"flex",alignItems:"center",justifyContent:"space-between",marginBottom:"16px"}),ve=e({fontSize:"16px",fontWeight:"600",color:"#262626",margin:0}),he=e({display:"flex",alignItems:"center",gap:"8px"}),pe=e({padding:"4px 8px",backgroundColor:"#1890ff",color:"white",border:"none",borderRadius:"4px",fontSize:"12px",cursor:"pointer"}),me=e({padding:"4px 8px",backgroundColor:"white",color:"#262626",border:"1px solid #d9d9d9",borderRadius:"4px",fontSize:"12px",cursor:"pointer"}),_e=e({padding:"4px 8px",backgroundColor:"white",color:"#262626",border:"1px solid #d9d9d9",borderRadius:"4px",fontSize:"12px",cursor:"pointer"}),Se=e({height:"200px",backgroundColor:"#fafafa",borderRadius:"4px",display:"flex",alignItems:"center",justifyContent:"center",flexDirection:"column",gap:"8px"}),Te=e({fontSize:"48px"}),Be=e({fontSize:"14px",color:"#8c8c8c"}),Pe=e({fontSize:"12px",color:"#8c8c8c"}),De=e({backgroundColor:"white",borderRadius:"8px",border:"1px solid #e8e8e8",padding:"16px"}),Le=e({display:"flex",alignItems:"center",justifyContent:"space-between",marginBottom:"16px"}),Ge=e({fontSize:"16px",fontWeight:"600",color:"#262626",margin:0}),Qe=e({fontSize:"12px",color:"#8c8c8c"}),Ze=e({display:"flex",flexDirection:"column",gap:"8px"}),Je=e({display:"grid",gridTemplateColumns:"1fr 1fr",gap:"16px"}),et=e({backgroundColor:"white",borderRadius:"8px",border:"1px solid #e8e8e8",padding:"16px"}),tt=e({display:"flex",alignItems:"center",justifyContent:"space-between",marginBottom:"16px"}),yt=e({fontSize:"16px",fontWeight:"600",color:"#262626",margin:0}),Ct=e({fontSize:"12px",color:"#8c8c8c"}),ot=e({display:"flex",flexDirection:"column",gap:"8px"}),qe=e({backgroundColor:"white",borderRadius:"8px",border:"1px solid #e8e8e8",padding:"16px"}),wt=e({display:"flex",alignItems:"center",justifyContent:"space-between",marginBottom:"16px"}),it=e({fontSize:"16px",fontWeight:"600",color:"#262626",margin:0}),rt=e({fontSize:"12px",color:"#1890ff",cursor:"pointer"}),nt=e({display:"flex",flexDirection:"column",gap:"8px"}),lt=e({display:"flex",alignItems:"center",justifyContent:"space-between",padding:"12px 16px",backgroundColor:"white",borderRadius:"8px",border:"1px solid #e8e8e8",fontSize:"12px",color:"#8c8c8c"}),kt=e({display:"flex",alignItems:"center",gap:"16px"}),o=e({display:"flex",alignItems:"center",gap:"4px"}),se=e({width:"6px",height:"6px",borderRadius:"50%",backgroundColor:"#52c41a"});return Z!==d.e&&t(u,d.e=Z),ee!==d.t&&t(w,d.t=ee),te!==d.a&&t(W,d.a=te),J!==d.o&&t(L,d.o=J),ie!==d.i&&t(x,d.i=ie),T!==d.n&&t(S,d.n=T),re!==d.s&&t(A,d.s=re),ae!==d.h&&t(y,d.h=ae),X!==d.r&&t(I,d.r=X),H!==d.d&&t(V,d.d=H),ne!==d.l&&t(E,d.l=ne),ve!==d.u&&t(j,d.u=ve),he!==d.c&&t(h,d.c=he),pe!==d.w&&t(z,d.w=pe),me!==d.m&&t(f,d.m=me),_e!==d.f&&t(k,d.f=_e),Se!==d.y&&t(c,d.y=Se),Te!==d.g&&t(_,d.g=Te),Be!==d.p&&t($,d.p=Be),Pe!==d.b&&t(U,d.b=Pe),De!==d.T&&t(p,d.T=De),Le!==d.A&&t(a,d.A=Le),Ge!==d.O&&t(C,d.O=Ge),Qe!==d.I&&t(M,d.I=Qe),Ze!==d.S&&t(P,d.S=Ze),Je!==d.W&&t(F,d.W=Je),et!==d.C&&t(O,d.C=et),tt!==d.B&&t(K,d.B=tt),yt!==d.v&&t(G,d.v=yt),Ct!==d.k&&t(N,d.k=Ct),ot!==d.x&&t(n,d.x=ot),qe!==d.j&&t(Y,d.j=qe),wt!==d.q&&t(Q,d.q=wt),it!==d.z&&t(oe,d.z=it),rt!==d.P&&t(de,d.P=rt),nt!==d.H&&t(ce,d.H=nt),lt!==d.F&&t(be,d.F=lt),kt!==d.M&&t(ye,d.M=kt),o!==d.D&&t(fe,d.D=o),se!==d.R&&t(Ce,d.R=se),d},{e:void 0,t:void 0,a:void 0,o:void 0,i:void 0,n:void 0,s:void 0,h:void 0,r:void 0,d:void 0,l:void 0,u:void 0,c:void 0,w:void 0,m:void 0,f:void 0,y:void 0,g:void 0,p:void 0,b:void 0,T:void 0,A:void 0,O:void 0,I:void 0,S:void 0,W:void 0,C:void 0,B:void 0,v:void 0,k:void 0,x:void 0,j:void 0,q:void 0,z:void 0,P:void 0,H:void 0,F:void 0,M:void 0,D:void 0,R:void 0}),u})()}var Wi=R("<div><div><h1>🔧 API 连接测试</h1><p>测试前端与后端API的连接状态</p></div><div><button>开始测试</button></div><div><div><h2>测试结果</h2></div><div></div></div><div><div><h2>配置信息</h2></div><div><div><div><h3>API Base URL</h3><p></p></div><div><h3>环境模式</h3><p></p></div><div><h3>代理配置</h3><p>/api → localhost:8000"),Ai=R('<p>点击"开始测试"按钮运行API连接测试'),Ti=R("<div>"),Bi=R("<div><div><div></div><div><h3></h3><p>"),Pi=R("<div>ms");const _t={get:async(i,r)=>{const g=r?"?"+new URLSearchParams(r).toString():"",s=await fetch("https://api.yourdomain.com"+i+g);if(!s.ok)throw new Error(`HTTP ${s.status}`);return s.json().catch(()=>({}))}},zt={SYSTEM:{HEALTH:"/v1/health"},MARKET:{OVERVIEW:"/v1/market/overview",SEARCH:"/v1/market/search"},AUTH:{PROFILE:"/v1/auth/profile"}};function Di(){const[i,r]=xe([]),g=(s,v,m,u)=>{r(w=>[...w,{name:s,status:v,message:m,duration:u}])},b=async()=>{r([]);try{const s=Date.now();await _t.get(zt.SYSTEM.HEALTH);const v=Date.now()-s;g("系统健康检查","success","连接成功",v)}catch(s){g("系统健康检查","error",s.message||"连接失败")}try{const s=Date.now();await _t.get(zt.MARKET.OVERVIEW);const v=Date.now()-s;g("市场概览","success","数据获取成功",v)}catch(s){g("市场概览","error",s.message||"数据获取失败")}try{const s=Date.now();await _t.get(zt.MARKET.SEARCH,{q:"AAPL"});const v=Date.now()-s;g("股票搜索","success","搜索成功",v)}catch(s){g("股票搜索","error",s.message||"搜索失败")}try{const s=Date.now();await _t.get(zt.AUTH.PROFILE);const v=Date.now()-s;g("用户信息","success","获取成功",v)}catch(s){g("用户信息","error",s.message||"获取失败（预期，因为未登录）")}};return St(()=>{console.log("ApiTest mounted")}),(()=>{var s=Wi(),v=s.firstChild,m=v.firstChild,u=m.nextSibling,w=v.nextSibling,W=w.firstChild,L=w.nextSibling,x=L.firstChild,S=x.firstChild,A=x.nextSibling,y=L.nextSibling,I=y.firstChild,V=I.firstChild,E=I.nextSibling,j=E.firstChild,h=j.firstChild,z=h.firstChild,f=z.nextSibling,k=h.nextSibling,c=k.firstChild,_=c.nextSibling,$=k.nextSibling,U=$.firstChild,p=U.nextSibling;return W.$$click=b,l(A,(()=>{var a=ue(()=>i().length===0);return()=>a()?(()=>{var C=Ai();return B(()=>t(C,e({color:"gray.500",textAlign:"center",padding:"40px 0"}))),C})():(()=>{var C=Ti();return l(C,()=>i().map(M=>(()=>{var P=Bi(),F=P.firstChild,O=F.firstChild,K=O.nextSibling,G=K.firstChild,N=G.nextSibling;return l(O,()=>M.status==="success"?"✅":"❌"),l(G,()=>M.name),l(N,()=>M.message),l(P,(()=>{var n=ue(()=>!!M.duration);return()=>n()&&(()=>{var Y=Pi(),Q=Y.firstChild;return l(Y,()=>M.duration,Q),B(()=>t(Y,e({fontSize:"12px",color:"gray.500",backgroundColor:"gray.100",padding:"4px 8px",borderRadius:"4px"}))),Y})()})(),null),B(n=>{var Y=e({display:"flex",alignItems:"center",justifyContent:"space-between",padding:"16px",borderRadius:"8px",border:"1px solid",borderColor:M.status==="success"?"green.200":"red.200",backgroundColor:M.status==="success"?"green.50":"red.50"}),Q=e({display:"flex",alignItems:"center",gap:"12px"}),oe=e({fontSize:"20px"}),de=e({fontSize:"16px",fontWeight:"600",color:"gray.900",marginBottom:"4px"}),ce=e({fontSize:"14px",color:"gray.600"});return Y!==n.e&&t(P,n.e=Y),Q!==n.t&&t(F,n.t=Q),oe!==n.a&&t(O,n.a=oe),de!==n.o&&t(G,n.o=de),ce!==n.i&&t(N,n.i=ce),n},{e:void 0,t:void 0,a:void 0,o:void 0,i:void 0}),P})())),B(()=>t(C,e({display:"flex",flexDirection:"column",gap:"16px"}))),C})()})()),l(f,()=>"https://api.yourdomain.com"),l(_,()=>"production"),B(a=>{var C=e({padding:"24px",maxWidth:"1200px",margin:"0 auto"}),M=e({marginBottom:"32px"}),P=e({fontSize:"32px",fontWeight:"bold",color:"gray.900",marginBottom:"8px"}),F=e({fontSize:"16px",color:"gray.600"}),O=e({marginBottom:"32px"}),K=e({padding:"12px 24px",backgroundColor:"blue.600",color:"white",border:"none",borderRadius:"8px",fontSize:"16px",fontWeight:"500",cursor:"pointer",transition:"all 0.2s",_hover:{backgroundColor:"blue.700"}}),G=e({backgroundColor:"white",borderRadius:"12px",boxShadow:"0 1px 3px rgba(0, 0, 0, 0.1)",border:"1px solid #e5e7eb",overflow:"hidden"}),N=e({padding:"24px",borderBottom:"1px solid #e5e7eb"}),n=e({fontSize:"20px",fontWeight:"bold",color:"gray.900"}),Y=e({padding:"24px"}),Q=e({marginTop:"32px",backgroundColor:"white",borderRadius:"12px",boxShadow:"0 1px 3px rgba(0, 0, 0, 0.1)",border:"1px solid #e5e7eb",overflow:"hidden"}),oe=e({padding:"24px",borderBottom:"1px solid #e5e7eb"}),de=e({fontSize:"20px",fontWeight:"bold",color:"gray.900"}),ce=e({padding:"24px"}),be=e({display:"grid",gridTemplateColumns:"repeat(auto-fit, minmax(300px, 1fr))",gap:"16px"}),ge=e({fontSize:"14px",fontWeight:"600",color:"gray.700",marginBottom:"8px"}),ye=e({fontSize:"14px",color:"gray.600",fontFamily:"monospace",backgroundColor:"gray.100",padding:"8px",borderRadius:"4px"}),ke=e({fontSize:"14px",fontWeight:"600",color:"gray.700",marginBottom:"8px"}),We=e({fontSize:"14px",color:"gray.600",fontFamily:"monospace",backgroundColor:"gray.100",padding:"8px",borderRadius:"4px"}),fe=e({fontSize:"14px",fontWeight:"600",color:"gray.700",marginBottom:"8px"}),Ce=e({fontSize:"14px",color:"gray.600",fontFamily:"monospace",backgroundColor:"gray.100",padding:"8px",borderRadius:"4px"});return C!==a.e&&t(s,a.e=C),M!==a.t&&t(v,a.t=M),P!==a.a&&t(m,a.a=P),F!==a.o&&t(u,a.o=F),O!==a.i&&t(w,a.i=O),K!==a.n&&t(W,a.n=K),G!==a.s&&t(L,a.s=G),N!==a.h&&t(x,a.h=N),n!==a.r&&t(S,a.r=n),Y!==a.d&&t(A,a.d=Y),Q!==a.l&&t(y,a.l=Q),oe!==a.u&&t(I,a.u=oe),de!==a.c&&t(V,a.c=de),ce!==a.w&&t(E,a.w=ce),be!==a.m&&t(j,a.m=be),ge!==a.f&&t(z,a.f=ge),ye!==a.y&&t(f,a.y=ye),ke!==a.g&&t(c,a.g=ke),We!==a.p&&t(_,a.p=We),fe!==a.b&&t(U,a.b=fe),Ce!==a.T&&t(p,a.T=Ce),a},{e:void 0,t:void 0,a:void 0,o:void 0,i:void 0,n:void 0,s:void 0,h:void 0,r:void 0,d:void 0,l:void 0,u:void 0,c:void 0,w:void 0,m:void 0,f:void 0,y:void 0,g:void 0,p:void 0,b:void 0,T:void 0}),s})()}Rt(["click"]);class Li{constructor(){this.socket=null,this.reconnectAttempts=0,this.maxReconnectAttempts=5,this.reconnectDelay=1e3,this.connectionStatusSignal=xe("disconnected"),this.marketDataSignal=xe(new Map),this.connectionStatus=this.connectionStatusSignal[0],this.setConnectionStatus=this.connectionStatusSignal[1],this.marketData=this.marketDataSignal[0],this.setMarketData=this.marketDataSignal[1],this.connect()}connect(){try{this.setConnectionStatus("connecting"),console.log("尝试连接WebSocket服务器:","wss://api.yourdomain.com/ws"),this.socket=null}catch(r){console.error("WebSocket连接失败:",r),this.setConnectionStatus("error"),this.handleReconnect()}}handleReconnect(){this.reconnectAttempts<this.maxReconnectAttempts?(this.reconnectAttempts++,setTimeout(()=>{console.log(`尝试重连 (${this.reconnectAttempts}/${this.maxReconnectAttempts})`),this.connect()},this.reconnectDelay*this.reconnectAttempts)):(console.log("达到最大重连次数，停止重连"),this.setConnectionStatus("error"))}updateMarketData(r){this.setMarketData(g=>{const b=new Map(g);return b.set(r.symbol,r),b})}subscribeToMarketData(r){console.log("订阅市场数据:",r),this.socket&&this.socket.connected&&this.socket.emit("subscribe",{symbols:r})}unsubscribeFromMarketData(r){console.log("取消订阅市场数据:",r),this.socket&&this.socket.connected&&this.socket.emit("unsubscribe",{symbols:r})}reconnect(){console.log("手动重连WebSocket..."),this.disconnect(),this.reconnectAttempts=0,this.connect()}disconnect(){this.socket&&(this.socket.disconnect(),this.socket=null),this.setConnectionStatus("disconnected")}}const Ie=new Li;function Mi(){return{connectionStatus:Ie.connectionStatus,marketData:Ie.marketData,subscribeToMarketData:Ie.subscribeToMarketData.bind(Ie),unsubscribeFromMarketData:Ie.unsubscribeFromMarketData.bind(Ie),disconnect:Ie.disconnect.bind(Ie),reconnect:Ie.reconnect.bind(Ie)}}var Ei=R("<div><div><div><h1>行情分析</h1><div><span>沪深A股</span><span>数据更新: 15:30</span><div><div></div><span></span></div></div></div><div><button type=button>导出数据</button><button type=button>自选股</button></div></div><div><div><h2>市场概览</h2><div><button type=button>日</button><button type=button>周</button><button type=button>月</button></div></div><div><div><div>上证指数</div><div>3,247.89</div><div>-12.34 (-0.38%)</div></div><div><div>深证成指</div><div>10,567.23</div><div>+45.67 (+0.43%)</div></div><div><div>创业板指</div><div>2,234.56</div><div>-8.90 (-0.40%)</div></div><div><div>科创50</div><div>1,123.45</div><div>+15.23 (+1.37%)</div></div></div></div><div><div><h3>市场筛选</h3><span>共 <!> 只股票</span></div><div><div><input type=text placeholder=搜索股票代码或名称><button type=button>搜索</button></div><div><span>板块:</span><button type=button>全部</button><button type=button>沪A</button><button type=button>深A</button><button type=button>创业板</button></div></div></div><div><div><h2>股票列表</h2></div><div><table><thead><tr><th>代码</th><th>名称</th><th>现价</th><th>涨跌额</th><th>涨跌幅</th><th>成交量</th><th>最高价</th><th>最低价</th><th>操作</th></tr></thead><tbody></tbody></table></div><div><div>共 <!> 条数据</div><div><button type=button>上一页</button><span>1</span><button type=button>下一页"),ji=R("<button type=button>重新连接"),Oi=R("<tr><td></td><td></td><td></td><td></td><td>%</td><td></td><td></td><td></td><td><div><button type=button>卖</button><button type=button>买"),Fi=R("<div><div><h3> 详细信息</h3></div><div><div>📊</div><p>K线图表和技术指标</p><p>这里将显示选中股票的详细分析图表");function Hi(){const i=[{symbol:"000725",name:"京东方A",price:3.45,change:-1.43,changePercent:-29.3,volume:7340750,high:3.52,low:3.4,open:3.48,marketCap:12e10},{symbol:"300519",name:"新光药业",price:68.94,change:-1.05,changePercent:-1.5,volume:410750,high:70.1,low:68.5,open:69.9,marketCap:28e9},{symbol:"000831",name:"五矿稀土",price:26.09,change:-1.37,changePercent:-5,volume:7558720,high:27.5,low:25.8,open:27.2,marketCap:45e9},{symbol:"000001",name:"平安银行",price:12.45,change:.23,changePercent:1.88,volume:1568e4,high:12.58,low:12.2,open:12.3,marketCap:24e10},{symbol:"000002",name:"万科A",price:8.76,change:-.15,changePercent:-1.68,volume:895e4,high:8.95,low:8.65,open:8.85,marketCap:98e9}],[r,g]=xe(i),b=Mi(),[s,v]=xe("AAPL"),[m,u]=xe("");To(()=>{const x=b.marketData();x.size>0&&g(S=>S.map(A=>{const y=x.get(A.symbol);return y?{...A,price:y.price,change:y.change,changePercent:y.changePercent,volume:y.volume}:A}))}),St(()=>{console.log("Market page mounted");const x=i.map(y=>y.symbol);b.subscribeToMarketData(x);let S;setTimeout(()=>{S=setInterval(()=>{b.connectionStatus()!=="connected"&&g(y=>y.map(I=>({...I,price:Math.max(.01,I.price+(Math.random()-.5)*2),change:I.change+(Math.random()-.5)*.5,changePercent:I.changePercent+(Math.random()-.5)*.2,volume:Math.max(0,I.volume+Math.floor((Math.random()-.5)*1e5))})))},3e3)},2e3),It(()=>{S&&clearInterval(S),b.unsubscribeFromMarketData(x)})});const w=()=>{const x=m().toLowerCase();return r().filter(S=>S.symbol.toLowerCase().includes(x)||S.name.toLowerCase().includes(x))},W=(x,S=2)=>new Intl.NumberFormat("zh-CN",{minimumFractionDigits:S,maximumFractionDigits:S}).format(x),L=x=>x>=1e6?`${(x/1e6).toFixed(1)}M`:x>=1e3?`${(x/1e3).toFixed(1)}K`:x.toString();return(()=>{var x=Ei(),S=x.firstChild,A=S.firstChild,y=A.firstChild,I=y.nextSibling,V=I.firstChild,E=V.nextSibling,j=E.nextSibling,h=j.firstChild,z=h.nextSibling,f=A.nextSibling,k=f.firstChild,c=k.nextSibling,_=S.nextSibling,$=_.firstChild,U=$.firstChild,p=U.nextSibling,a=p.firstChild,C=a.nextSibling,M=C.nextSibling,P=$.nextSibling,F=P.firstChild,O=F.firstChild,K=O.nextSibling,G=K.nextSibling,N=F.nextSibling,n=N.firstChild,Y=n.nextSibling,Q=Y.nextSibling,oe=N.nextSibling,de=oe.firstChild,ce=de.nextSibling,be=ce.nextSibling,ge=oe.nextSibling,ye=ge.firstChild,ke=ye.nextSibling,We=ke.nextSibling,fe=_.nextSibling,Ce=fe.firstChild,d=Ce.firstChild,Z=d.nextSibling,ee=Z.firstChild,te=ee.nextSibling;te.nextSibling;var J=Ce.nextSibling,ie=J.firstChild,T=ie.firstChild,re=T.nextSibling,ae=ie.nextSibling,X=ae.firstChild,H=X.nextSibling,ne=H.nextSibling,ve=ne.nextSibling,he=ve.nextSibling,pe=fe.nextSibling,me=pe.firstChild,_e=me.firstChild,Se=me.nextSibling,Te=Se.firstChild,Be=Te.firstChild,Pe=Be.firstChild,De=Pe.firstChild,Le=De.nextSibling,Ge=Le.nextSibling,Qe=Ge.nextSibling,Ze=Qe.nextSibling,Je=Ze.nextSibling,et=Je.nextSibling,tt=et.nextSibling,yt=tt.nextSibling,Ct=Be.nextSibling,ot=Se.nextSibling,qe=ot.firstChild,wt=qe.firstChild,it=wt.nextSibling;it.nextSibling;var rt=qe.nextSibling,nt=rt.firstChild,lt=nt.nextSibling,kt=lt.nextSibling;return l(z,()=>b.connectionStatus()==="connected"?"实时连接":"模拟数据"),l(f,(()=>{var o=ue(()=>b.connectionStatus()!=="connected");return()=>o()&&(()=>{var se=ji();return se.$$click=()=>b.reconnect(),B(()=>t(se,e({padding:"6px 12px",backgroundColor:"#1890ff",color:"white",border:"none",borderRadius:"4px",fontSize:"12px",cursor:"pointer"}))),se})()})(),k),l(Z,()=>w().length,te),T.$$input=o=>u(o.currentTarget.value),l(Ct,()=>w().map(o=>(()=>{var se=Oi(),ze=se.firstChild,$e=ze.nextSibling,Me=$e.nextSibling,Re=Me.nextSibling,we=Re.nextSibling,Ne=we.firstChild,Ee=we.nextSibling,le=Ee.nextSibling,Ae=le.nextSibling,je=Ae.nextSibling,Oe=je.firstChild,Fe=Oe.firstChild,Xe=Fe.nextSibling;return se.$$click=()=>v(o.symbol),l(ze,()=>o.symbol),l($e,()=>o.name),l(Me,()=>W(o.price)),l(Re,()=>o.change>=0?"+":"",null),l(Re,()=>W(o.change),null),l(we,()=>o.changePercent>=0?"+":"",Ne),l(we,()=>W(o.changePercent),Ne),l(Ee,()=>L(o.volume)),l(le,()=>W(o.high)),l(Ae,()=>W(o.low)),B(q=>{var dt=e({borderBottom:"1px solid #f0f0f0",cursor:"pointer",transition:"background-color 0.2s",backgroundColor:s()===o.symbol?"#e6f7ff":"transparent",_hover:{backgroundColor:"#fafafa"}}),at=e({padding:"12px 16px",fontSize:"14px",fontWeight:"600",color:"#262626"}),st=e({padding:"12px 16px",fontSize:"14px",color:"#262626"}),ct=e({padding:"12px 16px",textAlign:"right",fontSize:"14px",fontWeight:"600",color:"#262626"}),gt=e({padding:"12px 16px",textAlign:"right",fontSize:"14px",fontWeight:"500",color:o.change>=0?"#52c41a":"#f5222d"}),xt=e({padding:"12px 16px",textAlign:"right",fontSize:"14px",fontWeight:"500",color:o.changePercent>=0?"#52c41a":"#f5222d"}),ut=e({padding:"12px 16px",textAlign:"right",fontSize:"14px",color:"#8c8c8c"}),vt=e({padding:"12px 16px",textAlign:"right",fontSize:"14px",color:"#8c8c8c"}),pt=e({padding:"12px 16px",textAlign:"right",fontSize:"14px",color:"#8c8c8c"}),bt=e({padding:"12px 16px",textAlign:"right"}),ht=e({display:"flex",gap:"4px",justifyContent:"flex-end"}),ft=e({padding:"4px 8px",backgroundColor:"#f5222d",color:"white",border:"none",borderRadius:"2px",fontSize:"12px",cursor:"pointer"}),mt=e({padding:"4px 8px",backgroundColor:"#52c41a",color:"white",border:"none",borderRadius:"2px",fontSize:"12px",cursor:"pointer"});return dt!==q.e&&t(se,q.e=dt),at!==q.t&&t(ze,q.t=at),st!==q.a&&t($e,q.a=st),ct!==q.o&&t(Me,q.o=ct),gt!==q.i&&t(Re,q.i=gt),xt!==q.n&&t(we,q.n=xt),ut!==q.s&&t(Ee,q.s=ut),vt!==q.h&&t(le,q.h=vt),pt!==q.r&&t(Ae,q.r=pt),bt!==q.d&&t(je,q.d=bt),ht!==q.l&&t(Oe,q.l=ht),ft!==q.u&&t(Fe,q.u=ft),mt!==q.c&&t(Xe,q.c=mt),q},{e:void 0,t:void 0,a:void 0,o:void 0,i:void 0,n:void 0,s:void 0,h:void 0,r:void 0,d:void 0,l:void 0,u:void 0,c:void 0}),se})())),l(qe,()=>w().length,it),l(x,(()=>{var o=ue(()=>!!s());return()=>o()&&(()=>{var se=Fi(),ze=se.firstChild,$e=ze.firstChild,Me=$e.firstChild,Re=ze.nextSibling,we=Re.firstChild,Ne=we.nextSibling,Ee=Ne.nextSibling;return l($e,s,Me),B(le=>{var Ae=e({marginTop:"24px",backgroundColor:"white",borderRadius:"8px",border:"1px solid #e8e8e8",overflow:"hidden"}),je=e({padding:"20px",borderBottom:"1px solid #e8e8e8"}),Oe=e({fontSize:"16px",fontWeight:"600",color:"#262626",margin:"0"}),Fe=e({padding:"20px",textAlign:"center",color:"#8c8c8c"}),Xe=e({fontSize:"48px",marginBottom:"16px"}),q=e({fontSize:"12px"});return Ae!==le.e&&t(se,le.e=Ae),je!==le.t&&t(ze,le.t=je),Oe!==le.a&&t($e,le.a=Oe),Fe!==le.o&&t(Re,le.o=Fe),Xe!==le.i&&t(we,le.i=Xe),q!==le.n&&t(Ee,le.n=q),le},{e:void 0,t:void 0,a:void 0,o:void 0,i:void 0,n:void 0}),se})()})(),null),B(o=>{var se=e({padding:"16px",backgroundColor:"#f0f2f5",minHeight:"100%"}),ze=e({display:"flex",alignItems:"center",justifyContent:"space-between",marginBottom:"16px"}),$e=e({fontSize:"20px",fontWeight:"600",color:"#262626",margin:0,marginBottom:"4px"}),Me=e({display:"flex",alignItems:"center",gap:"12px",fontSize:"12px",color:"#8c8c8c"}),Re=e({padding:"2px 6px",backgroundColor:"#f6ffed",color:"#52c41a",borderRadius:"2px",fontSize:"11px"}),we=e({display:"flex",alignItems:"center",gap:"4px"}),Ne=e({width:"6px",height:"6px",borderRadius:"50%",backgroundColor:b.connectionStatus()==="connected"?"#52c41a":"#faad14"}),Ee=e({display:"flex",alignItems:"center",gap:"8px"}),le=e({padding:"6px 12px",backgroundColor:"white",color:"#262626",border:"1px solid #d9d9d9",borderRadius:"4px",fontSize:"12px",cursor:"pointer"}),Ae=e({padding:"6px 12px",backgroundColor:"#52c41a",color:"white",border:"none",borderRadius:"4px",fontSize:"12px",cursor:"pointer"}),je=e({marginBottom:"16px",backgroundColor:"white",borderRadius:"8px",border:"1px solid #e8e8e8",padding:"16px"}),Oe=e({display:"flex",alignItems:"center",justifyContent:"space-between",marginBottom:"12px"}),Fe=e({fontSize:"16px",fontWeight:"600",color:"#262626",margin:0}),Xe=e({display:"flex",alignItems:"center",gap:"8px"}),q=e({padding:"4px 8px",backgroundColor:"#1890ff",color:"white",border:"none",borderRadius:"4px",fontSize:"12px",cursor:"pointer"}),dt=e({padding:"4px 8px",backgroundColor:"white",color:"#262626",border:"1px solid #d9d9d9",borderRadius:"4px",fontSize:"12px",cursor:"pointer"}),at=e({padding:"4px 8px",backgroundColor:"white",color:"#262626",border:"1px solid #d9d9d9",borderRadius:"4px",fontSize:"12px",cursor:"pointer"}),st=e({display:"grid",gridTemplateColumns:"repeat(4, 1fr)",gap:"16px"}),ct=e({padding:"12px",backgroundColor:"#fafafa",borderRadius:"4px",textAlign:"center"}),gt=e({fontSize:"12px",color:"#8c8c8c",marginBottom:"4px"}),xt=e({fontSize:"16px",fontWeight:"600",color:"#f5222d",marginBottom:"2px"}),ut=e({fontSize:"11px",color:"#f5222d"}),vt=e({padding:"12px",backgroundColor:"#fafafa",borderRadius:"4px",textAlign:"center"}),pt=e({fontSize:"12px",color:"#8c8c8c",marginBottom:"4px"}),bt=e({fontSize:"16px",fontWeight:"600",color:"#52c41a",marginBottom:"2px"}),ht=e({fontSize:"11px",color:"#52c41a"}),ft=e({padding:"12px",backgroundColor:"#fafafa",borderRadius:"4px",textAlign:"center"}),mt=e({fontSize:"12px",color:"#8c8c8c",marginBottom:"4px"}),Mt=e({fontSize:"16px",fontWeight:"600",color:"#f5222d",marginBottom:"2px"}),Et=e({fontSize:"11px",color:"#f5222d"}),jt=e({padding:"12px",backgroundColor:"#fafafa",borderRadius:"4px",textAlign:"center"}),Ot=e({fontSize:"12px",color:"#8c8c8c",marginBottom:"4px"}),Ft=e({fontSize:"16px",fontWeight:"600",color:"#52c41a",marginBottom:"2px"}),Ht=e({fontSize:"11px",color:"#52c41a"}),qt=e({marginBottom:"16px",backgroundColor:"white",borderRadius:"8px",border:"1px solid #e8e8e8",padding:"12px 16px"}),Nt=e({display:"flex",alignItems:"center",justifyContent:"space-between",marginBottom:"12px"}),Xt=e({fontSize:"14px",fontWeight:"600",color:"#262626",margin:0}),Kt=e({fontSize:"12px",color:"#8c8c8c"}),Yt=e({display:"flex",alignItems:"center",justifyContent:"space-between"}),Vt=e({display:"flex",alignItems:"center",gap:"8px"}),Ut=e({padding:"6px 12px",border:"1px solid #d9d9d9",borderRadius:"4px",fontSize:"12px",width:"180px",_focus:{outline:"none",borderColor:"#1890ff"}}),Gt=e({padding:"6px 12px",backgroundColor:"#1890ff",color:"white",border:"none",borderRadius:"4px",fontSize:"12px",cursor:"pointer"}),Qt=e({display:"flex",alignItems:"center",gap:"6px"}),Zt=e({fontSize:"12px",color:"#8c8c8c",marginRight:"4px"}),Jt=e({padding:"4px 8px",backgroundColor:"#1890ff",color:"white",border:"none",borderRadius:"4px",fontSize:"11px",cursor:"pointer"}),eo=e({padding:"4px 8px",backgroundColor:"white",color:"#262626",border:"1px solid #d9d9d9",borderRadius:"4px",fontSize:"11px",cursor:"pointer"}),to=e({padding:"4px 8px",backgroundColor:"white",color:"#262626",border:"1px solid #d9d9d9",borderRadius:"4px",fontSize:"11px",cursor:"pointer"}),oo=e({padding:"4px 8px",backgroundColor:"white",color:"#262626",border:"1px solid #d9d9d9",borderRadius:"4px",fontSize:"11px",cursor:"pointer"}),io=e({backgroundColor:"white",borderRadius:"8px",border:"1px solid #e8e8e8",overflow:"hidden"}),ro=e({padding:"16px 20px",borderBottom:"1px solid #e8e8e8",backgroundColor:"#fafafa"}),no=e({fontSize:"16px",fontWeight:"600",color:"#262626",margin:"0"}),lo=e({overflowX:"auto"}),ao=e({width:"100%",borderCollapse:"collapse"}),so=e({backgroundColor:"#fafafa"}),co=e({padding:"12px 16px",textAlign:"left",fontSize:"12px",fontWeight:"600",color:"#8c8c8c"}),go=e({padding:"12px 16px",textAlign:"left",fontSize:"12px",fontWeight:"600",color:"#8c8c8c"}),xo=e({padding:"12px 16px",textAlign:"right",fontSize:"12px",fontWeight:"600",color:"#8c8c8c"}),uo=e({padding:"12px 16px",textAlign:"right",fontSize:"12px",fontWeight:"600",color:"#8c8c8c"}),vo=e({padding:"12px 16px",textAlign:"right",fontSize:"12px",fontWeight:"600",color:"#8c8c8c"}),po=e({padding:"12px 16px",textAlign:"right",fontSize:"12px",fontWeight:"600",color:"#8c8c8c"}),bo=e({padding:"12px 16px",textAlign:"right",fontSize:"12px",fontWeight:"600",color:"#8c8c8c"}),ho=e({padding:"12px 16px",textAlign:"right",fontSize:"12px",fontWeight:"600",color:"#8c8c8c"}),fo=e({padding:"12px 16px",textAlign:"right",fontSize:"12px",fontWeight:"600",color:"#8c8c8c"}),mo=e({padding:"16px 20px",borderTop:"1px solid #f0f0f0",display:"flex",justifyContent:"space-between",alignItems:"center"}),So=e({fontSize:"14px",color:"#8c8c8c"}),yo=e({display:"flex",gap:"8px",alignItems:"center"}),Co=e({padding:"4px 8px",backgroundColor:"white",color:"#262626",border:"1px solid #d9d9d9",borderRadius:"4px",fontSize:"12px",cursor:"pointer"}),wo=e({padding:"4px 8px",backgroundColor:"#1890ff",color:"white",borderRadius:"4px",fontSize:"12px"}),ko=e({padding:"4px 8px",backgroundColor:"white",color:"#262626",border:"1px solid #d9d9d9",borderRadius:"4px",fontSize:"12px",cursor:"pointer"});return se!==o.e&&t(x,o.e=se),ze!==o.t&&t(S,o.t=ze),$e!==o.a&&t(y,o.a=$e),Me!==o.o&&t(I,o.o=Me),Re!==o.i&&t(V,o.i=Re),we!==o.n&&t(j,o.n=we),Ne!==o.s&&t(h,o.s=Ne),Ee!==o.h&&t(f,o.h=Ee),le!==o.r&&t(k,o.r=le),Ae!==o.d&&t(c,o.d=Ae),je!==o.l&&t(_,o.l=je),Oe!==o.u&&t($,o.u=Oe),Fe!==o.c&&t(U,o.c=Fe),Xe!==o.w&&t(p,o.w=Xe),q!==o.m&&t(a,o.m=q),dt!==o.f&&t(C,o.f=dt),at!==o.y&&t(M,o.y=at),st!==o.g&&t(P,o.g=st),ct!==o.p&&t(F,o.p=ct),gt!==o.b&&t(O,o.b=gt),xt!==o.T&&t(K,o.T=xt),ut!==o.A&&t(G,o.A=ut),vt!==o.O&&t(N,o.O=vt),pt!==o.I&&t(n,o.I=pt),bt!==o.S&&t(Y,o.S=bt),ht!==o.W&&t(Q,o.W=ht),ft!==o.C&&t(oe,o.C=ft),mt!==o.B&&t(de,o.B=mt),Mt!==o.v&&t(ce,o.v=Mt),Et!==o.k&&t(be,o.k=Et),jt!==o.x&&t(ge,o.x=jt),Ot!==o.j&&t(ye,o.j=Ot),Ft!==o.q&&t(ke,o.q=Ft),Ht!==o.z&&t(We,o.z=Ht),qt!==o.P&&t(fe,o.P=qt),Nt!==o.H&&t(Ce,o.H=Nt),Xt!==o.F&&t(d,o.F=Xt),Kt!==o.M&&t(Z,o.M=Kt),Yt!==o.D&&t(J,o.D=Yt),Vt!==o.R&&t(ie,o.R=Vt),Ut!==o.E&&t(T,o.E=Ut),Gt!==o.L&&t(re,o.L=Gt),Qt!==o.N&&t(ae,o.N=Qt),Zt!==o.G&&t(X,o.G=Zt),Jt!==o.U&&t(H,o.U=Jt),eo!==o.K&&t(ne,o.K=eo),to!==o.V&&t(ve,o.V=to),oo!==o.Y&&t(he,o.Y=oo),io!==o.J&&t(pe,o.J=io),ro!==o.Q&&t(me,o.Q=ro),no!==o.Z&&t(_e,o.Z=no),lo!==o.X&&t(Se,o.X=lo),ao!==o._&&t(Te,o._=ao),so!==o.$&&t(Pe,o.$=so),co!==o.te&&t(De,o.te=co),go!==o.tt&&t(Le,o.tt=go),xo!==o.ta&&t(Ge,o.ta=xo),uo!==o.to&&t(Qe,o.to=uo),vo!==o.ti&&t(Ze,o.ti=vo),po!==o.tn&&t(Je,o.tn=po),bo!==o.ts&&t(et,o.ts=bo),ho!==o.th&&t(tt,o.th=ho),fo!==o.tr&&t(yt,o.tr=fo),mo!==o.td&&t(ot,o.td=mo),So!==o.tl&&t(qe,o.tl=So),yo!==o.tu&&t(rt,o.tu=yo),Co!==o.tc&&t(nt,o.tc=Co),wo!==o.tw&&t(lt,o.tw=wo),ko!==o.tm&&t(kt,o.tm=ko),o},{e:void 0,t:void 0,a:void 0,o:void 0,i:void 0,n:void 0,s:void 0,h:void 0,r:void 0,d:void 0,l:void 0,u:void 0,c:void 0,w:void 0,m:void 0,f:void 0,y:void 0,g:void 0,p:void 0,b:void 0,T:void 0,A:void 0,O:void 0,I:void 0,S:void 0,W:void 0,C:void 0,B:void 0,v:void 0,k:void 0,x:void 0,j:void 0,q:void 0,z:void 0,P:void 0,H:void 0,F:void 0,M:void 0,D:void 0,R:void 0,E:void 0,L:void 0,N:void 0,G:void 0,U:void 0,K:void 0,V:void 0,Y:void 0,J:void 0,Q:void 0,Z:void 0,X:void 0,_:void 0,$:void 0,te:void 0,tt:void 0,ta:void 0,to:void 0,ti:void 0,tn:void 0,ts:void 0,th:void 0,tr:void 0,td:void 0,tl:void 0,tu:void 0,tc:void 0,tw:void 0,tm:void 0}),B(()=>T.value=m()),x})()}Rt(["input","click"]);var qi=R("<div><div>");function Ni(i){const[r,g]=xe();let b;return St(async()=>{const s=r();if(s)try{const m="/libs/monaco-editor/min/vs";console.log(`Monaco Editor 配置: 本地模式, 路径: ${m}`),_o.config({paths:{vs:m}});const u=await _o.init();i.language==="python"&&u.languages.registerCompletionItemProvider("python",{provideCompletionItems:(w,W)=>{const L=w.getWordUntilPosition(W),x={startLineNumber:W.lineNumber,endLineNumber:W.lineNumber,startColumn:L.startColumn,endColumn:L.endColumn};return{suggestions:[{label:"def",kind:u.languages.CompletionItemKind.Keyword,insertText:"def ${1:function_name}(${2:parameters}):\n    ${3:pass}",insertTextRules:u.languages.CompletionItemInsertTextRule.InsertAsSnippet,documentation:"Define a function",range:x},{label:"initialize",kind:u.languages.CompletionItemKind.Function,insertText:"def initialize(context):\n    ${1:pass}",insertTextRules:u.languages.CompletionItemInsertTextRule.InsertAsSnippet,documentation:"策略初始化函数",range:x},{label:"handle_data",kind:u.languages.CompletionItemKind.Function,insertText:"def handle_data(context, data):\n    ${1:pass}",insertTextRules:u.languages.CompletionItemInsertTextRule.InsertAsSnippet,documentation:"主要的交易逻辑函数",range:x},{label:"order_target_percent",kind:u.languages.CompletionItemKind.Function,insertText:"order_target_percent(${1:security}, ${2:percent})",insertTextRules:u.languages.CompletionItemInsertTextRule.InsertAsSnippet,documentation:"下单到目标百分比",range:x},{label:"attribute_history",kind:u.languages.CompletionItemKind.Function,insertText:"attribute_history(${1:security}, ${2:count}, ${3:unit}, ${4:fields})",insertTextRules:u.languages.CompletionItemInsertTextRule.InsertAsSnippet,documentation:"获取历史数据",range:x},{label:"log.info",kind:u.languages.CompletionItemKind.Function,insertText:"log.info(${1:message})",insertTextRules:u.languages.CompletionItemInsertTextRule.InsertAsSnippet,documentation:"输出日志信息",range:x}]}}}),b=u.editor.create(s,{value:i.value||"",language:i.language||"python",theme:i.theme||"vs",fontSize:13,lineNumbers:"on",roundedSelection:!1,scrollBeyondLastLine:!1,minimap:{enabled:!0},automaticLayout:!0,tabSize:4,insertSpaces:!0,wordWrap:"on",folding:!0,renderLineHighlight:"all",selectOnLineNumbers:!0,matchBrackets:"always",...i.options}),b.onDidChangeModelContent(()=>{i.onChange&&b&&i.onChange(b.getValue())}),b.addCommand(u.KeyMod.CtrlCmd|u.KeyCode.KeyS,()=>{console.log("保存策略快捷键触发")}),b.addCommand(u.KeyMod.CtrlCmd|u.KeyCode.Enter,()=>{console.log("运行策略快捷键触发")})}catch(v){console.error("Monaco Editor 初始化失败:",v)}}),It(()=>{b&&b.dispose()}),(()=>{var s=qi(),v=s.firstChild;return Xo(g,v),B(m=>{var u=e({width:"100%",height:`${i.height||400}px`,border:"1px solid #e5e7eb",borderRadius:"6px",overflow:"hidden"}),w=e({width:"100%",height:"100%"});return u!==m.e&&t(s,m.e=u),w!==m.t&&t(v,m.t=w),m},{e:void 0,t:void 0}),s})()}var Xi=R('<div><div><h1>🧠 策略编辑器</h1><p>创建和编辑量化交易策略</p></div><div><div><div><h2>策略代码</h2><div><button>运行回测</button><button>保存策略</button></div></div><div></div></div><div><div><h2>回测结果</h2></div><div><div><div>📊</div><p>等待回测结果</p><p>点击"运行回测"开始策略测试</p></div></div></div></div><div><h3>策略模板</h3><div><button><div>双均线策略</div><div>基于移动平均线的经典策略</div></button><button><div>RSI策略</div><div>基于相对强弱指标的策略</div></button><button><div>布林带策略</div><div>利用布林带进行交易决策</div></button><button><div>机器学习策略</div><div>基于AI模型的量化策略');function Ki(){const[i,r]=xe(`# 量化策略示例
# 这是一个简单的移动平均线策略

def initialize(context):
    # 设置基准和股票
    g.benchmark = '000300.XSHG'
    g.stock = '000001.XSHE'
    
def handle_data(context, data):
    # 获取历史价格
    hist = attribute_history(g.stock, 20, '1d', ['close'])
    ma5 = hist['close'][-5:].mean()
    ma20 = hist['close'][-20:].mean()
    current_price = data[g.stock].close
    
    # 交易逻辑
    if ma5 > ma20 and current_price > ma5:
        # 金叉买入信号
        order_target_percent(g.stock, 0.8)
        log.info(f"买入信号，价格: {current_price}")
    elif ma5 < ma20:
        # 死叉卖出信号
        order_target_percent(g.stock, 0)
        log.info(f"卖出信号，价格: {current_price}")
`);return(()=>{var g=Xi(),b=g.firstChild,s=b.firstChild,v=s.nextSibling,m=b.nextSibling,u=m.firstChild,w=u.firstChild,W=w.firstChild,L=W.nextSibling,x=L.firstChild,S=x.nextSibling,A=w.nextSibling,y=u.nextSibling,I=y.firstChild,V=I.firstChild,E=I.nextSibling,j=E.firstChild,h=j.firstChild,z=h.nextSibling,f=z.nextSibling,k=m.nextSibling,c=k.firstChild,_=c.nextSibling,$=_.firstChild,U=$.firstChild,p=U.nextSibling,a=$.nextSibling,C=a.firstChild,M=C.nextSibling,P=a.nextSibling,F=P.firstChild,O=F.nextSibling,K=P.nextSibling,G=K.firstChild,N=G.nextSibling;return l(A,D(Ni,{get value(){return i()},language:"python",theme:"vs",height:500,onChange:r,options:{minimap:{enabled:!0},fontSize:14,wordWrap:"on",automaticLayout:!0}})),B(n=>{var Y=e({padding:"24px",maxWidth:"1400px",margin:"0 auto",height:"100%"}),Q=e({marginBottom:"32px"}),oe=e({fontSize:"32px",fontWeight:"bold",color:"gray.900",marginBottom:"8px"}),de=e({fontSize:"16px",color:"gray.600"}),ce=e({display:"grid",gridTemplateColumns:"1fr 1fr",gap:"24px",height:"calc(100vh - 200px)"}),be=e({backgroundColor:"white",borderRadius:"12px",boxShadow:"0 1px 3px rgba(0, 0, 0, 0.1)",border:"1px solid #e5e7eb",overflow:"hidden",display:"flex",flexDirection:"column"}),ge=e({padding:"16px 20px",borderBottom:"1px solid #e5e7eb",backgroundColor:"#f9fafb",display:"flex",justifyContent:"space-between",alignItems:"center"}),ye=e({fontSize:"16px",fontWeight:"600",color:"gray.900"}),ke=e({display:"flex",gap:"8px"}),We=e({padding:"6px 12px",backgroundColor:"blue.600",color:"white",border:"none",borderRadius:"6px",fontSize:"12px",fontWeight:"500",cursor:"pointer",_hover:{backgroundColor:"blue.700"}}),fe=e({padding:"6px 12px",backgroundColor:"green.600",color:"white",border:"none",borderRadius:"6px",fontSize:"12px",fontWeight:"500",cursor:"pointer",_hover:{backgroundColor:"green.700"}}),Ce=e({flex:1,padding:"8px"}),d=e({backgroundColor:"white",borderRadius:"12px",boxShadow:"0 1px 3px rgba(0, 0, 0, 0.1)",border:"1px solid #e5e7eb",overflow:"hidden",display:"flex",flexDirection:"column"}),Z=e({padding:"16px 20px",borderBottom:"1px solid #e5e7eb",backgroundColor:"#f9fafb"}),ee=e({fontSize:"16px",fontWeight:"600",color:"gray.900"}),te=e({flex:1,padding:"20px",display:"flex",alignItems:"center",justifyContent:"center"}),J=e({textAlign:"center",color:"gray.500"}),ie=e({fontSize:"48px",marginBottom:"16px"}),T=e({fontSize:"16px",marginBottom:"8px"}),re=e({fontSize:"14px"}),ae=e({marginTop:"24px",backgroundColor:"white",borderRadius:"12px",boxShadow:"0 1px 3px rgba(0, 0, 0, 0.1)",border:"1px solid #e5e7eb",padding:"20px"}),X=e({fontSize:"18px",fontWeight:"600",color:"gray.900",marginBottom:"16px"}),H=e({display:"grid",gridTemplateColumns:"repeat(auto-fit, minmax(200px, 1fr))",gap:"12px"}),ne=e({padding:"12px 16px",border:"1px solid #e5e7eb",borderRadius:"8px",backgroundColor:"white",textAlign:"left",cursor:"pointer",transition:"all 0.2s",_hover:{borderColor:"blue.500",backgroundColor:"blue.50"}}),ve=e({fontSize:"14px",fontWeight:"500",color:"gray.900",marginBottom:"4px"}),he=e({fontSize:"12px",color:"gray.600"}),pe=e({padding:"12px 16px",border:"1px solid #e5e7eb",borderRadius:"8px",backgroundColor:"white",textAlign:"left",cursor:"pointer",transition:"all 0.2s",_hover:{borderColor:"blue.500",backgroundColor:"blue.50"}}),me=e({fontSize:"14px",fontWeight:"500",color:"gray.900",marginBottom:"4px"}),_e=e({fontSize:"12px",color:"gray.600"}),Se=e({padding:"12px 16px",border:"1px solid #e5e7eb",borderRadius:"8px",backgroundColor:"white",textAlign:"left",cursor:"pointer",transition:"all 0.2s",_hover:{borderColor:"blue.500",backgroundColor:"blue.50"}}),Te=e({fontSize:"14px",fontWeight:"500",color:"gray.900",marginBottom:"4px"}),Be=e({fontSize:"12px",color:"gray.600"}),Pe=e({padding:"12px 16px",border:"1px solid #e5e7eb",borderRadius:"8px",backgroundColor:"white",textAlign:"left",cursor:"pointer",transition:"all 0.2s",_hover:{borderColor:"blue.500",backgroundColor:"blue.50"}}),De=e({fontSize:"14px",fontWeight:"500",color:"gray.900",marginBottom:"4px"}),Le=e({fontSize:"12px",color:"gray.600"});return Y!==n.e&&t(g,n.e=Y),Q!==n.t&&t(b,n.t=Q),oe!==n.a&&t(s,n.a=oe),de!==n.o&&t(v,n.o=de),ce!==n.i&&t(m,n.i=ce),be!==n.n&&t(u,n.n=be),ge!==n.s&&t(w,n.s=ge),ye!==n.h&&t(W,n.h=ye),ke!==n.r&&t(L,n.r=ke),We!==n.d&&t(x,n.d=We),fe!==n.l&&t(S,n.l=fe),Ce!==n.u&&t(A,n.u=Ce),d!==n.c&&t(y,n.c=d),Z!==n.w&&t(I,n.w=Z),ee!==n.m&&t(V,n.m=ee),te!==n.f&&t(E,n.f=te),J!==n.y&&t(j,n.y=J),ie!==n.g&&t(h,n.g=ie),T!==n.p&&t(z,n.p=T),re!==n.b&&t(f,n.b=re),ae!==n.T&&t(k,n.T=ae),X!==n.A&&t(c,n.A=X),H!==n.O&&t(_,n.O=H),ne!==n.I&&t($,n.I=ne),ve!==n.S&&t(U,n.S=ve),he!==n.W&&t(p,n.W=he),pe!==n.C&&t(a,n.C=pe),me!==n.B&&t(C,n.B=me),_e!==n.v&&t(M,n.v=_e),Se!==n.k&&t(P,n.k=Se),Te!==n.x&&t(F,n.x=Te),Be!==n.j&&t(O,n.j=Be),Pe!==n.q&&t(K,n.q=Pe),De!==n.z&&t(G,n.z=De),Le!==n.P&&t(N,n.P=Le),n},{e:void 0,t:void 0,a:void 0,o:void 0,i:void 0,n:void 0,s:void 0,h:void 0,r:void 0,d:void 0,l:void 0,u:void 0,c:void 0,w:void 0,m:void 0,f:void 0,y:void 0,g:void 0,p:void 0,b:void 0,T:void 0,A:void 0,O:void 0,I:void 0,S:void 0,W:void 0,C:void 0,B:void 0,v:void 0,k:void 0,x:void 0,j:void 0,q:void 0,z:void 0,P:void 0}),g})()}var Yi=R("<button>"),Vi=R("<div>");const Wt=i=>{const[r,g]=Bt(i,["variant","size","loading","icon","fullWidth","children","class","disabled"]),b=e({display:"inline-flex",alignItems:"center",justifyContent:"center",gap:"8px",borderRadius:"8px",fontWeight:"500",transition:"all 0.2s",cursor:"pointer",border:"none",outline:"none",textDecoration:"none",userSelect:"none",_focus:{boxShadow:"0 0 0 3px rgba(59, 130, 246, 0.1)"},_disabled:{opacity:.6,cursor:"not-allowed"}}),s={primary:e({backgroundColor:"blue.600",color:"white",_hover:{backgroundColor:"blue.700"},_active:{backgroundColor:"blue.800"}}),secondary:e({backgroundColor:"gray.100",color:"gray.900",_hover:{backgroundColor:"gray.200"},_active:{backgroundColor:"gray.300"}}),success:e({backgroundColor:"green.600",color:"white",_hover:{backgroundColor:"green.700"},_active:{backgroundColor:"green.800"}}),warning:e({backgroundColor:"yellow.500",color:"white",_hover:{backgroundColor:"yellow.600"},_active:{backgroundColor:"yellow.700"}}),danger:e({backgroundColor:"red.600",color:"white",_hover:{backgroundColor:"red.700"},_active:{backgroundColor:"red.800"}}),ghost:e({backgroundColor:"transparent",color:"gray.700",border:"1px solid",borderColor:"gray.300",_hover:{backgroundColor:"gray.50",borderColor:"gray.400"},_active:{backgroundColor:"gray.100"}})},v={sm:e({padding:"6px 12px",fontSize:"14px",minHeight:"32px"}),md:e({padding:"8px 16px",fontSize:"14px",minHeight:"40px"}),lg:e({padding:"12px 24px",fontSize:"16px",minHeight:"48px"})},m=e({width:"100%"}),u=e({width:"16px",height:"16px",border:"2px solid currentColor",borderTopColor:"transparent",borderRadius:"50%",animation:"spin 1s linear infinite"}),w=r.variant||"primary",W=r.size||"md";return(()=>{var L=Yi();return Pt(L,Dt({get class(){return Ye(b,s[w],v[W],r.fullWidth&&m,r.class)},get disabled(){return r.disabled||r.loading}},g),!1,!0),l(L,(()=>{var x=ue(()=>!!r.loading);return()=>x()&&(()=>{var S=Vi();return t(S,u),S})()})(),null),l(L,(()=>{var x=ue(()=>!!(!r.loading&&r.icon));return()=>x()&&r.icon})(),null),l(L,()=>r.children,null),L})()};var Ui=R("<div><div>"),Gi=R("<div><div><div>"),Qi=R("<h3>"),Zi=R("<p>");const Ve=i=>{const[r,g]=Bt(i,["title","subtitle","headerAction","padding","shadow","border","hover","children","class"]),b=e({backgroundColor:"white",borderRadius:"12px",overflow:"hidden",transition:"all 0.2s"}),s={none:"",sm:e({boxShadow:"0 1px 2px rgba(0, 0, 0, 0.05)"}),md:e({boxShadow:"0 4px 6px rgba(0, 0, 0, 0.07)"}),lg:e({boxShadow:"0 10px 15px rgba(0, 0, 0, 0.1)"})},v=e({border:"1px solid",borderColor:"gray.200"}),m=e({_hover:{transform:"translateY(-2px)",boxShadow:"0 8px 25px rgba(0, 0, 0, 0.1)"}}),u={none:"",sm:e({padding:"16px"}),md:e({padding:"24px"}),lg:e({padding:"32px"})},w=e({padding:"24px 24px 0 24px",marginBottom:"16px"}),W=e({fontSize:"18px",fontWeight:"600",color:"gray.900",marginBottom:"4px"}),L=e({fontSize:"14px",color:"gray.600"}),x=e({display:"flex",justifyContent:"space-between",alignItems:"flex-start"}),S=r.shadow||"md",A=r.padding||"md";return(()=>{var y=Ui(),I=y.firstChild;return Pt(y,Dt({get class(){return Ye(b,s[S],r.border&&v,r.hover&&m,r.class)}},g),!1,!0),l(y,(()=>{var V=ue(()=>!!(r.title||r.subtitle||r.headerAction));return()=>V()&&(()=>{var E=Gi(),j=E.firstChild,h=j.firstChild;return t(E,w),l(h,(()=>{var z=ue(()=>!!r.title);return()=>z()&&(()=>{var f=Qi();return t(f,W),l(f,()=>r.title),f})()})(),null),l(h,(()=>{var z=ue(()=>!!r.subtitle);return()=>z()&&(()=>{var f=Zi();return t(f,L),l(f,()=>r.subtitle),f})()})(),null),l(j,(()=>{var z=ue(()=>!!r.headerAction);return()=>z()&&r.headerAction})(),null),B(()=>t(j,r.headerAction?x:"")),E})()})(),I),l(I,()=>r.children),B(()=>t(I,u[A])),y})()};var Ji=R('<button aria-label="Close modal">×'),er=R("<div><h2><span>"),tr=R("<div>"),or=R("<div><div><div>");const ir=i=>{To(()=>{if(i.isOpen){const x=S=>{S.key==="Escape"&&i.closable!==!1&&i.onClose()};document.addEventListener("keydown",x),document.body.style.overflow="hidden",It(()=>{document.removeEventListener("keydown",x),document.body.style.overflow=""})}});const r=e({position:"fixed",top:0,left:0,right:0,bottom:0,backgroundColor:"rgba(0, 0, 0, 0.5)",display:"flex",alignItems:"center",justifyContent:"center",zIndex:1e3,padding:"16px"}),g=e({backgroundColor:"white",borderRadius:"12px",boxShadow:"0 20px 25px rgba(0, 0, 0, 0.1)",maxHeight:"90vh",overflow:"hidden",display:"flex",flexDirection:"column",animation:"modalEnter 0.2s ease-out"}),b={sm:e({width:"400px",maxWidth:"90vw"}),md:e({width:"600px",maxWidth:"90vw"}),lg:e({width:"800px",maxWidth:"90vw"}),xl:e({width:"1000px",maxWidth:"90vw"})},s=e({padding:"24px 24px 0 24px",borderBottom:"1px solid",borderColor:"gray.200",paddingBottom:"16px",marginBottom:"24px"}),v=e({fontSize:"20px",fontWeight:"600",color:"gray.900",margin:0,display:"flex",justifyContent:"space-between",alignItems:"center"}),m=e({background:"none",border:"none",fontSize:"24px",cursor:"pointer",color:"gray.400",padding:"4px",borderRadius:"4px",_hover:{color:"gray.600",backgroundColor:"gray.100"}}),u=e({padding:"0 24px",flex:1,overflow:"auto"}),w=e({padding:"16px 24px 24px 24px",borderTop:"1px solid",borderColor:"gray.200",marginTop:"24px",display:"flex",justifyContent:"flex-end",gap:"12px"}),W=i.size||"md",L=x=>{x.target===x.currentTarget&&i.maskClosable!==!1&&i.onClose()};return D(He,{get when(){return i.isOpen},get children(){return D(Ko,{get children(){var x=or(),S=x.firstChild,A=S.firstChild;return x.$$click=L,t(x,r),S.$$click=y=>y.stopPropagation(),l(S,D(He,{get when(){return i.title||i.closable!==!1},get children(){var y=er(),I=y.firstChild,V=I.firstChild;return t(y,s),t(I,v),l(V,()=>i.title),l(I,D(He,{get when(){return i.closable!==!1},get children(){var E=Ji();return Yo(E,"click",i.onClose,!0),t(E,m),E}}),null),y}}),A),t(A,u),l(A,()=>i.children),l(S,D(He,{get when(){return i.footer},get children(){var y=tr();return t(y,w),l(y,()=>i.footer),y}}),null),B(()=>t(S,Ye(g,b[W],i.class))),x}})}})};Rt(["click"]);var rr=R("<label>"),At=R("<div>"),nr=R("<div><div><input>");const Tt=i=>{const[r,g]=Bt(i,["label","error","helperText","leftIcon","rightIcon","size","fullWidth","class"]),b=e({display:"flex",flexDirection:"column",gap:"6px"}),s=e({width:"100%"}),v=e({fontSize:"14px",fontWeight:"500",color:"gray.700"}),m=e({position:"relative",display:"flex",alignItems:"center"}),u=e({width:"100%",border:"1px solid",borderColor:"gray.300",borderRadius:"8px",transition:"all 0.2s",backgroundColor:"white",color:"gray.900",_focus:{outline:"none",borderColor:"blue.500",boxShadow:"0 0 0 3px rgba(59, 130, 246, 0.1)"},_disabled:{backgroundColor:"gray.100",color:"gray.500",cursor:"not-allowed"},_placeholder:{color:"gray.400"}}),w=e({borderColor:"red.500",_focus:{borderColor:"red.500",boxShadow:"0 0 0 3px rgba(239, 68, 68, 0.1)"}}),W={sm:e({padding:"8px 12px",fontSize:"14px",minHeight:"36px"}),md:e({padding:"10px 14px",fontSize:"14px",minHeight:"40px"}),lg:e({padding:"12px 16px",fontSize:"16px",minHeight:"48px"})},L=e({position:"absolute",top:"50%",transform:"translateY(-50%)",color:"gray.400",pointerEvents:"none",zIndex:1}),x=e({left:"12px"}),S=e({right:"12px"}),A=e({paddingLeft:"40px"}),y=e({paddingRight:"40px"}),I=e({fontSize:"12px",color:"gray.600"}),V=e({fontSize:"12px",color:"red.600"}),E=r.size||"md";return(()=>{var j=nr(),h=j.firstChild,z=h.firstChild;return l(j,D(He,{get when(){return r.label},get children(){var f=rr();return t(f,v),l(f,()=>r.label),f}}),h),t(h,m),l(h,D(He,{get when(){return r.leftIcon},get children(){var f=At();return l(f,()=>r.leftIcon),B(()=>t(f,Ye(L,x))),f}}),z),Pt(z,Dt({get class(){return Ye(u,W[E],r.error?w:void 0,r.leftIcon?A:void 0,r.rightIcon?y:void 0,r.class)}},g),!1,!1),l(h,D(He,{get when(){return r.rightIcon},get children(){var f=At();return l(f,()=>r.rightIcon),B(()=>t(f,Ye(L,S))),f}}),null),l(j,D(He,{get when(){return r.error||r.helperText},get children(){var f=At();return l(f,()=>r.error||r.helperText),B(()=>t(f,r.error?V:I)),f}}),null),B(()=>t(j,Ye(b,r.fullWidth?s:void 0))),j})()};var lr=R("<div><h3>账户总值</h3><p>¥"),dr=R("<div><h3>总盈亏</h3><p>¥</p><p>%"),ar=R("<div><h3>可用资金</h3><p>¥"),sr=R("<div><h3>已用保证金</h3><p>¥"),cr=R("<div><table><thead><tr><th>代码</th><th>数量</th><th>盈亏</th></tr></thead><tbody>"),gr=R("<div><table><thead><tr><th>代码</th><th>类型</th><th>状态</th></tr></thead><tbody>"),xr=R("<div><div><div><label>交易类型</label><select aria-label=交易类型><option value=buy>买入</option><option value=sell>卖出</option></select></div><div><label>订单类型</label><select aria-label=订单类型><option value=market>市价单</option><option value=limit>限价单</option></select></div></div><div>"),ur=R("<div slot=footer>"),vr=R("<div><div><div><h1>💼 </h1><p>管理您的交易订单和持仓</p></div></div><div></div><div>"),pr=R("<span>➕"),br=R("<tr><td></td><td></td><td>¥<br><span>%"),hr=R("<tr><td><br><span>股 @ ¥</span></td><td><span></span></td><td><span>");const fr=()=>({t:i=>i});function mr(){const{t:i}=fr(),[r,g]=xe([{id:"1",symbol:"AAPL",type:"buy",quantity:100,price:150.25,status:"filled",timestamp:new Date("2024-01-15T10:30:00")},{id:"2",symbol:"TSLA",type:"sell",quantity:50,price:245.8,status:"pending",timestamp:new Date("2024-01-15T11:15:00")},{id:"3",symbol:"MSFT",type:"buy",quantity:75,price:310.45,status:"filled",timestamp:new Date("2024-01-15T09:45:00")}]),[b,s]=xe([{symbol:"AAPL",quantity:100,avgPrice:150.25,currentPrice:152.3,unrealizedPnL:205,unrealizedPnLPercent:1.36},{symbol:"MSFT",quantity:75,avgPrice:310.45,currentPrice:308.9,unrealizedPnL:-116.25,unrealizedPnLPercent:-.5}]),[v,m]=xe(!1),[u,w]=xe({symbol:"",type:"buy",quantity:"",price:"",orderType:"limit"}),[W]=xe({totalValue:45678.9,totalPnL:88.75,totalPnLPercent:.19,buyingPower:12345.67,marginUsed:5432.1});St(()=>{console.log("Trading page mounted");const S=setInterval(()=>{s(A=>A.map(y=>{const I=(Math.random()-.5)*2,V=y.currentPrice+I,E=(V-y.avgPrice)*y.quantity,j=E/(y.avgPrice*y.quantity)*100;return{...y,currentPrice:V,unrealizedPnL:E,unrealizedPnLPercent:j}}))},3e3);return()=>clearInterval(S)});const L=()=>{const S=u();if(!S.symbol||!S.quantity||!S.price&&S.orderType==="limit"){alert("请填写完整的订单信息");return}const A={id:Date.now().toString(),symbol:S.symbol.toUpperCase(),type:S.type,quantity:parseInt(S.quantity),price:S.orderType==="market"?0:parseFloat(S.price),status:"pending",timestamp:new Date};g(y=>[A,...y]),m(!1),w({symbol:"",type:"buy",quantity:"",price:"",orderType:"limit"})},x=W();return(()=>{var S=vr(),A=S.firstChild,y=A.firstChild,I=y.firstChild;I.firstChild;var V=I.nextSibling,E=A.nextSibling,j=E.nextSibling;return l(I,()=>i("nav.trading"),null),l(A,D(Wt,{variant:"primary",size:"lg",get icon(){return pr()},onClick:()=>m(!0),children:"新建订单"}),null),l(E,D(Ve,{padding:"md",shadow:"md",get children(){var h=lr(),z=h.firstChild,f=z.nextSibling;return f.firstChild,l(f,()=>x.totalValue.toLocaleString(),null),B(k=>{var c=e({textAlign:"center"}),_=e({fontSize:"14px",fontWeight:"500",color:"gray.600",marginBottom:"8px"}),$=e({fontSize:"24px",fontWeight:"bold",color:"gray.900"});return c!==k.e&&t(h,k.e=c),_!==k.t&&t(z,k.t=_),$!==k.a&&t(f,k.a=$),k},{e:void 0,t:void 0,a:void 0}),h}}),null),l(E,D(Ve,{padding:"md",shadow:"md",get children(){var h=dr(),z=h.firstChild,f=z.nextSibling,k=f.firstChild,c=f.nextSibling,_=c.firstChild;return l(f,()=>x.totalPnL>=0?"+":"",k),l(f,()=>Math.abs(x.totalPnL).toFixed(2),null),l(c,()=>x.totalPnL>=0?"+":"",_),l(c,()=>x.totalPnLPercent.toFixed(2),_),B($=>{var U=e({textAlign:"center"}),p=e({fontSize:"14px",fontWeight:"500",color:"gray.600",marginBottom:"8px"}),a=e({fontSize:"24px",fontWeight:"bold",color:x.totalPnL>=0?"green.600":"red.600"}),C=e({fontSize:"12px",color:x.totalPnL>=0?"green.600":"red.600"});return U!==$.e&&t(h,$.e=U),p!==$.t&&t(z,$.t=p),a!==$.a&&t(f,$.a=a),C!==$.o&&t(c,$.o=C),$},{e:void 0,t:void 0,a:void 0,o:void 0}),h}}),null),l(E,D(Ve,{padding:"md",shadow:"md",get children(){var h=ar(),z=h.firstChild,f=z.nextSibling;return f.firstChild,l(f,()=>x.buyingPower.toLocaleString(),null),B(k=>{var c=e({textAlign:"center"}),_=e({fontSize:"14px",fontWeight:"500",color:"gray.600",marginBottom:"8px"}),$=e({fontSize:"24px",fontWeight:"bold",color:"gray.900"});return c!==k.e&&t(h,k.e=c),_!==k.t&&t(z,k.t=_),$!==k.a&&t(f,k.a=$),k},{e:void 0,t:void 0,a:void 0}),h}}),null),l(E,D(Ve,{padding:"md",shadow:"md",get children(){var h=sr(),z=h.firstChild,f=z.nextSibling;return f.firstChild,l(f,()=>x.marginUsed.toLocaleString(),null),B(k=>{var c=e({textAlign:"center"}),_=e({fontSize:"14px",fontWeight:"500",color:"gray.600",marginBottom:"8px"}),$=e({fontSize:"24px",fontWeight:"bold",color:"orange.600"});return c!==k.e&&t(h,k.e=c),_!==k.t&&t(z,k.t=_),$!==k.a&&t(f,k.a=$),k},{e:void 0,t:void 0,a:void 0}),h}}),null),l(j,D(Ve,{title:"当前持仓",padding:"none",shadow:"md",get children(){var h=cr(),z=h.firstChild,f=z.firstChild,k=f.firstChild,c=k.firstChild,_=c.nextSibling,$=_.nextSibling,U=f.nextSibling;return l(U,D(Ue,{get each(){return b()},children:p=>(()=>{var a=br(),C=a.firstChild,M=C.nextSibling,P=M.nextSibling,F=P.firstChild,O=F.nextSibling,K=O.nextSibling,G=K.firstChild;return l(C,()=>p.symbol),l(M,()=>p.quantity),l(P,()=>p.unrealizedPnL>=0?"+":"",F),l(P,()=>Math.abs(p.unrealizedPnL).toFixed(2),O),l(K,()=>p.unrealizedPnL>=0?"+":"",G),l(K,()=>p.unrealizedPnLPercent.toFixed(2),G),B(N=>{var n=e({borderBottom:"1px solid",borderColor:"gray.200"}),Y=e({padding:"12px 16px",fontSize:"14px",fontWeight:"500",color:"gray.900"}),Q=e({padding:"12px 16px",textAlign:"right",fontSize:"14px",color:"gray.700"}),oe=e({padding:"12px 16px",textAlign:"right",fontSize:"14px",fontWeight:"500",color:p.unrealizedPnL>=0?"green.600":"red.600"}),de=e({fontSize:"12px"});return n!==N.e&&t(a,N.e=n),Y!==N.t&&t(C,N.t=Y),Q!==N.a&&t(M,N.a=Q),oe!==N.o&&t(P,N.o=oe),de!==N.i&&t(K,N.i=de),N},{e:void 0,t:void 0,a:void 0,o:void 0,i:void 0}),a})()})),B(p=>{var a=e({overflowX:"auto"}),C=e({width:"100%",borderCollapse:"collapse"}),M=e({backgroundColor:"gray.50"}),P=e({padding:"12px 16px",textAlign:"left",fontSize:"12px",fontWeight:"600",color:"gray.600"}),F=e({padding:"12px 16px",textAlign:"right",fontSize:"12px",fontWeight:"600",color:"gray.600"}),O=e({padding:"12px 16px",textAlign:"right",fontSize:"12px",fontWeight:"600",color:"gray.600"});return a!==p.e&&t(h,p.e=a),C!==p.t&&t(z,p.t=C),M!==p.a&&t(k,p.a=M),P!==p.o&&t(c,p.o=P),F!==p.i&&t(_,p.i=F),O!==p.n&&t($,p.n=O),p},{e:void 0,t:void 0,a:void 0,o:void 0,i:void 0,n:void 0}),h}}),null),l(j,D(Ve,{title:"订单历史",padding:"none",shadow:"md",get children(){var h=gr(),z=h.firstChild,f=z.firstChild,k=f.firstChild,c=k.firstChild,_=c.nextSibling,$=_.nextSibling,U=f.nextSibling;return l(U,D(Ue,{get each(){return r()},children:p=>(()=>{var a=hr(),C=a.firstChild,M=C.firstChild,P=M.nextSibling,F=P.firstChild,O=C.nextSibling,K=O.firstChild,G=O.nextSibling,N=G.firstChild;return l(C,()=>p.symbol,M),l(P,()=>p.quantity,F),l(P,()=>p.price,null),l(K,()=>p.type==="buy"?"买入":"卖出"),l(N,(()=>{var n=ue(()=>p.status==="filled");return()=>n()?"已成交":p.status==="pending"?"待成交":"已取消"})()),B(n=>{var Y=e({borderBottom:"1px solid",borderColor:"gray.200"}),Q=e({padding:"12px 16px",fontSize:"14px",fontWeight:"500",color:"gray.900"}),oe=e({fontSize:"12px",color:"gray.600"}),de=e({padding:"12px 16px",textAlign:"center",fontSize:"14px"}),ce=e({padding:"4px 8px",borderRadius:"4px",fontSize:"12px",fontWeight:"500",backgroundColor:p.type==="buy"?"green.100":"red.100",color:p.type==="buy"?"green.800":"red.800"}),be=e({padding:"12px 16px",textAlign:"right",fontSize:"14px"}),ge=e({padding:"4px 8px",borderRadius:"4px",fontSize:"12px",fontWeight:"500",backgroundColor:p.status==="filled"?"green.100":p.status==="pending"?"yellow.100":"red.100",color:p.status==="filled"?"green.800":p.status==="pending"?"yellow.800":"red.800"});return Y!==n.e&&t(a,n.e=Y),Q!==n.t&&t(C,n.t=Q),oe!==n.a&&t(P,n.a=oe),de!==n.o&&t(O,n.o=de),ce!==n.i&&t(K,n.i=ce),be!==n.n&&t(G,n.n=be),ge!==n.s&&t(N,n.s=ge),n},{e:void 0,t:void 0,a:void 0,o:void 0,i:void 0,n:void 0,s:void 0}),a})()})),B(p=>{var a=e({overflowX:"auto"}),C=e({width:"100%",borderCollapse:"collapse"}),M=e({backgroundColor:"gray.50"}),P=e({padding:"12px 16px",textAlign:"left",fontSize:"12px",fontWeight:"600",color:"gray.600"}),F=e({padding:"12px 16px",textAlign:"center",fontSize:"12px",fontWeight:"600",color:"gray.600"}),O=e({padding:"12px 16px",textAlign:"right",fontSize:"12px",fontWeight:"600",color:"gray.600"});return a!==p.e&&t(h,p.e=a),C!==p.t&&t(z,p.t=C),M!==p.a&&t(k,p.a=M),P!==p.o&&t(c,p.o=P),F!==p.i&&t(_,p.i=F),O!==p.n&&t($,p.n=O),p},{e:void 0,t:void 0,a:void 0,o:void 0,i:void 0,n:void 0}),h}}),null),l(S,D(ir,{get isOpen(){return v()},onClose:()=>m(!1),title:"新建订单",size:"md",get children(){return[(()=>{var h=xr(),z=h.firstChild,f=z.firstChild,k=f.firstChild,c=k.nextSibling,_=f.nextSibling,$=_.firstChild,U=$.nextSibling,p=z.nextSibling;return l(h,D(Tt,{label:"股票代码",placeholder:"例如: AAPL",get value(){return u().symbol},onInput:a=>w(C=>({...C,symbol:a.currentTarget.value}))}),z),c.addEventListener("change",a=>w(C=>({...C,type:a.currentTarget.value}))),U.addEventListener("change",a=>w(C=>({...C,orderType:a.currentTarget.value}))),l(p,D(Tt,{label:"数量",type:"number",placeholder:"100",get value(){return u().quantity},onInput:a=>w(C=>({...C,quantity:a.currentTarget.value}))}),null),l(p,(()=>{var a=ue(()=>u().orderType==="limit");return()=>a()&&D(Tt,{label:"价格",type:"number",step:"0.01",placeholder:"150.25",get value(){return u().price},onInput:C=>w(M=>({...M,price:C.currentTarget.value}))})})(),null),B(a=>{var C=e({display:"flex",flexDirection:"column",gap:"16px"}),M=e({display:"grid",gridTemplateColumns:"1fr 1fr",gap:"16px"}),P=e({display:"block",fontSize:"14px",fontWeight:"500",color:"gray.700",marginBottom:"8px"}),F=e({width:"100%",padding:"10px 14px",border:"1px solid",borderColor:"gray.300",borderRadius:"8px",backgroundColor:"white",color:"gray.900"}),O=e({display:"block",fontSize:"14px",fontWeight:"500",color:"gray.700",marginBottom:"8px"}),K=e({width:"100%",padding:"10px 14px",border:"1px solid",borderColor:"gray.300",borderRadius:"8px",backgroundColor:"white",color:"gray.900"}),G=e({display:"grid",gridTemplateColumns:"1fr 1fr",gap:"16px"});return C!==a.e&&t(h,a.e=C),M!==a.t&&t(z,a.t=M),P!==a.a&&t(k,a.a=P),F!==a.o&&t(c,a.o=F),O!==a.i&&t($,a.i=O),K!==a.n&&t(U,a.n=K),G!==a.s&&t(p,a.s=G),a},{e:void 0,t:void 0,a:void 0,o:void 0,i:void 0,n:void 0,s:void 0}),B(()=>c.value=u().type),B(()=>U.value=u().orderType),h})(),(()=>{var h=ur();return l(h,D(Wt,{variant:"ghost",onClick:()=>m(!1),children:"取消"}),null),l(h,D(Wt,{variant:"primary",onClick:L,children:"提交订单"}),null),h})()]}}),null),B(h=>{var z=e({padding:"24px",maxWidth:"1400px",margin:"0 auto",backgroundColor:"gray.50",minHeight:"100vh"}),f=e({marginBottom:"32px",display:"flex",justifyContent:"space-between",alignItems:"center"}),k=e({fontSize:"32px",fontWeight:"bold",color:"gray.900",marginBottom:"8px"}),c=e({fontSize:"16px",color:"gray.600"}),_=e({display:"grid",gridTemplateColumns:"repeat(auto-fit, minmax(200px, 1fr))",gap:"24px",marginBottom:"32px"}),$=e({display:"grid",gridTemplateColumns:"1fr 1fr",gap:"24px","@media (max-width: 1024px)":{gridTemplateColumns:"1fr"}});return z!==h.e&&t(S,h.e=z),f!==h.t&&t(A,h.t=f),k!==h.a&&t(I,h.a=k),c!==h.o&&t(V,h.o=c),_!==h.i&&t(E,h.i=_),$!==h.n&&t(j,h.n=$),h},{e:void 0,t:void 0,a:void 0,o:void 0,i:void 0,n:void 0}),S})()}function Sr(){return D(Vo,{get children(){return D(Ke,{path:"/",component:ki,get children(){return[D(Ke,{path:"/",component:Wo}),D(Ke,{path:"/dashboard",component:Wo}),D(Ke,{path:"/market",component:Hi}),D(Ke,{path:"/trading",component:mr}),D(Ke,{path:"/strategy-editor",component:Ki}),D(Ke,{path:"/api-test",component:Di})]}})}})}const Ao=document.getElementById("root");Ao&&Uo(()=>D(Sr,{}),Ao);console.log("🚀 量化交易前端平台启动成功"),console.log("📊 基于 SolidJS + Panda CSS"),console.log("⚡ 极致性能，专业体验");
