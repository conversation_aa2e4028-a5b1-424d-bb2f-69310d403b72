import { JSX, splitProps, Show } from 'solid-js'
import { Popover as KPopover } from '@kobalte/core/popover'
import { css } from '../../../styled-system/css'
import clsx from 'clsx'

export interface PopoverProps {
  trigger: JSX.Element
  content: JSX.Element | string
  title?: string
  placement?: 'top' | 'bottom' | 'left' | 'right' | 'top-start' | 'top-end' | 'bottom-start' | 'bottom-end'
  width?: string
  disabled?: boolean
  open?: boolean
  onOpenChange?: (open: boolean) => void
  class?: string
}

export function Popover(props: PopoverProps) {
  const [local, others] = splitProps(props, [
    'trigger',
    'content',
    'title',
    'placement',
    'width',
    'disabled',
    'open',
    'onOpenChange',
    'class',
  ])

  const getTriggerStyles = () => ({
    display: 'inline-flex',
    cursor: local.disabled ? 'not-allowed' : 'pointer',
    opacity: local.disabled ? 0.5 : 1,
  })

  const getContentStyles = () => ({
    backgroundColor: 'white',
    borderRadius: '4px',
    border: '1px solid',
    borderColor: 'border.base',
    boxShadow: 'light',
    padding: 0,
    width: local.width || 'auto',
    maxWidth: '400px',
    zIndex: 1000,
    animation: 'popoverFadeIn 0.2s ease',
  })

  const getHeaderStyles = () => ({
    padding: '12px 16px',
    borderBottom: '1px solid',
    borderColor: 'border.lighter',
    fontSize: '14px',
    fontWeight: '500',
    color: 'text.primary',
    backgroundColor: 'bg.page',
  })

  const getBodyStyles = () => ({
    padding: '12px 16px',
    fontSize: '14px',
    color: 'text.regular',
    lineHeight: 1.5,
  })

  const getArrowStyles = () => ({
    fill: 'white',
    stroke: 'border.base',
    strokeWidth: 1,
  })

  return (
    <KPopover
      placement={local.placement || 'bottom'}
      disabled={local.disabled}
      open={local.open}
      onOpenChange={local.onOpenChange}
    >
      <KPopover.Trigger class={css(getTriggerStyles())}>
        {local.trigger}
      </KPopover.Trigger>

      <KPopover.Portal>
        <KPopover.Content class={clsx(css(getContentStyles()), local.class)}>
          <KPopover.Arrow class={css(getArrowStyles())} />
          
          <Show when={local.title}>
            <div class={css(getHeaderStyles())}>
              {local.title}
            </div>
          </Show>
          
          <div class={css(getBodyStyles())}>
            {typeof local.content === 'string' ? local.content : local.content}
          </div>
        </KPopover.Content>
      </KPopover.Portal>
    </KPopover>
  )
}

// 添加动画样式
const popoverStyle = document.createElement('style')
popoverStyle.textContent = `
  @keyframes popoverFadeIn {
    from {
      opacity: 0;
      transform: scale(0.95);
    }
    to {
      opacity: 1;
      transform: scale(1);
    }
  }
`
if (!document.head.querySelector('style[data-popover]')) {
  popoverStyle.setAttribute('data-popover', 'true')
  document.head.appendChild(popoverStyle)
}
