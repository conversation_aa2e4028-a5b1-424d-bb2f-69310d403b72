import { createSignal, onMount, onCleanup } from 'solid-js';
import loader from '@monaco-editor/loader';
import type { editor } from 'monaco-editor';
import { css } from '../../styled-system/css';

interface MonacoEditorProps {
  value?: string;
  language?: string;
  theme?: string;
  height?: number;
  onChange?: (value: string) => void;
  options?: editor.IStandaloneEditorConstructionOptions;
}

export default function MonacoEditor(props: MonacoEditorProps) {
  const [container, setContainer] = createSignal<HTMLDivElement>();
  let editor: editor.IStandaloneCodeEditor | undefined;

  onMount(async () => {
    const containerElement = container();
    if (!containerElement) return;

    try {
      // 配置 Monaco Editor - 使用本地资源，避免CDN依赖
      const isLocal = import.meta.env.VITE_MONACO_LOCAL === 'true';
      const monacoPath = isLocal 
        ? '/libs/monaco-editor/min/vs'
        : 'https://cdn.jsdelivr.net/npm/monaco-editor@0.45.0/min/vs';
      
      console.log(`Monaco Editor 配置: ${isLocal ? '本地模式' : 'CDN模式'}, 路径: ${monacoPath}`);
      
      loader.config({
        paths: {
          vs: monacoPath
        }
      });

      const monaco = await loader.init();

      // 注册 Python 语言的自动完成功能
      if (props.language === 'python') {
        monaco.languages.registerCompletionItemProvider('python', {
          provideCompletionItems: (model, position) => {
            const word = model.getWordUntilPosition(position);
            const range = {
              startLineNumber: position.lineNumber,
              endLineNumber: position.lineNumber,
              startColumn: word.startColumn,
              endColumn: word.endColumn
            };

            const suggestions = [
              {
                label: 'def',
                kind: monaco.languages.CompletionItemKind.Keyword,
                insertText: 'def ${1:function_name}(${2:parameters}):\n    ${3:pass}',
                insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
                documentation: 'Define a function',
                range
              },
              {
                label: 'initialize',
                kind: monaco.languages.CompletionItemKind.Function,
                insertText: 'def initialize(context):\n    ${1:pass}',
                insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
                documentation: '策略初始化函数',
                range
              },
              {
                label: 'handle_data',
                kind: monaco.languages.CompletionItemKind.Function,
                insertText: 'def handle_data(context, data):\n    ${1:pass}',
                insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
                documentation: '主要的交易逻辑函数',
                range
              },
              {
                label: 'order_target_percent',
                kind: monaco.languages.CompletionItemKind.Function,
                insertText: 'order_target_percent(${1:security}, ${2:percent})',
                insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
                documentation: '下单到目标百分比',
                range
              },
              {
                label: 'attribute_history',
                kind: monaco.languages.CompletionItemKind.Function,
                insertText: 'attribute_history(${1:security}, ${2:count}, ${3:unit}, ${4:fields})',
                insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
                documentation: '获取历史数据',
                range
              },
              {
                label: 'log.info',
                kind: monaco.languages.CompletionItemKind.Function,
                insertText: 'log.info(${1:message})',
                insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
                documentation: '输出日志信息',
                range
              }
            ];

            return { suggestions };
          }
        });
      }

      // 创建编辑器实例
      editor = monaco.editor.create(containerElement, {
        value: props.value || '',
        language: props.language || 'python',
        theme: props.theme || 'vs',
        fontSize: 13,
        lineNumbers: 'on',
        roundedSelection: false,
        scrollBeyondLastLine: false,
        minimap: { enabled: true },
        automaticLayout: true,
        tabSize: 4,
        insertSpaces: true,
        wordWrap: 'on',
        folding: true,
        renderLineHighlight: 'all',
        selectOnLineNumbers: true,
        matchBrackets: 'always',
        ...props.options
      });

      // 监听内容变化
      editor.onDidChangeModelContent(() => {
        if (props.onChange && editor) {
          props.onChange(editor.getValue());
        }
      });

      // 设置快捷键
      editor.addCommand(monaco.KeyMod.CtrlCmd | monaco.KeyCode.KeyS, () => {
        console.log('保存策略快捷键触发');
      });

      editor.addCommand(monaco.KeyMod.CtrlCmd | monaco.KeyCode.Enter, () => {
        console.log('运行策略快捷键触发');
      });

    } catch (error) {
      console.error('Monaco Editor 初始化失败:', error);
    }
  });

  onCleanup(() => {
    if (editor) {
      editor.dispose();
    }
  });

  return (
    <div class={css({
      width: '100%',
      height: `${props.height || 400}px`,
      border: '1px solid #e5e7eb',
      borderRadius: '6px',
      overflow: 'hidden'
    })}>
      <div 
        ref={setContainer}
        class={css({
          width: '100%',
          height: '100%'
        })}
      />
    </div>
  );
}