# 关于Monaco Editor本地化

## 下载Monaco Editor到本地

如果需要离线或内网部署，可以将Monaco Editor下载到本地：

```bash
# 1. 下载Monaco Editor
npm install monaco-editor@0.45.0

# 2. 复制到public目录
mkdir -p public/libs/monaco-editor
cp -r node_modules/monaco-editor/min public/libs/monaco-editor/

# 3. 设置环境变量使用本地资源
echo "VITE_MONACO_LOCAL=true" > .env
```

## 配置说明

- `VITE_MONACO_LOCAL=false`: 使用CDN (默认)
- `VITE_MONACO_LOCAL=true`: 使用本地资源

默认使用CDN以减少构建体积，生产环境建议使用本地资源确保稳定性。
