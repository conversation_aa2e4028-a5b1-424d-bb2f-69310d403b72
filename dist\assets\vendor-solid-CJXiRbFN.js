const nt=(e,t)=>e===t,Y=Symbol("solid-proxy"),Ie=typeof Proxy=="function",rt=Symbol("solid-track"),st=Symbol("solid-dev-component"),J={equals:nt};let Ne=Fe;const N=1,Q=2,ot={};var y=null;let oe=null,it=null,A=null,x=null,T=null,te=0;function B(e,t){const n=A,r=y,s=e.length===0,i=t===void 0?r:t,l=s?{owned:null,cleanups:null,context:null,owner:null}:{owned:null,cleanups:null,context:i?i.context:null,owner:i},o=s?()=>e(()=>{throw new Error("Dispose method must be an explicit argument to createRoot function")}):()=>e(()=>O(()=>q(l)));y=l,A=null;try{return D(o,!0)}finally{A=n,y=r}}function $(e,t){t=t?Object.assign({},J,t):J;const n={value:e,observers:null,observerSlots:null,comparator:t.equals||void 0};t.name&&(n.name=t.name),t.internal?n.internal=!0:ut(n);const r=s=>(typeof s=="function"&&(s=s(n.value)),Ue(n,s));return[Me.bind(n),r]}function k(e,t,n){const r=ne(e,t,!1,N,n);K(r)}function De(e,t,n){Ne=ht;const r=ne(e,t,!1,N,n);(!n||!n.render)&&(r.user=!0),T?T.push(r):K(r)}function P(e,t,n){n=n?Object.assign({},J,n):J;const r=ne(e,t,!0,0,n);return r.observers=null,r.observerSlots=null,r.comparator=n.equals||void 0,K(r),Me.bind(r)}function lt(e){return D(e,!1)}function O(e){if(A===null)return e();const t=A;A=null;try{return e()}finally{A=t}}function we(e,t,n){const r=Array.isArray(e);let s,i=n&&n.defer;return l=>{let o;if(r){o=Array(e.length);for(let c=0;c<e.length;c++)o[c]=e[c]()}else o=e();if(i)return i=!1,l;const a=O(()=>t(o,s,l));return s=o,a}}function Sn(e){De(()=>O(e))}function W(e){return y===null?console.warn("cleanups created outside a `createRoot` or `render` will never be run"):y.cleanups===null?y.cleanups=[e]:y.cleanups.push(e),e}function ye(){return y}function be(e,t){const n=y,r=A;y=e,A=null;try{return D(t,!0)}catch(s){Se(s)}finally{y=n,A=r}}function at(e){const t=A,n=y;return Promise.resolve().then(()=>{A=t,y=n;let r;return D(e,!1),A=y=null,r?r.done:void 0})}const[Pn,En]=$(!1);function ct(e,t){const n=ne(()=>O(()=>(Object.assign(e,{[st]:!0}),e(t))),void 0,!0,0);return n.props=t,n.observers=null,n.observerSlots=null,n.name=e.name,n.component=e,K(n),n.tValue!==void 0?n.tValue:n.value}function ut(e){y&&(y.sourceMap?y.sourceMap.push(e):y.sourceMap=[e],e.graph=y)}function je(e,t){const n=Symbol("context");return{id:n,Provider:gt(n,t),defaultValue:e}}function pe(e){let t;return y&&y.context&&(t=y.context[e.id])!==void 0?t:e.defaultValue}function Ae(e){const t=P(e),n=P(()=>ce(t()),void 0,{name:"children"});return n.toArray=()=>{const r=n();return Array.isArray(r)?r:r!=null?[r]:[]},n}function Me(){if(this.sources&&this.state)if(this.state===N)K(this);else{const e=x;x=null,D(()=>ee(this),!1),x=e}if(A){const e=this.observers?this.observers.length:0;A.sources?(A.sources.push(this),A.sourceSlots.push(e)):(A.sources=[this],A.sourceSlots=[e]),this.observers?(this.observers.push(A),this.observerSlots.push(A.sources.length-1)):(this.observers=[A],this.observerSlots=[A.sources.length-1])}return this.value}function Ue(e,t,n){let r=e.value;return(!e.comparator||!e.comparator(r,t))&&(e.value=t,e.observers&&e.observers.length&&D(()=>{for(let s=0;s<e.observers.length;s+=1){const i=e.observers[s],l=oe&&oe.running;l&&oe.disposed.has(i),(l?!i.tState:!i.state)&&(i.pure?x.push(i):T.push(i),i.observers&&_e(i)),l||(i.state=N)}if(x.length>1e6){throw x=[],new Error("Potential Infinite Loop Detected.");throw new Error}},!1)),t}function K(e){if(!e.fn)return;q(e);const t=te;ft(e,e.value,t)}function ft(e,t,n){let r;const s=y,i=A;A=y=e;try{r=e.fn(t)}catch(l){return e.pure&&(e.state=N,e.owned&&e.owned.forEach(q),e.owned=null),e.updatedAt=n+1,Se(l)}finally{A=i,y=s}(!e.updatedAt||e.updatedAt<=n)&&(e.updatedAt!=null&&"observers"in e?Ue(e,r):e.value=r,e.updatedAt=n)}function ne(e,t,n,r=N,s){const i={fn:e,state:r,updatedAt:null,owned:null,sources:null,sourceSlots:null,cleanups:null,value:t,owner:y,context:y?y.context:null,pure:n};return y===null?console.warn("computations created outside a `createRoot` or `render` will never be disposed"):y!==ot&&(y.owned?y.owned.push(i):y.owned=[i]),s&&s.name&&(i.name=s.name),i}function Z(e){if(e.state===0)return;if(e.state===Q)return ee(e);if(e.suspense&&O(e.suspense.inFallback))return e.suspense.effects.push(e);const t=[e];for(;(e=e.owner)&&(!e.updatedAt||e.updatedAt<te);)e.state&&t.push(e);for(let n=t.length-1;n>=0;n--)if(e=t[n],e.state===N)K(e);else if(e.state===Q){const r=x;x=null,D(()=>ee(e,t[0]),!1),x=r}}function D(e,t){if(x)return e();let n=!1;t||(x=[]),T?n=!0:T=[],te++;try{const r=e();return dt(n),r}catch(r){n||(T=null),x=null,Se(r)}}function dt(e){if(x&&(Fe(x),x=null),e)return;const t=T;T=null,t.length&&D(()=>Ne(t),!1)}function Fe(e){for(let t=0;t<e.length;t++)Z(e[t])}function ht(e){let t,n=0;for(t=0;t<e.length;t++){const r=e[t];r.user?e[n++]=r:Z(r)}for(t=0;t<n;t++)Z(e[t])}function ee(e,t){e.state=0;for(let n=0;n<e.sources.length;n+=1){const r=e.sources[n];if(r.sources){const s=r.state;s===N?r!==t&&(!r.updatedAt||r.updatedAt<te)&&Z(r):s===Q&&ee(r,t)}}}function _e(e){for(let t=0;t<e.observers.length;t+=1){const n=e.observers[t];n.state||(n.state=Q,n.pure?x.push(n):T.push(n),n.observers&&_e(n))}}function q(e){let t;if(e.sources)for(;e.sources.length;){const n=e.sources.pop(),r=e.sourceSlots.pop(),s=n.observers;if(s&&s.length){const i=s.pop(),l=n.observerSlots.pop();r<s.length&&(i.sourceSlots[l]=r,s[r]=i,n.observerSlots[r]=l)}}if(e.tOwned){for(t=e.tOwned.length-1;t>=0;t--)q(e.tOwned[t]);delete e.tOwned}if(e.owned){for(t=e.owned.length-1;t>=0;t--)q(e.owned[t]);e.owned=null}if(e.cleanups){for(t=e.cleanups.length-1;t>=0;t--)e.cleanups[t]();e.cleanups=null}e.state=0,delete e.sourceMap}function mt(e){return e instanceof Error?e:new Error(typeof e=="string"?e:"Unknown error",{cause:e})}function Se(e,t=y){throw mt(e)}function ce(e){if(typeof e=="function"&&!e.length)return ce(e());if(Array.isArray(e)){const t=[];for(let n=0;n<e.length;n++){const r=ce(e[n]);Array.isArray(r)?t.push.apply(t,r):t.push(r)}return t}return e}function gt(e,t){return function(r){let s;return k(()=>s=O(()=>(y.context={...y.context,[e]:r.value},Ae(()=>r.children))),void 0,t),s}}const wt=Symbol("fallback");function Ce(e){for(let t=0;t<e.length;t++)e[t]()}function yt(e,t,n={}){let r=[],s=[],i=[],l=0,o=t.length>1?[]:null;return W(()=>Ce(i)),()=>{let a=e()||[],c=a.length,f,u;return a[rt],O(()=>{let m,E,d,g,b,w,R,v,C;if(c===0)l!==0&&(Ce(i),i=[],r=[],s=[],l=0,o&&(o=[])),n.fallback&&(r=[wt],s[0]=B(j=>(i[0]=j,n.fallback())),l=1);else if(l===0){for(s=new Array(c),u=0;u<c;u++)r[u]=a[u],s[u]=B(h);l=c}else{for(d=new Array(c),g=new Array(c),o&&(b=new Array(c)),w=0,R=Math.min(l,c);w<R&&r[w]===a[w];w++);for(R=l-1,v=c-1;R>=w&&v>=w&&r[R]===a[v];R--,v--)d[v]=s[R],g[v]=i[R],o&&(b[v]=o[R]);for(m=new Map,E=new Array(v+1),u=v;u>=w;u--)C=a[u],f=m.get(C),E[u]=f===void 0?-1:f,m.set(C,u);for(f=w;f<=R;f++)C=r[f],u=m.get(C),u!==void 0&&u!==-1?(d[u]=s[f],g[u]=i[f],o&&(b[u]=o[f]),u=E[u],m.set(C,u)):i[f]();for(u=w;u<c;u++)u in d?(s[u]=d[u],i[u]=g[u],o&&(o[u]=b[u],o[u](u))):s[u]=B(h);s=s.slice(0,l=c),r=a.slice(0)}return s});function h(m){if(i[u]=m,o){const[E,d]=$(u,{name:"index"});return o[u]=d,t(a[u],E)}return t(a[u])}}}function I(e,t){return ct(e,t||{})}function X(){return!0}const ue={get(e,t,n){return t===Y?n:e.get(t)},has(e,t){return t===Y?!0:e.has(t)},set:X,deleteProperty:X,getOwnPropertyDescriptor(e,t){return{configurable:!0,enumerable:!0,get(){return e.get(t)},set:X,deleteProperty:X}},ownKeys(e){return e.keys()}};function ie(e){return(e=typeof e=="function"?e():e)?e:{}}function bt(){for(let e=0,t=this.length;e<t;++e){const n=this[e]();if(n!==void 0)return n}}function fe(...e){let t=!1;for(let l=0;l<e.length;l++){const o=e[l];t=t||!!o&&Y in o,e[l]=typeof o=="function"?(t=!0,P(o)):o}if(Ie&&t)return new Proxy({get(l){for(let o=e.length-1;o>=0;o--){const a=ie(e[o])[l];if(a!==void 0)return a}},has(l){for(let o=e.length-1;o>=0;o--)if(l in ie(e[o]))return!0;return!1},keys(){const l=[];for(let o=0;o<e.length;o++)l.push(...Object.keys(ie(e[o])));return[...new Set(l)]}},ue);const n={},r=Object.create(null);for(let l=e.length-1;l>=0;l--){const o=e[l];if(!o)continue;const a=Object.getOwnPropertyNames(o);for(let c=a.length-1;c>=0;c--){const f=a[c];if(f==="__proto__"||f==="constructor")continue;const u=Object.getOwnPropertyDescriptor(o,f);if(!r[f])r[f]=u.get?{enumerable:!0,configurable:!0,get:bt.bind(n[f]=[u.get.bind(o)])}:u.value!==void 0?u:void 0;else{const h=n[f];h&&(u.get?h.push(u.get.bind(o)):u.value!==void 0&&h.push(()=>u.value))}}}const s={},i=Object.keys(r);for(let l=i.length-1;l>=0;l--){const o=i[l],a=r[o];a&&a.get?Object.defineProperty(s,o,a):s[o]=a?a.value:void 0}return s}function pt(e,...t){if(Ie&&Y in e){const s=new Set(t.length>1?t.flat():t[0]),i=t.map(l=>new Proxy({get(o){return l.includes(o)?e[o]:void 0},has(o){return l.includes(o)&&o in e},keys(){return l.filter(o=>o in e)}},ue));return i.push(new Proxy({get(l){return s.has(l)?void 0:e[l]},has(l){return s.has(l)?!1:l in e},keys(){return Object.keys(e).filter(l=>!s.has(l))}},ue)),i}const n={},r=t.map(()=>({}));for(const s of Object.getOwnPropertyNames(e)){const i=Object.getOwnPropertyDescriptor(e,s),l=!i.get&&!i.set&&i.enumerable&&i.writable&&i.configurable;let o=!1,a=0;for(const c of t)c.includes(s)&&(o=!0,l?r[a][s]=i.value:Object.defineProperty(r[a],s,i)),++a;o||(l?n[s]=i.value:Object.defineProperty(n,s,i))}return[...r,n]}const At=e=>`Attempting to access a stale value from <${e}> that could possibly be undefined. This may occur because you are reading the accessor returned from the component at a time where it has already been unmounted. We recommend cleaning up any stale timers or async, or reading from the initial condition.`;function vn(e){const t="fallback"in e&&{fallback:()=>e.fallback};return P(yt(()=>e.each,e.children,t||void 0),void 0,{name:"value"})}function Ve(e){const t=e.keyed,n=P(()=>e.when,void 0,{name:"condition value"}),r=t?n:P(n,void 0,{equals:(s,i)=>!s==!i,name:"condition"});return P(()=>{const s=r();if(s){const i=e.children;return typeof i=="function"&&i.length>0?O(()=>i(t?s:()=>{if(!O(r))throw At("Show");return n()})):i}return e.fallback},void 0,{name:"value"})}globalThis&&(globalThis.Solid$$?console.warn("You appear to have multiple instances of Solid. This can lead to unexpected behavior."):globalThis.Solid$$=!0);const St=["allowfullscreen","async","alpha","autofocus","autoplay","checked","controls","default","disabled","formnovalidate","hidden","indeterminate","inert","ismap","loop","multiple","muted","nomodule","novalidate","open","playsinline","readonly","required","reversed","seamless","selected","adauctionheaders","browsingtopics","credentialless","defaultchecked","defaultmuted","defaultselected","defer","disablepictureinpicture","disableremoteplayback","preservespitch","shadowrootclonable","shadowrootcustomelementregistry","shadowrootdelegatesfocus","shadowrootserializable","sharedstoragewritable"],Pt=new Set(["className","value","readOnly","noValidate","formNoValidate","isMap","noModule","playsInline","adAuctionHeaders","allowFullscreen","browsingTopics","defaultChecked","defaultMuted","defaultSelected","disablePictureInPicture","disableRemotePlayback","preservesPitch","shadowRootClonable","shadowRootCustomElementRegistry","shadowRootDelegatesFocus","shadowRootSerializable","sharedStorageWritable",...St]),Et=new Set(["innerHTML","textContent","innerText","children"]),vt=Object.assign(Object.create(null),{className:"class",htmlFor:"for"}),Rt=Object.assign(Object.create(null),{class:"className",novalidate:{$:"noValidate",FORM:1},formnovalidate:{$:"formNoValidate",BUTTON:1,INPUT:1},ismap:{$:"isMap",IMG:1},nomodule:{$:"noModule",SCRIPT:1},playsinline:{$:"playsInline",VIDEO:1},readonly:{$:"readOnly",INPUT:1,TEXTAREA:1},adauctionheaders:{$:"adAuctionHeaders",IFRAME:1},allowfullscreen:{$:"allowFullscreen",IFRAME:1},browsingtopics:{$:"browsingTopics",IMG:1},defaultchecked:{$:"defaultChecked",INPUT:1},defaultmuted:{$:"defaultMuted",AUDIO:1,VIDEO:1},defaultselected:{$:"defaultSelected",OPTION:1},disablepictureinpicture:{$:"disablePictureInPicture",VIDEO:1},disableremoteplayback:{$:"disableRemotePlayback",AUDIO:1,VIDEO:1},preservespitch:{$:"preservesPitch",AUDIO:1,VIDEO:1},shadowrootclonable:{$:"shadowRootClonable",TEMPLATE:1},shadowrootdelegatesfocus:{$:"shadowRootDelegatesFocus",TEMPLATE:1},shadowrootserializable:{$:"shadowRootSerializable",TEMPLATE:1},sharedstoragewritable:{$:"sharedStorageWritable",IFRAME:1,IMG:1}});function xt(e,t){const n=Rt[e];return typeof n=="object"?n[t]?n.$:void 0:n}const Ct=new Set(["beforeinput","click","dblclick","contextmenu","focusin","focusout","input","keydown","keyup","mousedown","mousemove","mouseout","mouseover","mouseup","pointerdown","pointermove","pointerout","pointerover","pointerup","touchend","touchmove","touchstart"]),Ot=e=>P(()=>e());function Lt(e,t,n){let r=n.length,s=t.length,i=r,l=0,o=0,a=t[s-1].nextSibling,c=null;for(;l<s||o<i;){if(t[l]===n[o]){l++,o++;continue}for(;t[s-1]===n[i-1];)s--,i--;if(s===l){const f=i<r?o?n[o-1].nextSibling:n[i-o]:a;for(;o<i;)e.insertBefore(n[o++],f)}else if(i===o)for(;l<s;)(!c||!c.has(t[l]))&&t[l].remove(),l++;else if(t[l]===n[i-1]&&n[o]===t[s-1]){const f=t[--s].nextSibling;e.insertBefore(n[o++],t[l++].nextSibling),e.insertBefore(n[--i],f),t[s]=n[i]}else{if(!c){c=new Map;let u=o;for(;u<i;)c.set(n[u],u++)}const f=c.get(t[l]);if(f!=null)if(o<f&&f<i){let u=l,h=1,m;for(;++u<s&&u<i&&!((m=c.get(t[u]))==null||m!==f+h);)h++;if(h>f-o){const E=t[l];for(;o<f;)e.insertBefore(n[o++],E)}else e.replaceChild(n[o++],t[l++])}else l++;else t[l++].remove()}}}const Oe="_$DX_DELEGATE";function Rn(e,t,n,r={}){if(!t)throw new Error("The `element` passed to `render(..., element)` doesn't exist. Make sure `element` exists in the document.");let s;return B(i=>{s=i,t===document?e():he(t,e(),t.firstChild?null:void 0,n)},r.owner),()=>{s(),t.textContent=""}}function Tt(e,t,n,r){let s;const i=()=>{const o=document.createElement("template");return o.innerHTML=e,o.content.firstChild},l=()=>(s||(s=i())).cloneNode(!0);return l.cloneNode=l,l}function Be(e,t=window.document){const n=t[Oe]||(t[Oe]=new Set);for(let r=0,s=e.length;r<s;r++){const i=e[r];n.has(i)||(n.add(i),t.addEventListener(i,_t))}}function de(e,t,n){n==null?e.removeAttribute(t):e.setAttribute(t,n)}function $t(e,t,n){n?e.setAttribute(t,""):e.removeAttribute(t)}function kt(e,t){t==null?e.removeAttribute("class"):e.className=t}function It(e,t,n,r){if(r)Array.isArray(n)?(e[`$$${t}`]=n[0],e[`$$${t}Data`]=n[1]):e[`$$${t}`]=n;else if(Array.isArray(n)){const s=n[0];e.addEventListener(t,n[0]=i=>s.call(e,n[1],i))}else e.addEventListener(t,n,typeof n!="function"&&n)}function Nt(e,t,n={}){const r=Object.keys(t||{}),s=Object.keys(n);let i,l;for(i=0,l=s.length;i<l;i++){const o=s[i];!o||o==="undefined"||t[o]||(Le(e,o,!1),delete n[o])}for(i=0,l=r.length;i<l;i++){const o=r[i],a=!!t[o];!o||o==="undefined"||n[o]===a||!a||(Le(e,o,!0),n[o]=a)}return n}function Dt(e,t,n){if(!t)return n?de(e,"style"):t;const r=e.style;if(typeof t=="string")return r.cssText=t;typeof n=="string"&&(r.cssText=n=void 0),n||(n={}),t||(t={});let s,i;for(i in n)t[i]==null&&r.removeProperty(i),delete n[i];for(i in t)s=t[i],s!==n[i]&&(r.setProperty(i,s),n[i]=s);return n}function jt(e,t={},n,r){const s={};return r||k(()=>s.children=G(e,t.children,s.children)),k(()=>typeof t.ref=="function"&&Mt(t.ref,e)),k(()=>Ut(e,t,n,!0,s,!0)),s}function Mt(e,t,n){return O(()=>e(t,n))}function he(e,t,n,r){if(n!==void 0&&!r&&(r=[]),typeof t!="function")return G(e,t,r,n);k(s=>G(e,t(),s,n),r)}function Ut(e,t,n,r,s={},i=!1){t||(t={});for(const l in s)if(!(l in t)){if(l==="children")continue;s[l]=Te(e,l,null,s[l],n,i,t)}for(const l in t){if(l==="children")continue;const o=t[l];s[l]=Te(e,l,o,s[l],n,i,t)}}function Ft(e){return e.toLowerCase().replace(/-([a-z])/g,(t,n)=>n.toUpperCase())}function Le(e,t,n){const r=t.trim().split(/\s+/);for(let s=0,i=r.length;s<i;s++)e.classList.toggle(r[s],n)}function Te(e,t,n,r,s,i,l){let o,a,c,f,u;if(t==="style")return Dt(e,n,r);if(t==="classList")return Nt(e,n,r);if(n===r)return r;if(t==="ref")i||n(e);else if(t.slice(0,3)==="on:"){const h=t.slice(3);r&&e.removeEventListener(h,r,typeof r!="function"&&r),n&&e.addEventListener(h,n,typeof n!="function"&&n)}else if(t.slice(0,10)==="oncapture:"){const h=t.slice(10);r&&e.removeEventListener(h,r,!0),n&&e.addEventListener(h,n,!0)}else if(t.slice(0,2)==="on"){const h=t.slice(2).toLowerCase(),m=Ct.has(h);if(!m&&r){const E=Array.isArray(r)?r[0]:r;e.removeEventListener(h,E)}(m||n)&&(It(e,h,n,m),m&&Be([h]))}else t.slice(0,5)==="attr:"?de(e,t.slice(5),n):t.slice(0,5)==="bool:"?$t(e,t.slice(5),n):(u=t.slice(0,5)==="prop:")||(c=Et.has(t))||(f=xt(t,e.tagName))||(a=Pt.has(t))||(o=e.nodeName.includes("-")||"is"in l)?(u&&(t=t.slice(5),a=!0),t==="class"||t==="className"?kt(e,n):o&&!a&&!c?e[Ft(t)]=n:e[f||t]=n):de(e,vt[t]||t,n);return n}function _t(e){let t=e.target;const n=`$$${e.type}`,r=e.target,s=e.currentTarget,i=a=>Object.defineProperty(e,"target",{configurable:!0,value:a}),l=()=>{const a=t[n];if(a&&!t.disabled){const c=t[`${n}Data`];if(c!==void 0?a.call(t,c,e):a.call(t,e),e.cancelBubble)return}return t.host&&typeof t.host!="string"&&!t.host._$host&&t.contains(e.target)&&i(t.host),!0},o=()=>{for(;l()&&(t=t._$host||t.parentNode||t.host););};if(Object.defineProperty(e,"currentTarget",{configurable:!0,get(){return t||document}}),e.composedPath){const a=e.composedPath();i(a[0]);for(let c=0;c<a.length-2&&(t=a[c],!!l());c++){if(t._$host){t=t._$host,o();break}if(t.parentNode===s)break}}else o();i(r)}function G(e,t,n,r,s){for(;typeof n=="function";)n=n();if(t===n)return n;const i=typeof t,l=r!==void 0;if(e=l&&n[0]&&n[0].parentNode||e,i==="string"||i==="number"){if(i==="number"&&(t=t.toString(),t===n))return n;if(l){let o=n[0];o&&o.nodeType===3?o.data!==t&&(o.data=t):o=document.createTextNode(t),n=V(e,n,r,o)}else n!==""&&typeof n=="string"?n=e.firstChild.data=t:n=e.textContent=t}else if(t==null||i==="boolean")n=V(e,n,r);else{if(i==="function")return k(()=>{let o=t();for(;typeof o=="function";)o=o();n=G(e,o,n,r)}),()=>n;if(Array.isArray(t)){const o=[],a=n&&Array.isArray(n);if(me(o,t,n,s))return k(()=>n=G(e,o,n,r,!0)),()=>n;if(o.length===0){if(n=V(e,n,r),l)return n}else a?n.length===0?$e(e,o,r):Lt(e,n,o):(n&&V(e),$e(e,o));n=o}else if(t.nodeType){if(Array.isArray(n)){if(l)return n=V(e,n,r,t);V(e,n,null,t)}else n==null||n===""||!e.firstChild?e.appendChild(t):e.replaceChild(t,e.firstChild);n=t}else console.warn("Unrecognized value. Skipped inserting",t)}return n}function me(e,t,n,r){let s=!1;for(let i=0,l=t.length;i<l;i++){let o=t[i],a=n&&n[e.length],c;if(!(o==null||o===!0||o===!1))if((c=typeof o)=="object"&&o.nodeType)e.push(o);else if(Array.isArray(o))s=me(e,o,a)||s;else if(c==="function")if(r){for(;typeof o=="function";)o=o();s=me(e,Array.isArray(o)?o:[o],Array.isArray(a)?a:[a])||s}else e.push(o),s=!0;else{const f=String(o);a&&a.nodeType===3&&a.data===f?e.push(a):e.push(document.createTextNode(f))}}return s}function $e(e,t,n=null){for(let r=0,s=t.length;r<s;r++)e.insertBefore(t[r],n)}function V(e,t,n,r){if(n===void 0)return e.textContent="";const s=r||document.createTextNode("");if(t.length){let i=!1;for(let l=t.length-1;l>=0;l--){const o=t[l];if(s!==o){const a=o.parentNode===e;!i&&!l?a?e.replaceChild(s,o):e.insertBefore(s,n):a&&o.remove()}else i=!0}}else e.insertBefore(s,n);return[s]}const Vt=!1,Bt="http://www.w3.org/2000/svg";function Kt(e,t=!1,n=void 0){return t?document.createElementNS(Bt,e):document.createElement(e,{is:n})}function xn(e){const{useShadow:t}=e,n=document.createTextNode(""),r=()=>e.mount||document.body,s=ye();let i;return De(()=>{i||(i=be(s,()=>P(()=>e.children)));const l=r();if(l instanceof HTMLHeadElement){const[o,a]=$(!1),c=()=>a(!0);B(f=>he(l,()=>o()?f():i(),null)),W(c)}else{const o=Kt(e.isSVG?"g":"div",e.isSVG),a=t&&o.attachShadow?o.attachShadow({mode:"open"}):o;Object.defineProperty(o,"_$host",{get(){return n.parentNode},configurable:!0}),he(a,i),l.appendChild(o),e.ref&&e.ref(o),W(()=>l.removeChild(o))}},void 0,{render:!0}),n}function Ke(){let e=new Set;function t(s){return e.add(s),()=>e.delete(s)}let n=!1;function r(s,i){if(n)return!(n=!1);const l={to:s,options:i,defaultPrevented:!1,preventDefault:()=>l.defaultPrevented=!0};for(const o of e)o.listener({...l,from:o.location,retry:a=>{a&&(n=!0),o.navigate(s,{...i,resolve:!1})}});return!l.defaultPrevented}return{subscribe:t,confirm:r}}let ge;function Pe(){(!window.history.state||window.history.state._depth==null)&&window.history.replaceState({...window.history.state,_depth:window.history.length-1},""),ge=window.history.state._depth}Pe();function Wt(e){return{...e,_depth:window.history.state&&window.history.state._depth}}function qt(e,t){let n=!1;return()=>{const r=ge;Pe();const s=r==null?null:ge-r;if(n){n=!1;return}s&&t(s)?(n=!0,window.history.go(-s)):e()}}const Gt=/^(?:[a-z0-9]+:)?\/\//i,Ht=/^\/+|(\/)\/+$/g,We="http://sr";function F(e,t=!1){const n=e.replace(Ht,"$1");return n?t||/^[?#]/.test(n)?n:"/"+n:""}function z(e,t,n){if(Gt.test(t))return;const r=F(e),s=n&&F(n);let i="";return!s||t.startsWith("/")?i=r:s.toLowerCase().indexOf(r.toLowerCase())!==0?i=r+s:i=s,(i||"/")+F(t,!i)}function Xt(e,t){if(e==null)throw new Error(t);return e}function zt(e,t){return F(e).replace(/\/*(\*.*)?$/g,"")+F(t)}function qe(e){const t={};return e.searchParams.forEach((n,r)=>{t[r]=n}),t}function Yt(e,t,n){const[r,s]=e.split("/*",2),i=r.split("/").filter(Boolean),l=i.length;return o=>{const a=o.split("/").filter(Boolean),c=a.length-l;if(c<0||c>0&&s===void 0&&!t)return null;const f={path:l?"":"/",params:{}},u=h=>n===void 0?void 0:n[h];for(let h=0;h<l;h++){const m=i[h],E=a[h],d=m[0]===":",g=d?m.slice(1):m;if(d&&le(E,u(g)))f.params[g]=E;else if(d||!le(E,m))return null;f.path+=`/${E}`}if(s){const h=c?a.slice(-c).join("/"):"";if(le(h,u(s)))f.params[s]=h;else return null}return f}}function le(e,t){const n=r=>r.localeCompare(e,void 0,{sensitivity:"base"})===0;return t===void 0?!0:typeof t=="string"?n(t):typeof t=="function"?t(e):Array.isArray(t)?t.some(n):t instanceof RegExp?t.test(e):!1}function Jt(e){const[t,n]=e.pattern.split("/*",2),r=t.split("/").filter(Boolean);return r.reduce((s,i)=>s+(i.startsWith(":")?2:3),r.length-(n===void 0?0:1))}function Ge(e){const t=new Map,n=ye();return new Proxy({},{get(r,s){return t.has(s)||be(n,()=>t.set(s,P(()=>e()[s]))),t.get(s)()},getOwnPropertyDescriptor(){return{enumerable:!0,configurable:!0}},ownKeys(){return Reflect.ownKeys(e())}})}function He(e){let t=/(\/?\:[^\/]+)\?/.exec(e);if(!t)return[e];let n=e.slice(0,t.index),r=e.slice(t.index+t[0].length);const s=[n,n+=t[1]];for(;t=/^(\/\:[^\/]+)\?/.exec(r);)s.push(n+=t[1]),r=r.slice(t[0].length);return He(r).reduce((i,l)=>[...i,...s.map(o=>o+l)],[])}const Qt=100,Xe=je(),Ee=je(),ve=()=>Xt(pe(Xe),"<A> and 'use' router primitives can be only used inside a Route."),Zt=()=>pe(Ee)||ve().base,en=e=>{const t=Zt();return P(()=>t.resolvePath(e()))},tn=e=>{const t=ve();return P(()=>{const n=e();return n!==void 0?t.renderPath(n):n})},nn=()=>ve().location;function rn(e,t=""){const{component:n,load:r,children:s,info:i}=e,l=!s||Array.isArray(s)&&!s.length,o={key:e,component:n,load:r,info:i};return ze(e.path).reduce((a,c)=>{for(const f of He(c)){const u=zt(t,f);let h=l?u:u.split("/*",1)[0];h=h.split("/").map(m=>m.startsWith(":")||m.startsWith("*")?m:encodeURIComponent(m)).join("/"),a.push({...o,originalPath:c,pattern:h,matcher:Yt(h,!l,e.matchFilters)})}return a},[])}function sn(e,t=0){return{routes:e,score:Jt(e[e.length-1])*1e4-t,matcher(n){const r=[];for(let s=e.length-1;s>=0;s--){const i=e[s],l=i.matcher(n);if(!l)return null;r.unshift({...l,route:i})}return r}}}function ze(e){return Array.isArray(e)?e:[e]}function Ye(e,t="",n=[],r=[]){const s=ze(e);for(let i=0,l=s.length;i<l;i++){const o=s[i];if(o&&typeof o=="object"){o.hasOwnProperty("path")||(o.path="");const a=rn(o,t);for(const c of a){n.push(c);const f=Array.isArray(o.children)&&o.children.length===0;if(o.children&&!f)Ye(o.children,c.pattern,n,r);else{const u=sn([...n],r.length);r.push(u)}n.pop()}}}return n.length?r:r.sort((i,l)=>l.score-i.score)}function ae(e,t){for(let n=0,r=e.length;n<r;n++){const s=e[n].matcher(t);if(s)return s}return[]}function on(e,t){const n=new URL(We),r=P(a=>{const c=e();try{return new URL(c,n)}catch{return console.error(`Invalid path ${c}`),a}},n,{equals:(a,c)=>a.href===c.href}),s=P(()=>r().pathname),i=P(()=>r().search,!0),l=P(()=>r().hash),o=()=>"";return{get pathname(){return s()},get search(){return i()},get hash(){return l()},get state(){return t()},get key(){return o()},query:Ge(we(i,()=>qe(r())))}}let U;function ln(){return U}function an(e,t,n,r={}){const{signal:[s,i],utils:l={}}=e,o=l.parsePath||(p=>p),a=l.renderPath||(p=>p),c=l.beforeLeave||Ke(),f=z("",r.base||"");if(f===void 0)throw new Error(`${f} is not a valid base path`);f&&!s().value&&i({value:f,replace:!0,scroll:!1});const[u,h]=$(!1);let m;const E=(p,S)=>{S.value===d()&&S.state===b()||(m===void 0&&h(!0),U=p,m=S,at(()=>{m===S&&(g(m.value),w(m.state),C[1]([]))}).finally(()=>{m===S&&lt(()=>{U=void 0,p==="navigate"&&et(m),h(!1),m=void 0})}))},[d,g]=$(s().value),[b,w]=$(s().state),R=on(d,b),v=[],C=$([]),j=P(()=>typeof r.transformUrl=="function"?ae(t(),r.transformUrl(R.pathname)):ae(t(),R.pathname)),Je=Ge(()=>{const p=j(),S={};for(let L=0;L<p.length;L++)Object.assign(S,p[L].params);return S}),Re={pattern:f,path:()=>f,outlet:()=>null,resolvePath(p){return z(f,p)}};return k(we(s,p=>E("native",p),{defer:!0})),{base:Re,location:R,params:Je,isRouting:u,renderPath:a,parsePath:o,navigatorFactory:Ze,matches:j,beforeLeave:c,preloadRoute:tt,singleFlight:r.singleFlight===void 0?!0:r.singleFlight,submissions:C};function Qe(p,S,L){O(()=>{if(typeof S=="number"){S&&(l.go?l.go(S):console.warn("Router integration does not support relative routing"));return}const{replace:re,resolve:se,scroll:_,state:H}={replace:!1,resolve:!0,scroll:!0,...L},M=se?p.resolvePath(S):z("",S);if(M===void 0)throw new Error(`Path '${S}' is not a routable path`);if(v.length>=Qt)throw new Error("Too many redirects");const xe=d();(M!==xe||H!==b())&&(Vt||c.confirm(M,L)&&(v.push({value:xe,replace:re,scroll:_,state:b()}),E("navigate",{value:M,state:H})))})}function Ze(p){return p=p||pe(Ee)||Re,(S,L)=>Qe(p,S,L)}function et(p){const S=v[0];S&&(i({...p,replace:S.replace,scroll:S.scroll}),v.length=0)}function tt(p,S={}){const L=ae(t(),p.pathname),re=U;U="preload";for(let se in L){const{route:_,params:H}=L[se];_.component&&_.component.preload&&_.component.preload();const{load:M}=_;S.preloadData&&M&&be(n(),()=>M({params:H,location:{pathname:p.pathname,search:p.search,hash:p.hash,query:qe(p),state:null,key:""},intent:"preload"}))}U=re}}function cn(e,t,n,r){const{base:s,location:i,params:l}=e,{pattern:o,component:a,load:c}=r().route,f=P(()=>r().path);a&&a.preload&&a.preload();const u=c?c({params:l,location:i,intent:U||"initial"}):void 0;return{parent:t,pattern:o,path:f,outlet:()=>a?I(a,{params:l,location:i,data:u,get children(){return n()}}):n(),resolvePath(m){return z(s.path(),m,f())}}}const un=e=>t=>{const{base:n}=t,r=Ae(()=>t.children),s=P(()=>Ye(r(),t.base||""));let i;const l=an(e,s,()=>i,{base:n,singleFlight:t.singleFlight,transformUrl:t.transformUrl});return e.create&&e.create(l),I(Xe.Provider,{value:l,get children(){return I(fn,{routerState:l,get root(){return t.root},get load(){return t.rootLoad},get children(){return[Ot(()=>(i=ye())&&null),I(dn,{routerState:l,get branches(){return s()}})]}})}})};function fn(e){const t=e.routerState.location,n=e.routerState.params,r=P(()=>e.load&&O(()=>{e.load({params:n,location:t,intent:ln()||"initial"})}));return I(Ve,{get when(){return e.root},keyed:!0,get fallback(){return e.children},children:s=>I(s,{params:n,location:t,get data(){return r()},get children(){return e.children}})})}function dn(e){const t=[];let n;const r=P(we(e.routerState.matches,(s,i,l)=>{let o=i&&s.length===i.length;const a=[];for(let c=0,f=s.length;c<f;c++){const u=i&&i[c],h=s[c];l&&u&&h.route.key===u.route.key?a[c]=l[c]:(o=!1,t[c]&&t[c](),B(m=>{t[c]=m,a[c]=cn(e.routerState,a[c-1]||e.routerState.base,ke(()=>r()[c+1]),()=>e.routerState.matches()[c])}))}return t.splice(s.length).forEach(c=>c()),l&&o?l:(n=a[0],a)}));return ke(()=>r()&&n)()}const ke=e=>()=>I(Ve,{get when(){return e()},keyed:!0,children:t=>I(Ee.Provider,{value:t,get children(){return t.outlet()}})}),Cn=e=>{const t=Ae(()=>e.children);return fe(e,{get children(){return t()}})};function hn([e,t],n,r){return[e,r?s=>t(r(s)):t]}function mn(e){if(e==="#")return null;try{return document.querySelector(e)}catch{return null}}function gn(e){let t=!1;const n=s=>typeof s=="string"?{value:s}:s,r=hn($(n(e.get()),{equals:(s,i)=>s.value===i.value&&s.state===i.state}),void 0,s=>(!t&&e.set(s),s));return e.init&&W(e.init((s=e.get())=>{t=!0,r[1](n(s)),t=!1})),un({signal:r,create:e.create,utils:e.utils})}function wn(e,t,n){return e.addEventListener(t,n),()=>e.removeEventListener(t,n)}function yn(e,t){const n=mn(`#${e}`);n?n.scrollIntoView():t&&window.scrollTo(0,0)}const bn=new Map;function pn(e=!0,t=!1,n="/_server",r){return s=>{const i=s.base.path(),l=s.navigatorFactory(s.base);let o={};function a(d){return d.namespaceURI==="http://www.w3.org/2000/svg"}function c(d){if(d.defaultPrevented||d.button!==0||d.metaKey||d.altKey||d.ctrlKey||d.shiftKey)return;const g=d.composedPath().find(j=>j instanceof Node&&j.nodeName.toUpperCase()==="A");if(!g||t&&!g.hasAttribute("link"))return;const b=a(g),w=b?g.href.baseVal:g.href;if((b?g.target.baseVal:g.target)||!w&&!g.hasAttribute("state"))return;const v=(g.getAttribute("rel")||"").split(/\s+/);if(g.hasAttribute("download")||v&&v.includes("external"))return;const C=b?new URL(w,document.baseURI):new URL(w);if(!(C.origin!==window.location.origin||i&&C.pathname&&!C.pathname.toLowerCase().startsWith(i.toLowerCase())))return[g,C]}function f(d){const g=c(d);if(!g)return;const[b,w]=g,R=s.parsePath(w.pathname+w.search+w.hash),v=b.getAttribute("state");d.preventDefault(),l(R,{resolve:!1,replace:b.hasAttribute("replace"),scroll:!b.hasAttribute("noscroll"),state:v&&JSON.parse(v)})}function u(d){const g=c(d);if(!g)return;const[b,w]=g;typeof r=="function"&&(w.pathname=r(w.pathname)),o[w.pathname]||s.preloadRoute(w,{preloadData:b.getAttribute("preload")!=="false"})}function h(d){const g=c(d);if(!g)return;const[b,w]=g;typeof r=="function"&&(w.pathname=r(w.pathname)),!o[w.pathname]&&(o[w.pathname]=setTimeout(()=>{s.preloadRoute(w,{preloadData:b.getAttribute("preload")!=="false"}),delete o[w.pathname]},200))}function m(d){const g=c(d);if(!g)return;const[,b]=g;typeof r=="function"&&(b.pathname=r(b.pathname)),o[b.pathname]&&(clearTimeout(o[b.pathname]),delete o[b.pathname])}function E(d){if(d.defaultPrevented)return;let g=d.submitter&&d.submitter.hasAttribute("formaction")?d.submitter.getAttribute("formaction"):d.target.getAttribute("action");if(!g)return;if(!g.startsWith("https://action/")){const w=new URL(g,We);if(g=s.parsePath(w.pathname+w.search),!g.startsWith(n))return}if(d.target.method.toUpperCase()!=="POST")throw new Error("Only POST forms are supported for Actions");const b=bn.get(g);if(b){d.preventDefault();const w=new FormData(d.target);d.submitter&&d.submitter.name&&w.append(d.submitter.name,d.submitter.value),b.call({r:s,f:d.target},w)}}Be(["click","submit"]),document.addEventListener("click",f),e&&(document.addEventListener("mouseover",h),document.addEventListener("mouseout",m),document.addEventListener("focusin",u),document.addEventListener("touchstart",u)),document.addEventListener("submit",E),W(()=>{document.removeEventListener("click",f),e&&(document.removeEventListener("mouseover",h),document.removeEventListener("mouseout",m),document.removeEventListener("focusin",u),document.removeEventListener("touchstart",u)),document.removeEventListener("submit",E)})}}function On(e){const t=()=>{const r=window.location.pathname+window.location.search;return{value:e.transformUrl?e.transformUrl(r)+window.location.hash:r+window.location.hash,state:window.history.state}},n=Ke();return gn({get:t,set({value:r,replace:s,scroll:i,state:l}){s?window.history.replaceState(Wt(l),"",r):window.history.pushState(l,"",r),yn(decodeURIComponent(window.location.hash.slice(1)),i),Pe()},init:r=>wn(window,"popstate",qt(r,s=>{if(s&&s<0)return!n.confirm(s);{const i=t();return!n.confirm(i.value,{state:i.state})}})),create:pn(e.preload,e.explicitLinks,e.actionBase,e.transformUrl),utils:{go:r=>window.history.go(r),beforeLeave:n}})(e)}var An=Tt("<a>");function Ln(e){e=fe({inactiveClass:"inactive",activeClass:"active"},e);const[,t]=pt(e,["href","state","class","activeClass","inactiveClass","end"]),n=en(()=>e.href),r=tn(n),s=nn(),i=P(()=>{const l=n();if(l===void 0)return[!1,!1];const o=F(l.split(/[?#]/,1)[0]).toLowerCase(),a=F(s.pathname).toLowerCase();return[e.end?o===a:a.startsWith(o+"/")||a===o,o===a]});return(()=>{var l=An();return jt(l,fe(t,{get href(){return r()||e.href},get state(){return JSON.stringify(e.state)},get classList(){return{...e.class&&{[e.class]:!0},[e.inactiveClass]:!i()[0],[e.activeClass]:i()[0],...t.classList}},link:"",get"aria-current"(){return i()[1]?"page":void 0}}),!1,!1),l})()}export{Ln as A,vn as F,xn as P,Cn as R,Ve as S,jt as a,Ot as b,k as c,kt as d,Be as e,$ as f,I as g,W as h,he as i,De as j,Mt as k,It as l,fe as m,On as n,Sn as o,Rn as r,pt as s,Tt as t,nn as u};
