import { Component } from 'solid-js';
import { css, cx } from '../../styled-system/css';

interface LoadingProps {
  size?: 'sm' | 'md' | 'lg';
  color?: 'primary' | 'secondary' | 'white';
  text?: string;
  fullScreen?: boolean;
  class?: string;
}

const Loading: Component<LoadingProps> = (props) => {
  const containerStyles = css({
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
    justifyContent: 'center',
    gap: '12px',
  });

  const fullScreenStyles = css({
    position: 'fixed',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
    zIndex: 9999,
  });

  const spinnerStyles = css({
    borderRadius: '50%',
    border: '3px solid',
    borderTopColor: 'transparent',
    animation: 'spin 1s linear infinite',
  });

  const sizeStyles = {
    sm: css({
      width: '20px',
      height: '20px',
    }),
    md: css({
      width: '32px',
      height: '32px',
    }),
    lg: css({
      width: '48px',
      height: '48px',
    }),
  };

  const colorStyles = {
    primary: css({
      borderColor: 'blue.200',
      borderTopColor: 'transparent',
    }),
    secondary: css({
      borderColor: 'gray.300',
      borderTopColor: 'transparent',
    }),
    white: css({
      borderColor: 'rgba(255, 255, 255, 0.3)',
      borderTopColor: 'transparent',
    }),
  };

  const textStyles = css({
    fontSize: '14px',
    color: 'gray.600',
    fontWeight: '500',
  });

  const size = props.size || 'md';
  const color = props.color || 'primary';

  return (
    <div 
      class={cx(
        containerStyles,
        props.fullScreen && fullScreenStyles,
        props.class
      )}
    >
      <div 
        class={cx(
          spinnerStyles,
          sizeStyles[size],
          colorStyles[color]
        )}
      />
      {props.text && (
        <div class={textStyles}>
          {props.text}
        </div>
      )}
    </div>
  );
};

export default Loading;
