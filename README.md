# 量化交易前端平台 - 简化版本

这是一个基于 SolidJS 的轻量级量化交易前端平台的简化版本。

## 特性

- ✅ **完整的API系统**: 实现了市场数据、策略管理、回测分析、用户管理等完整API
- ✅ **实时数据支持**: WebSocket连接实现实时行情和数据推送
- ✅ **状态管理**: 基于SolidJS的响应式状态管理系统
- ✅ **模拟数据**: 内置模拟数据生成，无需后端即可运行
- ✅ **类型安全**: 完整的TypeScript类型定义
- ✅ **响应式设计**: 适配不同屏幕尺寸
- ✅ **主题切换**: 支持明暗主题切换

## 技术栈

- **前端框架**: SolidJS 1.8.0
- **路由**: @solidjs/router
- **构建工具**: Vite
- **类型检查**: TypeScript
- **状态管理**: SolidJS Signals + 自定义Store
- **API客户端**: 自定义HTTP客户端 + WebSocket
- **样式**: 内联样式 (简化版本)

## 快速开始

### 安装依赖

```bash
npm install
```

### 启动开发服务器

```bash
npm run dev
```

应用通常在 http://localhost:3001 或空闲端口启动（见控制台输出）。

### 环境变量

提供 `.env.example` 作为模板：

```
VITE_MONACO_LOCAL=true
VITE_API_BASE_URL=/api
```

默认启用本地 Monaco 资源；API 经 Vite 代理转发到后端。

### 构建生产版本

```bash
npm run build
```

### 预览生产版本

```bash
npm run preview
```

## 项目结构

```
src/
├── api/                # API层
│   ├── market.ts       # 市场数据API
│   ├── strategy.ts     # 策略管理API
│   ├── backtest.ts     # 回测分析API
│   ├── user.ts         # 用户管理API
│   └── index.ts        # API统一入口
├── stores/             # 状态管理
│   ├── market.ts       # 市场数据状态
│   ├── strategy.ts     # 策略状态
│   ├── user.ts         # 用户状态
│   └── index.ts        # Store统一入口
├── utils/              # 工具函数
│   ├── http.ts         # HTTP客户端
│   ├── websocket.ts    # WebSocket管理
│   └── constants.ts    # 常量定义
├── components/         # 组件
│   ├── Header.tsx      # 顶部导航
│   ├── Footer.tsx      # 底部信息
│   └── LoadingSpinner.tsx # 加载动画
├── pages/              # 页面
│   ├── Dashboard.tsx   # 仪表盘
│   ├── MarketData.tsx  # 市场数据
│   ├── StrategyEditor.tsx # 策略编辑
│   └── BacktestAnalysis.tsx # 回测分析
├── context/            # 上下文
│   └── ThemeContext.tsx # 主题管理
├── types/              # 类型定义
│   └── index.ts
├── App.tsx             # 主应用组件
├── index.tsx           # 应用入口
└── styles.css          # 全局样式
```

## 页面说明

### 仪表盘 (/)
- 显示关键指标卡片
- 市场热点信息
- 策略表现概览

### 市场数据 (/market)
- 实时行情卡片
- 市场分析信息
- 热门资产列表

### 策略编辑 (/strategy)
- 策略列表管理
- AI策略助手界面
- 代码编辑器占位

### 回测分析 (/backtest)
- 回测配置表单
- 关键指标展示
- 历史回测记录

## API功能

### 市场数据API
- ✅ 实时行情获取
- ✅ K线数据查询
- ✅ 股票搜索
- ✅ 市场概览
- ✅ WebSocket实时推送

### 策略管理API
- ✅ 策略CRUD操作
- ✅ 策略模板管理
- ✅ 策略启动/停止
- ✅ 策略性能统计

### 回测分析API
- ✅ 回测任务创建
- ✅ 回测结果查询
- ✅ 回测历史管理
- ✅ 性能指标计算

### 用户管理API
- ✅ 用户认证(登录/注册)
- ✅ 用户资料管理
- ✅ 偏好设置
- ✅ 权限控制

### WebSocket实时数据
- ✅ 实时行情推送
- ✅ 策略状态更新
- ✅ 回测进度通知
- ✅ 自动重连机制

## 简化说明

这个版本相比完整版本进行了以下简化：

1. **移除了复杂依赖**:
   - 删除了 Panda CSS
   - 删除了 Jotai 状态管理
   - 删除了图标库

2. **简化了样式系统**:
   - 使用内联样式替代CSS-in-JS
   - 保留了基本的响应式设计

3. **移除了复杂组件**:
   - 图表组件 (占位显示)
   - AI编辑器组件 (占位显示)
   - 实时数据组件 (占位显示)

4. **简化了状态管理**:
   - 使用本地状态替代全局状态
   - 移除了复杂的数据流

## 开发说明

这个简化版本专注于展示应用的基本结构和页面布局，适合：

- 快速原型开发
- 学习 SolidJS 基础
- 作为更复杂版本的起点

如需完整功能，请参考原始的完整版本。

## 许可证

MIT License
